(()=>{var e={};e.id=2262,e.ids=[2262],e.modules={1463:(e,t,r)=>{"use strict";r.d(t,{U9:()=>a});var s=r(96657);function a(e){return(0,s.ll)`count(${e||s.ll.raw("*")})`.mapWith(Number)}},2371:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4536:(e,t,r)=>{let{createProxy:s}=r(39844);e.exports=s("C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\node_modules\\next\\dist\\client\\app-dir\\link.js")},5148:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(26373).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},6727:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("clipboard-list",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]])},7766:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var s=r(49384),a=r(82348);function i(...e){return(0,a.QP)((0,s.$)(e))}},10022:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10590:(e,t,r)=>{"use strict";r.d(t,{Header:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\layout\\header.tsx","Header")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14189:(e,t,r)=>{"use strict";r.d(t,{o:()=>h});var s=r(37413),a=r(30084),i=r(23469),n=r(32127),l=r(19001);let d=(0,r(26373).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var o=r(4536),c=r.n(o);function h({organization:e}){return(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"p-2 bg-blue-50 rounded-lg",children:(0,s.jsx)(n.A,{className:"h-6 w-6 text-blue-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-900",children:e.name}),(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["@",e.slug]})]})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)(a.E,{className:(e=>{switch(e){case"active":return"bg-green-100 text-green-800";case"suspended":return"bg-yellow-100 text-yellow-800";case"disabled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(e.status),children:e.status}),(0,s.jsx)(a.E,{className:(e=>{switch(e){case"enterprise":return"bg-purple-100 text-purple-800";case"premium":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}})(e.billingPlan),children:e.billingPlan})]})]}),(0,s.jsxs)("div",{className:"space-y-3 mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,s.jsx)(l.A,{className:"h-4 w-4 mr-2"}),(0,s.jsxs)("span",{children:["Created ",new Date(e.createdAt).toLocaleDateString()]})]}),e.features&&e.features.length>0&&(0,s.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.features.slice(0,3).map(e=>(0,s.jsx)("span",{className:"px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded",children:e.replace("_"," ")},e)),e.features.length>3&&(0,s.jsxs)("span",{className:"px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded",children:["+",e.features.length-3," more"]})]})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)(c(),{href:`/master/organizations/${e.id}`,className:"flex-1",children:(0,s.jsxs)(i.$,{variant:"outline",size:"sm",className:"w-full",children:[(0,s.jsx)(d,{className:"h-4 w-4 mr-1"}),"Manage"]})}),(0,s.jsx)(c(),{href:`/master/organizations/${e.id}/analytics`,className:"flex-1",children:(0,s.jsx)(i.$,{variant:"outline",size:"sm",className:"w-full",children:"View Analytics"})})]})]})}},16443:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23))},17313:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},19001:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(26373).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23469:(e,t,r)=>{"use strict";r.d(t,{$:()=>n});var s=r(37413),a=r(61120),i=r(72984);let n=a.forwardRef(({className:e,variant:t="default",size:r="default",...a},n)=>(0,s.jsx)("button",{className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{"bg-blue-600 text-white hover:bg-blue-700":"default"===t,"bg-red-600 text-white hover:bg-red-700":"destructive"===t,"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50":"outline"===t,"bg-gray-100 text-gray-900 hover:bg-gray-200":"secondary"===t,"hover:bg-gray-100 hover:text-gray-900":"ghost"===t,"text-blue-600 underline-offset-4 hover:underline":"link"===t},{"h-10 px-4 py-2":"default"===r,"h-9 rounded-md px-3":"sm"===r,"h-11 rounded-md px-8":"lg"===r,"h-10 w-10":"icon"===r},e),ref:n,...a}));n.displayName="Button"},26373:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var s=r(61120);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),n=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),d=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:i="",children:n,iconNode:c,...h},m)=>(0,s.createElement)("svg",{ref:m,...o,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:l("lucide",i),...!n&&!d(h)&&{"aria-hidden":"true"},...h},[...c.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(n)?n:[n]])),h=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...i},d)=>(0,s.createElement)(c,{ref:d,iconNode:t,className:l(`lucide-${a(n(e))}`,`lucide-${e}`,r),...i}));return r.displayName=n(e),r}},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>n});var s=r(60687),a=r(43210),i=r(7766);let n=a.forwardRef(({className:e,variant:t="default",size:r="default",...a},n)=>(0,s.jsx)("button",{className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{"bg-blue-600 text-white hover:bg-blue-700":"default"===t,"bg-red-600 text-white hover:bg-red-700":"destructive"===t,"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50":"outline"===t,"bg-gray-100 text-gray-900 hover:bg-gray-200":"secondary"===t,"hover:bg-gray-100 hover:text-gray-900":"ghost"===t,"text-blue-600 underline-offset-4 hover:underline":"link"===t},{"h-10 px-4 py-2":"default"===r,"h-9 rounded-md px-3":"sm"===r,"h-11 rounded-md px-8":"lg"===r,"h-10 w-10":"icon"===r},e),ref:n,...a}));n.displayName="Button"},30084:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var s=r(37413);r(61120);var a=r(72984);function i({className:e,variant:t="default",...r}){return(0,s.jsx)("div",{className:(0,a.cn)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{"border-transparent bg-primary text-primary-foreground hover:bg-primary/80":"default"===t,"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80":"secondary"===t,"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80":"destructive"===t,"text-foreground":"outline"===t,"border-transparent bg-blue-100 text-blue-800":"blue"===t,"border-transparent bg-green-100 text-green-800":"green"===t,"border-transparent bg-yellow-100 text-yellow-800":"yellow"===t,"border-transparent bg-red-100 text-red-800":"red"===t,"border-transparent bg-purple-100 text-purple-800":"purple"===t,"border-transparent bg-orange-100 text-orange-800":"orange"===t,"border-transparent bg-gray-100 text-gray-800":"gray"===t},e),...r})}},30599:(e,t,r)=>{"use strict";r.d(t,{Sidebar:()=>g});var s=r(60687),a=r(85814),i=r.n(a),n=r(16189),l=r(7766),d=r(49625),o=r(17313),c=r(53411),h=r(41312),m=r(10022),u=r(85778),x=r(80428),p=r(6727);function g({userRole:e,isMasterAdmin:t}){let r=(0,n.usePathname)(),a=[{href:"/master/dashboard",label:"Master Dashboard",icon:d.A},{href:"/master/organizations",label:"Organizations",icon:o.A},{href:"/master/analytics",label:"Analytics",icon:c.A}],g=[{href:"/admin/dashboard",label:"Dashboard",icon:d.A},{href:"/admin/candidates",label:"Candidates",icon:h.A},{href:"/admin/results",label:"Test Results",icon:m.A},{href:"/admin/payments",label:"Payments",icon:u.A},{href:"/admin/promotions",label:"Promotions",icon:x.A}],b=[{href:"/checker/dashboard",label:"Dashboard",icon:d.A},{href:"/checker/entry",label:"Result Entry",icon:p.A},{href:"/checker/results",label:"My Results",icon:m.A}],y=t?[...a,...g]:"admin"===e?g:b;return(0,s.jsx)("aside",{className:"w-64 bg-white shadow-sm border-r border-gray-200 min-h-screen",children:(0,s.jsx)("nav",{className:"mt-8 px-4",children:(0,s.jsx)("ul",{className:"space-y-2",children:y.map(e=>{let t=e.icon,a=r===e.href||r.startsWith(e.href+"/");return(0,s.jsx)("li",{children:(0,s.jsxs)(i(),{href:e.href,className:(0,l.cn)("flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors",a?"bg-blue-50 text-blue-700 border-r-2 border-blue-700":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"),children:[(0,s.jsx)(t,{className:"mr-3 h-5 w-5"}),e.label]})},e.href)})})})})}},32127:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(26373).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37882:()=>{},40083:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},41312:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},43648:(e,t,r)=>{"use strict";r.d(t,{C:()=>a});var s=r(37413);function a({title:e,value:t,icon:r,description:a,trend:i}){return(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e}),(0,s.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:t.toLocaleString()})]}),(0,s.jsx)("div",{className:"p-3 bg-blue-50 rounded-full",children:(0,s.jsx)(r,{className:"h-6 w-6 text-blue-600"})})]}),(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)("p",{className:"text-sm text-gray-600",children:a}),(0,s.jsx)("p",{className:"text-sm text-green-600 font-medium",children:i})]})]})}},49625:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},50259:(e,t,r)=>{Promise.resolve().then(r.bind(r,74456)),Promise.resolve().then(r.bind(r,30599))},53411:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},55511:e=>{"use strict";e.exports=require("crypto")},58869:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},59105:(e,t,r)=>{"use strict";r.d(t,{Sidebar:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\layout\\sidebar.tsx","Sidebar")},61135:()=>{},62311:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>h,pages:()=>c,routeModule:()=>m,tree:()=>o});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),l=r(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);r.d(t,d);let o={children:["",{children:["master",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,63314)),"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\master\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,70090)),"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\master\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\master\\dashboard\\page.tsx"],h={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/master/dashboard/page",pathname:"/master/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},62688:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var s=r(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),n=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),d=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:i="",children:n,iconNode:c,...h},m)=>(0,s.createElement)("svg",{ref:m,...o,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:l("lucide",i),...!n&&!d(h)&&{"aria-hidden":"true"},...h},[...c.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(n)?n:[n]])),h=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...i},d)=>(0,s.createElement)(c,{ref:d,iconNode:t,className:l(`lucide-${a(n(e))}`,`lucide-${e}`,r),...i}));return r.displayName=n(e),r}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63314:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y});var s=r(37413),a=r(26326),i=r(71682),n=r(32767),l=r(1463),d=r(94634),o=r(43648),c=r(14189),h=r(32127),m=r(19001),u=r(88804),x=r(5148),p=r(23469),g=r(4536),b=r.n(g);async function y(){let e=await (0,a.j2)();if(!e?.user?.masterAdmin)return(0,s.jsx)("div",{children:"Access denied"});let[t,r,g,y,f]=await Promise.all([i.db.select({count:(0,l.U9)()}).from(n.organizations),i.db.select({count:(0,l.U9)()}).from(n.users),i.db.select({count:(0,l.U9)()}).from(n.candidates),i.db.select({count:(0,l.U9)()}).from(n.paymentTransactions).where((0,d.Uo)((0,d.eq)(n.paymentTransactions.status,"completed"),(0,d.RO)(n.paymentTransactions.createdAt,new Date(Date.now()-2592e6)))),i.db.select().from(n.organizations).orderBy(n.organizations.createdAt).limit(6)]),v=[{title:"Total Organizations",value:t[0]?.count||0,icon:h.A,description:"Active test centers",trend:"+5% from last month"},{title:"Total Users",value:r[0]?.count||0,icon:m.A,description:"System users",trend:"+12% from last month"},{title:"Total Candidates",value:g[0]?.count||0,icon:u.A,description:"Registered candidates",trend:"+18% from last month"},{title:"Recent Payments",value:y[0]?.count||0,icon:x.A,description:"Last 30 days",trend:"+25% from last month"}];return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Master Dashboard"}),(0,s.jsx)("p",{className:"text-gray-600",children:"System-wide overview and management"})]}),(0,s.jsx)(b(),{href:"/master/organizations",children:(0,s.jsxs)(p.$,{children:[(0,s.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Create Organization"]})})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:v.map((e,t)=>(0,s.jsx)(o.C,{...e},t))}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Organizations"}),(0,s.jsx)(b(),{href:"/master/organizations",className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:"View all →"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:f.map(e=>(0,s.jsx)(c.o,{organization:e},e.id))})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"System Health"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600",children:"99.9%"}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"Uptime"})]}),(0,s.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:"1.2s"}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"Avg Response"})]}),(0,s.jsxs)("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:"0"}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"Critical Issues"})]})]})]})]})}},67973:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},70090:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(37413),a=r(26326),i=r(39916),n=r(59105),l=r(10590);async function d({children:e}){let t=await (0,a.j2)();return t?.user||(0,i.redirect)("/login"),t.user.masterAdmin||(0,i.redirect)("/admin"),(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)(l.Header,{user:t.user}),(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)(n.Sidebar,{userRole:t.user.role,isMasterAdmin:t.user.masterAdmin}),(0,s.jsx)("main",{className:"flex-1 p-6",children:e})]})]})}},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},72984:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var s=r(75986),a=r(8974);function i(...e){return(0,a.QP)((0,s.$)(e))}},74456:(e,t,r)=>{"use strict";r.d(t,{Header:()=>o});var s=r(60687),a=r(99208),i=r(29523),n=r(58869),l=r(84027),d=r(40083);function o({user:e}){return(0,s.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"Enhanced IELTS System"}),e.masterAdmin&&(0,s.jsx)("span",{className:"ml-3 px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full",children:"Master Admin"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(n.A,{className:"h-4 w-4 text-gray-500"}),(0,s.jsxs)("div",{className:"text-sm",children:[(0,s.jsx)("div",{className:"font-medium text-gray-900",children:e.name}),(0,s.jsx)("div",{className:"text-gray-500 capitalize",children:e.role})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(i.$,{variant:"ghost",size:"sm",children:(0,s.jsx)(l.A,{className:"h-4 w-4"})}),(0,s.jsxs)(i.$,{variant:"ghost",size:"sm",onClick:()=>{(0,a.CI)({callbackUrl:"/login"})},children:[(0,s.jsx)(d.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"ml-1",children:"Sign Out"})]})]})]})]})})})}},74998:e=>{"use strict";e.exports=require("perf_hooks")},77598:e=>{"use strict";e.exports=require("node:crypto")},78245:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},79551:e=>{"use strict";e.exports=require("url")},80428:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]])},84027:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},85778:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},88804:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(26373).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},91026:()=>{},91645:e=>{"use strict";e.exports=require("net")},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,metadata:()=>d});var s=r(37413),a=r(22376),i=r.n(a),n=r(68726),l=r.n(n);r(61135);let d={title:"Create Next App",description:"Generated by create next app"};function o({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${i().variable} ${l().variable} antialiased`,children:e})})}},97115:(e,t,r)=>{Promise.resolve().then(r.bind(r,10590)),Promise.resolve().then(r.bind(r,59105))}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,5552,2190,1057,145,1658,9069,5814,892,4017,6326],()=>r(62311));module.exports=s})();