(()=>{var e={};e.id=7968,e.ids=[7968],e.modules={1459:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>d});var a=s(65239),r=s(48088),n=s(88170),i=s.n(n),l=s(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);s.d(t,c);let d={children:["",{children:["admin",{children:["payments",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,55824)),"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\admin\\payments\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,99111)),"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\admin\\payments\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/payments/page",pathname:"/admin/payments",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5148:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(26373).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},5336:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},8737:(e,t,s)=>{"use strict";s.d(t,{PaymentStatistics:()=>m});var a=s(60687),r=s(85778);let n=(0,s(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]);var i=s(5336),l=s(48730),c=s(43649),d=s(25541),o=s(20916);function m({totalTransactions:e,completedPayments:t,pendingPayments:s,totalRevenue:m,pendingManualPayments:x}){let p=e>0?Math.round(t/e*100):0,u=[{title:"Total Transactions",value:e.toLocaleString(),icon:(0,a.jsx)(r.A,{className:"h-8 w-8 text-blue-600"}),color:"blue",description:"All payment transactions"},{title:"Total Revenue",value:(0,o.ej)(m,"UZS"),icon:(0,a.jsx)(n,{className:"h-8 w-8 text-green-600"}),color:"green",description:"From completed payments"},{title:"Completed Payments",value:t.toLocaleString(),icon:(0,a.jsx)(i.A,{className:"h-8 w-8 text-green-600"}),color:"green",description:`${p}% completion rate`},{title:"Pending Payments",value:s.toLocaleString(),icon:(0,a.jsx)(l.A,{className:"h-8 w-8 text-yellow-600"}),color:"yellow",description:"Awaiting completion"},{title:"Manual Approvals",value:x.toLocaleString(),icon:(0,a.jsx)(c.A,{className:"h-8 w-8 text-orange-600"}),color:"orange",description:"Require admin approval"},{title:"Success Rate",value:`${p}%`,icon:(0,a.jsx)(d.A,{className:"h-8 w-8 text-purple-600"}),color:"purple",description:"Payment completion rate"}];return(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6",children:u.map((e,t)=>(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:e.icon}),(0,a.jsxs)("div",{className:"ml-4 flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.title}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.value}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:e.description})]})]})},t))})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},14929:(e,t,s)=>{"use strict";s.d(t,{ManualPaymentsList:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call ManualPaymentsList() from the server but ManualPaymentsList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\admin\\manual-payments-list.tsx","ManualPaymentsList")},17581:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]])},18760:(e,t,s)=>{"use strict";s.d(t,{PaymentStatistics:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call PaymentStatistics() from the server but PaymentStatistics is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\admin\\payment-statistics.tsx","PaymentStatistics")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20916:(e,t,s)=>{"use strict";function a(e,t="UZS"){return"UZS"===t?new Intl.NumberFormat("uz-UZ",{style:"currency",currency:"UZS",minimumFractionDigits:0}).format(e):new Intl.NumberFormat("en-US",{style:"currency",currency:t}).format(e)}s.d(t,{ej:()=>a})},21389:(e,t,s)=>{"use strict";s.d(t,{ManualPaymentsList:()=>A});var a=s(60687),r=s(43210),n=s(96834),i=s(29523),l=s(27605),c=s(63442),d=s(9275),o=s(19352),m=s(17313),x=s(58869),p=s(5336),u=s(35071),h=s(43649),y=s(41862),g=s(20916);let j=d.z.object({action:d.z.enum(["approve","reject"]),notes:d.z.string().optional()});function v({isOpen:e,onClose:t,transaction:s,onApprovalComplete:d}){var v;let[f,N]=(0,r.useState)(!1),{register:b,handleSubmit:w,watch:A,formState:{errors:k}}=(0,l.mN)({resolver:(0,c.u)(j)}),P=A("action"),S=async e=>{N(!0);try{let t=await fetch(`/api/payments/manual/${s.transaction.id}/approve`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to process approval")}d()}catch(e){console.error("Approval error:",e),alert("Failed to process approval. Please try again.")}finally{N(!1)}};return(0,a.jsx)(o.a,{isOpen:e,onClose:t,size:"large",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[(0,a.jsx)(m.A,{className:"h-6 w-6 text-orange-600 mr-2"}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Manual Payment Review"}),(0,a.jsx)(n.E,{variant:"yellow",className:"ml-3",children:"Pending Approval"})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6 mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Transaction Details"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,a.jsx)("div",{children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Transaction ID"}),(0,a.jsxs)("div",{className:"text-sm text-gray-900 font-mono",children:["#",s.transaction.id]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Amount"}),(0,a.jsx)("div",{className:"text-lg font-bold text-gray-900",children:(0,g.ej)(parseFloat(s.transaction.amount),s.transaction.currency)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Feature Type"}),(0,a.jsx)("div",{className:"text-sm text-gray-900",children:{feedback:"AI Feedback",certificate:"Certificate",progress:"Progress Analytics"}[v=s.transaction.featureType]||v})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Submitted Date"}),(0,a.jsx)("div",{className:"text-sm text-gray-900",children:new Date(s.transaction.createdAt).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})})]})]})}),(0,a.jsx)("div",{children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Candidate"}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 text-gray-400 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:s.candidate?.fullName||"Unknown"}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:s.candidate?.passportNumber})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Email"}),(0,a.jsx)("div",{className:"text-sm text-gray-900",children:s.candidate?.email||"Not provided"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Payment Method"}),(0,a.jsx)("div",{className:"text-sm text-gray-900 capitalize",children:s.transaction.metadata?.paymentMethod?.replace("_"," ")||"Not specified"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Reference Number"}),(0,a.jsx)("div",{className:"text-sm text-gray-900 font-mono",children:s.transaction.metadata?.referenceNumber||"Not provided"})]})]})})]}),s.transaction.metadata?.paymentDate&&(0,a.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Payment Date (as reported)"}),(0,a.jsx)("div",{className:"text-sm text-gray-900",children:new Date(s.transaction.metadata.paymentDate).toLocaleDateString()})]}),s.transaction.metadata?.notes&&(0,a.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Additional Notes"}),(0,a.jsx)("div",{className:"text-sm text-gray-900 mt-1 p-3 bg-white rounded border",children:s.transaction.metadata.notes})]})]}),(0,a.jsxs)("form",{onSubmit:w(S),className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Approval Decision"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("label",{className:"relative",children:[(0,a.jsx)("input",{type:"radio",value:"approve",...b("action"),className:"sr-only"}),(0,a.jsxs)("div",{className:`
                  border-2 rounded-lg p-4 cursor-pointer transition-all
                  ${"approve"===P?"border-green-500 bg-green-50":"border-gray-200 hover:border-gray-300"}
                `,children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(p.A,{className:"h-5 w-5 text-green-600 mr-2"}),(0,a.jsx)("span",{className:"font-medium text-green-900",children:"Approve Payment"})]}),(0,a.jsx)("p",{className:"text-sm text-green-700 mt-1",children:"Grant access to the premium feature"})]})]}),(0,a.jsxs)("label",{className:"relative",children:[(0,a.jsx)("input",{type:"radio",value:"reject",...b("action"),className:"sr-only"}),(0,a.jsxs)("div",{className:`
                  border-2 rounded-lg p-4 cursor-pointer transition-all
                  ${"reject"===P?"border-red-500 bg-red-50":"border-gray-200 hover:border-gray-300"}
                `,children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(u.A,{className:"h-5 w-5 text-red-600 mr-2"}),(0,a.jsx)("span",{className:"font-medium text-red-900",children:"Reject Payment"})]}),(0,a.jsx)("p",{className:"text-sm text-red-700 mt-1",children:"Decline the payment request"})]})]})]}),k.action&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:k.action.message})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Admin Notes (Optional)"}),(0,a.jsx)("textarea",{...b("notes"),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Add any notes about this approval decision..."})]}),"reject"===P&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(h.A,{className:"h-5 w-5 text-red-600 mr-3 mt-0.5"}),(0,a.jsxs)("div",{className:"text-sm text-red-800",children:[(0,a.jsx)("div",{className:"font-medium mb-1",children:"Payment Rejection"}),(0,a.jsx)("div",{children:"Rejecting this payment will notify the candidate that their payment was not approved. They will not gain access to the premium feature."})]})]})}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,a.jsx)(i.$,{type:"button",variant:"outline",onClick:t,disabled:f,children:"Cancel"}),(0,a.jsx)(i.$,{type:"submit",disabled:f||!P,variant:"approve"===P?"default":"destructive",children:f?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(y.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Processing..."]}):"approve"===P?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Approve Payment"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Reject Payment"]})})]})]})]})})}var f=s(48730),N=s(85778),b=s(17581),w=s(13861);function A({transactions:e}){let[t,s]=(0,r.useState)(null),[l,c]=(0,r.useState)(!1),d=e=>{let t={pending:{color:"yellow",label:"Pending",icon:f.A},completed:{color:"green",label:"Completed",icon:p.A},failed:{color:"red",label:"Failed",icon:u.A},cancelled:{color:"gray",label:"Cancelled",icon:u.A}}[e]||{color:"gray",label:e,icon:f.A},s=t.icon;return(0,a.jsxs)(n.E,{variant:t.color,className:"flex items-center",children:[(0,a.jsx)(s,{className:"h-3 w-3 mr-1"}),t.label]})},o=e=>{switch(e){case"click":return(0,a.jsx)(N.A,{className:"h-4 w-4 text-blue-600"});case"payme":return(0,a.jsx)(b.A,{className:"h-4 w-4 text-green-600"});case"manual":return(0,a.jsx)(m.A,{className:"h-4 w-4 text-orange-600"});default:return(0,a.jsx)(N.A,{className:"h-4 w-4 text-gray-600"})}},h=e=>({feedback:"AI Feedback",certificate:"Certificate",progress:"Progress Analytics"})[e]||e,y=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),j=e=>{s(e),c(!0)};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Payment Transactions"})}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Transaction"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Candidate"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Feature"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Gateway"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsxs)("div",{className:"font-medium text-gray-900",children:["#",e.transaction.id.slice(-8)]}),e.transaction.metadata?.referenceNumber&&(0,a.jsxs)("div",{className:"text-gray-500",children:["Ref: ",e.transaction.metadata.referenceNumber]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(x.A,{className:"h-8 w-8 text-gray-400 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.candidate?.fullName||"Unknown"}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.candidate?.email||e.candidate?.passportNumber})]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:(0,g.ej)(parseFloat(e.transaction.amount),e.transaction.currency)})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:h(e.transaction.featureType)})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[o(e.transaction.gateway),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-900 capitalize",children:e.transaction.gateway})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:d(e.transaction.status)}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm text-gray-900",children:y(e.transaction.createdAt)}),e.transaction.completedAt&&(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["Completed: ",y(e.transaction.completedAt)]})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)(i.$,{size:"sm",variant:"outline",onClick:()=>j(e),children:[(0,a.jsx)(w.A,{className:"h-4 w-4 mr-1"}),"View"]}),"manual"===e.transaction.gateway&&"pending"===e.transaction.status&&(0,a.jsxs)(i.$,{size:"sm",onClick:()=>j(e),children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-1"}),"Review"]})]})})]},e.transaction.id))})]})}),0===e.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(N.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No transactions found"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Payment transactions will appear here once candidates start purchasing features."})]})]}),t&&(0,a.jsx)(v,{isOpen:l,onClose:()=>c(!1),transaction:t,onApprovalComplete:()=>{c(!1),s(null),window.location.reload()}})]})}},21820:e=>{"use strict";e.exports=require("os")},25541:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35071:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},41862:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},43649:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},48730:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55824:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var a=s(37413),r=s(26326),n=s(71682),i=s(32767),l=s(94634),c=s(16048),d=s(14929),o=s(18760),m=s(23469),x=s(78593);let p=(0,s(26373).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]);var u=s(5148),h=s(78768);async function y(){let e=await (0,r.j2)();if(!e?.user?.organizationId)return(0,a.jsx)("div",{children:"Access denied"});if("admin"!==e.user.role&&!e.user.masterAdmin)return(0,a.jsx)("div",{children:"Insufficient permissions"});let t=await n.db.select({transaction:i.paymentTransactions,candidate:i.candidates}).from(i.paymentTransactions).leftJoin(i.candidates,(0,l.eq)(i.paymentTransactions.candidateId,i.candidates.id)).where((0,l.eq)(i.candidates.organizationId,e.user.organizationId)).orderBy((0,c.i)(i.paymentTransactions.createdAt)),s=t.filter(e=>"manual"===e.transaction.gateway&&"pending"===e.transaction.status),y=t.length,g=t.filter(e=>"completed"===e.transaction.status).length,j=t.filter(e=>"pending"===e.transaction.status).length,v=t.filter(e=>"completed"===e.transaction.status).reduce((e,t)=>e+parseFloat(t.transaction.amount),0);return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Payment Management"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage payments and approve manual transactions"})]}),(0,a.jsx)("div",{className:"flex space-x-2",children:(0,a.jsxs)(m.$,{variant:"outline",children:[(0,a.jsx)(p,{className:"h-4 w-4 mr-2"}),"Export"]})})]}),(0,a.jsx)(o.PaymentStatistics,{totalTransactions:y,completedPayments:g,pendingPayments:j,totalRevenue:v,pendingManualPayments:s.length}),s.length>0&&(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(u.A,{className:"h-5 w-5 text-yellow-600 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"font-medium text-yellow-900",children:[s.length," Manual Payment",s.length>1?"s":""," Awaiting Approval"]}),(0,a.jsx)("p",{className:"text-sm text-yellow-700",children:"Review and approve manual payments to grant access to premium features."})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(h.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,a.jsx)(x.p,{placeholder:"Search by candidate name, transaction ID, or reference...",className:"pl-10"})]})}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("select",{className:"px-3 py-2 border border-gray-300 rounded-md text-sm",children:[(0,a.jsx)("option",{value:"",children:"All Gateways"}),(0,a.jsx)("option",{value:"click",children:"Click"}),(0,a.jsx)("option",{value:"payme",children:"Payme"}),(0,a.jsx)("option",{value:"manual",children:"Manual"})]}),(0,a.jsxs)("select",{className:"px-3 py-2 border border-gray-300 rounded-md text-sm",children:[(0,a.jsx)("option",{value:"",children:"All Status"}),(0,a.jsx)("option",{value:"pending",children:"Pending"}),(0,a.jsx)("option",{value:"completed",children:"Completed"}),(0,a.jsx)("option",{value:"failed",children:"Failed"}),(0,a.jsx)("option",{value:"cancelled",children:"Cancelled"})]}),(0,a.jsxs)("select",{className:"px-3 py-2 border border-gray-300 rounded-md text-sm",children:[(0,a.jsx)("option",{value:"",children:"All Features"}),(0,a.jsx)("option",{value:"feedback",children:"AI Feedback"}),(0,a.jsx)("option",{value:"certificate",children:"Certificate"}),(0,a.jsx)("option",{value:"progress",children:"Progress Analytics"})]})]})]})}),(0,a.jsx)(d.ManualPaymentsList,{transactions:t}),0===t.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(u.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No payment transactions"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Payment transactions will appear here once candidates start purchasing premium features."})]})]})}},62518:(e,t,s)=>{Promise.resolve().then(s.bind(s,14929)),Promise.resolve().then(s.bind(s,18760))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74142:(e,t,s)=>{Promise.resolve().then(s.bind(s,21389)),Promise.resolve().then(s.bind(s,8737))},74998:e=>{"use strict";e.exports=require("perf_hooks")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},91645:e=>{"use strict";e.exports=require("net")},96834:(e,t,s)=>{"use strict";s.d(t,{E:()=>n});var a=s(60687);s(43210);var r=s(7766);function n({className:e,variant:t="default",...s}){return(0,a.jsx)("div",{className:(0,r.cn)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{"border-transparent bg-primary text-primary-foreground hover:bg-primary/80":"default"===t,"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80":"secondary"===t,"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80":"destructive"===t,"text-foreground":"outline"===t,"border-transparent bg-blue-100 text-blue-800":"blue"===t,"border-transparent bg-green-100 text-green-800":"green"===t,"border-transparent bg-yellow-100 text-yellow-800":"yellow"===t,"border-transparent bg-red-100 text-red-800":"red"===t,"border-transparent bg-purple-100 text-purple-800":"purple"===t,"border-transparent bg-orange-100 text-orange-800":"orange"===t,"border-transparent bg-gray-100 text-gray-800":"gray"===t},e),...s})}},99111:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var a=s(37413),r=s(26326),n=s(39916),i=s(59105),l=s(10590);async function c({children:e}){let t=await (0,r.j2)();return t?.user||(0,n.redirect)("/login"),"admin"===t.user.role||t.user.masterAdmin||(0,n.redirect)("/checker"),(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)(l.Header,{user:t.user}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)(i.Sidebar,{userRole:t.user.role,isMasterAdmin:t.user.masterAdmin}),(0,a.jsx)("main",{className:"flex-1 p-6",children:e})]})]})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4447,5552,2190,1057,145,1658,9069,5814,892,4017,367,6326,5807],()=>s(1459));module.exports=a})();