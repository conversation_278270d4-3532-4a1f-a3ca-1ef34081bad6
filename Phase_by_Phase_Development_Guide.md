# Enhanced IELTS System - Phase-by-Phase Development Guide

## 📊 **CURRENT IMPLEMENTATION STATUS** (Updated: December 2024)

### ✅ **COMPLETED PHASES**
- ✅ **Phase 1**: Project Foundation (100% Complete)
- ✅ **Phase 2**: Authentication & Organization System (100% Complete)
- ✅ **Phase 3**: Core Candidate & Test Management (100% Complete)
- ✅ **Phase 4**: Payment Integration & Paywall System (100% Complete)
- ✅ **Phase 5**: Public Results Interface (100% Complete)
- ✅ **Phase 6**: AI Feedback System (100% Complete)
- ✅ **Phase 7**: Certificate System (100% Complete)

### 🚀 **LIVE SYSTEM FEATURES**
- ✅ Multi-role authentication (Master Admin, Org Admin, Test Checker)
- ✅ Organization management with CRUD operations
- ✅ Candidate management with validation
- ✅ Test registration and comprehensive IELTS results entry
- ✅ Automatic band score calculations and test history tracking
- ✅ Payment gateway integration (Click & Payme APIs)
- ✅ Paywall system with premium feature access control
- ✅ Manual payment processing with admin approval
- ✅ Payment transaction tracking and analytics
- ✅ Public results search by passport/birth certificate
- ✅ Tabbed results interface (Results/Progress/Feedback/Certificate)
- ✅ Detailed score breakdown with IELTS band descriptions
- ✅ **AI-Powered Feedback System with Anthropic Claude integration**
- ✅ **Professional PDF Certificate Generation with QR verification**
- ✅ **Certificate lifecycle management with 6-month expiration**
- ✅ **Public certificate verification system**
- ✅ Premium features integration with paywall system
- ✅ Responsive public interface for mobile and desktop
- ✅ Role-based dashboards and navigation
- ✅ Database with full schema and sample data
- ✅ Responsive UI with Tailwind CSS

### 🔑 **TEST CREDENTIALS**
```
Master Admin: <EMAIL> / admin123
Org Admin: <EMAIL> / password123
Test Checker: <EMAIL> / password123
```

### 🎯 **NEXT MILESTONE**
Ready to implement Phase 8: Promotional System with flexible promotional rules engine and student discounts

---

## �🎯 Development Approach

Each phase builds upon the previous one, creating a fully functional system incrementally. Follow this guide step-by-step to ensure proper implementation.

---

## 📋 Phase 1: Project Foundation (Weeks 1-2)

### Week 1: Project Setup & Environment

#### Step 1.1: Initialize Project
```bash
# Create Next.js project
npx create-next-app@latest enhanced-ielts-system --typescript --tailwind --eslint --app --src-dir

# Install core dependencies
npm install @auth/drizzle-adapter @paralleldrive/cuid2 bcryptjs drizzle-orm drizzle-kit postgres next-auth@beta

# Install UI dependencies
npm install @radix-ui/react-dialog @radix-ui/react-dropdown-menu @radix-ui/react-select @radix-ui/react-toast @radix-ui/react-tabs lucide-react

# Install form and validation
npm install react-hook-form @hookform/resolvers zod

# Install additional tools
npm install @anthropic-ai/sdk jspdf html2canvas recharts zustand @tanstack/react-query
```

#### Step 1.2: Configure Development Tools
```bash
# Install dev dependencies
npm install -D @types/bcryptjs @types/pg prettier prettier-plugin-tailwindcss husky lint-staged

# Setup Husky for git hooks
npx husky install
npx husky add .husky/pre-commit "lint-staged"
```

#### Step 1.3: Create Project Structure
```bash
# Create main directories
mkdir -p src/{components,lib,types,styles}
mkdir -p src/components/{ui,forms,paywall,results,charts,layout,specialized}
mkdir -p src/lib/{db,auth,payments,ai,certificates,promotions,utils,hooks}
mkdir -p src/app/{master,admin,checker,api}
mkdir -p public/{images,fonts}
mkdir -p scripts docs tests
```

#### Step 1.4: Setup Configuration Files
Create all configuration files (next.config.ts, tailwind.config.ts, drizzle.config.ts, etc.) as specified in the Project Structure document.

### Week 2: Database Design & Setup

#### Step 2.1: Create Database Schema
```typescript
// src/lib/db/schema.ts
import { pgTable, text, timestamp, integer, decimal, boolean, json, unique } from 'drizzle-orm/pg-core';
import { createId } from '@paralleldrive/cuid2';

// Organizations table
export const organizations = pgTable('organizations', {
  id: text('id').primaryKey().$defaultFn(() => createId()),
  name: text('name').notNull(),
  slug: text('slug').unique().notNull(),
  settings: json('settings').$type<OrganizationSettings>().default({}),
  features: json('features').$type<string[]>().default([]),
  billingPlan: text('billing_plan').default('basic'),
  status: text('status', { enum: ['active', 'suspended', 'disabled'] }).default('active'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Continue with all other tables...
```

#### Step 2.2: Setup Database Connection
```typescript
// src/lib/db/index.ts
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema';

const connectionString = process.env.DATABASE_URL!;
const client = postgres(connectionString, { prepare: false });
export const db = drizzle(client, { schema });
```

#### Step 2.3: Create Migration Scripts
```bash
# Generate initial migration
npm run db:generate

# Create seed data script
# scripts/seed-data.ts - Create initial organizations, users, etc.
```

---

## 🔐 Phase 2: Authentication & Organization System (Weeks 3-4)

### Week 3: Authentication Foundation

#### Step 3.1: Configure NextAuth.js
```typescript
// src/lib/auth/config.ts
import NextAuth from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { DrizzleAdapter } from '@auth/drizzle-adapter';
import { db } from '@/lib/db';
import bcrypt from 'bcryptjs';

export const { handlers, auth, signIn, signOut } = NextAuth({
  adapter: DrizzleAdapter(db),
  session: { strategy: 'jwt' },
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        // Implementation for user authentication
        // Check credentials against database
        // Return user object or null
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role;
        token.organizationId = user.organizationId;
        token.masterAdmin = user.masterAdmin;
      }
      return token;
    },
    async session({ session, token }) {
      session.user.role = token.role as string;
      session.user.organizationId = token.organizationId as string;
      session.user.masterAdmin = token.masterAdmin as boolean;
      return session;
    },
  },
});
```

#### Step 3.2: Create Authentication Pages
```typescript
// src/app/(auth)/login/page.tsx
'use client';

import { useState } from 'react';
import { signIn } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const result = await signIn('credentials', {
        email,
        password,
        redirect: false,
      });

      if (result?.ok) {
        // Redirect based on user role
        router.push('/dashboard');
      }
    } catch (error) {
      console.error('Login error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center">
      <form onSubmit={handleSubmit} className="space-y-4 w-full max-w-md">
        <Input
          type="email"
          placeholder="Email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
        />
        <Input
          type="password"
          placeholder="Password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
        />
        <Button type="submit" disabled={isLoading} className="w-full">
          {isLoading ? 'Signing in...' : 'Sign In'}
        </Button>
      </form>
    </div>
  );
}
```

#### Step 3.3: Create Middleware for Route Protection
```typescript
// src/middleware.ts
import { auth } from '@/lib/auth/config';
import { NextResponse } from 'next/server';

export default auth((req) => {
  const { pathname } = req.nextUrl;
  const user = req.auth?.user;

  // Public routes
  const publicRoutes = ['/', '/login', '/search', '/results', '/verify'];
  const isPublicRoute = publicRoutes.some(route => pathname.startsWith(route));

  if (isPublicRoute) {
    return NextResponse.next();
  }

  // Require authentication for protected routes
  if (!user) {
    return NextResponse.redirect(new URL('/login', req.url));
  }

  // Role-based access control
  if (pathname.startsWith('/master') && !user.masterAdmin) {
    return NextResponse.redirect(new URL('/admin', req.url));
  }

  if (pathname.startsWith('/admin') && user.role !== 'admin' && !user.masterAdmin) {
    return NextResponse.redirect(new URL('/checker', req.url));
  }

  return NextResponse.next();
});

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
};
```

### Week 4: Organization Management

#### Step 4.1: Create Master Admin Dashboard
```typescript
// src/app/master/dashboard/page.tsx
import { auth } from '@/lib/auth/config';
import { db } from '@/lib/db';
import { organizations } from '@/lib/db/schema';
import { OrganizationCard } from '@/components/specialized/organization-card';

export default async function MasterDashboardPage() {
  const session = await auth();

  if (!session?.user?.masterAdmin) {
    throw new Error('Unauthorized');
  }

  const orgs = await db.select().from(organizations);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Master Dashboard</h1>
        <Button>Create Organization</Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {orgs.map((org) => (
          <OrganizationCard key={org.id} organization={org} />
        ))}
      </div>
    </div>
  );
}
```

#### Step 4.2: Create Organization CRUD Operations
```typescript
// src/app/api/master/organizations/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth/config';
import { db } from '@/lib/db';
import { organizations, users } from '@/lib/db/schema';
import bcrypt from 'bcryptjs';

export async function POST(request: NextRequest) {
  const session = await auth();

  if (!session?.user?.masterAdmin) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { name, slug, adminEmail, adminPassword, features } = await request.json();

  try {
    // Create organization
    const [org] = await db.insert(organizations).values({
      name,
      slug,
      features,
    }).returning();

    // Create organization admin
    const hashedPassword = await bcrypt.hash(adminPassword, 12);
    await db.insert(users).values({
      organizationId: org.id,
      email: adminEmail,
      password: hashedPassword,
      role: 'admin',
      name: `${name} Admin`,
    });

    return NextResponse.json(org, { status: 201 });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to create organization' }, { status: 500 });
  }
}
```

---

## 👤 Phase 3: Core Candidate & Test Management (Weeks 5-6)

### Week 5: Candidate Management

#### Step 5.1: Create Candidate Registration Form
```typescript
// src/components/forms/candidate-form.tsx
'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { FileUpload } from '@/components/ui/file-upload';

const candidateSchema = z.object({
  fullName: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  phoneNumber: z.string().min(10, 'Phone number must be at least 10 digits'),
  dateOfBirth: z.string(),
  nationality: z.string().min(2, 'Nationality is required'),
  passportNumber: z.string().min(5, 'Passport/Birth certificate number is required'),
  photo: z.instanceof(File).optional(),
  studentStatus: z.boolean().default(false),
});

type CandidateFormData = z.infer<typeof candidateSchema>;

interface CandidateFormProps {
  onSubmit: (data: CandidateFormData) => Promise<void>;
  initialData?: Partial<CandidateFormData>;
}

export function CandidateForm({ onSubmit, initialData }: CandidateFormProps) {
  const form = useForm<CandidateFormData>({
    resolver: zodResolver(candidateSchema),
    defaultValues: initialData,
  });

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
      <Input
        {...form.register('fullName')}
        placeholder="Full Name"
        error={form.formState.errors.fullName?.message}
      />

      <Input
        {...form.register('email')}
        type="email"
        placeholder="Email"
        error={form.formState.errors.email?.message}
      />

      <Input
        {...form.register('phoneNumber')}
        placeholder="Phone Number"
        error={form.formState.errors.phoneNumber?.message}
      />

      <Input
        {...form.register('dateOfBirth')}
        type="date"
        placeholder="Date of Birth"
        error={form.formState.errors.dateOfBirth?.message}
      />

      <Input
        {...form.register('nationality')}
        placeholder="Nationality"
        error={form.formState.errors.nationality?.message}
      />

      <Input
        {...form.register('passportNumber')}
        placeholder="Passport/Birth Certificate Number"
        error={form.formState.errors.passportNumber?.message}
      />

      <FileUpload
        {...form.register('photo')}
        accept="image/*"
        label="Candidate Photo"
      />

      <label className="flex items-center space-x-2">
        <input
          {...form.register('studentStatus')}
          type="checkbox"
          className="rounded"
        />
        <span>Student of this test center</span>
      </label>

      <Button type="submit" disabled={form.formState.isSubmitting}>
        {form.formState.isSubmitting ? 'Saving...' : 'Save Candidate'}
      </Button>
    </form>
  );
}
```

#### Step 5.2: Create Candidate API Endpoints
```typescript
// src/app/api/candidates/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth/config';
import { db } from '@/lib/db';
import { candidates } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  const session = await auth();

  if (!session?.user?.organizationId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const data = await request.json();

  try {
    // Check for existing candidate with same passport number
    const existing = await db
      .select()
      .from(candidates)
      .where(eq(candidates.passportNumber, data.passportNumber))
      .limit(1);

    if (existing.length > 0) {
      return NextResponse.json(
        { error: 'Candidate with this passport/birth certificate number already exists' },
        { status: 409 }
      );
    }

    // Create new candidate
    const [candidate] = await db.insert(candidates).values({
      ...data,
      organizationId: session.user.organizationId,
    }).returning();

    return NextResponse.json(candidate, { status: 201 });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to create candidate' }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  const session = await auth();

  if (!session?.user?.organizationId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { searchParams } = new URL(request.url);
  const search = searchParams.get('search');
  const page = parseInt(searchParams.get('page') || '1');
  const limit = parseInt(searchParams.get('limit') || '20');

  try {
    let query = db
      .select()
      .from(candidates)
      .where(eq(candidates.organizationId, session.user.organizationId));

    if (search) {
      query = query.where(
        or(
          ilike(candidates.fullName, `%${search}%`),
          eq(candidates.passportNumber, search)
        )
      );
    }

    const results = await query
      .limit(limit)
      .offset((page - 1) * limit);

    return NextResponse.json(results);
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch candidates' }, { status: 500 });
  }
}
```

### Week 6: Test Results System

#### Step 6.1: Create Test Registration System
```typescript
// src/components/forms/test-registration-form.tsx
'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const testRegistrationSchema = z.object({
  candidateId: z.string(),
  testDate: z.string(),
  testCenter: z.string(),
  candidateNumber: z.string(),
});

type TestRegistrationData = z.infer<typeof testRegistrationSchema>;

export function TestRegistrationForm({ onSubmit }: { onSubmit: (data: TestRegistrationData) => Promise<void> }) {
  const form = useForm<TestRegistrationData>({
    resolver: zodResolver(testRegistrationSchema),
  });

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
      {/* Form fields for test registration */}
    </form>
  );
}
```

#### Step 6.2: Create Test Results Entry Interface
```typescript
// src/components/forms/result-entry-form.tsx
'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const testResultSchema = z.object({
  listeningScore: z.number().min(0).max(40),
  listeningBandScore: z.number().min(0).max(9).step(0.5),
  readingScore: z.number().min(0).max(40),
  readingBandScore: z.number().min(0).max(9).step(0.5),
  writingTask1Score: z.number().min(0).max(9).step(0.5),
  writingTask2Score: z.number().min(0).max(9).step(0.5),
  writingBandScore: z.number().min(0).max(9).step(0.5),
  speakingFluencyScore: z.number().min(0).max(9).step(0.5),
  speakingLexicalScore: z.number().min(0).max(9).step(0.5),
  speakingGrammarScore: z.number().min(0).max(9).step(0.5),
  speakingPronunciationScore: z.number().min(0).max(9).step(0.5),
  speakingBandScore: z.number().min(0).max(9).step(0.5),
  overallBandScore: z.number().min(0).max(9).step(0.5),
});

type TestResultData = z.infer<typeof testResultSchema>;

export function ResultEntryForm({ onSubmit }: { onSubmit: (data: TestResultData) => Promise<void> }) {
  const form = useForm<TestResultData>({
    resolver: zodResolver(testResultSchema),
  });

  // Auto-calculate overall band score
  const watchedScores = form.watch(['listeningBandScore', 'readingBandScore', 'writingBandScore', 'speakingBandScore']);

  useEffect(() => {
    const [listening, reading, writing, speaking] = watchedScores;
    if (listening && reading && writing && speaking) {
      const overall = Math.round(((listening + reading + writing + speaking) / 4) * 2) / 2;
      form.setValue('overallBandScore', overall);
    }
  }, [watchedScores, form]);

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
      {/* Listening Section */}
      <div className="grid grid-cols-2 gap-4">
        <Input
          {...form.register('listeningScore', { valueAsNumber: true })}
          type="number"
          placeholder="Listening Raw Score (0-40)"
          min="0"
          max="40"
        />
        <Input
          {...form.register('listeningBandScore', { valueAsNumber: true })}
          type="number"
          placeholder="Listening Band Score"
          min="0"
          max="9"
          step="0.5"
        />
      </div>

      {/* Reading Section */}
      <div className="grid grid-cols-2 gap-4">
        <Input
          {...form.register('readingScore', { valueAsNumber: true })}
          type="number"
          placeholder="Reading Raw Score (0-40)"
          min="0"
          max="40"
        />
        <Input
          {...form.register('readingBandScore', { valueAsNumber: true })}
          type="number"
          placeholder="Reading Band Score"
          min="0"
          max="9"
          step="0.5"
        />
      </div>

      {/* Writing Section */}
      <div className="grid grid-cols-3 gap-4">
        <Input
          {...form.register('writingTask1Score', { valueAsNumber: true })}
          type="number"
          placeholder="Writing Task 1"
          min="0"
          max="9"
          step="0.5"
        />
        <Input
          {...form.register('writingTask2Score', { valueAsNumber: true })}
          type="number"
          placeholder="Writing Task 2"
          min="0"
          max="9"
          step="0.5"
        />
        <Input
          {...form.register('writingBandScore', { valueAsNumber: true })}
          type="number"
          placeholder="Writing Band Score"
          min="0"
          max="9"
          step="0.5"
        />
      </div>

      {/* Speaking Section */}
      <div className="grid grid-cols-5 gap-4">
        <Input
          {...form.register('speakingFluencyScore', { valueAsNumber: true })}
          type="number"
          placeholder="Fluency"
          min="0"
          max="9"
          step="0.5"
        />
        <Input
          {...form.register('speakingLexicalScore', { valueAsNumber: true })}
          type="number"
          placeholder="Lexical"
          min="0"
          max="9"
          step="0.5"
        />
        <Input
          {...form.register('speakingGrammarScore', { valueAsNumber: true })}
          type="number"
          placeholder="Grammar"
          min="0"
          max="9"
          step="0.5"
        />
        <Input
          {...form.register('speakingPronunciationScore', { valueAsNumber: true })}
          type="number"
          placeholder="Pronunciation"
          min="0"
          max="9"
          step="0.5"
        />
        <Input
          {...form.register('speakingBandScore', { valueAsNumber: true })}
          type="number"
          placeholder="Speaking Band"
          min="0"
          max="9"
          step="0.5"
        />
      </div>

      {/* Overall Score */}
      <div>
        <Input
          {...form.register('overallBandScore', { valueAsNumber: true })}
          type="number"
          placeholder="Overall Band Score"
          min="0"
          max="9"
          step="0.5"
          readOnly
          className="bg-gray-100"
        />
      </div>

      <Button type="submit" disabled={form.formState.isSubmitting}>
        {form.formState.isSubmitting ? 'Saving...' : 'Save Results'}
      </Button>
    </form>
  );
}
```

---

## 🤖 Phase 6: AI Feedback System (Weeks 11-12)

### Week 11: AI Integration Foundation

#### Step 11.1: Setup Anthropic Claude API Integration
```typescript
// src/lib/ai/claude.ts
import Anthropic from '@anthropic-ai/sdk';

const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY!,
});

export interface FeedbackRequest {
  testResultId: string;
  candidateName: string;
  listeningScore: number;
  listeningBandScore: number;
  readingScore: number;
  readingBandScore: number;
  writingTask1Score: number;
  writingTask2Score: number;
  writingBandScore: number;
  speakingFluencyScore: number;
  speakingLexicalScore: number;
  speakingGrammarScore: number;
  speakingPronunciationScore: number;
  speakingBandScore: number;
  overallBandScore: number;
}

export interface GeneratedFeedback {
  listeningFeedback: string;
  readingFeedback: string;
  writingFeedback: string;
  speakingFeedback: string;
  overallFeedback: string;
  studyRecommendations: string;
  strengths: string[];
  weaknesses: string[];
  studyPlan: string;
}

export async function generateFeedback(request: FeedbackRequest): Promise<GeneratedFeedback> {
  try {
    const prompt = createFeedbackPrompt(request);

    const response = await anthropic.messages.create({
      model: 'claude-3-5-sonnet-20241022',
      max_tokens: 4000,
      temperature: 0.7,
      messages: [
        {
          role: 'user',
          content: prompt,
        },
      ],
    });

    const content = response.content[0];
    if (content.type === 'text') {
      return parseFeedbackResponse(content.text);
    }

    throw new Error('Invalid response format from Claude API');
  } catch (error) {
    console.error('Error generating AI feedback:', error);
    throw new Error('Failed to generate AI feedback');
  }
}

function createFeedbackPrompt(request: FeedbackRequest): string {
  return `
You are an expert IELTS instructor providing detailed feedback for a candidate named ${request.candidateName}.

Test Results:
- Listening: ${request.listeningScore}/40 (Band ${request.listeningBandScore})
- Reading: ${request.readingScore}/40 (Band ${request.readingBandScore})
- Writing: Task 1: ${request.writingTask1Score}, Task 2: ${request.writingTask2Score} (Band ${request.writingBandScore})
- Speaking: Fluency: ${request.speakingFluencyScore}, Lexical: ${request.speakingLexicalScore}, Grammar: ${request.speakingGrammarScore}, Pronunciation: ${request.speakingPronunciationScore} (Band ${request.speakingBandScore})
- Overall Band Score: ${request.overallBandScore}

Please provide comprehensive feedback in the following JSON format:
{
  "listeningFeedback": "Detailed analysis of listening performance with specific improvement areas",
  "readingFeedback": "Detailed analysis of reading performance with specific improvement areas",
  "writingFeedback": "Detailed analysis of writing performance covering both tasks",
  "speakingFeedback": "Detailed analysis of speaking performance covering all criteria",
  "overallFeedback": "Overall performance summary and band score explanation",
  "studyRecommendations": "Specific study recommendations and resources",
  "strengths": ["strength1", "strength2", "strength3"],
  "weaknesses": ["weakness1", "weakness2", "weakness3"],
  "studyPlan": "Detailed 4-week study plan with daily activities"
}

Make the feedback constructive, specific, and actionable. Focus on concrete improvement strategies.
`;
}

function parseFeedbackResponse(response: string): GeneratedFeedback {
  try {
    // Extract JSON from the response
    const jsonMatch = response.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No JSON found in response');
    }

    const parsed = JSON.parse(jsonMatch[0]);

    // Validate required fields
    const requiredFields = [
      'listeningFeedback', 'readingFeedback', 'writingFeedback',
      'speakingFeedback', 'overallFeedback', 'studyRecommendations',
      'strengths', 'weaknesses', 'studyPlan'
    ];

    for (const field of requiredFields) {
      if (!parsed[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    return parsed as GeneratedFeedback;
  } catch (error) {
    console.error('Error parsing feedback response:', error);
    throw new Error('Failed to parse AI feedback response');
  }
}
```

#### Step 11.2: Create AI Feedback Database Schema
```typescript
// Add to src/lib/db/schema.ts
export const aiFeedback = pgTable('ai_feedback', {
  id: text('id').primaryKey().$defaultFn(() => createId()),
  testResultId: text('test_result_id').notNull().references(() => testResults.id, { onDelete: 'cascade' }),
  listeningFeedback: text('listening_feedback').notNull(),
  readingFeedback: text('reading_feedback').notNull(),
  writingFeedback: text('writing_feedback').notNull(),
  speakingFeedback: text('speaking_feedback').notNull(),
  overallFeedback: text('overall_feedback').notNull(),
  studyRecommendations: text('study_recommendations').notNull(),
  strengths: json('strengths').$type<string[]>().notNull(),
  weaknesses: json('weaknesses').$type<string[]>().notNull(),
  studyPlan: text('study_plan').notNull(),
  generatedAt: timestamp('generated_at').defaultNow().notNull(),
  status: text('status', { enum: ['generating', 'completed', 'failed'] }).default('generating'),
});
```

#### Step 11.3: Create AI Feedback API Endpoints
```typescript
// src/app/api/ai/feedback/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth/config';
import { db } from '@/lib/db';
import { aiFeedback, testResults, candidates } from '@/lib/db/schema';
import { generateFeedback } from '@/lib/ai/claude';
import { eq } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  const session = await auth();

  if (!session?.user?.organizationId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { testResultId } = await request.json();

  try {
    // Get test result with candidate info
    const testResult = await db
      .select({
        testResult: testResults,
        candidate: candidates,
      })
      .from(testResults)
      .innerJoin(candidates, eq(testResults.candidateId, candidates.id))
      .where(eq(testResults.id, testResultId))
      .limit(1);

    if (testResult.length === 0) {
      return NextResponse.json({ error: 'Test result not found' }, { status: 404 });
    }

    const { testResult: result, candidate } = testResult[0];

    // Check if feedback already exists
    const existingFeedback = await db
      .select()
      .from(aiFeedback)
      .where(eq(aiFeedback.testResultId, testResultId))
      .limit(1);

    if (existingFeedback.length > 0) {
      return NextResponse.json(existingFeedback[0]);
    }

    // Create initial feedback record
    const [feedbackRecord] = await db.insert(aiFeedback).values({
      testResultId,
      listeningFeedback: '',
      readingFeedback: '',
      writingFeedback: '',
      speakingFeedback: '',
      overallFeedback: '',
      studyRecommendations: '',
      strengths: [],
      weaknesses: [],
      studyPlan: '',
      status: 'generating',
    }).returning();

    // Generate feedback asynchronously
    generateFeedbackAsync(testResultId, result, candidate);

    return NextResponse.json(feedbackRecord, { status: 201 });
  } catch (error) {
    console.error('Error initiating feedback generation:', error);
    return NextResponse.json({ error: 'Failed to generate feedback' }, { status: 500 });
  }
}

async function generateFeedbackAsync(testResultId: string, result: any, candidate: any) {
  try {
    const feedbackRequest = {
      testResultId,
      candidateName: candidate.fullName,
      listeningScore: result.listeningScore,
      listeningBandScore: result.listeningBandScore,
      readingScore: result.readingScore,
      readingBandScore: result.readingBandScore,
      writingTask1Score: result.writingTask1Score,
      writingTask2Score: result.writingTask2Score,
      writingBandScore: result.writingBandScore,
      speakingFluencyScore: result.speakingFluencyScore,
      speakingLexicalScore: result.speakingLexicalScore,
      speakingGrammarScore: result.speakingGrammarScore,
      speakingPronunciationScore: result.speakingPronunciationScore,
      speakingBandScore: result.speakingBandScore,
      overallBandScore: result.overallBandScore,
    };

    const generatedFeedback = await generateFeedback(feedbackRequest);

    // Update feedback record with generated content
    await db
      .update(aiFeedback)
      .set({
        ...generatedFeedback,
        status: 'completed',
      })
      .where(eq(aiFeedback.testResultId, testResultId));

  } catch (error) {
    console.error('Error generating feedback:', error);

    // Mark as failed
    await db
      .update(aiFeedback)
      .set({ status: 'failed' })
      .where(eq(aiFeedback.testResultId, testResultId));
  }
}

export async function GET(request: NextRequest) {
  const session = await auth();

  if (!session?.user?.organizationId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { searchParams } = new URL(request.url);
  const testResultId = searchParams.get('testResultId');

  if (!testResultId) {
    return NextResponse.json({ error: 'Test result ID required' }, { status: 400 });
  }

  try {
    const feedback = await db
      .select()
      .from(aiFeedback)
      .where(eq(aiFeedback.testResultId, testResultId))
      .limit(1);

    if (feedback.length === 0) {
      return NextResponse.json({ error: 'Feedback not found' }, { status: 404 });
    }

    return NextResponse.json(feedback[0]);
  } catch (error) {
    console.error('Error fetching feedback:', error);
    return NextResponse.json({ error: 'Failed to fetch feedback' }, { status: 500 });
  }
}
```

This phase-by-phase guide provides detailed implementation steps for building the Enhanced IELTS System from scratch. Each phase includes specific code examples, file structures, and implementation details to guide the development process systematically.

### Week 12: Feedback Paywall Integration

#### Step 12.1: Create Feedback Display Components
```typescript
// src/components/results/feedback-tab.tsx
'use client';

import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { PaywallOverlay } from '@/components/paywall/paywall-overlay';
import { Loader2, Brain, Target, TrendingUp, BookOpen } from 'lucide-react';

interface FeedbackTabProps {
  testResultId: string;
  hasAccess: boolean;
  onUnlock: () => void;
}

interface AIFeedback {
  id: string;
  listeningFeedback: string;
  readingFeedback: string;
  writingFeedback: string;
  speakingFeedback: string;
  overallFeedback: string;
  studyRecommendations: string;
  strengths: string[];
  weaknesses: string[];
  studyPlan: string;
  status: 'generating' | 'completed' | 'failed';
}

export function FeedbackTab({ testResultId, hasAccess, onUnlock }: FeedbackTabProps) {
  const [feedback, setFeedback] = useState<AIFeedback | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchFeedback();
  }, [testResultId]);

  const fetchFeedback = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/ai/feedback?testResultId=${testResultId}`);

      if (response.status === 404) {
        // Generate feedback if it doesn't exist
        await generateFeedback();
        return;
      }

      if (!response.ok) {
        throw new Error('Failed to fetch feedback');
      }

      const data = await response.json();
      setFeedback(data);

      // Poll for completion if still generating
      if (data.status === 'generating') {
        setTimeout(fetchFeedback, 3000);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load feedback');
    } finally {
      setLoading(false);
    }
  };

  const generateFeedback = async () => {
    try {
      const response = await fetch('/api/ai/feedback', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ testResultId }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate feedback');
      }

      const data = await response.json();
      setFeedback(data);

      // Poll for completion
      setTimeout(fetchFeedback, 3000);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate feedback');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">
            {feedback?.status === 'generating'
              ? 'AI is analyzing your performance...'
              : 'Loading feedback...'}
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 mb-4">{error}</p>
        <Button onClick={fetchFeedback} variant="outline">
          Try Again
        </Button>
      </div>
    );
  }

  if (!feedback) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600 mb-4">No feedback available</p>
        <Button onClick={generateFeedback}>
          Generate AI Feedback
        </Button>
      </div>
    );
  }

  const content = (
    <div className="space-y-6">
      {/* Overall Feedback */}
      <Card className="p-6">
        <div className="flex items-center gap-2 mb-4">
          <Brain className="h-5 w-5 text-blue-600" />
          <h3 className="text-lg font-semibold">Overall Performance Analysis</h3>
        </div>
        <p className="text-gray-700 leading-relaxed">{feedback.overallFeedback}</p>
      </Card>

      {/* Strengths and Weaknesses */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-6">
          <div className="flex items-center gap-2 mb-4">
            <TrendingUp className="h-5 w-5 text-green-600" />
            <h3 className="text-lg font-semibold text-green-700">Strengths</h3>
          </div>
          <div className="space-y-2">
            {feedback.strengths.map((strength, index) => (
              <Badge key={index} variant="secondary" className="bg-green-100 text-green-800">
                {strength}
              </Badge>
            ))}
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-2 mb-4">
            <Target className="h-5 w-5 text-orange-600" />
            <h3 className="text-lg font-semibold text-orange-700">Areas for Improvement</h3>
          </div>
          <div className="space-y-2">
            {feedback.weaknesses.map((weakness, index) => (
              <Badge key={index} variant="secondary" className="bg-orange-100 text-orange-800">
                {weakness}
              </Badge>
            ))}
          </div>
        </Card>
      </div>

      {/* Skill-specific Feedback */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-3">Listening Feedback</h3>
          <p className="text-gray-700 text-sm leading-relaxed">{feedback.listeningFeedback}</p>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-3">Reading Feedback</h3>
          <p className="text-gray-700 text-sm leading-relaxed">{feedback.readingFeedback}</p>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-3">Writing Feedback</h3>
          <p className="text-gray-700 text-sm leading-relaxed">{feedback.writingFeedback}</p>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-3">Speaking Feedback</h3>
          <p className="text-gray-700 text-sm leading-relaxed">{feedback.speakingFeedback}</p>
        </Card>
      </div>

      {/* Study Recommendations */}
      <Card className="p-6">
        <div className="flex items-center gap-2 mb-4">
          <BookOpen className="h-5 w-5 text-purple-600" />
          <h3 className="text-lg font-semibold">Study Recommendations</h3>
        </div>
        <p className="text-gray-700 leading-relaxed mb-4">{feedback.studyRecommendations}</p>

        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-semibold mb-2">4-Week Study Plan</h4>
          <div className="whitespace-pre-line text-sm text-gray-700">
            {feedback.studyPlan}
          </div>
        </div>
      </Card>
    </div>
  );

  return (
    <div className="relative">
      {hasAccess ? (
        content
      ) : (
        <PaywallOverlay
          title="AI-Powered Feedback"
          description="Get detailed, personalized feedback on your IELTS performance with specific improvement recommendations and a custom study plan."
          features={[
            'Detailed analysis of each skill area',
            'Personalized strengths and weaknesses',
            'Specific improvement strategies',
            '4-week custom study plan',
            'Expert-level recommendations'
          ]}
          onUnlock={onUnlock}
          previewContent={
            <div className="space-y-4">
              <Card className="p-6 blur-sm">
                <div className="flex items-center gap-2 mb-4">
                  <Brain className="h-5 w-5 text-blue-600" />
                  <h3 className="text-lg font-semibold">Overall Performance Analysis</h3>
                </div>
                <p className="text-gray-700">Your overall performance shows strong potential...</p>
              </Card>
              <div className="grid grid-cols-2 gap-4">
                <Card className="p-4 blur-sm">
                  <h4 className="font-semibold text-green-700">Strengths</h4>
                  <div className="space-y-1 mt-2">
                    <Badge className="bg-green-100">Strong vocabulary</Badge>
                    <Badge className="bg-green-100">Good pronunciation</Badge>
                  </div>
                </Card>
                <Card className="p-4 blur-sm">
                  <h4 className="font-semibold text-orange-700">Areas to Improve</h4>
                  <div className="space-y-1 mt-2">
                    <Badge className="bg-orange-100">Grammar accuracy</Badge>
                    <Badge className="bg-orange-100">Task response</Badge>
                  </div>
                </Card>
              </div>
            </div>
          }
        />
      )}
    </div>
  );
}
```

#### Step 12.2: Update Public Results Interface
```typescript
// Update src/app/(public)/results/[id]/page.tsx to include feedback tab
import { FeedbackTab } from '@/components/results/feedback-tab';

// Add to the tabs array:
{
  id: 'feedback',
  label: 'AI Feedback',
  icon: Brain,
  content: (
    <FeedbackTab
      testResultId={result.id}
      hasAccess={hasAccess('feedback', result.id)}
      onUnlock={() => handleUnlock('feedback', result.id)}
    />
  ),
  premium: true,
}
```

#### Step 12.3: Add Environment Variables
```bash
# Add to .env.local
ANTHROPIC_API_KEY=your_anthropic_api_key_here
```

#### Step 12.4: Update Database Migration
```bash
# Generate migration for AI feedback table
npm run db:generate

# Apply migration
npm run db:migrate
```

**Phase 6 Status**: ✅ 100% Complete - AI Feedback System with Paywall Integration

---

## 📋 Next Phase: Certificate System (Phase 7)

With Phase 6 complete, the system now includes:
- ✅ Anthropic Claude API integration for AI feedback generation
- ✅ Comprehensive feedback analysis for all IELTS skills
- ✅ Paywall protection for premium AI feedback
- ✅ Asynchronous feedback generation with status tracking
- ✅ Rich feedback display with strengths, weaknesses, and study plans
- ✅ Integration with existing public results interface

**Ready to proceed with Phase 7: Certificate System** - PDF certificate generation with lifecycle management and 6-month expiration.

---

## 📜 Phase 7: Certificate System (Weeks 13-14)

### Week 13: Certificate Generation Foundation

#### Step 13.1: Setup Certificate Database Schema
```typescript
// Add to src/lib/db/schema.ts
export const certificateLifecycle = pgTable('certificate_lifecycle', {
  id: text('id').primaryKey().$defaultFn(() => createId()),
  resultId: text('result_id').notNull().references(() => testResults.id, { onDelete: 'cascade' }),
  serialNumber: text('serial_number').unique().notNull(),
  generatedAt: timestamp('generated_at').defaultNow().notNull(),
  expiresAt: timestamp('expires_at').notNull(),
  status: text('status', {
    enum: ['active', 'expired', 'deleted', 'revoked']
  }).default('active'),
  deletionScheduledAt: timestamp('deletion_scheduled_at'),
  metadata: json('metadata').$type<{
    candidateName: string;
    testDate: string;
    overallBandScore: number;
    organizationName: string;
    certificateType: string;
  }>(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Certificate verification logs
export const certificateVerifications = pgTable('certificate_verifications', {
  id: text('id').primaryKey().$defaultFn(() => createId()),
  certificateId: text('certificate_id').notNull().references(() => certificateLifecycle.id),
  verifiedAt: timestamp('verified_at').defaultNow().notNull(),
  verifierIp: text('verifier_ip'),
  verifierUserAgent: text('verifier_user_agent'),
  verificationMethod: text('verification_method', {
    enum: ['qr_code', 'serial_number', 'direct_link']
  }).notNull(),
});
```

#### Step 13.2: Create Certificate Generation Service
```typescript
// src/lib/certificates/generator.ts
import jsPDF from 'jspdf';
import { db } from '@/lib/db';
import { certificateLifecycle, testResults, candidates, organizations } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import QRCode from 'qrcode';

export interface CertificateData {
  candidateName: string;
  passportNumber: string;
  testDate: string;
  testCenter: string;
  listeningBandScore: number;
  readingBandScore: number;
  writingBandScore: number;
  speakingBandScore: number;
  overallBandScore: number;
  organizationName: string;
  serialNumber: string;
  issueDate: string;
  expiryDate: string;
  candidatePhoto?: string;
}

export class CertificateGenerator {
  private static readonly CERTIFICATE_WIDTH = 297; // A4 landscape width in mm
  private static readonly CERTIFICATE_HEIGHT = 210; // A4 landscape height in mm

  static async generateCertificate(resultId: string): Promise<{
    certificateId: string;
    serialNumber: string;
    pdfBuffer: Buffer;
  }> {
    // Get test result with related data
    const result = await db
      .select({
        testResult: testResults,
        candidate: candidates,
        organization: organizations,
      })
      .from(testResults)
      .innerJoin(candidates, eq(testResults.candidateId, candidates.id))
      .innerJoin(organizations, eq(candidates.organizationId, organizations.id))
      .where(eq(testResults.id, resultId))
      .limit(1);

    if (result.length === 0) {
      throw new Error('Test result not found');
    }

    const { testResult, candidate, organization } = result[0];

    // Check if certificate already exists
    const existingCert = await db
      .select()
      .from(certificateLifecycle)
      .where(eq(certificateLifecycle.resultId, resultId))
      .limit(1);

    if (existingCert.length > 0 && existingCert[0].status === 'active') {
      throw new Error('Certificate already exists for this result');
    }

    // Generate serial number
    const serialNumber = await this.generateSerialNumber();

    // Calculate expiry date (6 months from test date)
    const testDate = new Date(testResult.testDate);
    const expiryDate = new Date(testDate);
    expiryDate.setMonth(expiryDate.getMonth() + 6);

    // Create certificate record
    const [certificate] = await db.insert(certificateLifecycle).values({
      resultId,
      serialNumber,
      expiresAt: expiryDate,
      metadata: {
        candidateName: candidate.fullName,
        testDate: testResult.testDate,
        overallBandScore: testResult.overallBandScore,
        organizationName: organization.name,
        certificateType: 'IELTS Academic',
      },
    }).returning();

    // Generate PDF
    const certificateData: CertificateData = {
      candidateName: candidate.fullName,
      passportNumber: candidate.passportNumber,
      testDate: testResult.testDate,
      testCenter: organization.name,
      listeningBandScore: testResult.listeningBandScore,
      readingBandScore: testResult.readingBandScore,
      writingBandScore: testResult.writingBandScore,
      speakingBandScore: testResult.speakingBandScore,
      overallBandScore: testResult.overallBandScore,
      organizationName: organization.name,
      serialNumber,
      issueDate: new Date().toISOString().split('T')[0],
      expiryDate: expiryDate.toISOString().split('T')[0],
      candidatePhoto: candidate.photoData,
    };

    const pdfBuffer = await this.createPDF(certificateData);

    return {
      certificateId: certificate.id,
      serialNumber,
      pdfBuffer,
    };
  }

  private static async generateSerialNumber(): Promise<string> {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');

    // Get count of certificates this month
    const startOfMonth = new Date(year, new Date().getMonth(), 1);
    const endOfMonth = new Date(year, new Date().getMonth() + 1, 0);

    const count = await db
      .select({ count: sql<number>`count(*)` })
      .from(certificateLifecycle)
      .where(
        and(
          gte(certificateLifecycle.generatedAt, startOfMonth),
          lte(certificateLifecycle.generatedAt, endOfMonth)
        )
      );

    const sequence = String((count[0]?.count || 0) + 1).padStart(4, '0');

    return `IELTS-${year}${month}-${sequence}`;
  }

  private static async createPDF(data: CertificateData): Promise<Buffer> {
    const pdf = new jsPDF({
      orientation: 'landscape',
      unit: 'mm',
      format: 'a4',
    });

    // Set up fonts and colors
    pdf.setFont('helvetica', 'normal');

    // Background and border
    pdf.setFillColor(248, 250, 252); // Light gray background
    pdf.rect(0, 0, this.CERTIFICATE_WIDTH, this.CERTIFICATE_HEIGHT, 'F');

    pdf.setDrawColor(59, 130, 246); // Blue border
    pdf.setLineWidth(2);
    pdf.rect(10, 10, this.CERTIFICATE_WIDTH - 20, this.CERTIFICATE_HEIGHT - 20);

    // Header
    pdf.setFontSize(24);
    pdf.setFont('helvetica', 'bold');
    pdf.setTextColor(59, 130, 246);
    pdf.text('IELTS CERTIFICATE', this.CERTIFICATE_WIDTH / 2, 35, { align: 'center' });

    pdf.setFontSize(16);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(75, 85, 99);
    pdf.text('International English Language Testing System', this.CERTIFICATE_WIDTH / 2, 45, { align: 'center' });

    // Certificate content
    pdf.setFontSize(14);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(0, 0, 0);

    const leftColumn = 30;
    const rightColumn = 180;
    let yPosition = 70;

    // Candidate information
    pdf.setFont('helvetica', 'bold');
    pdf.text('Candidate Name:', leftColumn, yPosition);
    pdf.setFont('helvetica', 'normal');
    pdf.text(data.candidateName, leftColumn + 50, yPosition);

    pdf.setFont('helvetica', 'bold');
    pdf.text('Passport/ID Number:', rightColumn, yPosition);
    pdf.setFont('helvetica', 'normal');
    pdf.text(data.passportNumber, rightColumn + 50, yPosition);

    yPosition += 15;

    pdf.setFont('helvetica', 'bold');
    pdf.text('Test Date:', leftColumn, yPosition);
    pdf.setFont('helvetica', 'normal');
    pdf.text(data.testDate, leftColumn + 30, yPosition);

    pdf.setFont('helvetica', 'bold');
    pdf.text('Test Center:', rightColumn, yPosition);
    pdf.setFont('helvetica', 'normal');
    pdf.text(data.testCenter, rightColumn + 30, yPosition);

    yPosition += 25;

    // Test scores section
    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(16);
    pdf.text('Test Results', this.CERTIFICATE_WIDTH / 2, yPosition, { align: 'center' });

    yPosition += 15;

    // Scores table
    const scoreData = [
      ['Skill', 'Band Score'],
      ['Listening', data.listeningBandScore.toString()],
      ['Reading', data.readingBandScore.toString()],
      ['Writing', data.writingBandScore.toString()],
      ['Speaking', data.speakingBandScore.toString()],
      ['Overall Band Score', data.overallBandScore.toString()],
    ];

    const tableX = this.CERTIFICATE_WIDTH / 2 - 40;
    const tableY = yPosition;
    const cellWidth = 40;
    const cellHeight = 8;

    scoreData.forEach((row, rowIndex) => {
      row.forEach((cell, colIndex) => {
        const x = tableX + (colIndex * cellWidth);
        const y = tableY + (rowIndex * cellHeight);

        if (rowIndex === 0) {
          pdf.setFillColor(59, 130, 246);
          pdf.setTextColor(255, 255, 255);
          pdf.setFont('helvetica', 'bold');
        } else if (rowIndex === scoreData.length - 1) {
          pdf.setFillColor(34, 197, 94);
          pdf.setTextColor(255, 255, 255);
          pdf.setFont('helvetica', 'bold');
        } else {
          pdf.setFillColor(248, 250, 252);
          pdf.setTextColor(0, 0, 0);
          pdf.setFont('helvetica', 'normal');
        }

        pdf.rect(x, y, cellWidth, cellHeight, 'F');
        pdf.setDrawColor(0, 0, 0);
        pdf.rect(x, y, cellWidth, cellHeight);

        pdf.text(cell, x + cellWidth / 2, y + cellHeight / 2 + 1, { align: 'center' });
      });
    });

    yPosition += (scoreData.length * cellHeight) + 20;

    // Certificate details
    pdf.setTextColor(0, 0, 0);
    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(10);

    pdf.text(`Certificate Serial Number: ${data.serialNumber}`, leftColumn, yPosition);
    pdf.text(`Issue Date: ${data.issueDate}`, rightColumn, yPosition);

    yPosition += 8;

    pdf.text(`Valid Until: ${data.expiryDate}`, leftColumn, yPosition);
    pdf.text(`Issued by: ${data.organizationName}`, rightColumn, yPosition);

    // Generate QR code for verification
    const verificationUrl = `${process.env.NEXT_PUBLIC_APP_URL}/verify/${data.serialNumber}`;
    const qrCodeDataUrl = await QRCode.toDataURL(verificationUrl, {
      width: 100,
      margin: 1,
    });

    // Add QR code
    pdf.addImage(qrCodeDataUrl, 'PNG', this.CERTIFICATE_WIDTH - 40, this.CERTIFICATE_HEIGHT - 40, 25, 25);

    pdf.setFontSize(8);
    pdf.text('Scan to verify', this.CERTIFICATE_WIDTH - 27, this.CERTIFICATE_HEIGHT - 10, { align: 'center' });

    // Footer
    pdf.setFontSize(8);
    pdf.setTextColor(107, 114, 128);
    pdf.text(
      'This certificate is valid for 6 months from the test date. For verification, visit our website or scan the QR code.',
      this.CERTIFICATE_WIDTH / 2,
      this.CERTIFICATE_HEIGHT - 5,
      { align: 'center' }
    );

    return Buffer.from(pdf.output('arraybuffer'));
  }
}
```

#### Step 13.3: Create Certificate API Endpoints
```typescript
// src/app/api/certificates/generate/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth/config';
import { CertificateGenerator } from '@/lib/certificates/generator';

export async function POST(request: NextRequest) {
  const session = await auth();

  if (!session?.user?.organizationId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { resultId } = await request.json();

  try {
    const certificate = await CertificateGenerator.generateCertificate(resultId);

    return NextResponse.json({
      certificateId: certificate.certificateId,
      serialNumber: certificate.serialNumber,
      downloadUrl: `/api/certificates/download/${certificate.certificateId}`,
    }, { status: 201 });
  } catch (error) {
    console.error('Error generating certificate:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to generate certificate' },
      { status: 500 }
    );
  }
}
```

#### Step 13.4: Create Certificate Download Endpoint
```typescript
// src/app/api/certificates/download/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth/config';
import { db } from '@/lib/db';
import { certificateLifecycle } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { CertificateGenerator } from '@/lib/certificates/generator';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const session = await auth();

  if (!session?.user?.organizationId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const certificate = await db
      .select()
      .from(certificateLifecycle)
      .where(eq(certificateLifecycle.id, params.id))
      .limit(1);

    if (certificate.length === 0) {
      return NextResponse.json({ error: 'Certificate not found' }, { status: 404 });
    }

    const cert = certificate[0];

    // Check if certificate is still valid
    if (cert.status !== 'active' || new Date() > cert.expiresAt) {
      return NextResponse.json({ error: 'Certificate is no longer valid' }, { status: 410 });
    }

    // Regenerate PDF (in production, you might want to cache this)
    const { pdfBuffer } = await CertificateGenerator.generateCertificate(cert.resultId);

    return new NextResponse(pdfBuffer, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="IELTS_Certificate_${cert.serialNumber}.pdf"`,
      },
    });
  } catch (error) {
    console.error('Error downloading certificate:', error);
    return NextResponse.json({ error: 'Failed to download certificate' }, { status: 500 });
  }
}
```

### Week 14: Certificate Lifecycle Management

#### Step 14.1: Create Certificate Verification System
```typescript
// src/app/(public)/verify/[serial]/page.tsx
import { db } from '@/lib/db';
import { certificateLifecycle, certificateVerifications } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { headers } from 'next/headers';
import { CertificateVerificationDisplay } from '@/components/specialized/certificate-verification';

interface VerifyPageProps {
  params: { serial: string };
}

export default async function VerifyPage({ params }: VerifyPageProps) {
  const headersList = headers();
  const userAgent = headersList.get('user-agent') || '';
  const forwardedFor = headersList.get('x-forwarded-for');
  const realIp = headersList.get('x-real-ip');
  const ip = forwardedFor?.split(',')[0] || realIp || 'unknown';

  try {
    // Find certificate by serial number
    const certificate = await db
      .select()
      .from(certificateLifecycle)
      .where(eq(certificateLifecycle.serialNumber, params.serial))
      .limit(1);

    if (certificate.length === 0) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-red-600 mb-4">Certificate Not Found</h1>
            <p className="text-gray-600">The certificate with serial number {params.serial} could not be found.</p>
          </div>
        </div>
      );
    }

    const cert = certificate[0];

    // Log verification attempt
    await db.insert(certificateVerifications).values({
      certificateId: cert.id,
      verifierIp: ip,
      verifierUserAgent: userAgent,
      verificationMethod: 'qr_code',
    });

    // Check certificate status
    const isExpired = new Date() > cert.expiresAt;
    const isValid = cert.status === 'active' && !isExpired;

    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4">
          <CertificateVerificationDisplay
            certificate={cert}
            isValid={isValid}
            isExpired={isExpired}
          />
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error verifying certificate:', error);
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Verification Error</h1>
          <p className="text-gray-600">An error occurred while verifying the certificate.</p>
        </div>
      </div>
    );
  }
}
```

#### Step 14.2: Create Certificate Verification Display Component
```typescript
// src/components/specialized/certificate-verification.tsx
'use client';

import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, AlertTriangle, Calendar, User, Award } from 'lucide-react';

interface CertificateVerificationProps {
  certificate: {
    id: string;
    serialNumber: string;
    generatedAt: Date;
    expiresAt: Date;
    status: string;
    metadata: {
      candidateName: string;
      testDate: string;
      overallBandScore: number;
      organizationName: string;
      certificateType: string;
    };
  };
  isValid: boolean;
  isExpired: boolean;
}

export function CertificateVerificationDisplay({
  certificate,
  isValid,
  isExpired
}: CertificateVerificationProps) {
  const getStatusIcon = () => {
    if (isValid) return <CheckCircle className="h-8 w-8 text-green-600" />;
    if (isExpired) return <AlertTriangle className="h-8 w-8 text-orange-600" />;
    return <XCircle className="h-8 w-8 text-red-600" />;
  };

  const getStatusText = () => {
    if (isValid) return 'Valid Certificate';
    if (isExpired) return 'Expired Certificate';
    return 'Invalid Certificate';
  };

  const getStatusColor = () => {
    if (isValid) return 'text-green-600';
    if (isExpired) return 'text-orange-600';
    return 'text-red-600';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <div className="flex justify-center mb-4">
          {getStatusIcon()}
        </div>
        <h1 className={`text-3xl font-bold ${getStatusColor()}`}>
          {getStatusText()}
        </h1>
        <p className="text-gray-600 mt-2">
          Certificate Serial Number: {certificate.serialNumber}
        </p>
      </div>

      {/* Certificate Details */}
      <Card className="p-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Candidate Information */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <User className="h-5 w-5 text-blue-600" />
              <h3 className="text-lg font-semibold">Candidate Information</h3>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">Full Name</label>
              <p className="text-lg font-semibold">{certificate.metadata.candidateName}</p>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">Test Date</label>
              <p className="text-lg">{certificate.metadata.testDate}</p>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">Test Center</label>
              <p className="text-lg">{certificate.metadata.organizationName}</p>
            </div>
          </div>

          {/* Test Results */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <Award className="h-5 w-5 text-green-600" />
              <h3 className="text-lg font-semibold">Test Results</h3>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">Certificate Type</label>
              <p className="text-lg">{certificate.metadata.certificateType}</p>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">Overall Band Score</label>
              <div className="flex items-center gap-2">
                <span className="text-2xl font-bold text-green-600">
                  {certificate.metadata.overallBandScore}
                </span>
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  Band {certificate.metadata.overallBandScore}
                </Badge>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Certificate Status */}
      <Card className="p-6">
        <div className="flex items-center gap-2 mb-4">
          <Calendar className="h-5 w-5 text-blue-600" />
          <h3 className="text-lg font-semibold">Certificate Status</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="text-sm font-medium text-gray-500">Issue Date</label>
            <p className="text-lg">{certificate.generatedAt.toLocaleDateString()}</p>
          </div>

          <div>
            <label className="text-sm font-medium text-gray-500">Expiry Date</label>
            <p className="text-lg">{certificate.expiresAt.toLocaleDateString()}</p>
          </div>

          <div>
            <label className="text-sm font-medium text-gray-500">Status</label>
            <Badge
              variant={isValid ? "default" : "destructive"}
              className={isValid ? "bg-green-100 text-green-800" : ""}
            >
              {certificate.status.toUpperCase()}
            </Badge>
          </div>
        </div>

        {isExpired && (
          <div className="mt-4 p-4 bg-orange-50 border border-orange-200 rounded-lg">
            <p className="text-orange-800">
              <strong>Note:</strong> This certificate has expired. IELTS certificates are valid for 6 months from the test date.
            </p>
          </div>
        )}

        {!isValid && !isExpired && (
          <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-800">
              <strong>Warning:</strong> This certificate is not valid. It may have been revoked or deleted.
            </p>
          </div>
        )}
      </Card>

      {/* Verification Info */}
      <Card className="p-6 bg-blue-50 border-blue-200">
        <h3 className="text-lg font-semibold text-blue-800 mb-2">Verification Information</h3>
        <p className="text-blue-700 text-sm">
          This certificate has been verified against our secure database.
          The verification was performed on {new Date().toLocaleDateString()} at {new Date().toLocaleTimeString()}.
        </p>
      </Card>
    </div>
  );
}
```

#### Step 14.3: Create Certificate Lifecycle Management
```typescript
// src/lib/certificates/lifecycle.ts
import { db } from '@/lib/db';
import { certificateLifecycle } from '@/lib/db/schema';
import { lt, eq, and } from 'drizzle-orm';

export class CertificateLifecycleManager {
  /**
   * Mark expired certificates and schedule deletion
   */
  static async processExpiredCertificates(): Promise<void> {
    const now = new Date();

    // Find certificates that have expired but are still active
    const expiredCertificates = await db
      .select()
      .from(certificateLifecycle)
      .where(
        and(
          lt(certificateLifecycle.expiresAt, now),
          eq(certificateLifecycle.status, 'active')
        )
      );

    for (const cert of expiredCertificates) {
      // Schedule deletion 30 days after expiry
      const deletionDate = new Date(cert.expiresAt);
      deletionDate.setDate(deletionDate.getDate() + 30);

      await db
        .update(certificateLifecycle)
        .set({
          status: 'expired',
          deletionScheduledAt: deletionDate,
          updatedAt: now,
        })
        .where(eq(certificateLifecycle.id, cert.id));
    }

    console.log(`Processed ${expiredCertificates.length} expired certificates`);
  }

  /**
   * Delete certificates that are scheduled for deletion
   */
  static async deleteScheduledCertificates(): Promise<void> {
    const now = new Date();

    // Find certificates scheduled for deletion
    const certificatesToDelete = await db
      .select()
      .from(certificateLifecycle)
      .where(
        and(
          lt(certificateLifecycle.deletionScheduledAt, now),
          eq(certificateLifecycle.status, 'expired')
        )
      );

    for (const cert of certificatesToDelete) {
      await db
        .update(certificateLifecycle)
        .set({
          status: 'deleted',
          updatedAt: now,
        })
        .where(eq(certificateLifecycle.id, cert.id));
    }

    console.log(`Deleted ${certificatesToDelete.length} expired certificates`);
  }

  /**
   * Send expiration warnings (to be called daily)
   */
  static async sendExpirationWarnings(): Promise<void> {
    const warningDate = new Date();
    warningDate.setDate(warningDate.getDate() + 30); // 30 days before expiry

    const certificatesNearExpiry = await db
      .select()
      .from(certificateLifecycle)
      .where(
        and(
          lt(certificateLifecycle.expiresAt, warningDate),
          eq(certificateLifecycle.status, 'active')
        )
      );

    // TODO: Implement email notification system
    console.log(`Found ${certificatesNearExpiry.length} certificates expiring within 30 days`);
  }

  /**
   * Get certificate statistics
   */
  static async getCertificateStats(): Promise<{
    total: number;
    active: number;
    expired: number;
    deleted: number;
  }> {
    const stats = await db
      .select({
        status: certificateLifecycle.status,
        count: sql<number>`count(*)`,
      })
      .from(certificateLifecycle)
      .groupBy(certificateLifecycle.status);

    const result = {
      total: 0,
      active: 0,
      expired: 0,
      deleted: 0,
    };

    stats.forEach(stat => {
      result.total += stat.count;
      result[stat.status as keyof typeof result] = stat.count;
    });

    return result;
  }
}
```

#### Step 14.4: Create Certificate Tab Component
```typescript
// src/components/results/certificate-tab.tsx
'use client';

import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { PaywallOverlay } from '@/components/paywall/paywall-overlay';
import { Download, FileText, Calendar, CheckCircle, AlertTriangle } from 'lucide-react';

interface CertificateTabProps {
  testResultId: string;
  hasAccess: boolean;
  onUnlock: () => void;
}

interface Certificate {
  id: string;
  serialNumber: string;
  generatedAt: string;
  expiresAt: string;
  status: 'active' | 'expired' | 'deleted' | 'revoked';
  downloadUrl: string;
}

export function CertificateTab({ testResultId, hasAccess, onUnlock }: CertificateTabProps) {
  const [certificate, setCertificate] = useState<Certificate | null>(null);
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (hasAccess) {
      fetchCertificate();
    } else {
      setLoading(false);
    }
  }, [testResultId, hasAccess]);

  const fetchCertificate = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/certificates?resultId=${testResultId}`);

      if (response.status === 404) {
        setCertificate(null);
        return;
      }

      if (!response.ok) {
        throw new Error('Failed to fetch certificate');
      }

      const data = await response.json();
      setCertificate(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load certificate');
    } finally {
      setLoading(false);
    }
  };

  const generateCertificate = async () => {
    try {
      setGenerating(true);
      setError(null);

      const response = await fetch('/api/certificates/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ resultId: testResultId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate certificate');
      }

      const data = await response.json();
      setCertificate({
        id: data.certificateId,
        serialNumber: data.serialNumber,
        generatedAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString(), // 6 months
        status: 'active',
        downloadUrl: data.downloadUrl,
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate certificate');
    } finally {
      setGenerating(false);
    }
  };

  const downloadCertificate = async () => {
    if (!certificate) return;

    try {
      const response = await fetch(certificate.downloadUrl);
      if (!response.ok) throw new Error('Download failed');

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `IELTS_Certificate_${certificate.serialNumber}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      setError('Failed to download certificate');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'expired':
        return <AlertTriangle className="h-5 w-5 text-orange-600" />;
      default:
        return <AlertTriangle className="h-5 w-5 text-red-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'expired':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-red-100 text-red-800';
    }
  };

  const isExpired = certificate && new Date() > new Date(certificate.expiresAt);

  const content = (
    <div className="space-y-6">
      {loading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading certificate information...</p>
        </div>
      ) : error ? (
        <div className="text-center py-12">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchCertificate} variant="outline">
            Try Again
          </Button>
        </div>
      ) : certificate ? (
        <div className="space-y-6">
          {/* Certificate Status */}
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-blue-600" />
                <h3 className="text-lg font-semibold">IELTS Certificate</h3>
              </div>
              <div className="flex items-center gap-2">
                {getStatusIcon(certificate.status)}
                <Badge className={getStatusColor(certificate.status)}>
                  {certificate.status.toUpperCase()}
                </Badge>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div>
                <label className="text-sm font-medium text-gray-500">Serial Number</label>
                <p className="text-lg font-mono">{certificate.serialNumber}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Issue Date</label>
                <p className="text-lg">{new Date(certificate.generatedAt).toLocaleDateString()}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Expiry Date</label>
                <p className="text-lg">{new Date(certificate.expiresAt).toLocaleDateString()}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Validity</label>
                <p className={`text-lg ${isExpired ? 'text-orange-600' : 'text-green-600'}`}>
                  {isExpired ? 'Expired' : 'Valid'}
                </p>
              </div>
            </div>

            {isExpired && (
              <div className="mb-6 p-4 bg-orange-50 border border-orange-200 rounded-lg">
                <p className="text-orange-800">
                  <strong>Note:</strong> This certificate has expired. IELTS certificates are valid for 6 months from the test date.
                </p>
              </div>
            )}

            <div className="flex gap-4">
              <Button
                onClick={downloadCertificate}
                className="flex items-center gap-2"
                disabled={certificate.status === 'deleted'}
              >
                <Download className="h-4 w-4" />
                Download Certificate
              </Button>

              <Button
                variant="outline"
                onClick={() => window.open(`/verify/${certificate.serialNumber}`, '_blank')}
                className="flex items-center gap-2"
              >
                <CheckCircle className="h-4 w-4" />
                Verify Online
              </Button>
            </div>
          </Card>

          {/* Certificate Information */}
          <Card className="p-6">
            <div className="flex items-center gap-2 mb-4">
              <Calendar className="h-5 w-5 text-blue-600" />
              <h3 className="text-lg font-semibold">Certificate Information</h3>
            </div>

            <div className="space-y-3 text-sm text-gray-600">
              <p>• This certificate is an official document verifying your IELTS test results.</p>
              <p>• The certificate is valid for 6 months from the test date.</p>
              <p>• You can verify the authenticity of this certificate using the QR code or serial number.</p>
              <p>• The certificate includes a secure verification system to prevent fraud.</p>
              <p>• After expiration, the certificate will be automatically deleted from our system.</p>
            </div>
          </Card>
        </div>
      ) : (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No Certificate Generated</h3>
          <p className="text-gray-600 mb-6">
            Generate an official IELTS certificate for your test results.
          </p>
          <Button
            onClick={generateCertificate}
            disabled={generating}
            className="flex items-center gap-2"
          >
            {generating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Generating...
              </>
            ) : (
              <>
                <FileText className="h-4 w-4" />
                Generate Certificate
              </>
            )}
          </Button>
        </div>
      )}
    </div>
  );

  return (
    <div className="relative">
      {hasAccess ? (
        content
      ) : (
        <PaywallOverlay
          title="Official IELTS Certificate"
          description="Generate and download your official IELTS certificate with secure verification and QR code."
          features={[
            'Official PDF certificate',
            'Secure QR code verification',
            'Valid for 6 months',
            'Professional format',
            'Instant download'
          ]}
          onUnlock={onUnlock}
          previewContent={
            <div className="space-y-4">
              <Card className="p-6 blur-sm">
                <div className="flex items-center gap-2 mb-4">
                  <FileText className="h-5 w-5 text-blue-600" />
                  <h3 className="text-lg font-semibold">IELTS Certificate</h3>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Serial Number</label>
                    <p className="text-lg font-mono">IELTS-202412-****</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Status</label>
                    <Badge className="bg-green-100 text-green-800">ACTIVE</Badge>
                  </div>
                </div>
              </Card>
            </div>
          }
        />
      )}
    </div>
  );
}
```

**Phase 7 Status**: ✅ 100% Complete - Certificate System with Lifecycle Management

---

## 📋 Next Phase: Promotional System (Phase 8)

With Phase 7 complete, the system now includes:
- ✅ Professional PDF certificate generation with jsPDF
- ✅ QR code verification system with public verification pages
- ✅ 6-month certificate lifecycle with automatic expiration
- ✅ Certificate download and verification tracking
- ✅ Paywall integration for premium certificate access
- ✅ Automatic deletion scheduling for expired certificates
- ✅ Certificate status management (active/expired/deleted/revoked)
- ✅ Integration with existing public results interface

**Ready to proceed with Phase 8: Promotional System** - Flexible promotional rules engine with student discounts and loyalty rewards.