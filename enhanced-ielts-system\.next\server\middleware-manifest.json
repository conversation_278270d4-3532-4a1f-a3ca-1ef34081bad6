{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "Cll4F487D0HpVoIXylVa_", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "1RmyiZGY3naNJQl5yZ/tfqJuCQ2vIZv83DQj4tN4p4c=", "__NEXT_PREVIEW_MODE_ID": "43af2b7c4334616734b8b17477ea9d6e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "07ae1494384a8f777412521b15444991714144bd95e56da917c1ec3cee1766a6", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "fa36dd006bc6690c777867c200a175f533cd19a8212bb58604b65b83be2fd470"}}}, "functions": {}, "sortedMiddleware": ["/"]}