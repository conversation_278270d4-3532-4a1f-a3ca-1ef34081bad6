"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-auth";
exports.ids = ["vendor-chunks/next-auth"];
exports.modules = {

/***/ "(rsc)/./node_modules/next-auth/index.js":
/*!*****************************************!*\
  !*** ./node_modules/next-auth/index.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthError: () => (/* reexport safe */ _auth_core_errors__WEBPACK_IMPORTED_MODULE_4__.AuthError),\n/* harmony export */   CredentialsSignin: () => (/* reexport safe */ _auth_core_errors__WEBPACK_IMPORTED_MODULE_4__.CredentialsSignin),\n/* harmony export */   customFetch: () => (/* reexport safe */ _auth_core__WEBPACK_IMPORTED_MODULE_0__.customFetch),\n/* harmony export */   \"default\": () => (/* binding */ NextAuth)\n/* harmony export */ });\n/* harmony import */ var _auth_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/core */ \"(rsc)/./node_modules/@auth/core/index.js\");\n/* harmony import */ var _lib_env_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/env.js */ \"(rsc)/./node_modules/next-auth/lib/env.js\");\n/* harmony import */ var _lib_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/index.js */ \"(rsc)/./node_modules/next-auth/lib/index.js\");\n/* harmony import */ var _lib_actions_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/actions.js */ \"(rsc)/./node_modules/next-auth/lib/actions.js\");\n/* harmony import */ var _auth_core_errors__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @auth/core/errors */ \"(rsc)/./node_modules/@auth/core/errors.js\");\n/**\n * _If you are looking to migrate from v4, visit the [Upgrade Guide (v5)](https://authjs.dev/getting-started/migrating-to-v5)._\n *\n * ## Installation\n *\n * ```bash npm2yarn\n * npm install next-auth@beta\n * ```\n *\n * ## Environment variable inference\n *\n * `NEXTAUTH_URL` and `NEXTAUTH_SECRET` have been inferred since v4.\n *\n * Since NextAuth.js v5 can also automatically infer environment variables that are prefixed with `AUTH_`.\n *\n * For example `AUTH_GITHUB_ID` and `AUTH_GITHUB_SECRET` will be used as the `clientId` and `clientSecret` options for the GitHub provider.\n *\n * :::tip\n * The environment variable name inferring has the following format for OAuth providers: `AUTH_{PROVIDER}_{ID|SECRET}`.\n *\n * `PROVIDER` is the uppercase snake case version of the provider's id, followed by either `ID` or `SECRET` respectively.\n * :::\n *\n * `AUTH_SECRET` and `AUTH_URL` are also aliased for `NEXTAUTH_SECRET` and `NEXTAUTH_URL` for consistency.\n *\n * To add social login to your app, the configuration becomes:\n *\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"next-auth/providers/github\"\n * export const { handlers, auth } = NextAuth({ providers: [ GitHub ] })\n * ```\n *\n * And the `.env.local` file:\n *\n * ```sh title=\".env.local\"\n * AUTH_GITHUB_ID=...\n * AUTH_GITHUB_SECRET=...\n * AUTH_SECRET=...\n * ```\n *\n * :::tip\n * In production, `AUTH_SECRET` is a required environment variable - if not set, NextAuth.js will throw an error. See [MissingSecretError](https://authjs.dev/reference/core/errors#missingsecret) for more details.\n * :::\n *\n * If you need to override the default values for a provider, you can still call it as a function `GitHub({...})` as before.\n *\n * ## Lazy initialization\n * You can also initialize NextAuth.js lazily (previously known as advanced intialization), which allows you to access the request context in the configuration in some cases, like Route Handlers, Middleware, API Routes or `getServerSideProps`.\n * The above example becomes:\n *\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"next-auth/providers/github\"\n * export const { handlers, auth } = NextAuth(req => {\n *  if (req) {\n *   console.log(req) // do something with the request\n *  }\n *  return { providers: [ GitHub ] }\n * })\n * ```\n *\n * :::tip\n * This is useful if you want to customize the configuration based on the request, for example, to add a different provider in staging/dev environments.\n * :::\n *\n * @module next-auth\n */\n\n\n\n\n\n\n/**\n *  Initialize NextAuth.js.\n *\n *  @example\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"@auth/core/providers/github\"\n *\n * export const { handlers, auth } = NextAuth({ providers: [GitHub] })\n * ```\n *\n * Lazy initialization:\n *\n * @example\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"@auth/core/providers/github\"\n *\n * export const { handlers, auth } = NextAuth(async (req) => {\n *   console.log(req) // do something with the request\n *   return {\n *     providers: [GitHub],\n *   },\n * })\n * ```\n */\nfunction NextAuth(config) {\n    if (typeof config === \"function\") {\n        const httpHandler = async (req) => {\n            const _config = await config(req);\n            (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(_config);\n            return (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)((0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.reqWithEnvURL)(req), _config);\n        };\n        return {\n            handlers: { GET: httpHandler, POST: httpHandler },\n            // @ts-expect-error\n            auth: (0,_lib_index_js__WEBPACK_IMPORTED_MODULE_2__.initAuth)(config, (c) => (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(c)),\n            signIn: async (provider, options, authorizationParams) => {\n                const _config = await config(undefined);\n                (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(_config);\n                return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.signIn)(provider, options, authorizationParams, _config);\n            },\n            signOut: async (options) => {\n                const _config = await config(undefined);\n                (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(_config);\n                return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.signOut)(options, _config);\n            },\n            unstable_update: async (data) => {\n                const _config = await config(undefined);\n                (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(_config);\n                return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.update)(data, _config);\n            },\n        };\n    }\n    (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(config);\n    const httpHandler = (req) => (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)((0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.reqWithEnvURL)(req), config);\n    return {\n        handlers: { GET: httpHandler, POST: httpHandler },\n        // @ts-expect-error\n        auth: (0,_lib_index_js__WEBPACK_IMPORTED_MODULE_2__.initAuth)(config),\n        signIn: (provider, options, authorizationParams) => {\n            return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.signIn)(provider, options, authorizationParams, config);\n        },\n        signOut: (options) => {\n            return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.signOut)(options, config);\n        },\n        unstable_update: (data) => {\n            return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.update)(data, config);\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/lib/actions.js":
/*!***********************************************!*\
  !*** ./node_modules/next-auth/lib/actions.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   update: () => (/* binding */ update)\n/* harmony export */ });\n/* harmony import */ var _auth_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/core */ \"(rsc)/./node_modules/@auth/core/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\n\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\n\nasync function signIn(provider, options = {}, authorizationParams, config) {\n    const headers = new Headers(await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)());\n    const { redirect: shouldRedirect = true, redirectTo, ...rest } = options instanceof FormData ? Object.fromEntries(options) : options;\n    const callbackUrl = redirectTo?.toString() ?? headers.get(\"Referer\") ?? \"/\";\n    const signInURL = (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.createActionURL)(\"signin\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    if (!provider) {\n        signInURL.searchParams.append(\"callbackUrl\", callbackUrl);\n        if (shouldRedirect)\n            (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(signInURL.toString());\n        return signInURL.toString();\n    }\n    let url = `${signInURL}/${provider}?${new URLSearchParams(authorizationParams)}`;\n    let foundProvider = {};\n    for (const providerConfig of config.providers) {\n        const { options, ...defaults } = typeof providerConfig === \"function\" ? providerConfig() : providerConfig;\n        const id = options?.id ?? defaults.id;\n        if (id === provider) {\n            foundProvider = {\n                id,\n                type: options?.type ?? defaults.type,\n            };\n            break;\n        }\n    }\n    if (!foundProvider.id) {\n        const url = `${signInURL}?${new URLSearchParams({ callbackUrl })}`;\n        if (shouldRedirect)\n            (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(url);\n        return url;\n    }\n    if (foundProvider.type === \"credentials\") {\n        url = url.replace(\"signin\", \"callback\");\n    }\n    headers.set(\"Content-Type\", \"application/x-www-form-urlencoded\");\n    const body = new URLSearchParams({ ...rest, callbackUrl });\n    const req = new Request(url, { method: \"POST\", headers, body });\n    const res = await (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)(req, { ...config, raw: _auth_core__WEBPACK_IMPORTED_MODULE_0__.raw, skipCSRFCheck: _auth_core__WEBPACK_IMPORTED_MODULE_0__.skipCSRFCheck });\n    const cookieJar = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    for (const c of res?.cookies ?? [])\n        cookieJar.set(c.name, c.value, c.options);\n    const responseUrl = res instanceof Response ? res.headers.get(\"Location\") : res.redirect;\n    // NOTE: if for some unexpected reason the responseUrl is not set,\n    // we redirect to the original url\n    const redirectUrl = responseUrl ?? url;\n    if (shouldRedirect)\n        return (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(redirectUrl);\n    return redirectUrl;\n}\nasync function signOut(options, config) {\n    const headers = new Headers(await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)());\n    headers.set(\"Content-Type\", \"application/x-www-form-urlencoded\");\n    const url = (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.createActionURL)(\"signout\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    const callbackUrl = options?.redirectTo ?? headers.get(\"Referer\") ?? \"/\";\n    const body = new URLSearchParams({ callbackUrl });\n    const req = new Request(url, { method: \"POST\", headers, body });\n    const res = await (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)(req, { ...config, raw: _auth_core__WEBPACK_IMPORTED_MODULE_0__.raw, skipCSRFCheck: _auth_core__WEBPACK_IMPORTED_MODULE_0__.skipCSRFCheck });\n    const cookieJar = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    for (const c of res?.cookies ?? [])\n        cookieJar.set(c.name, c.value, c.options);\n    if (options?.redirect ?? true)\n        return (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(res.redirect);\n    return res;\n}\nasync function update(data, config) {\n    const headers = new Headers(await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)());\n    headers.set(\"Content-Type\", \"application/json\");\n    const url = (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.createActionURL)(\"session\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    const body = JSON.stringify({ data });\n    const req = new Request(url, { method: \"POST\", headers, body });\n    const res = await (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)(req, { ...config, raw: _auth_core__WEBPACK_IMPORTED_MODULE_0__.raw, skipCSRFCheck: _auth_core__WEBPACK_IMPORTED_MODULE_0__.skipCSRFCheck });\n    const cookieJar = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    for (const c of res?.cookies ?? [])\n        cookieJar.set(c.name, c.value, c.options);\n    return res.body;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/lib/actions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/lib/env.js":
/*!*******************************************!*\
  !*** ./node_modules/next-auth/lib/env.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reqWithEnvURL: () => (/* binding */ reqWithEnvURL),\n/* harmony export */   setEnvDefaults: () => (/* binding */ setEnvDefaults)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _auth_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth/core */ \"(rsc)/./node_modules/@auth/core/index.js\");\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\n\n\n/** If `NEXTAUTH_URL` or `AUTH_URL` is defined, override the request's URL. */\nfunction reqWithEnvURL(req) {\n    const url = process.env.AUTH_URL ?? process.env.NEXTAUTH_URL;\n    if (!url)\n        return req;\n    const { origin: envOrigin } = new URL(url);\n    const { href, origin } = req.nextUrl;\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextRequest(href.replace(origin, envOrigin), req);\n}\n/**\n * For backwards compatibility, `next-auth` checks for `NEXTAUTH_URL`\n * and the `basePath` by default is `/api/auth` instead of `/auth`\n * (which is the default for all other Auth.js integrations).\n *\n * For the same reason, `NEXTAUTH_SECRET` is also checked.\n */\nfunction setEnvDefaults(config) {\n    try {\n        config.secret ?? (config.secret = process.env.AUTH_SECRET ?? process.env.NEXTAUTH_SECRET);\n        const url = process.env.AUTH_URL ?? process.env.NEXTAUTH_URL;\n        if (!url)\n            return;\n        const { pathname } = new URL(url);\n        if (pathname === \"/\")\n            return;\n        config.basePath || (config.basePath = pathname);\n    }\n    catch {\n        // Catching and swallowing potential URL parsing errors, we'll fall\n        // back to `/api/auth` below.\n    }\n    finally {\n        config.basePath || (config.basePath = \"/api/auth\");\n        (0,_auth_core__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(process.env, config, true);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/lib/env.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/lib/index.js":
/*!*********************************************!*\
  !*** ./node_modules/next-auth/lib/index.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initAuth: () => (/* binding */ initAuth)\n/* harmony export */ });\n/* harmony import */ var _auth_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/core */ \"(rsc)/./node_modules/@auth/core/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _env_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./env.js */ \"(rsc)/./node_modules/next-auth/lib/env.js\");\n\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\n\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\n\n\nasync function getSession(headers, config) {\n    const url = (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.createActionURL)(\"session\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    const request = new Request(url, {\n        headers: { cookie: headers.get(\"cookie\") ?? \"\" },\n    });\n    return (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)(request, {\n        ...config,\n        callbacks: {\n            ...config.callbacks,\n            // Since we are server-side, we don't need to filter out the session data\n            // See https://authjs.dev/getting-started/migrating-to-v5#authenticating-server-side\n            // TODO: Taint the session data to prevent accidental leakage to the client\n            // https://react.dev/reference/react/experimental_taintObjectReference\n            async session(...args) {\n                const session = \n                // If the user defined a custom session callback, use that instead\n                (await config.callbacks?.session?.(...args)) ?? {\n                    ...args[0].session,\n                    expires: args[0].session.expires?.toISOString?.() ??\n                        args[0].session.expires,\n                };\n                const user = args[0].user ?? args[0].token;\n                return { user, ...session };\n            },\n        },\n    });\n}\nfunction isReqWrapper(arg) {\n    return typeof arg === \"function\";\n}\nfunction initAuth(config, onLazyLoad // To set the default env vars\n) {\n    if (typeof config === \"function\") {\n        return async (...args) => {\n            if (!args.length) {\n                // React Server Components\n                const _headers = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)();\n                const _config = await config(undefined); // Review: Should we pass headers() here instead?\n                onLazyLoad?.(_config);\n                return getSession(_headers, _config).then((r) => r.json());\n            }\n            if (args[0] instanceof Request) {\n                // middleware.ts inline\n                // export { auth as default } from \"auth\"\n                const req = args[0];\n                const ev = args[1];\n                const _config = await config(req);\n                onLazyLoad?.(_config);\n                // args[0] is supposed to be NextRequest but the instanceof check is failing.\n                return handleAuth([req, ev], _config);\n            }\n            if (isReqWrapper(args[0])) {\n                // middleware.ts wrapper/route.ts\n                // import { auth } from \"auth\"\n                // export default auth((req) => { console.log(req.auth) }})\n                const userMiddlewareOrRoute = args[0];\n                return async (...args) => {\n                    const _config = await config(args[0]);\n                    onLazyLoad?.(_config);\n                    return handleAuth(args, _config, userMiddlewareOrRoute);\n                };\n            }\n            // API Routes, getServerSideProps\n            const request = \"req\" in args[0] ? args[0].req : args[0];\n            const response = \"res\" in args[0] ? args[0].res : args[1];\n            const _config = await config(request);\n            onLazyLoad?.(_config);\n            // @ts-expect-error -- request is NextRequest\n            return getSession(new Headers(request.headers), _config).then(async (authResponse) => {\n                const auth = await authResponse.json();\n                for (const cookie of authResponse.headers.getSetCookie())\n                    if (\"headers\" in response)\n                        response.headers.append(\"set-cookie\", cookie);\n                    else\n                        response.appendHeader(\"set-cookie\", cookie);\n                return auth;\n            });\n        };\n    }\n    return (...args) => {\n        if (!args.length) {\n            // React Server Components\n            return Promise.resolve((0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)()).then((h) => getSession(h, config).then((r) => r.json()));\n        }\n        if (args[0] instanceof Request) {\n            // middleware.ts inline\n            // export { auth as default } from \"auth\"\n            const req = args[0];\n            const ev = args[1];\n            return handleAuth([req, ev], config);\n        }\n        if (isReqWrapper(args[0])) {\n            // middleware.ts wrapper/route.ts\n            // import { auth } from \"auth\"\n            // export default auth((req) => { console.log(req.auth) }})\n            const userMiddlewareOrRoute = args[0];\n            return async (...args) => {\n                return handleAuth(args, config, userMiddlewareOrRoute).then((res) => {\n                    return res;\n                });\n            };\n        }\n        // API Routes, getServerSideProps\n        const request = \"req\" in args[0] ? args[0].req : args[0];\n        const response = \"res\" in args[0] ? args[0].res : args[1];\n        return getSession(\n        // @ts-expect-error\n        new Headers(request.headers), config).then(async (authResponse) => {\n            const auth = await authResponse.json();\n            for (const cookie of authResponse.headers.getSetCookie())\n                if (\"headers\" in response)\n                    response.headers.append(\"set-cookie\", cookie);\n                else\n                    response.appendHeader(\"set-cookie\", cookie);\n            return auth;\n        });\n    };\n}\nasync function handleAuth(args, config, userMiddlewareOrRoute) {\n    const request = (0,_env_js__WEBPACK_IMPORTED_MODULE_3__.reqWithEnvURL)(args[0]);\n    const sessionResponse = await getSession(request.headers, config);\n    const auth = await sessionResponse.json();\n    let authorized = true;\n    if (config.callbacks?.authorized) {\n        authorized = await config.callbacks.authorized({ request, auth });\n    }\n    let response = next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.next?.();\n    if (authorized instanceof Response) {\n        // User returned a custom response, like redirecting to a page or 401, respect it\n        response = authorized;\n        const redirect = authorized.headers.get(\"Location\");\n        const { pathname } = request.nextUrl;\n        // If the user is redirecting to the same NextAuth.js action path as the current request,\n        // don't allow the redirect to prevent an infinite loop\n        if (redirect &&\n            isSameAuthAction(pathname, new URL(redirect).pathname, config)) {\n            authorized = true;\n        }\n    }\n    else if (userMiddlewareOrRoute) {\n        // Execute user's middleware/handler with the augmented request\n        const augmentedReq = request;\n        augmentedReq.auth = auth;\n        response =\n            (await userMiddlewareOrRoute(augmentedReq, args[1])) ??\n                next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.next();\n    }\n    else if (!authorized) {\n        const signInPage = config.pages?.signIn ?? `${config.basePath}/signin`;\n        if (request.nextUrl.pathname !== signInPage) {\n            // Redirect to signin page by default if not authorized\n            const signInUrl = request.nextUrl.clone();\n            signInUrl.pathname = signInPage;\n            signInUrl.searchParams.set(\"callbackUrl\", request.nextUrl.href);\n            response = next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.redirect(signInUrl);\n        }\n    }\n    const finalResponse = new Response(response?.body, response);\n    // Preserve cookies from the session response\n    for (const cookie of sessionResponse.headers.getSetCookie())\n        finalResponse.headers.append(\"set-cookie\", cookie);\n    return finalResponse;\n}\nfunction isSameAuthAction(requestPath, redirectPath, config) {\n    const action = redirectPath.replace(`${requestPath}/`, \"\");\n    const pages = Object.values(config.pages ?? {});\n    return ((actions.has(action) || pages.includes(redirectPath)) &&\n        redirectPath === requestPath);\n}\nconst actions = new Set([\n    \"providers\",\n    \"session\",\n    \"csrf\",\n    \"signin\",\n    \"signout\",\n    \"callback\",\n    \"verify-request\",\n    \"error\",\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/providers/credentials.js":
/*!*********************************************************!*\
  !*** ./node_modules/next-auth/providers/credentials.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _auth_core_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _auth_core_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/core/providers/credentials */ \"(rsc)/./node_modules/@auth/core/providers/credentials.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL3Byb3ZpZGVycy9jcmVkZW50aWFscy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFpRDtBQUNVIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdpbmRvd3MgMTFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcVExEIFN5c3RlbVxcZW5oYW5jZWQtaWVsdHMtc3lzdGVtXFxub2RlX21vZHVsZXNcXG5leHQtYXV0aFxccHJvdmlkZXJzXFxjcmVkZW50aWFscy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiQGF1dGgvY29yZS9wcm92aWRlcnMvY3JlZGVudGlhbHNcIjtcbmV4cG9ydCB7IGRlZmF1bHQgfSBmcm9tIFwiQGF1dGgvY29yZS9wcm92aWRlcnMvY3JlZGVudGlhbHNcIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/providers/credentials.js\n");

/***/ })

};
;