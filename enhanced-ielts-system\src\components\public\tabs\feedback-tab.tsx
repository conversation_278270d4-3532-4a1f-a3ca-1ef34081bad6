'use client';

import { useState } from 'react';
import { PaywallOverlay } from '@/components/paywall/paywall-overlay';
import { usePaywall } from '@/hooks/use-paywall';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  MessageSquare, 
  Star, 
  Lock, 
  Brain,
  Target,
  BookOpen,
  TrendingUp,
  CheckCircle,
  AlertCircle,
  Lightbulb
} from 'lucide-react';

interface Candidate {
  id: string;
  fullName: string;
}

interface TestData {
  registration: {
    id: string;
    testDate: Date;
    testCenter: string;
  };
  result: {
    id: string;
    listeningBandScore: string | null;
    readingBandScore: string | null;
    writingBandScore: string | null;
    speakingBandScore: string | null;
    overallBandScore: string | null;
    createdAt: Date;
  };
}

interface FeedbackTabProps {
  candidate: Candidate;
  testData: TestData[];
}

export function FeedbackTab({ candidate, testData }: FeedbackTabProps) {
  const [selectedTest, setSelectedTest] = useState(testData[0]?.result.id || '');
  const [showPaywall, setShowPaywall] = useState(false);
  
  const { hasAccess, isLoading } = usePaywall({
    candidateId: candidate.id,
    featureType: 'feedback',
    resultId: selectedTest,
  });

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const renderBasicFeedback = () => {
    if (testData.length === 0) {
      return (
        <div className="text-center py-12">
          <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No Test Results</h3>
          <p className="text-gray-600">
            Complete a test to receive AI-powered feedback and recommendations.
          </p>
        </div>
      );
    }

    const selectedTestData = testData.find(test => test.result.id === selectedTest) || testData[0];

    return (
      <div className="space-y-6">
        {/* Test Selection */}
        {testData.length > 1 && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Test for Feedback
            </label>
            <select
              value={selectedTest}
              onChange={(e) => setSelectedTest(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {testData.map((test) => (
                <option key={test.result.id} value={test.result.id}>
                  {formatDate(test.registration.testDate)} - Band {test.result.overallBandScore}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Basic Performance Overview */}
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Overview</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {selectedTestData.result.listeningBandScore || 'N/A'}
              </div>
              <div className="text-sm text-gray-600">Listening</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {selectedTestData.result.readingBandScore || 'N/A'}
              </div>
              <div className="text-sm text-gray-600">Reading</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {selectedTestData.result.writingBandScore || 'N/A'}
              </div>
              <div className="text-sm text-gray-600">Writing</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {selectedTestData.result.speakingBandScore || 'N/A'}
              </div>
              <div className="text-sm text-gray-600">Speaking</div>
            </div>
          </div>
        </div>

        {/* General Recommendations */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-4 flex items-center">
            <Lightbulb className="h-5 w-5 mr-2" />
            General Study Tips
          </h3>
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
              <div className="text-sm text-blue-800">
                Practice regularly with authentic IELTS materials
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
              <div className="text-sm text-blue-800">
                Focus on time management during practice sessions
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
              <div className="text-sm text-blue-800">
                Expand your vocabulary through reading diverse topics
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderPremiumFeedback = () => {
    const selectedTestData = testData.find(test => test.result.id === selectedTest) || testData[0];

    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">AI-Powered Feedback</h2>
          <Badge variant="green">Premium Feature</Badge>
        </div>

        {/* Test Selection */}
        {testData.length > 1 && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Test for Detailed Feedback
            </label>
            <select
              value={selectedTest}
              onChange={(e) => setSelectedTest(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {testData.map((test) => (
                <option key={test.result.id} value={test.result.id}>
                  {formatDate(test.registration.testDate)} - Band {test.result.overallBandScore}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* AI Analysis */}
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-purple-900 mb-4 flex items-center">
            <Brain className="h-5 w-5 mr-2" />
            AI Performance Analysis
          </h3>
          <div className="space-y-4">
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-medium text-purple-900 mb-2">Overall Assessment</h4>
              <p className="text-sm text-purple-800">
                Based on your band score of {selectedTestData.result.overallBandScore}, you demonstrate 
                {parseFloat(selectedTestData.result.overallBandScore || '0') >= 7 ? ' good' : ' developing'} 
                English proficiency. Your performance shows particular strength in areas where you scored above your overall average.
              </p>
            </div>
          </div>
        </div>

        {/* Detailed Skill Analysis */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Listening Analysis */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h4 className="font-medium text-gray-900 mb-3 flex items-center">
              <Target className="h-4 w-4 mr-2 text-blue-600" />
              Listening Analysis
            </h4>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Band Score</span>
                <span className="font-bold text-blue-600">{selectedTestData.result.listeningBandScore}</span>
              </div>
              <div className="bg-blue-50 p-3 rounded">
                <h5 className="font-medium text-blue-900 text-sm mb-1">Strengths</h5>
                <p className="text-xs text-blue-800">
                  Good comprehension of main ideas and supporting details
                </p>
              </div>
              <div className="bg-orange-50 p-3 rounded">
                <h5 className="font-medium text-orange-900 text-sm mb-1">Areas for Improvement</h5>
                <p className="text-xs text-orange-800">
                  Practice with different accents and faster speech patterns
                </p>
              </div>
            </div>
          </div>

          {/* Reading Analysis */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h4 className="font-medium text-gray-900 mb-3 flex items-center">
              <BookOpen className="h-4 w-4 mr-2 text-green-600" />
              Reading Analysis
            </h4>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Band Score</span>
                <span className="font-bold text-green-600">{selectedTestData.result.readingBandScore}</span>
              </div>
              <div className="bg-green-50 p-3 rounded">
                <h5 className="font-medium text-green-900 text-sm mb-1">Strengths</h5>
                <p className="text-xs text-green-800">
                  Effective skimming and scanning techniques
                </p>
              </div>
              <div className="bg-orange-50 p-3 rounded">
                <h5 className="font-medium text-orange-900 text-sm mb-1">Areas for Improvement</h5>
                <p className="text-xs text-orange-800">
                  Work on inference and understanding implicit meaning
                </p>
              </div>
            </div>
          </div>

          {/* Writing Analysis */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h4 className="font-medium text-gray-900 mb-3 flex items-center">
              <MessageSquare className="h-4 w-4 mr-2 text-purple-600" />
              Writing Analysis
            </h4>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Band Score</span>
                <span className="font-bold text-purple-600">{selectedTestData.result.writingBandScore}</span>
              </div>
              <div className="bg-purple-50 p-3 rounded">
                <h5 className="font-medium text-purple-900 text-sm mb-1">Strengths</h5>
                <p className="text-xs text-purple-800">
                  Clear task response and good organization
                </p>
              </div>
              <div className="bg-orange-50 p-3 rounded">
                <h5 className="font-medium text-orange-900 text-sm mb-1">Areas for Improvement</h5>
                <p className="text-xs text-orange-800">
                  Enhance lexical resource and grammatical range
                </p>
              </div>
            </div>
          </div>

          {/* Speaking Analysis */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h4 className="font-medium text-gray-900 mb-3 flex items-center">
              <Star className="h-4 w-4 mr-2 text-yellow-600" />
              Speaking Analysis
            </h4>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Band Score</span>
                <span className="font-bold text-yellow-600">{selectedTestData.result.speakingBandScore}</span>
              </div>
              <div className="bg-yellow-50 p-3 rounded">
                <h5 className="font-medium text-yellow-900 text-sm mb-1">Strengths</h5>
                <p className="text-xs text-yellow-800">
                  Good fluency and natural conversation flow
                </p>
              </div>
              <div className="bg-orange-50 p-3 rounded">
                <h5 className="font-medium text-orange-900 text-sm mb-1">Areas for Improvement</h5>
                <p className="text-xs text-orange-800">
                  Focus on pronunciation clarity and intonation
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Personalized Study Plan */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-green-900 mb-4 flex items-center">
            <TrendingUp className="h-5 w-5 mr-2" />
            Personalized Study Plan
          </h3>
          <div className="space-y-4">
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-medium text-green-900 mb-2">Week 1-2: Focus on Writing</h4>
              <ul className="text-sm text-green-800 space-y-1">
                <li>• Practice Task 2 essay structure daily</li>
                <li>• Learn 10 new academic vocabulary words per day</li>
                <li>• Complete 3 practice essays with time limits</li>
              </ul>
            </div>
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-medium text-green-900 mb-2">Week 3-4: Speaking Enhancement</h4>
              <ul className="text-sm text-green-800 space-y-1">
                <li>• Record yourself speaking for 2 minutes daily</li>
                <li>• Practice pronunciation with tongue twisters</li>
                <li>• Engage in conversation practice with native speakers</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!hasAccess) {
    return (
      <PaywallOverlay
        isOpen={showPaywall}
        onClose={() => setShowPaywall(false)}
        featureType="feedback"
        candidateId={candidate.id}
        resultId={selectedTest}
        onPaymentSuccess={() => window.location.reload()}
      >
        <div className="relative">
          <div className="filter blur-sm pointer-events-none">
            {renderBasicFeedback()}
          </div>
          <div className="absolute inset-0 bg-white/80 flex items-center justify-center">
            <div className="text-center p-6">
              <Lock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                AI-Powered Feedback
              </h3>
              <p className="text-gray-600 mb-4">
                Get detailed AI analysis, personalized study recommendations, and improvement strategies
              </p>
              <Button onClick={() => setShowPaywall(true)}>
                <Star className="h-4 w-4 mr-2" />
                Unlock AI Feedback - 50,000 UZS
              </Button>
            </div>
          </div>
        </div>
      </PaywallOverlay>
    );
  }

  return renderPremiumFeedback();
}
