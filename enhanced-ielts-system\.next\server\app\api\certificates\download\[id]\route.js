(()=>{var e={};e.id=4177,e.ids=[4177],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},27957:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>h,serverHooks:()=>I,workAsyncStorage:()=>T,workUnitAsyncStorage:()=>f});var i={};r.r(i),r.d(i,{GET:()=>g});var a=r(96559),s=r(48088),n=r(37719),o=r(32190),c=r(26326),l=r(71682),d=r(32767),u=r(94634),p=r(78537);async function g(e,{params:t}){let r=await (0,c.j2)();if(!r?.user?.organizationId)return o.NextResponse.json({error:"Unauthorized"},{status:401});try{let e=await l.db.select().from(d.certificateLifecycle).where((0,u.eq)(d.certificateLifecycle.id,t.id)).limit(1);if(0===e.length)return o.NextResponse.json({error:"Certificate not found"},{status:404});let r=e[0];if("active"!==r.status||new Date>r.expiresAt)return o.NextResponse.json({error:"Certificate is no longer valid"},{status:410});let{pdfBuffer:i}=await p.J.generateCertificate(r.resultId);return new o.NextResponse(i,{headers:{"Content-Type":"application/pdf","Content-Disposition":`attachment; filename="IELTS_Certificate_${r.serialNumber}.pdf"`}})}catch(e){return console.error("Error downloading certificate:",e),o.NextResponse.json({error:"Failed to download certificate"},{status:500})}}let h=new a.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/certificates/download/[id]/route",pathname:"/api/certificates/download/[id]",filename:"route",bundlePath:"app/api/certificates/download/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\certificates\\download\\[id]\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:T,workUnitAsyncStorage:f,serverHooks:I}=h;function x(){return(0,n.patchFetch)({workAsyncStorage:T,workUnitAsyncStorage:f})}},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},78537:(e,t,r)=>{"use strict";r.d(t,{J:()=>l});var i=r(80137),a=r(71682),s=r(32767),n=r(94634),o=r(96657),c=r(51890);class l{static{this.CERTIFICATE_WIDTH=297}static{this.CERTIFICATE_HEIGHT=210}static async generateCertificate(e){let t=await a.db.select({testResult:s.testResults,candidate:s.candidates,organization:s.organizations,testRegistration:s.testRegistrations}).from(s.testResults).innerJoin(s.testRegistrations,(0,n.eq)(s.testResults.testRegistrationId,s.testRegistrations.id)).innerJoin(s.candidates,(0,n.eq)(s.testRegistrations.candidateId,s.candidates.id)).innerJoin(s.organizations,(0,n.eq)(s.candidates.organizationId,s.organizations.id)).where((0,n.eq)(s.testResults.id,e)).limit(1);if(0===t.length)throw Error("Test result not found");let{testResult:r,candidate:i,organization:o,testRegistration:c}=t[0],l=await a.db.select().from(s.certificateLifecycle).where((0,n.eq)(s.certificateLifecycle.resultId,e)).limit(1);if(l.length>0&&"active"===l[0].status)throw Error("Certificate already exists for this result");let d=await this.generateSerialNumber(),u=new Date(new Date(c.testDate));u.setMonth(u.getMonth()+6);let[p]=await a.db.insert(s.certificateLifecycle).values({resultId:e,serialNumber:d,expiresAt:u,metadata:{candidateName:i.fullName,testDate:c.testDate.toISOString().split("T")[0],overallBandScore:parseFloat(r.overallBandScore||"0"),organizationName:o.name,certificateType:"IELTS Academic"}}).returning(),g={candidateName:i.fullName,passportNumber:i.passportNumber,testDate:c.testDate.toISOString().split("T")[0],testCenter:o.name,listeningBandScore:parseFloat(r.listeningBandScore||"0"),readingBandScore:parseFloat(r.readingBandScore||"0"),writingBandScore:parseFloat(r.writingBandScore||"0"),speakingBandScore:parseFloat(r.speakingBandScore||"0"),overallBandScore:parseFloat(r.overallBandScore||"0"),organizationName:o.name,serialNumber:d,issueDate:new Date().toISOString().split("T")[0],expiryDate:u.toISOString().split("T")[0],candidatePhoto:i.photoData},h=await this.createPDF(g);return{certificateId:p.id,serialNumber:d,pdfBuffer:h}}static async generateSerialNumber(){let e=new Date().getFullYear(),t=String(new Date().getMonth()+1).padStart(2,"0"),r=new Date(e,new Date().getMonth(),1),i=new Date(e,new Date().getMonth()+1,0),c=await a.db.select({count:(0,o.ll)`count(*)`}).from(s.certificateLifecycle).where((0,n.Uo)((0,n.RO)(s.certificateLifecycle.generatedAt,r),(0,n.wJ)(s.certificateLifecycle.generatedAt,i))),l=String((c[0]?.count||0)+1).padStart(4,"0");return`IELTS-${e}${t}-${l}`}static async createPDF(e){let t=new i.Ay({orientation:"landscape",unit:"mm",format:"a4"});t.setFont("helvetica","normal"),t.setFillColor(248,250,252),t.rect(0,0,this.CERTIFICATE_WIDTH,this.CERTIFICATE_HEIGHT,"F"),t.setDrawColor(59,130,246),t.setLineWidth(2),t.rect(10,10,this.CERTIFICATE_WIDTH-20,this.CERTIFICATE_HEIGHT-20),t.setFontSize(24),t.setFont("helvetica","bold"),t.setTextColor(59,130,246),t.text("IELTS CERTIFICATE",this.CERTIFICATE_WIDTH/2,35,{align:"center"}),t.setFontSize(16),t.setFont("helvetica","normal"),t.setTextColor(75,85,99),t.text("International English Language Testing System",this.CERTIFICATE_WIDTH/2,45,{align:"center"}),t.setFontSize(14),t.setFont("helvetica","normal"),t.setTextColor(0,0,0);let r=70;t.setFont("helvetica","bold"),t.text("Candidate Name:",30,r),t.setFont("helvetica","normal"),t.text(e.candidateName,80,r),t.setFont("helvetica","bold"),t.text("Passport/ID Number:",180,r),t.setFont("helvetica","normal"),t.text(e.passportNumber,230,r),r+=15,t.setFont("helvetica","bold"),t.text("Test Date:",30,r),t.setFont("helvetica","normal"),t.text(e.testDate,60,r),t.setFont("helvetica","bold"),t.text("Test Center:",180,r),t.setFont("helvetica","normal"),t.text(e.testCenter,210,r),r+=25,t.setFont("helvetica","bold"),t.setFontSize(16),t.text("Test Results",this.CERTIFICATE_WIDTH/2,r,{align:"center"}),r+=15;let a=[["Skill","Band Score"],["Listening",e.listeningBandScore.toString()],["Reading",e.readingBandScore.toString()],["Writing",e.writingBandScore.toString()],["Speaking",e.speakingBandScore.toString()],["Overall Band Score",e.overallBandScore.toString()]],s=this.CERTIFICATE_WIDTH/2-40,n=r;a.forEach((e,r)=>{e.forEach((e,i)=>{let o=s+40*i,c=n+8*r;0===r?(t.setFillColor(59,130,246),t.setTextColor(255,255,255),t.setFont("helvetica","bold")):r===a.length-1?(t.setFillColor(34,197,94),t.setTextColor(255,255,255),t.setFont("helvetica","bold")):(t.setFillColor(248,250,252),t.setTextColor(0,0,0),t.setFont("helvetica","normal")),t.rect(o,c,40,8,"F"),t.setDrawColor(0,0,0),t.rect(o,c,40,8),t.text(e,o+20,c+4+1,{align:"center"})})}),r+=8*a.length+20,t.setTextColor(0,0,0),t.setFont("helvetica","normal"),t.setFontSize(10),t.text(`Certificate Serial Number: ${e.serialNumber}`,30,r),t.text(`Issue Date: ${e.issueDate}`,180,r),r+=8,t.text(`Valid Until: ${e.expiryDate}`,30,r),t.text(`Issued by: ${e.organizationName}`,180,r);let o=`${process.env.NEXT_PUBLIC_APP_URL||"http://localhost:3000"}/verify/${e.serialNumber}`,l=await c.toDataURL(o,{width:100,margin:1});return t.addImage(l,"PNG",this.CERTIFICATE_WIDTH-40,this.CERTIFICATE_HEIGHT-40,25,25),t.setFontSize(8),t.text("Scan to verify",this.CERTIFICATE_WIDTH-27,this.CERTIFICATE_HEIGHT-10,{align:"center"}),t.setFontSize(8),t.setTextColor(107,114,128),t.text("This certificate is valid for 6 months from the test date. For verification, visit our website or scan the QR code.",this.CERTIFICATE_WIDTH/2,this.CERTIFICATE_HEIGHT-5,{align:"center"}),Buffer.from(t.output("arraybuffer"))}}},79428:e=>{"use strict";e.exports=require("buffer")},91645:e=>{"use strict";e.exports=require("net")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4447,5552,2190,1057,2140,6326],()=>r(27957));module.exports=i})();