exports.id=7346,exports.ids=[7346],exports.modules={18758:(e,r,t)=>{"use strict";t.d(r,{ee:()=>l,jc:()=>f,nz:()=>_,vi:()=>m});var a=t(71682),n=t(32767),s=t(94634),c=t(16048),i=t(52175),o=t(97450),u=t(93659),d=t(38955);async function m(e){try{let r,t,c=function(e){if(e.amount<o.lO.minAmount)return{isValid:!1,error:`Minimum amount is ${o.lO.minAmount} ${e.currency||"UZS"}`};if(e.amount>o.lO.maxAmount)return{isValid:!1,error:`Maximum amount is ${o.lO.maxAmount} ${e.currency||"UZS"}`};let r=e.currency||"UZS";if(!o.lO.allowedCurrencies.includes(r))return{isValid:!1,error:`Currency ${r} is not supported`};let t=o.kp[e.featureType];return e.amount!==t.amount?{isValid:!1,error:`Invalid amount for ${e.featureType}. Expected: ${t.amount} ${t.currency}`}:{isValid:!0}}(e);if(!c.isValid)return{transactionId:"",status:"failed",error:c.error};let m=await a.db.select().from(n.candidates).where((0,s.Uo)((0,s.eq)(n.candidates.id,e.candidateId),(0,s.eq)(n.candidates.organizationId,e.organizationId))).limit(1);if(0===m.length)return{transactionId:"",status:"failed",error:"Candidate not found"};if(e.resultId&&(await _(e.candidateId,e.featureType,e.resultId)).hasAccess)return{transactionId:"",status:"failed",error:"Feature already unlocked"};let l=(0,i.sX)(),[p]=await a.db.insert(n.paymentTransactions).values({id:l,candidateId:e.candidateId,organizationId:e.organizationId,amount:e.amount.toString(),currency:e.currency||"UZS",gateway:e.gateway,status:"pending",featureType:e.featureType,resultId:e.resultId,metadata:{}}).returning();return"click"===e.gateway?r=await (0,u.nA)({amount:e.amount,merchant_trans_id:l,return_url:e.returnUrl,merchant_user_id:e.candidateId}):"payme"===e.gateway&&(r=await (0,d.PI)({amount:e.amount,account:{order_id:l,user_id:e.candidateId},return_url:e.returnUrl,description:e.description})),{transactionId:l,paymentUrl:r,status:"pending",gatewayTransactionId:t}}catch(e){return console.error("Payment creation error:",e),{transactionId:"",status:"failed",error:"Payment creation failed"}}}async function l(e,r,t,c){try{let i={status:r,updatedAt:new Date};return t&&(i.gatewayTransactionId=t),c&&(i.metadata=c),"completed"===r&&(i.completedAt=new Date),await a.db.update(n.paymentTransactions).set(i).where((0,s.eq)(n.paymentTransactions.id,e)),"completed"===r&&await p(e),!0}catch(e){return console.error("Payment status update error:",e),!1}}async function p(e){try{let r=await a.db.select().from(n.paymentTransactions).where((0,s.eq)(n.paymentTransactions.id,e)).limit(1);if(0===r.length)return!1;let t=r[0],c=o.s_.defaultExpiry[t.featureType]?new Date(Date.now()+o.s_.defaultExpiry[t.featureType]):null;return await a.db.insert(n.accessPermissions).values({id:(0,i.sX)(),candidateId:t.candidateId,resultId:t.resultId,featureType:t.featureType,accessType:"paid",grantedAt:new Date,expiresAt:c,metadata:{transactionId:e}}),!0}catch(e){return console.error("Feature access grant error:",e),!1}}async function _(e,r,t){try{let i,o=a.db.select().from(n.accessPermissions).where((0,s.Uo)((0,s.eq)(n.accessPermissions.candidateId,e),(0,s.eq)(n.accessPermissions.featureType,r)));t&&(o=o.where((0,s.Uo)((0,s.eq)(n.accessPermissions.candidateId,e),(0,s.eq)(n.accessPermissions.featureType,r),(0,s.eq)(n.accessPermissions.resultId,t))));let u=await o.orderBy((0,c.i)(n.accessPermissions.grantedAt));if(0===u.length)return{hasAccess:!1};let d=u[0];if(d.expiresAt&&new Date>d.expiresAt)return{hasAccess:!1};if(d.expiresAt){let e=d.expiresAt.getTime()-Date.now();i=Math.ceil(e/864e5)}return{hasAccess:!0,accessType:d.accessType,expiresAt:d.expiresAt||void 0,daysRemaining:i}}catch(e){return console.error("Feature access check error:",e),{hasAccess:!1}}}function f(e){return o.kp[e]}},38955:(e,r,t)=>{"use strict";t.d(r,{PI:()=>i,d5:()=>o});var a=t(45697);let n={baseUrl:process.env.PAYME_BASE_URL||"https://checkout.paycom.uz",merchantId:process.env.PAYME_MERCHANT_ID||"",secretKey:process.env.PAYME_SECRET_KEY||"",testMode:!1},s=a.z.object({amount:a.z.number().positive(),account:a.z.object({order_id:a.z.string().min(1),user_id:a.z.string().optional()}),return_url:a.z.string().url(),description:a.z.string().optional()}),c=a.z.object({method:a.z.string(),params:a.z.object({id:a.z.string().optional(),time:a.z.number().optional(),amount:a.z.number().optional(),account:a.z.object({order_id:a.z.string(),user_id:a.z.string().optional()}).optional(),reason:a.z.number().optional()})});async function i(e){let r=s.parse(e),t=Math.round(100*r.amount),a=Buffer.from(JSON.stringify(r.account)).toString("base64"),c=new URLSearchParams({m:n.merchantId,"ac.order_id":r.account.order_id,a:t.toString(),c:a,cr:r.return_url});return r.account.user_id&&c.append("ac.user_id",r.account.user_id),r.description&&c.append("d",r.description),`${n.baseUrl}/?${c.toString()}`}async function o(e){try{let r=c.parse(e);switch(r.method){case"CheckPerformTransaction":return await u(r.params);case"CreateTransaction":return await d(r.params);case"PerformTransaction":return await m(r.params);case"CancelTransaction":return await l(r.params);case"CheckTransaction":return await p(r.params);case"GetStatement":return await _(r.params);default:return{success:!1,error:{code:-32601,message:"Method not found"}}}}catch(e){return console.error("Payme webhook processing error:",e),{success:!1,error:{code:-32700,message:"Parse error"}}}}async function u(e){try{let{account:r,amount:t}=e;return{success:!0,result:{allow:!0}}}catch(e){return{success:!1,error:{code:-31001,message:"Order not found"}}}}async function d(e){try{let{id:r,time:t,amount:a,account:n}=e;return{success:!0,result:{create_time:t,transaction:r,state:1}}}catch(e){return{success:!1,error:{code:-31001,message:"Transaction creation failed"}}}}async function m(e){try{let{id:r}=e,t=Date.now();return{success:!0,result:{perform_time:t,transaction:r,state:2}}}catch(e){return{success:!1,error:{code:-31001,message:"Transaction not found"}}}}async function l(e){try{let{id:r,reason:t}=e,a=Date.now();return{success:!0,result:{cancel_time:a,transaction:r,state:1===t?-1:-2}}}catch(e){return{success:!1,error:{code:-31001,message:"Transaction not found"}}}}async function p(e){try{let{id:r}=e;return{success:!0,result:{create_time:Date.now(),perform_time:0,cancel_time:0,transaction:r,state:1,reason:null}}}catch(e){return{success:!1,error:{code:-31001,message:"Transaction not found"}}}}async function _(e){try{let{from:r,to:t}=e;return{success:!0,result:{transactions:[]}}}catch(e){return{success:!1,error:{code:-32400,message:"Invalid request"}}}}},78335:()=>{},93659:(e,r,t)=>{"use strict";t.d(r,{HO:()=>o,nA:()=>i});var a=t(45697);let n={baseUrl:process.env.CLICK_BASE_URL||"https://api.click.uz/v2",merchantId:process.env.CLICK_MERCHANT_ID||"",serviceId:process.env.CLICK_SERVICE_ID||"",secretKey:process.env.CLICK_SECRET_KEY||"",userId:process.env.CLICK_USER_ID||""},s=a.z.object({amount:a.z.number().positive(),merchant_trans_id:a.z.string().min(1),return_url:a.z.string().url(),merchant_prepare_id:a.z.string().optional(),merchant_user_id:a.z.string().optional()}),c=a.z.object({click_trans_id:a.z.string(),service_id:a.z.string(),click_paydoc_id:a.z.string(),merchant_trans_id:a.z.string(),merchant_prepare_id:a.z.string(),amount:a.z.number(),action:a.z.number(),error:a.z.number(),error_note:a.z.string(),sign_time:a.z.string(),sign_string:a.z.string()});async function i(e){let r=s.parse(e),t=new URLSearchParams({service_id:n.serviceId,merchant_id:n.merchantId,amount:r.amount.toString(),transaction_param:r.merchant_trans_id,return_url:r.return_url});return r.merchant_user_id&&t.append("merchant_user_id",r.merchant_user_id),`${n.baseUrl}/services/pay?${t.toString()}`}async function o(e){try{var r,a,s,i,o,m,l,p,_;let f=c.parse(e);if(r=f.click_trans_id,a=f.service_id,s=n.secretKey,i=f.merchant_trans_id,o=f.merchant_prepare_id,m=f.amount,l=f.action,p=f.sign_time,_=`${r}${a}${s}${i}${o}${m}${l}${p}`,t(55511).createHash("md5").update(_).digest("hex")!==f.sign_string)return{success:!1,error:"Invalid signature"};if(f.service_id!==n.serviceId)return{success:!1,error:"Invalid service ID"};switch(f.action){case 0:return await u(f);case 1:return await d(f);default:return{success:!1,error:"Unknown action"}}}catch(e){return console.error("Click webhook processing error:",e),{success:!1,error:"Processing failed"}}}async function u(e){return 0!==e.error?{success:!1,error:e.error_note}:{success:!0,transactionId:e.merchant_trans_id}}async function d(e){return 0!==e.error?{success:!1,error:e.error_note}:{success:!0,transactionId:e.merchant_trans_id}}},96487:()=>{},97450:(e,r,t)=>{"use strict";t.d(r,{kp:()=>a,lO:()=>n,s_:()=>s});let a={feedback:{amount:5e4,currency:"UZS",description:"AI-powered personalized feedback and study recommendations"},certificate:{amount:3e4,currency:"UZS",description:"Official IELTS certificate with verification"},progress:{amount:25e3,currency:"UZS",description:"Detailed progress tracking and analytics"}},n={minAmount:1e3,maxAmount:1e7,allowedCurrencies:["UZS","USD"],transactionTimeout:18e5,maxRetries:3},s={defaultExpiry:{feedback:null,certificate:15552e6,progress:null},gracePeriod:6048e5,maxConcurrentAccess:{feedback:1,certificate:1,progress:1}}}};