(()=>{var e={};e.id=3967,e.ids=[3967],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10790:(e,t,s)=>{"use strict";s.d(t,{TestResultsTable:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call TestResultsTable() from the server but TestResultsTable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\specialized\\test-results-table.tsx","TestResultsTable")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19001:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(26373).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34219:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),l=s(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let c={children:["",{children:["admin",{children:["results",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,62826)),"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\admin\\results\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,99111)),"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\admin\\results\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/results/page",pathname:"/admin/results",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},34631:e=>{"use strict";e.exports=require("tls")},35906:(e,t,s)=>{"use strict";s.d(t,{TestRegistrationModal:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call TestRegistrationModal() from the server but TestRegistrationModal is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\forms\\test-registration-modal.tsx","TestRegistrationModal")},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41862:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},46701:(e,t,s)=>{"use strict";s.d(t,{TestResultsTable:()=>w});var r=s(60687),a=s(43210),i=s(96834),n=s(29523),l=s(27605),d=s(63442),c=s(9275),o=s(19352),m=s(89667);s(85008);var x=s(10022),u=s(62688);let p=(0,u.A)("calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]);var g=s(41862);let h=c.z.object({listeningScore:c.z.number().min(0).max(40),readingScore:c.z.number().min(0).max(40),writingTask1Score:c.z.number().min(0).max(9).step(.5),writingTask2Score:c.z.number().min(0).max(9).step(.5),speakingFluencyScore:c.z.number().min(0).max(9).step(.5),speakingLexicalScore:c.z.number().min(0).max(9).step(.5),speakingGrammarScore:c.z.number().min(0).max(9).step(.5),speakingPronunciationScore:c.z.number().min(0).max(9).step(.5)});function y({isOpen:e,onClose:t,registration:s}){let[i,c]=(0,a.useState)(!1),[u,y]=(0,a.useState)({listeningBand:0,readingBand:0,writingBand:0,speakingBand:0,overallBand:0}),{register:b,handleSubmit:j,watch:v,reset:f,setValue:N,formState:{errors:w}}=(0,l.mN)({resolver:(0,d.u)(h),defaultValues:s.result?{listeningScore:s.result.listeningScore||0,readingScore:s.result.readingScore||0,writingTask1Score:parseFloat(s.result.writingTask1Score||"0"),writingTask2Score:parseFloat(s.result.writingTask2Score||"0"),speakingFluencyScore:parseFloat(s.result.speakingFluencyScore||"0"),speakingLexicalScore:parseFloat(s.result.speakingLexicalScore||"0"),speakingGrammarScore:parseFloat(s.result.speakingGrammarScore||"0"),speakingPronunciationScore:parseFloat(s.result.speakingPronunciationScore||"0")}:void 0});v();let k=async e=>{c(!0);try{let r={testRegistrationId:s.registration.id,...e,listeningBandScore:u.listeningBand,readingBandScore:u.readingBand,writingBandScore:u.writingBand,speakingBandScore:u.speakingBand,overallBandScore:u.overallBand},a=s.result?`/api/test-results/${s.result.id}`:"/api/test-results",i=s.result?"PUT":"POST";if(!(await fetch(a,{method:i,headers:{"Content-Type":"application/json"},body:JSON.stringify(r)})).ok)throw Error("Failed to save test result");f(),t(),window.location.reload()}catch(e){console.error("Error saving test result:",e),alert("Failed to save test result. Please try again.")}finally{c(!1)}};return(0,r.jsx)(o.a,{isOpen:e,onClose:t,size:"large",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[(0,r.jsx)(x.A,{className:"h-6 w-6 text-blue-600 mr-2"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:s.result?"Edit Test Result":"Enter Test Result"}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[s.candidate?.fullName," - ",s.registration.candidateNumber]})]})]}),(0,r.jsxs)("form",{onSubmit:j(k),className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-3",children:"Listening"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Raw Score (0-40)"}),(0,r.jsx)(m.p,{type:"number",min:"0",max:"40",...b("listeningScore",{valueAsNumber:!0}),error:w.listeningScore?.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Band Score (Calculated)"}),(0,r.jsx)("div",{className:"px-3 py-2 bg-white border border-gray-300 rounded-md text-lg font-bold text-blue-600",children:u.listeningBand.toFixed(1)})]})]})]}),(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-3",children:"Reading"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Raw Score (0-40)"}),(0,r.jsx)(m.p,{type:"number",min:"0",max:"40",...b("readingScore",{valueAsNumber:!0}),error:w.readingScore?.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Band Score (Calculated)"}),(0,r.jsx)("div",{className:"px-3 py-2 bg-white border border-gray-300 rounded-md text-lg font-bold text-blue-600",children:u.readingBand.toFixed(1)})]})]})]}),(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-3",children:"Writing"}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Task 1 Score (0-9)"}),(0,r.jsx)(m.p,{type:"number",min:"0",max:"9",step:"0.5",...b("writingTask1Score",{valueAsNumber:!0}),error:w.writingTask1Score?.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Task 2 Score (0-9)"}),(0,r.jsx)(m.p,{type:"number",min:"0",max:"9",step:"0.5",...b("writingTask2Score",{valueAsNumber:!0}),error:w.writingTask2Score?.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Band Score (Average)"}),(0,r.jsx)("div",{className:"px-3 py-2 bg-white border border-gray-300 rounded-md text-lg font-bold text-blue-600",children:u.writingBand.toFixed(1)})]})]})]}),(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-3",children:"Speaking"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Fluency & Coherence (0-9)"}),(0,r.jsx)(m.p,{type:"number",min:"0",max:"9",step:"0.5",...b("speakingFluencyScore",{valueAsNumber:!0}),error:w.speakingFluencyScore?.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Lexical Resource (0-9)"}),(0,r.jsx)(m.p,{type:"number",min:"0",max:"9",step:"0.5",...b("speakingLexicalScore",{valueAsNumber:!0}),error:w.speakingLexicalScore?.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Grammatical Range (0-9)"}),(0,r.jsx)(m.p,{type:"number",min:"0",max:"9",step:"0.5",...b("speakingGrammarScore",{valueAsNumber:!0}),error:w.speakingGrammarScore?.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Pronunciation (0-9)"}),(0,r.jsx)(m.p,{type:"number",min:"0",max:"9",step:"0.5",...b("speakingPronunciationScore",{valueAsNumber:!0}),error:w.speakingPronunciationScore?.message})]})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Speaking Band Score (Average)"}),(0,r.jsx)("div",{className:"inline-block px-4 py-2 bg-white border border-gray-300 rounded-md text-lg font-bold text-blue-600",children:u.speakingBand.toFixed(1)})]})]}),(0,r.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg text-center",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center mb-2",children:[(0,r.jsx)(p,{className:"h-5 w-5 text-blue-600 mr-2"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Overall Band Score"})]}),(0,r.jsx)("div",{className:"text-3xl font-bold text-blue-600",children:u.overallBand.toFixed(1)})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,r.jsx)(n.$,{type:"button",variant:"outline",onClick:t,disabled:i,children:"Cancel"}),(0,r.jsx)(n.$,{type:"submit",disabled:i,children:i?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(g.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Saving..."]}):s.result?"Update Result":"Save Result"})]})]})]})})}var b=s(58869),j=s(40228),v=s(97992);let f=(0,u.A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]);var N=s(13861);function w({registrations:e}){let[t,s]=(0,a.useState)(null),[l,d]=(0,a.useState)(!1),c=e=>{let t={registered:{color:"blue",label:"Registered"},completed:{color:"green",label:"Completed"},cancelled:{color:"red",label:"Cancelled"}}[e]||{color:"gray",label:e};return(0,r.jsx)(i.E,{variant:t.color,children:t.label})},o=e=>{if(!e)return(0,r.jsx)(i.E,{variant:"gray",children:"No Result"});let t={draft:{color:"yellow",label:"Draft"},completed:{color:"green",label:"Completed"},verified:{color:"blue",label:"Verified"}}[e.status]||{color:"gray",label:e.status};return(0,r.jsx)(i.E,{variant:t.color,children:t.label})},m=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),u=e=>{s(e),d(!0)};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,r.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Test Registrations & Results"})}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Candidate"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Test Details"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Result"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Band Score"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(b.A,{className:"h-8 w-8 text-gray-400 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.candidate?.fullName||"Unknown"}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.candidate?.passportNumber})]})]})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,r.jsxs)("div",{className:"flex items-center mb-1",children:[(0,r.jsx)(j.A,{className:"h-4 w-4 text-gray-400 mr-1"}),m(e.registration.testDate)]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(v.A,{className:"h-4 w-4 text-gray-400 mr-1"}),e.registration.testCenter]})]}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["#",e.registration.candidateNumber]})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:c(e.registration.status)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:o(e.result)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.result?.overallBandScore?(0,r.jsx)("span",{className:"text-lg font-bold text-blue-600",children:e.result.overallBandScore}):(0,r.jsx)("span",{className:"text-gray-400",children:"-"})})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex space-x-2",children:[!e.result&&"completed"===e.registration.status&&(0,r.jsxs)(n.$,{size:"sm",onClick:()=>u(e),children:[(0,r.jsx)(x.A,{className:"h-4 w-4 mr-1"}),"Enter Result"]}),e.result&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(n.$,{size:"sm",variant:"outline",onClick:()=>u(e),children:[(0,r.jsx)(f,{className:"h-4 w-4 mr-1"}),"Edit"]}),(0,r.jsxs)(n.$,{size:"sm",variant:"outline",children:[(0,r.jsx)(N.A,{className:"h-4 w-4 mr-1"}),"View"]})]})]})})]},e.registration.id))})]})}),0===e.length&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(x.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,r.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No registrations found"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"No test registrations match your current filters."})]})]}),t&&(0,r.jsx)(y,{isOpen:l,onClose:()=>{d(!1),s(null)},registration:t})]})}},55511:e=>{"use strict";e.exports=require("crypto")},59463:(e,t,s)=>{Promise.resolve().then(s.bind(s,82332)),Promise.resolve().then(s.bind(s,46701))},61348:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(26373).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},62826:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>b});var r=s(37413),a=s(26326),i=s(71682),n=s(32767),l=s(94634),d=s(16048),c=s(35906),o=s(10790),m=s(23469),x=s(78593),u=s(61348);let p=(0,s(26373).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);var g=s(88804),h=s(19001),y=s(78768);async function b(){let e=await (0,a.j2)();if(!e?.user?.organizationId)return(0,r.jsx)("div",{children:"Access denied"});let t=await i.db.select({registration:n.testRegistrations,candidate:n.candidates,result:n.testResults}).from(n.testRegistrations).leftJoin(n.candidates,(0,l.eq)(n.testRegistrations.candidateId,n.candidates.id)).leftJoin(n.testResults,(0,l.eq)(n.testRegistrations.id,n.testResults.testRegistrationId)).where((0,l.eq)(n.candidates.organizationId,e.user.organizationId)).orderBy((0,d.i)(n.testRegistrations.testDate)),s=await i.db.select({id:n.candidates.id,fullName:n.candidates.fullName,passportNumber:n.candidates.passportNumber}).from(n.candidates).where((0,l.eq)(n.candidates.organizationId,e.user.organizationId)).orderBy(n.candidates.fullName),b=t.length,j=t.filter(e=>e.result?.status==="completed").length,v=t.filter(e=>"completed"===e.registration.status&&!e.result).length,f=t.filter(e=>"registered"===e.registration.status&&new Date(e.registration.testDate)>new Date).length;return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Test Results"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Manage test registrations and enter results"})]}),(0,r.jsx)(c.TestRegistrationModal,{candidates:s,children:(0,r.jsxs)(m.$,{children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Register Test"]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(p,{className:"h-8 w-8 text-blue-600"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Registrations"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:b})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(g.A,{className:"h-8 w-8 text-green-600"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Completed Results"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:j})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(g.A,{className:"h-8 w-8 text-orange-600"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Pending Results"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:v})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(h.A,{className:"h-8 w-8 text-purple-600"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Upcoming Tests"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:f})]})]})})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(y.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,r.jsx)(x.p,{placeholder:"Search by candidate name or test center...",className:"pl-10"})]})}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)("select",{className:"px-3 py-2 border border-gray-300 rounded-md text-sm",children:[(0,r.jsx)("option",{value:"",children:"All Status"}),(0,r.jsx)("option",{value:"registered",children:"Registered"}),(0,r.jsx)("option",{value:"completed",children:"Completed"}),(0,r.jsx)("option",{value:"cancelled",children:"Cancelled"})]}),(0,r.jsxs)("select",{className:"px-3 py-2 border border-gray-300 rounded-md text-sm",children:[(0,r.jsx)("option",{value:"",children:"All Results"}),(0,r.jsx)("option",{value:"with-results",children:"With Results"}),(0,r.jsx)("option",{value:"pending-results",children:"Pending Results"})]})]})]})}),(0,r.jsx)(o.TestResultsTable,{registrations:t}),0===t.length&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(p,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,r.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No test registrations"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Get started by registering your first test."}),(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsx)(c.TestRegistrationModal,{candidates:s,children:(0,r.jsxs)(m.$,{children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Register Test"]})})})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74998:e=>{"use strict";e.exports=require("perf_hooks")},76335:(e,t,s)=>{Promise.resolve().then(s.bind(s,35906)),Promise.resolve().then(s.bind(s,10790))},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},82332:(e,t,s)=>{"use strict";s.d(t,{TestRegistrationModal:()=>p});var r=s(60687),a=s(43210),i=s(27605),n=s(63442),l=s(9275),d=s(19352),c=s(29523),o=s(89667),m=s(40228),x=s(41862);let u=l.z.object({candidateId:l.z.string().min(1,"Please select a candidate"),testDate:l.z.string().min(1,"Test date is required"),testCenter:l.z.string().min(1,"Test center is required"),candidateNumber:l.z.string().min(1,"Candidate number is required")});function p({children:e,candidates:t=[]}){let[s,l]=(0,a.useState)(!1),[p,g]=(0,a.useState)(!1),{register:h,handleSubmit:y,reset:b,formState:{errors:j}}=(0,i.mN)({resolver:(0,n.u)(u)}),v=async e=>{g(!0);try{if(!(await fetch("/api/test-registrations",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).ok)throw Error("Failed to register test");b(),l(!1),window.location.reload()}catch(e){console.error("Error registering test:",e),alert("Failed to register test. Please try again.")}finally{g(!1)}};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{onClick:()=>l(!0),children:e}),(0,r.jsx)(d.a,{isOpen:s,onClose:()=>l(!1),children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)(m.A,{className:"h-6 w-6 text-blue-600 mr-2"}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Register New Test"})]}),(0,r.jsxs)("form",{onSubmit:y(v),className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Candidate"}),(0,r.jsxs)("select",{...h("candidateId"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"",children:"Select a candidate"}),t.map(e=>(0,r.jsxs)("option",{value:e.id,children:[e.fullName," (",e.passportNumber,")"]},e.id))]}),j.candidateId&&(0,r.jsx)("p",{className:"text-red-500 text-sm mt-1",children:j.candidateId.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Test Date"}),(0,r.jsx)(o.p,{type:"datetime-local",...h("testDate"),error:j.testDate?.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Test Center"}),(0,r.jsx)(o.p,{placeholder:"Enter test center location",...h("testCenter"),error:j.testCenter?.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Candidate Number"}),(0,r.jsx)(o.p,{placeholder:"Enter candidate test number",...h("candidateNumber"),error:j.candidateNumber?.message})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,r.jsx)(c.$,{type:"button",variant:"outline",onClick:()=>l(!1),disabled:p,children:"Cancel"}),(0,r.jsx)(c.$,{type:"submit",disabled:p,children:p?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Registering..."]}):"Register Test"})]})]})]})})]})}},85008:(e,t,s)=>{"use strict";s.d(t,{AI:()=>n,EH:()=>d,Fd:()=>c,dN:()=>l});let r={39:9,38:9,37:8.5,36:8.5,35:8,34:8,33:7.5,32:7.5,31:7,30:7,29:6.5,28:6.5,27:6,26:6,25:5.5,24:5.5,23:5,22:5,21:4.5,20:4.5,19:4,18:4,17:3.5,16:3.5,15:3,14:3,13:2.5,12:2.5,11:2,10:2,9:1.5,8:1.5,7:1,6:1,5:.5,4:.5,3:0,2:0,1:0,0:0},a={39:9,38:9,37:8.5,36:8.5,35:8,34:8,33:7.5,32:7.5,31:7,30:7,29:6.5,28:6.5,27:6,26:6,25:5.5,24:5.5,23:5,22:5,21:4.5,20:4.5,19:4,18:4,17:3.5,16:3.5,15:3,14:3,13:2.5,12:2.5,11:2,10:2,9:1.5,8:1.5,7:1,6:1,5:.5,4:.5,3:0,2:0,1:0,0:0},i={40:9,39:8.5,38:8.5,37:8,36:8,35:7.5,34:7.5,33:7,32:7,31:6.5,30:6.5,29:6,28:6,27:5.5,26:5.5,25:5,24:5,23:4.5,22:4.5,21:4,20:4,19:3.5,18:3.5,17:3,16:3,15:2.5,14:2.5,13:2,12:2,11:1.5,10:1.5,9:1,8:1,7:.5,6:.5,5:0,4:0,3:0,2:0,1:0,0:0};function n(e,t,s="academic"){let l=Math.max(0,Math.min(40,Math.floor(t)));return"listening"===e?r[l]||0:"reading"===e&&("academic"===s?a:i)[l]||0}function l(e,t,s,r){return Math.round((e+t+s+r)/4*2)/2}function d(e){return({9:"Expert User",8.5:"Very Good User",8:"Very Good User",7.5:"Good User",7:"Good User",6.5:"Competent User",6:"Competent User",5.5:"Modest User",5:"Modest User",4.5:"Limited User",4:"Limited User",3.5:"Extremely Limited User",3:"Extremely Limited User",2.5:"Intermittent User",2:"Intermittent User",1.5:"Non User",1:"Non User",.5:"Did not attempt the test",0:"Did not attempt the test"})[e]||"Invalid Score"}function c(e){return e>=8.5?{level:"Excellent",color:"green",description:"Very high proficiency level"}:e>=7?{level:"Good",color:"blue",description:"Good proficiency level"}:e>=6?{level:"Competent",color:"yellow",description:"Competent proficiency level"}:e>=5?{level:"Modest",color:"orange",description:"Modest proficiency level"}:{level:"Limited",color:"red",description:"Limited proficiency level"}}},88804:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(26373).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},89667:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var r=s(60687),a=s(43210),i=s(7766);let n=a.forwardRef(({className:e,type:t,error:s,...a},n)=>(0,r.jsxs)("div",{className:"w-full",children:[(0,r.jsx)("input",{type:t,className:(0,i.cn)("flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:cursor-not-allowed disabled:opacity-50",s&&"border-red-500 focus:ring-red-500 focus:border-red-500",e),ref:n,...a}),s&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-500",children:s})]}));n.displayName="Input"},91645:e=>{"use strict";e.exports=require("net")},96834:(e,t,s)=>{"use strict";s.d(t,{E:()=>i});var r=s(60687);s(43210);var a=s(7766);function i({className:e,variant:t="default",...s}){return(0,r.jsx)("div",{className:(0,a.cn)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{"border-transparent bg-primary text-primary-foreground hover:bg-primary/80":"default"===t,"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80":"secondary"===t,"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80":"destructive"===t,"text-foreground":"outline"===t,"border-transparent bg-blue-100 text-blue-800":"blue"===t,"border-transparent bg-green-100 text-green-800":"green"===t,"border-transparent bg-yellow-100 text-yellow-800":"yellow"===t,"border-transparent bg-red-100 text-red-800":"red"===t,"border-transparent bg-purple-100 text-purple-800":"purple"===t,"border-transparent bg-orange-100 text-orange-800":"orange"===t,"border-transparent bg-gray-100 text-gray-800":"gray"===t},e),...s})}},97992:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},99111:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(37413),a=s(26326),i=s(39916),n=s(59105),l=s(10590);async function d({children:e}){let t=await (0,a.j2)();return t?.user||(0,i.redirect)("/login"),"admin"===t.user.role||t.user.masterAdmin||(0,i.redirect)("/checker"),(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(l.Header,{user:t.user}),(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)(n.Sidebar,{userRole:t.user.role,isMasterAdmin:t.user.masterAdmin}),(0,r.jsx)("main",{className:"flex-1 p-6",children:e})]})]})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,5552,2190,1057,145,1658,9069,5814,892,4017,367,6326,5807],()=>s(34219));module.exports=r})();