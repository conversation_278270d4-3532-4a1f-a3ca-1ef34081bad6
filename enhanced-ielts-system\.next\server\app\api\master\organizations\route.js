(()=>{var e={};e.id=2273,e.ids=[2273],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},61730:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>y,routeModule:()=>h,serverHooks:()=>v,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>j});var s={};t.r(s),t.d(s,{GET:()=>f,POST:()=>z});var a=t(96559),n=t(48088),i=t(37719),o=t(32190),u=t(26326),l=t(71682),p=t(32767),d=t(94634),m=t(85663),g=t(52175),c=t(45697);let x=c.z.object({name:c.z.string().min(2),slug:c.z.string().min(2).regex(/^[a-z0-9-]+$/),adminEmail:c.z.string().email(),adminPassword:c.z.string().min(8),adminName:c.z.string().min(2),billingPlan:c.z.enum(["basic","premium","enterprise"]),features:c.z.array(c.z.string()).default([])});async function z(e){try{let r=await (0,u.j2)();if(!r?.user?.masterAdmin)return o.NextResponse.json({error:"Unauthorized"},{status:401});let t=await e.json(),s=x.parse(t);if((await l.db.select().from(p.organizations).where((0,d.eq)(p.organizations.slug,s.slug)).limit(1)).length>0)return o.NextResponse.json({error:"Organization slug already exists"},{status:409});if((await l.db.select().from(p.users).where((0,d.eq)(p.users.email,s.adminEmail)).limit(1)).length>0)return o.NextResponse.json({error:"Admin email already exists"},{status:409});let[a]=await l.db.insert(p.organizations).values({id:(0,g.sX)(),name:s.name,slug:s.slug,billingPlan:s.billingPlan,features:s.features,settings:{timezone:"Asia/Tashkent",currency:"UZS",language:"en",features:s.features},status:"active"}).returning(),n=await m.Ay.hash(s.adminPassword,12),[i]=await l.db.insert(p.users).values({id:(0,g.sX)(),organizationId:a.id,email:s.adminEmail,password:n,name:s.adminName,role:"admin",masterAdmin:!1,status:"active"}).returning();return o.NextResponse.json({organization:a,adminUser:{id:i.id,email:i.email,name:i.name,role:i.role}},{status:201})}catch(e){if(console.error("Error creating organization:",e),e instanceof c.z.ZodError)return o.NextResponse.json({error:"Invalid input data",details:e.errors},{status:400});return o.NextResponse.json({error:"Internal server error"},{status:500})}}async function f(e){try{let r=await (0,u.j2)();if(!r?.user?.masterAdmin)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),s=(t.get("search"),t.get("status")),a=t.get("plan"),n=parseInt(t.get("page")||"1"),i=parseInt(t.get("limit")||"20"),m=l.db.select().from(p.organizations),g=[];s&&g.push((0,d.eq)(p.organizations.status,s)),a&&g.push((0,d.eq)(p.organizations.billingPlan,a));let c=await m.limit(i).offset((n-1)*i).orderBy(p.organizations.createdAt);return o.NextResponse.json({organizations:c,pagination:{page:n,limit:i,total:c.length}})}catch(e){return console.error("Error fetching organizations:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}let h=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/master/organizations/route",pathname:"/api/master/organizations",filename:"route",bundlePath:"app/api/master/organizations/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\master\\organizations\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:w,workUnitAsyncStorage:j,serverHooks:v}=h;function y(){return(0,i.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:j})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74998:e=>{"use strict";e.exports=require("perf_hooks")},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},91645:e=>{"use strict";e.exports=require("net")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,5552,2190,1057,1595,6326],()=>t(61730));module.exports=s})();