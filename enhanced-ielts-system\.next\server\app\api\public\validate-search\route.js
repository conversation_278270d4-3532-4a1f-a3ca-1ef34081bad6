(()=>{var e={};e.id=3679,e.ids=[3679],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32767:(e,t,a)=>{"use strict";a.r(t),a.d(t,{accessPermissions:()=>N,accessPermissionsRelations:()=>h,aiFeedback:()=>I,aiFeedbackRelations:()=>z,candidates:()=>g,candidatesRelations:()=>w,certificateLifecycle:()=>Q,certificateLifecycleRelations:()=>S,organizations:()=>f,organizationsRelations:()=>b,paymentTransactions:()=>q,paymentTransactionsRelations:()=>A,promotionalRules:()=>v,promotionalRulesRelations:()=>E,testRegistrations:()=>y,testRegistrationsRelations:()=>k,testResults:()=>x,testResultsRelations:()=>R,users:()=>m,usersRelations:()=>P});var s=a(92768),r=a(29334),i=a(34359),d=a(9253),n=a(89697),o=a(54693),l=a(63431),u=a(91036),c=a(72170),p=a(52175),_=a(3884);let f=(0,s.cJ)("organizations",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>(0,p.sX)()),name:(0,r.Qq)("name").notNull(),slug:(0,r.Qq)("slug").unique().notNull(),settings:(0,i.Pq)("settings").$type().default({}),features:(0,i.Pq)("features").$type().default([]),billingPlan:(0,r.Qq)("billing_plan",{enum:["basic","premium","enterprise"]}).default("basic"),status:(0,r.Qq)("status",{enum:["active","suspended","disabled"]}).default("active"),createdAt:(0,d.vE)("created_at").defaultNow().notNull(),updatedAt:(0,d.vE)("updated_at").defaultNow().notNull()},e=>({slugIdx:(0,n.Pe)("org_slug_idx").on(e.slug),statusIdx:(0,n.Pe)("org_status_idx").on(e.status)})),m=(0,s.cJ)("users",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>(0,p.sX)()),organizationId:(0,r.Qq)("organization_id").references(()=>f.id,{onDelete:"cascade"}),email:(0,r.Qq)("email").unique().notNull(),password:(0,r.Qq)("password").notNull(),name:(0,r.Qq)("name").notNull(),role:(0,r.Qq)("role",{enum:["admin","checker"]}).notNull(),masterAdmin:(0,o.zM)("master_admin").default(!1),status:(0,r.Qq)("status",{enum:["active","inactive","suspended"]}).default("active"),lastLoginAt:(0,d.vE)("last_login_at"),createdAt:(0,d.vE)("created_at").defaultNow().notNull(),updatedAt:(0,d.vE)("updated_at").defaultNow().notNull()},e=>({emailIdx:(0,n.Pe)("user_email_idx").on(e.email),orgIdx:(0,n.Pe)("user_org_idx").on(e.organizationId),roleIdx:(0,n.Pe)("user_role_idx").on(e.role)})),g=(0,s.cJ)("candidates",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>(0,p.sX)()),organizationId:(0,r.Qq)("organization_id").references(()=>f.id,{onDelete:"cascade"}).notNull(),fullName:(0,r.Qq)("full_name").notNull(),email:(0,r.Qq)("email"),phoneNumber:(0,r.Qq)("phone_number"),dateOfBirth:(0,d.vE)("date_of_birth"),nationality:(0,r.Qq)("nationality"),passportNumber:(0,r.Qq)("passport_number").notNull(),photoData:(0,r.Qq)("photo_data"),studentStatus:(0,o.zM)("student_status").default(!1),totalTests:(0,l.nd)("total_tests").default(0),createdAt:(0,d.vE)("created_at").defaultNow().notNull(),updatedAt:(0,d.vE)("updated_at").defaultNow().notNull()},e=>({passportIdx:(0,n.Pe)("candidate_passport_idx").on(e.passportNumber),orgIdx:(0,n.Pe)("candidate_org_idx").on(e.organizationId),nameIdx:(0,n.Pe)("candidate_name_idx").on(e.fullName),uniquePassport:(0,u.Am)("unique_passport_per_org").on(e.organizationId,e.passportNumber)})),y=(0,s.cJ)("test_registrations",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>(0,p.sX)()),candidateId:(0,r.Qq)("candidate_id").references(()=>g.id,{onDelete:"cascade"}).notNull(),candidateNumber:(0,r.Qq)("candidate_number").notNull(),testDate:(0,d.vE)("test_date").notNull(),testCenter:(0,r.Qq)("test_center").notNull(),status:(0,r.Qq)("status",{enum:["registered","completed","cancelled"]}).default("registered"),createdAt:(0,d.vE)("created_at").defaultNow().notNull(),updatedAt:(0,d.vE)("updated_at").defaultNow().notNull()},e=>({candidateIdx:(0,n.Pe)("test_reg_candidate_idx").on(e.candidateId),dateIdx:(0,n.Pe)("test_reg_date_idx").on(e.testDate),statusIdx:(0,n.Pe)("test_reg_status_idx").on(e.status)})),x=(0,s.cJ)("test_results",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>(0,p.sX)()),testRegistrationId:(0,r.Qq)("test_registration_id").references(()=>y.id,{onDelete:"cascade"}).notNull(),listeningScore:(0,l.nd)("listening_score"),listeningBandScore:(0,c._)("listening_band_score",{precision:2,scale:1}),readingScore:(0,l.nd)("reading_score"),readingBandScore:(0,c._)("reading_band_score",{precision:2,scale:1}),writingTask1Score:(0,c._)("writing_task1_score",{precision:2,scale:1}),writingTask2Score:(0,c._)("writing_task2_score",{precision:2,scale:1}),writingBandScore:(0,c._)("writing_band_score",{precision:2,scale:1}),speakingFluencyScore:(0,c._)("speaking_fluency_score",{precision:2,scale:1}),speakingLexicalScore:(0,c._)("speaking_lexical_score",{precision:2,scale:1}),speakingGrammarScore:(0,c._)("speaking_grammar_score",{precision:2,scale:1}),speakingPronunciationScore:(0,c._)("speaking_pronunciation_score",{precision:2,scale:1}),speakingBandScore:(0,c._)("speaking_band_score",{precision:2,scale:1}),overallBandScore:(0,c._)("overall_band_score",{precision:2,scale:1}),status:(0,r.Qq)("status",{enum:["draft","completed","verified"]}).default("draft"),enteredBy:(0,r.Qq)("entered_by").references(()=>m.id),verifiedBy:(0,r.Qq)("verified_by").references(()=>m.id),createdAt:(0,d.vE)("created_at").defaultNow().notNull(),updatedAt:(0,d.vE)("updated_at").defaultNow().notNull()},e=>({testRegIdx:(0,n.Pe)("test_result_reg_idx").on(e.testRegistrationId),statusIdx:(0,n.Pe)("test_result_status_idx").on(e.status),overallScoreIdx:(0,n.Pe)("test_result_overall_idx").on(e.overallBandScore)})),q=(0,s.cJ)("payment_transactions",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>(0,p.sX)()),candidateId:(0,r.Qq)("candidate_id").references(()=>g.id,{onDelete:"cascade"}).notNull(),organizationId:(0,r.Qq)("organization_id").references(()=>f.id,{onDelete:"cascade"}).notNull(),amount:(0,c._)("amount",{precision:10,scale:2}).notNull(),currency:(0,r.Qq)("currency").default("UZS").notNull(),gateway:(0,r.Qq)("gateway",{enum:["click","payme","manual"]}).notNull(),gatewayTransactionId:(0,r.Qq)("gateway_transaction_id"),status:(0,r.Qq)("status",{enum:["pending","completed","failed","cancelled","refunded"]}).default("pending"),featureType:(0,r.Qq)("feature_type",{enum:["feedback","certificate","progress"]}).notNull(),resultId:(0,r.Qq)("result_id").references(()=>x.id),metadata:(0,i.Pq)("metadata").$type().default({}),createdAt:(0,d.vE)("created_at").defaultNow().notNull(),completedAt:(0,d.vE)("completed_at")},e=>({candidateIdx:(0,n.Pe)("payment_candidate_idx").on(e.candidateId),statusIdx:(0,n.Pe)("payment_status_idx").on(e.status),gatewayIdx:(0,n.Pe)("payment_gateway_idx").on(e.gateway),featureIdx:(0,n.Pe)("payment_feature_idx").on(e.featureType)})),N=(0,s.cJ)("access_permissions",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>(0,p.sX)()),candidateId:(0,r.Qq)("candidate_id").references(()=>g.id,{onDelete:"cascade"}).notNull(),resultId:(0,r.Qq)("result_id").references(()=>x.id,{onDelete:"cascade"}),featureType:(0,r.Qq)("feature_type",{enum:["feedback","certificate","progress"]}).notNull(),accessType:(0,r.Qq)("access_type",{enum:["paid","promotional","manual"]}).notNull(),grantedBy:(0,r.Qq)("granted_by").references(()=>m.id),grantedAt:(0,d.vE)("granted_at").defaultNow().notNull(),expiresAt:(0,d.vE)("expires_at"),metadata:(0,i.Pq)("metadata").$type().default({})},e=>({candidateIdx:(0,n.Pe)("access_candidate_idx").on(e.candidateId),resultIdx:(0,n.Pe)("access_result_idx").on(e.resultId),featureIdx:(0,n.Pe)("access_feature_idx").on(e.featureType),expiryIdx:(0,n.Pe)("access_expiry_idx").on(e.expiresAt)})),v=(0,s.cJ)("promotional_rules",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>(0,p.sX)()),organizationId:(0,r.Qq)("organization_id").references(()=>f.id,{onDelete:"cascade"}).notNull(),name:(0,r.Qq)("name").notNull(),type:(0,r.Qq)("type",{enum:["student_discount","loyalty_reward","time_based","custom"]}).notNull(),featureType:(0,r.Qq)("feature_type",{enum:["feedback","certificate","progress","all"]}).notNull(),criteria:(0,i.Pq)("criteria").$type().notNull(),benefits:(0,i.Pq)("benefits").$type().notNull(),status:(0,r.Qq)("status",{enum:["active","inactive","expired"]}).default("active"),validFrom:(0,d.vE)("valid_from").notNull(),validUntil:(0,d.vE)("valid_until"),usageLimit:(0,l.nd)("usage_limit"),usageCount:(0,l.nd)("usage_count").default(0),createdAt:(0,d.vE)("created_at").defaultNow().notNull(),updatedAt:(0,d.vE)("updated_at").defaultNow().notNull()},e=>({orgIdx:(0,n.Pe)("promo_org_idx").on(e.organizationId),statusIdx:(0,n.Pe)("promo_status_idx").on(e.status),typeIdx:(0,n.Pe)("promo_type_idx").on(e.type),validityIdx:(0,n.Pe)("promo_validity_idx").on(e.validFrom,e.validUntil)})),I=(0,s.cJ)("ai_feedback",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>(0,p.sX)()),testResultId:(0,r.Qq)("test_result_id").references(()=>x.id,{onDelete:"cascade"}).notNull().unique(),listeningFeedback:(0,r.Qq)("listening_feedback"),readingFeedback:(0,r.Qq)("reading_feedback"),writingFeedback:(0,r.Qq)("writing_feedback"),speakingFeedback:(0,r.Qq)("speaking_feedback"),overallFeedback:(0,r.Qq)("overall_feedback"),studyRecommendations:(0,r.Qq)("study_recommendations"),strengths:(0,i.Pq)("strengths").$type().default([]),weaknesses:(0,i.Pq)("weaknesses").$type().default([]),studyPlan:(0,i.Pq)("study_plan").$type().default({}),generatedAt:(0,d.vE)("generated_at").defaultNow().notNull()},e=>({resultIdx:(0,n.Pe)("ai_feedback_result_idx").on(e.testResultId)})),Q=(0,s.cJ)("certificate_lifecycle",{id:(0,r.Qq)("id").primaryKey().$defaultFn(()=>(0,p.sX)()),resultId:(0,r.Qq)("result_id").references(()=>x.id,{onDelete:"cascade"}).notNull().unique(),serialNumber:(0,r.Qq)("serial_number").unique().notNull(),generatedAt:(0,d.vE)("generated_at").defaultNow().notNull(),expiresAt:(0,d.vE)("expires_at").notNull(),status:(0,r.Qq)("status",{enum:["active","expired","deleted"]}).default("active"),deletionScheduledAt:(0,d.vE)("deletion_scheduled_at"),metadata:(0,i.Pq)("metadata").$type().default({}),createdAt:(0,d.vE)("created_at").defaultNow().notNull(),updatedAt:(0,d.vE)("updated_at").defaultNow().notNull()},e=>({resultIdx:(0,n.Pe)("cert_result_idx").on(e.resultId),serialIdx:(0,n.Pe)("cert_serial_idx").on(e.serialNumber),statusIdx:(0,n.Pe)("cert_status_idx").on(e.status),expiryIdx:(0,n.Pe)("cert_expiry_idx").on(e.expiresAt)})),b=(0,_.K1)(f,({many:e})=>({users:e(m),candidates:e(g),paymentTransactions:e(q),promotionalRules:e(v)})),P=(0,_.K1)(m,({one:e,many:t})=>({organization:e(f,{fields:[m.organizationId],references:[f.id]}),enteredResults:t(x,{relationName:"enteredBy"}),verifiedResults:t(x,{relationName:"verifiedBy"}),grantedPermissions:t(N)})),w=(0,_.K1)(g,({one:e,many:t})=>({organization:e(f,{fields:[g.organizationId],references:[f.id]}),testRegistrations:t(y),paymentTransactions:t(q),accessPermissions:t(N)})),k=(0,_.K1)(y,({one:e,many:t})=>({candidate:e(g,{fields:[y.candidateId],references:[g.id]}),testResults:t(x)})),R=(0,_.K1)(x,({one:e})=>({testRegistration:e(y,{fields:[x.testRegistrationId],references:[y.id]}),enteredByUser:e(m,{fields:[x.enteredBy],references:[m.id],relationName:"enteredBy"}),verifiedByUser:e(m,{fields:[x.verifiedBy],references:[m.id],relationName:"verifiedBy"}),aiFeedback:e(I,{fields:[x.id],references:[I.testResultId]}),certificate:e(Q,{fields:[x.id],references:[Q.resultId]})})),A=(0,_.K1)(q,({one:e})=>({candidate:e(g,{fields:[q.candidateId],references:[g.id]}),organization:e(f,{fields:[q.organizationId],references:[f.id]}),result:e(x,{fields:[q.resultId],references:[x.id]})})),h=(0,_.K1)(N,({one:e})=>({candidate:e(g,{fields:[N.candidateId],references:[g.id]}),result:e(x,{fields:[N.resultId],references:[x.id]}),grantedByUser:e(m,{fields:[N.grantedBy],references:[m.id]})})),E=(0,_.K1)(v,({one:e})=>({organization:e(f,{fields:[v.organizationId],references:[f.id]})})),z=(0,_.K1)(I,({one:e})=>({testResult:e(x,{fields:[I.testResultId],references:[x.id]})})),S=(0,_.K1)(Q,({one:e})=>({result:e(x,{fields:[Q.resultId],references:[x.id]})}))},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71682:(e,t,a)=>{"use strict";a.d(t,{db:()=>d});var s=a(30686),r=a(43971),i=a(32767);let d=function(){if(!process.env.DATABASE_URL)throw Error("DATABASE_URL environment variable is required");let e=process.env.DATABASE_URL,t=(0,r.A)(e,{prepare:!1});return(0,s.f)(t,{schema:i})}()},74998:e=>{"use strict";e.exports=require("perf_hooks")},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},79603:(e,t,a)=>{"use strict";a.r(t),a.d(t,{patchFetch:()=>x,routeModule:()=>f,serverHooks:()=>y,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var s={};a.r(s),a.d(s,{POST:()=>_});var r=a(96559),i=a(48088),d=a(37719),n=a(32190),o=a(71682),l=a(32767),u=a(94634),c=a(45697);let p=c.z.object({passport:c.z.string().min(5),dateOfBirth:c.z.string().min(1)});async function _(e){try{let t=await e.json(),{passport:a,dateOfBirth:s}=p.parse(t),r=await o.db.select({id:l.candidates.id,fullName:l.candidates.fullName}).from(l.candidates).where((0,u.Uo)((0,u.eq)(l.candidates.passportNumber,a.trim()),(0,u.eq)(l.candidates.dateOfBirth,new Date(s)))).limit(1);if(0===r.length)return n.NextResponse.json({message:"No candidate found with the provided passport/birth certificate number and date of birth. Please check your details and try again."},{status:404});return n.NextResponse.json({message:"Candidate found",candidateExists:!0})}catch(e){if(console.error("Search validation error:",e),e instanceof c.z.ZodError)return n.NextResponse.json({message:"Invalid search parameters"},{status:400});return n.NextResponse.json({message:"Search validation failed. Please try again."},{status:500})}}let f=new r.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/public/validate-search/route",pathname:"/api/public/validate-search",filename:"route",bundlePath:"app/api/public/validate-search/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\public\\validate-search\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:y}=f;function x(){return(0,d.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},91645:e=>{"use strict";e.exports=require("net")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[4447,5552,2190,1595],()=>a(79603));module.exports=s})();