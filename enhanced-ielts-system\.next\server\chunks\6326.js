"use strict";exports.id=6326,exports.ids=[6326],exports.modules={26326:(e,t,a)=>{a.d(t,{Y9:()=>u,j2:()=>c});var i=a(19443),s=a(10189),d=a(32705),n=a(71682),r=a(32767),l=a(94634),o=a(85663);let{handlers:u,auth:c,signIn:_,signOut:p}=(0,i.Ay)({adapter:(0,d._)(n.db),session:{strategy:"jwt"},pages:{signIn:"/login"},providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let t=await n.db.select().from(r.users).where((0,l.eq)(r.users.email,e.email)).limit(1);if(!t.length)return null;let a=t[0];if("active"!==a.status||!await o.Ay.compare(e.password,a.password))return null;return await n.db.update(r.users).set({lastLoginAt:new Date}).where((0,l.eq)(r.users.id,a.id)),{id:a.id,email:a.email,name:a.name,role:a.role,organizationId:a.organizationId,masterAdmin:a.masterAdmin}}catch(e){return console.error("Auth error:",e),null}}})],callbacks:{jwt:async({token:e,user:t})=>(t&&(e.role=t.role,e.organizationId=t.organizationId,e.masterAdmin=t.masterAdmin),e),session:async({session:e,token:t})=>(e.user&&(e.user.id=t.sub,e.user.role=t.role,e.user.organizationId=t.organizationId,e.user.masterAdmin=t.masterAdmin),e)}})},32767:(e,t,a)=>{a.r(t),a.d(t,{accessPermissions:()=>x,accessPermissionsRelations:()=>z,aiFeedback:()=>v,aiFeedbackRelations:()=>B,candidates:()=>g,candidatesRelations:()=>P,certificateLifecycle:()=>Q,certificateLifecycleRelations:()=>F,organizations:()=>f,organizationsRelations:()=>b,paymentTransactions:()=>N,paymentTransactionsRelations:()=>R,promotionalRules:()=>I,promotionalRulesRelations:()=>E,testRegistrations:()=>y,testRegistrationsRelations:()=>A,testResults:()=>q,testResultsRelations:()=>k,users:()=>m,usersRelations:()=>w});var i=a(92768),s=a(29334),d=a(34359),n=a(9253),r=a(89697),l=a(54693),o=a(63431),u=a(91036),c=a(72170),_=a(52175),p=a(3884);let f=(0,i.cJ)("organizations",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,_.sX)()),name:(0,s.Qq)("name").notNull(),slug:(0,s.Qq)("slug").unique().notNull(),settings:(0,d.Pq)("settings").$type().default({}),features:(0,d.Pq)("features").$type().default([]),billingPlan:(0,s.Qq)("billing_plan",{enum:["basic","premium","enterprise"]}).default("basic"),status:(0,s.Qq)("status",{enum:["active","suspended","disabled"]}).default("active"),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()},e=>({slugIdx:(0,r.Pe)("org_slug_idx").on(e.slug),statusIdx:(0,r.Pe)("org_status_idx").on(e.status)})),m=(0,i.cJ)("users",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,_.sX)()),organizationId:(0,s.Qq)("organization_id").references(()=>f.id,{onDelete:"cascade"}),email:(0,s.Qq)("email").unique().notNull(),password:(0,s.Qq)("password").notNull(),name:(0,s.Qq)("name").notNull(),role:(0,s.Qq)("role",{enum:["admin","checker"]}).notNull(),masterAdmin:(0,l.zM)("master_admin").default(!1),status:(0,s.Qq)("status",{enum:["active","inactive","suspended"]}).default("active"),lastLoginAt:(0,n.vE)("last_login_at"),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()},e=>({emailIdx:(0,r.Pe)("user_email_idx").on(e.email),orgIdx:(0,r.Pe)("user_org_idx").on(e.organizationId),roleIdx:(0,r.Pe)("user_role_idx").on(e.role)})),g=(0,i.cJ)("candidates",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,_.sX)()),organizationId:(0,s.Qq)("organization_id").references(()=>f.id,{onDelete:"cascade"}).notNull(),fullName:(0,s.Qq)("full_name").notNull(),email:(0,s.Qq)("email"),phoneNumber:(0,s.Qq)("phone_number"),dateOfBirth:(0,n.vE)("date_of_birth"),nationality:(0,s.Qq)("nationality"),passportNumber:(0,s.Qq)("passport_number").notNull(),photoData:(0,s.Qq)("photo_data"),studentStatus:(0,l.zM)("student_status").default(!1),totalTests:(0,o.nd)("total_tests").default(0),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()},e=>({passportIdx:(0,r.Pe)("candidate_passport_idx").on(e.passportNumber),orgIdx:(0,r.Pe)("candidate_org_idx").on(e.organizationId),nameIdx:(0,r.Pe)("candidate_name_idx").on(e.fullName),uniquePassport:(0,u.Am)("unique_passport_per_org").on(e.organizationId,e.passportNumber)})),y=(0,i.cJ)("test_registrations",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,_.sX)()),candidateId:(0,s.Qq)("candidate_id").references(()=>g.id,{onDelete:"cascade"}).notNull(),candidateNumber:(0,s.Qq)("candidate_number").notNull(),testDate:(0,n.vE)("test_date").notNull(),testCenter:(0,s.Qq)("test_center").notNull(),status:(0,s.Qq)("status",{enum:["registered","completed","cancelled"]}).default("registered"),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()},e=>({candidateIdx:(0,r.Pe)("test_reg_candidate_idx").on(e.candidateId),dateIdx:(0,r.Pe)("test_reg_date_idx").on(e.testDate),statusIdx:(0,r.Pe)("test_reg_status_idx").on(e.status)})),q=(0,i.cJ)("test_results",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,_.sX)()),testRegistrationId:(0,s.Qq)("test_registration_id").references(()=>y.id,{onDelete:"cascade"}).notNull(),listeningScore:(0,o.nd)("listening_score"),listeningBandScore:(0,c._)("listening_band_score",{precision:2,scale:1}),readingScore:(0,o.nd)("reading_score"),readingBandScore:(0,c._)("reading_band_score",{precision:2,scale:1}),writingTask1Score:(0,c._)("writing_task1_score",{precision:2,scale:1}),writingTask2Score:(0,c._)("writing_task2_score",{precision:2,scale:1}),writingBandScore:(0,c._)("writing_band_score",{precision:2,scale:1}),speakingFluencyScore:(0,c._)("speaking_fluency_score",{precision:2,scale:1}),speakingLexicalScore:(0,c._)("speaking_lexical_score",{precision:2,scale:1}),speakingGrammarScore:(0,c._)("speaking_grammar_score",{precision:2,scale:1}),speakingPronunciationScore:(0,c._)("speaking_pronunciation_score",{precision:2,scale:1}),speakingBandScore:(0,c._)("speaking_band_score",{precision:2,scale:1}),overallBandScore:(0,c._)("overall_band_score",{precision:2,scale:1}),status:(0,s.Qq)("status",{enum:["draft","completed","verified"]}).default("draft"),enteredBy:(0,s.Qq)("entered_by").references(()=>m.id),verifiedBy:(0,s.Qq)("verified_by").references(()=>m.id),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()},e=>({testRegIdx:(0,r.Pe)("test_result_reg_idx").on(e.testRegistrationId),statusIdx:(0,r.Pe)("test_result_status_idx").on(e.status),overallScoreIdx:(0,r.Pe)("test_result_overall_idx").on(e.overallBandScore)})),N=(0,i.cJ)("payment_transactions",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,_.sX)()),candidateId:(0,s.Qq)("candidate_id").references(()=>g.id,{onDelete:"cascade"}).notNull(),organizationId:(0,s.Qq)("organization_id").references(()=>f.id,{onDelete:"cascade"}).notNull(),amount:(0,c._)("amount",{precision:10,scale:2}).notNull(),currency:(0,s.Qq)("currency").default("UZS").notNull(),gateway:(0,s.Qq)("gateway",{enum:["click","payme","manual"]}).notNull(),gatewayTransactionId:(0,s.Qq)("gateway_transaction_id"),status:(0,s.Qq)("status",{enum:["pending","completed","failed","cancelled","refunded"]}).default("pending"),featureType:(0,s.Qq)("feature_type",{enum:["feedback","certificate","progress"]}).notNull(),resultId:(0,s.Qq)("result_id").references(()=>q.id),metadata:(0,d.Pq)("metadata").$type().default({}),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),completedAt:(0,n.vE)("completed_at")},e=>({candidateIdx:(0,r.Pe)("payment_candidate_idx").on(e.candidateId),statusIdx:(0,r.Pe)("payment_status_idx").on(e.status),gatewayIdx:(0,r.Pe)("payment_gateway_idx").on(e.gateway),featureIdx:(0,r.Pe)("payment_feature_idx").on(e.featureType)})),x=(0,i.cJ)("access_permissions",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,_.sX)()),candidateId:(0,s.Qq)("candidate_id").references(()=>g.id,{onDelete:"cascade"}).notNull(),resultId:(0,s.Qq)("result_id").references(()=>q.id,{onDelete:"cascade"}),featureType:(0,s.Qq)("feature_type",{enum:["feedback","certificate","progress"]}).notNull(),accessType:(0,s.Qq)("access_type",{enum:["paid","promotional","manual"]}).notNull(),grantedBy:(0,s.Qq)("granted_by").references(()=>m.id),grantedAt:(0,n.vE)("granted_at").defaultNow().notNull(),expiresAt:(0,n.vE)("expires_at"),metadata:(0,d.Pq)("metadata").$type().default({})},e=>({candidateIdx:(0,r.Pe)("access_candidate_idx").on(e.candidateId),resultIdx:(0,r.Pe)("access_result_idx").on(e.resultId),featureIdx:(0,r.Pe)("access_feature_idx").on(e.featureType),expiryIdx:(0,r.Pe)("access_expiry_idx").on(e.expiresAt)})),I=(0,i.cJ)("promotional_rules",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,_.sX)()),organizationId:(0,s.Qq)("organization_id").references(()=>f.id,{onDelete:"cascade"}).notNull(),name:(0,s.Qq)("name").notNull(),type:(0,s.Qq)("type",{enum:["student_discount","loyalty_reward","time_based","custom"]}).notNull(),featureType:(0,s.Qq)("feature_type",{enum:["feedback","certificate","progress","all"]}).notNull(),criteria:(0,d.Pq)("criteria").$type().notNull(),benefits:(0,d.Pq)("benefits").$type().notNull(),status:(0,s.Qq)("status",{enum:["active","inactive","expired"]}).default("active"),validFrom:(0,n.vE)("valid_from").notNull(),validUntil:(0,n.vE)("valid_until"),usageLimit:(0,o.nd)("usage_limit"),usageCount:(0,o.nd)("usage_count").default(0),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()},e=>({orgIdx:(0,r.Pe)("promo_org_idx").on(e.organizationId),statusIdx:(0,r.Pe)("promo_status_idx").on(e.status),typeIdx:(0,r.Pe)("promo_type_idx").on(e.type),validityIdx:(0,r.Pe)("promo_validity_idx").on(e.validFrom,e.validUntil)})),v=(0,i.cJ)("ai_feedback",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,_.sX)()),testResultId:(0,s.Qq)("test_result_id").references(()=>q.id,{onDelete:"cascade"}).notNull().unique(),listeningFeedback:(0,s.Qq)("listening_feedback"),readingFeedback:(0,s.Qq)("reading_feedback"),writingFeedback:(0,s.Qq)("writing_feedback"),speakingFeedback:(0,s.Qq)("speaking_feedback"),overallFeedback:(0,s.Qq)("overall_feedback"),studyRecommendations:(0,s.Qq)("study_recommendations"),strengths:(0,d.Pq)("strengths").$type().default([]),weaknesses:(0,d.Pq)("weaknesses").$type().default([]),studyPlan:(0,d.Pq)("study_plan").$type().default({}),generatedAt:(0,n.vE)("generated_at").defaultNow().notNull()},e=>({resultIdx:(0,r.Pe)("ai_feedback_result_idx").on(e.testResultId)})),Q=(0,i.cJ)("certificate_lifecycle",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,_.sX)()),resultId:(0,s.Qq)("result_id").references(()=>q.id,{onDelete:"cascade"}).notNull().unique(),serialNumber:(0,s.Qq)("serial_number").unique().notNull(),generatedAt:(0,n.vE)("generated_at").defaultNow().notNull(),expiresAt:(0,n.vE)("expires_at").notNull(),status:(0,s.Qq)("status",{enum:["active","expired","deleted"]}).default("active"),deletionScheduledAt:(0,n.vE)("deletion_scheduled_at"),metadata:(0,d.Pq)("metadata").$type().default({}),createdAt:(0,n.vE)("created_at").defaultNow().notNull(),updatedAt:(0,n.vE)("updated_at").defaultNow().notNull()},e=>({resultIdx:(0,r.Pe)("cert_result_idx").on(e.resultId),serialIdx:(0,r.Pe)("cert_serial_idx").on(e.serialNumber),statusIdx:(0,r.Pe)("cert_status_idx").on(e.status),expiryIdx:(0,r.Pe)("cert_expiry_idx").on(e.expiresAt)})),b=(0,p.K1)(f,({many:e})=>({users:e(m),candidates:e(g),paymentTransactions:e(N),promotionalRules:e(I)})),w=(0,p.K1)(m,({one:e,many:t})=>({organization:e(f,{fields:[m.organizationId],references:[f.id]}),enteredResults:t(q,{relationName:"enteredBy"}),verifiedResults:t(q,{relationName:"verifiedBy"}),grantedPermissions:t(x)})),P=(0,p.K1)(g,({one:e,many:t})=>({organization:e(f,{fields:[g.organizationId],references:[f.id]}),testRegistrations:t(y),paymentTransactions:t(N),accessPermissions:t(x)})),A=(0,p.K1)(y,({one:e,many:t})=>({candidate:e(g,{fields:[y.candidateId],references:[g.id]}),testResults:t(q)})),k=(0,p.K1)(q,({one:e})=>({testRegistration:e(y,{fields:[q.testRegistrationId],references:[y.id]}),enteredByUser:e(m,{fields:[q.enteredBy],references:[m.id],relationName:"enteredBy"}),verifiedByUser:e(m,{fields:[q.verifiedBy],references:[m.id],relationName:"verifiedBy"}),aiFeedback:e(v,{fields:[q.id],references:[v.testResultId]}),certificate:e(Q,{fields:[q.id],references:[Q.resultId]})})),R=(0,p.K1)(N,({one:e})=>({candidate:e(g,{fields:[N.candidateId],references:[g.id]}),organization:e(f,{fields:[N.organizationId],references:[f.id]}),result:e(q,{fields:[N.resultId],references:[q.id]})})),z=(0,p.K1)(x,({one:e})=>({candidate:e(g,{fields:[x.candidateId],references:[g.id]}),result:e(q,{fields:[x.resultId],references:[q.id]}),grantedByUser:e(m,{fields:[x.grantedBy],references:[m.id]})})),E=(0,p.K1)(I,({one:e})=>({organization:e(f,{fields:[I.organizationId],references:[f.id]})})),B=(0,p.K1)(v,({one:e})=>({testResult:e(q,{fields:[v.testResultId],references:[q.id]})})),F=(0,p.K1)(Q,({one:e})=>({result:e(q,{fields:[Q.resultId],references:[q.id]})}))},71682:(e,t,a)=>{a.d(t,{db:()=>n});var i=a(30686),s=a(43971),d=a(32767);let n=function(){if(!process.env.DATABASE_URL)throw Error("DATABASE_URL environment variable is required");let e=process.env.DATABASE_URL,t=(0,s.A)(e,{prepare:!1});return(0,i.f)(t,{schema:d})}()}};