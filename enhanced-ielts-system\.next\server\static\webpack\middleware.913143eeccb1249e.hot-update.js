"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/lib/db/index.ts":
/*!*****************************!*\
  !*** ./src/lib/db/index.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accessPermissions: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.accessPermissions),\n/* harmony export */   accessPermissionsRelations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.accessPermissionsRelations),\n/* harmony export */   aiFeedback: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.aiFeedback),\n/* harmony export */   aiFeedbackRelations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.aiFeedbackRelations),\n/* harmony export */   candidates: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.candidates),\n/* harmony export */   candidatesRelations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.candidatesRelations),\n/* harmony export */   certificateLifecycle: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.certificateLifecycle),\n/* harmony export */   certificateLifecycleRelations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.certificateLifecycleRelations),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   organizations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.organizations),\n/* harmony export */   organizationsRelations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.organizationsRelations),\n/* harmony export */   paymentTransactions: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.paymentTransactions),\n/* harmony export */   paymentTransactionsRelations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.paymentTransactionsRelations),\n/* harmony export */   promotionalRules: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.promotionalRules),\n/* harmony export */   promotionalRulesRelations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.promotionalRulesRelations),\n/* harmony export */   testRegistrations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.testRegistrations),\n/* harmony export */   testRegistrationsRelations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.testRegistrationsRelations),\n/* harmony export */   testResults: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.testResults),\n/* harmony export */   testResultsRelations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.testResultsRelations),\n/* harmony export */   users: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.users),\n/* harmony export */   usersRelations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.usersRelations)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/postgres-js */ \"(middleware)/./node_modules/drizzle-orm/postgres-js/driver.js\");\n/* harmony import */ var postgres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! postgres */ \"(middleware)/./node_modules/postgres/src/index.js\");\n/* harmony import */ var _schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./schema */ \"(middleware)/./src/lib/db/schema.ts\");\n\n\n\nfunction createDatabase() {\n    if (!process.env.DATABASE_URL) {\n        throw new Error('DATABASE_URL environment variable is required');\n    }\n    const connectionString = process.env.DATABASE_URL;\n    // Disable prefetch as it is not supported for \"Transaction\" pool mode\n    const client = (0,postgres__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(connectionString, {\n        prepare: false\n    });\n    return (0,drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__.drizzle)(client, {\n        schema: _schema__WEBPACK_IMPORTED_MODULE_1__\n    });\n}\nconst db = createDatabase();\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKG1pZGRsZXdhcmUpLy4vc3JjL2xpYi9kYi9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBa0Q7QUFDbEI7QUFDRztBQUVuQyxTQUFTRztJQUNQLElBQUksQ0FBQ0MsUUFBUUMsR0FBRyxDQUFDQyxZQUFZLEVBQUU7UUFDN0IsTUFBTSxJQUFJQyxNQUFNO0lBQ2xCO0lBRUEsTUFBTUMsbUJBQW1CSixRQUFRQyxHQUFHLENBQUNDLFlBQVk7SUFFakQsc0VBQXNFO0lBQ3RFLE1BQU1HLFNBQVNSLG9EQUFRQSxDQUFDTyxrQkFBa0I7UUFBRUUsU0FBUztJQUFNO0lBRTNELE9BQU9WLGdFQUFPQSxDQUFDUyxRQUFRO1FBQUVQLE1BQU1BLHNDQUFBQTtJQUFDO0FBQ2xDO0FBRU8sTUFBTVMsS0FBS1IsaUJBQWlCO0FBR1YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV2luZG93cyAxMVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxUTEQgU3lzdGVtXFxlbmhhbmNlZC1pZWx0cy1zeXN0ZW1cXHNyY1xcbGliXFxkYlxcaW5kZXgudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZHJpenpsZSB9IGZyb20gJ2RyaXp6bGUtb3JtL3Bvc3RncmVzLWpzJztcbmltcG9ydCBwb3N0Z3JlcyBmcm9tICdwb3N0Z3Jlcyc7XG5pbXBvcnQgKiBhcyBzY2hlbWEgZnJvbSAnLi9zY2hlbWEnO1xuXG5mdW5jdGlvbiBjcmVhdGVEYXRhYmFzZSgpIHtcbiAgaWYgKCFwcm9jZXNzLmVudi5EQVRBQkFTRV9VUkwpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ0RBVEFCQVNFX1VSTCBlbnZpcm9ubWVudCB2YXJpYWJsZSBpcyByZXF1aXJlZCcpO1xuICB9XG5cbiAgY29uc3QgY29ubmVjdGlvblN0cmluZyA9IHByb2Nlc3MuZW52LkRBVEFCQVNFX1VSTDtcblxuICAvLyBEaXNhYmxlIHByZWZldGNoIGFzIGl0IGlzIG5vdCBzdXBwb3J0ZWQgZm9yIFwiVHJhbnNhY3Rpb25cIiBwb29sIG1vZGVcbiAgY29uc3QgY2xpZW50ID0gcG9zdGdyZXMoY29ubmVjdGlvblN0cmluZywgeyBwcmVwYXJlOiBmYWxzZSB9KTtcblxuICByZXR1cm4gZHJpenpsZShjbGllbnQsIHsgc2NoZW1hIH0pO1xufVxuXG5leHBvcnQgY29uc3QgZGIgPSBjcmVhdGVEYXRhYmFzZSgpO1xuXG5leHBvcnQgdHlwZSBEYXRhYmFzZSA9IHR5cGVvZiBkYjtcbmV4cG9ydCAqIGZyb20gJy4vc2NoZW1hJztcbiJdLCJuYW1lcyI6WyJkcml6emxlIiwicG9zdGdyZXMiLCJzY2hlbWEiLCJjcmVhdGVEYXRhYmFzZSIsInByb2Nlc3MiLCJlbnYiLCJEQVRBQkFTRV9VUkwiLCJFcnJvciIsImNvbm5lY3Rpb25TdHJpbmciLCJjbGllbnQiLCJwcmVwYXJlIiwiZGIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(middleware)/./src/lib/db/index.ts\n");

/***/ }),

/***/ "(middleware)/./src/lib/db/schema.ts":
/*!******************************!*\
  !*** ./src/lib/db/schema.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accessPermissions: () => (/* binding */ accessPermissions),\n/* harmony export */   accessPermissionsRelations: () => (/* binding */ accessPermissionsRelations),\n/* harmony export */   aiFeedback: () => (/* binding */ aiFeedback),\n/* harmony export */   aiFeedbackRelations: () => (/* binding */ aiFeedbackRelations),\n/* harmony export */   candidates: () => (/* binding */ candidates),\n/* harmony export */   candidatesRelations: () => (/* binding */ candidatesRelations),\n/* harmony export */   certificateLifecycle: () => (/* binding */ certificateLifecycle),\n/* harmony export */   certificateLifecycleRelations: () => (/* binding */ certificateLifecycleRelations),\n/* harmony export */   organizations: () => (/* binding */ organizations),\n/* harmony export */   organizationsRelations: () => (/* binding */ organizationsRelations),\n/* harmony export */   paymentTransactions: () => (/* binding */ paymentTransactions),\n/* harmony export */   paymentTransactionsRelations: () => (/* binding */ paymentTransactionsRelations),\n/* harmony export */   promotionalRules: () => (/* binding */ promotionalRules),\n/* harmony export */   promotionalRulesRelations: () => (/* binding */ promotionalRulesRelations),\n/* harmony export */   testRegistrations: () => (/* binding */ testRegistrations),\n/* harmony export */   testRegistrationsRelations: () => (/* binding */ testRegistrationsRelations),\n/* harmony export */   testResults: () => (/* binding */ testResults),\n/* harmony export */   testResultsRelations: () => (/* binding */ testResultsRelations),\n/* harmony export */   users: () => (/* binding */ users),\n/* harmony export */   usersRelations: () => (/* binding */ usersRelations)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(middleware)/./node_modules/drizzle-orm/pg-core/table.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(middleware)/./node_modules/drizzle-orm/pg-core/columns/text.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(middleware)/./node_modules/drizzle-orm/pg-core/columns/json.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(middleware)/./node_modules/drizzle-orm/pg-core/columns/timestamp.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(middleware)/./node_modules/drizzle-orm/pg-core/indexes.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(middleware)/./node_modules/drizzle-orm/pg-core/columns/boolean.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(middleware)/./node_modules/drizzle-orm/pg-core/columns/integer.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(middleware)/./node_modules/drizzle-orm/pg-core/unique-constraint.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(middleware)/./node_modules/drizzle-orm/pg-core/columns/numeric.js\");\n/* harmony import */ var _paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @paralleldrive/cuid2 */ \"(middleware)/./node_modules/@paralleldrive/cuid2/index.js\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! drizzle-orm */ \"(middleware)/./node_modules/drizzle-orm/relations.js\");\n\n\n\n// Organizations table - Master level management\nconst organizations = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('organizations', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    name: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('name').notNull(),\n    slug: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('slug').unique().notNull(),\n    settings: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.json)('settings').$type().default({}),\n    features: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.json)('features').$type().default([]),\n    billingPlan: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('billing_plan', {\n        enum: [\n            'basic',\n            'premium',\n            'enterprise'\n        ]\n    }).default('basic'),\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status', {\n        enum: [\n            'active',\n            'suspended',\n            'disabled'\n        ]\n    }).default('active'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('updated_at').defaultNow().notNull()\n}, (table)=>({\n        slugIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('org_slug_idx').on(table.slug),\n        statusIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('org_status_idx').on(table.status)\n    }));\n// Users table - Multi-level authentication\nconst users = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('users', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    organizationId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('organization_id').references(()=>organizations.id, {\n        onDelete: 'cascade'\n    }),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').unique().notNull(),\n    password: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('password').notNull(),\n    name: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('name').notNull(),\n    role: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('role', {\n        enum: [\n            'admin',\n            'checker'\n        ]\n    }).notNull(),\n    masterAdmin: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.boolean)('master_admin').default(false),\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status', {\n        enum: [\n            'active',\n            'inactive',\n            'suspended'\n        ]\n    }).default('active'),\n    lastLoginAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('last_login_at'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('updated_at').defaultNow().notNull()\n}, (table)=>({\n        emailIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('user_email_idx').on(table.email),\n        orgIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('user_org_idx').on(table.organizationId),\n        roleIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('user_role_idx').on(table.role)\n    }));\n// Candidates table - Single profile per person\nconst candidates = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('candidates', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    organizationId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('organization_id').references(()=>organizations.id, {\n        onDelete: 'cascade'\n    }).notNull(),\n    fullName: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('full_name').notNull(),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email'),\n    phoneNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('phone_number'),\n    dateOfBirth: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('date_of_birth'),\n    nationality: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('nationality'),\n    passportNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('passport_number').notNull(),\n    photoData: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('photo_data'),\n    studentStatus: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.boolean)('student_status').default(false),\n    totalTests: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.integer)('total_tests').default(0),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('updated_at').defaultNow().notNull()\n}, (table)=>({\n        passportIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('candidate_passport_idx').on(table.passportNumber),\n        orgIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('candidate_org_idx').on(table.organizationId),\n        nameIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('candidate_name_idx').on(table.fullName),\n        uniquePassport: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_8__.unique)('unique_passport_per_org').on(table.organizationId, table.passportNumber)\n    }));\n// Test Registrations table - Multiple tests per candidate\nconst testRegistrations = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('test_registrations', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    candidateId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_id').references(()=>candidates.id, {\n        onDelete: 'cascade'\n    }).notNull(),\n    candidateNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_number').notNull(),\n    testDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('test_date').notNull(),\n    testCenter: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_center').notNull(),\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status', {\n        enum: [\n            'registered',\n            'completed',\n            'cancelled'\n        ]\n    }).default('registered'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('updated_at').defaultNow().notNull()\n}, (table)=>({\n        candidateIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('test_reg_candidate_idx').on(table.candidateId),\n        dateIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('test_reg_date_idx').on(table.testDate),\n        statusIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('test_reg_status_idx').on(table.status)\n    }));\n// Test Results table - Comprehensive IELTS scoring\nconst testResults = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('test_results', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    testRegistrationId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_registration_id').references(()=>testRegistrations.id, {\n        onDelete: 'cascade'\n    }).notNull(),\n    // Listening scores\n    listeningScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.integer)('listening_score'),\n    listeningBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_9__.decimal)('listening_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Reading scores\n    readingScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.integer)('reading_score'),\n    readingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_9__.decimal)('reading_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Writing scores\n    writingTask1Score: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_9__.decimal)('writing_task1_score', {\n        precision: 2,\n        scale: 1\n    }),\n    writingTask2Score: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_9__.decimal)('writing_task2_score', {\n        precision: 2,\n        scale: 1\n    }),\n    writingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_9__.decimal)('writing_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Speaking scores\n    speakingFluencyScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_9__.decimal)('speaking_fluency_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingLexicalScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_9__.decimal)('speaking_lexical_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingGrammarScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_9__.decimal)('speaking_grammar_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingPronunciationScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_9__.decimal)('speaking_pronunciation_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_9__.decimal)('speaking_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Overall score\n    overallBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_9__.decimal)('overall_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Metadata\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status', {\n        enum: [\n            'draft',\n            'completed',\n            'verified'\n        ]\n    }).default('draft'),\n    enteredBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('entered_by').references(()=>users.id),\n    verifiedBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('verified_by').references(()=>users.id),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('updated_at').defaultNow().notNull()\n}, (table)=>({\n        testRegIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('test_result_reg_idx').on(table.testRegistrationId),\n        statusIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('test_result_status_idx').on(table.status),\n        overallScoreIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('test_result_overall_idx').on(table.overallBandScore)\n    }));\n// Payment Transactions table - Payment tracking\nconst paymentTransactions = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('payment_transactions', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    candidateId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_id').references(()=>candidates.id, {\n        onDelete: 'cascade'\n    }).notNull(),\n    organizationId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('organization_id').references(()=>organizations.id, {\n        onDelete: 'cascade'\n    }).notNull(),\n    amount: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_9__.decimal)('amount', {\n        precision: 10,\n        scale: 2\n    }).notNull(),\n    currency: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('currency').default('UZS').notNull(),\n    gateway: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('gateway', {\n        enum: [\n            'click',\n            'payme',\n            'manual'\n        ]\n    }).notNull(),\n    gatewayTransactionId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('gateway_transaction_id'),\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status', {\n        enum: [\n            'pending',\n            'completed',\n            'failed',\n            'cancelled',\n            'refunded'\n        ]\n    }).default('pending'),\n    featureType: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('feature_type', {\n        enum: [\n            'feedback',\n            'certificate',\n            'progress'\n        ]\n    }).notNull(),\n    resultId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('result_id').references(()=>testResults.id),\n    metadata: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.json)('metadata').$type().default({}),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('created_at').defaultNow().notNull(),\n    completedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('completed_at')\n}, (table)=>({\n        candidateIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('payment_candidate_idx').on(table.candidateId),\n        statusIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('payment_status_idx').on(table.status),\n        gatewayIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('payment_gateway_idx').on(table.gateway),\n        featureIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('payment_feature_idx').on(table.featureType)\n    }));\n// Access Permissions table - Premium feature access\nconst accessPermissions = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('access_permissions', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    candidateId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_id').references(()=>candidates.id, {\n        onDelete: 'cascade'\n    }).notNull(),\n    resultId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('result_id').references(()=>testResults.id, {\n        onDelete: 'cascade'\n    }),\n    featureType: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('feature_type', {\n        enum: [\n            'feedback',\n            'certificate',\n            'progress'\n        ]\n    }).notNull(),\n    accessType: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('access_type', {\n        enum: [\n            'paid',\n            'promotional',\n            'manual'\n        ]\n    }).notNull(),\n    grantedBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('granted_by').references(()=>users.id),\n    grantedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('granted_at').defaultNow().notNull(),\n    expiresAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('expires_at'),\n    metadata: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.json)('metadata').$type().default({})\n}, (table)=>({\n        candidateIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('access_candidate_idx').on(table.candidateId),\n        resultIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('access_result_idx').on(table.resultId),\n        featureIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('access_feature_idx').on(table.featureType),\n        expiryIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('access_expiry_idx').on(table.expiresAt)\n    }));\n// Promotional Rules table - Flexible promotion system\nconst promotionalRules = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('promotional_rules', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    organizationId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('organization_id').references(()=>organizations.id, {\n        onDelete: 'cascade'\n    }).notNull(),\n    name: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('name').notNull(),\n    type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('type', {\n        enum: [\n            'student_discount',\n            'loyalty_reward',\n            'time_based',\n            'custom'\n        ]\n    }).notNull(),\n    featureType: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('feature_type', {\n        enum: [\n            'feedback',\n            'certificate',\n            'progress',\n            'all'\n        ]\n    }).notNull(),\n    criteria: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.json)('criteria').$type().notNull(),\n    benefits: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.json)('benefits').$type().notNull(),\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status', {\n        enum: [\n            'active',\n            'inactive',\n            'expired'\n        ]\n    }).default('active'),\n    validFrom: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('valid_from').notNull(),\n    validUntil: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('valid_until'),\n    usageLimit: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.integer)('usage_limit'),\n    usageCount: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.integer)('usage_count').default(0),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('updated_at').defaultNow().notNull()\n}, (table)=>({\n        orgIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('promo_org_idx').on(table.organizationId),\n        statusIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('promo_status_idx').on(table.status),\n        typeIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('promo_type_idx').on(table.type),\n        validityIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('promo_validity_idx').on(table.validFrom, table.validUntil)\n    }));\n// AI Feedback table - Generated feedback storage\nconst aiFeedback = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('ai_feedback', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    testResultId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_result_id').references(()=>testResults.id, {\n        onDelete: 'cascade'\n    }).notNull().unique(),\n    listeningFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('listening_feedback'),\n    readingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('reading_feedback'),\n    writingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('writing_feedback'),\n    speakingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('speaking_feedback'),\n    overallFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('overall_feedback'),\n    studyRecommendations: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('study_recommendations'),\n    strengths: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.json)('strengths').$type().default([]),\n    weaknesses: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.json)('weaknesses').$type().default([]),\n    studyPlan: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.json)('study_plan').$type().default({}),\n    generatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('generated_at').defaultNow().notNull()\n}, (table)=>({\n        resultIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('ai_feedback_result_idx').on(table.testResultId)\n    }));\n// Certificate Lifecycle table - Certificate management\nconst certificateLifecycle = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('certificate_lifecycle', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    resultId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('result_id').references(()=>testResults.id, {\n        onDelete: 'cascade'\n    }).notNull().unique(),\n    serialNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('serial_number').unique().notNull(),\n    generatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('generated_at').defaultNow().notNull(),\n    expiresAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('expires_at').notNull(),\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status', {\n        enum: [\n            'active',\n            'expired',\n            'deleted'\n        ]\n    }).default('active'),\n    deletionScheduledAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('deletion_scheduled_at'),\n    metadata: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.json)('metadata').$type().default({}),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('updated_at').defaultNow().notNull()\n}, (table)=>({\n        resultIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('cert_result_idx').on(table.resultId),\n        serialIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('cert_serial_idx').on(table.serialNumber),\n        statusIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('cert_status_idx').on(table.status),\n        expiryIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('cert_expiry_idx').on(table.expiresAt)\n    }));\n// Relations\nconst organizationsRelations = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_10__.relations)(organizations, ({ many })=>({\n        users: many(users),\n        candidates: many(candidates),\n        paymentTransactions: many(paymentTransactions),\n        promotionalRules: many(promotionalRules)\n    }));\nconst usersRelations = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_10__.relations)(users, ({ one, many })=>({\n        organization: one(organizations, {\n            fields: [\n                users.organizationId\n            ],\n            references: [\n                organizations.id\n            ]\n        }),\n        enteredResults: many(testResults, {\n            relationName: 'enteredBy'\n        }),\n        verifiedResults: many(testResults, {\n            relationName: 'verifiedBy'\n        }),\n        grantedPermissions: many(accessPermissions)\n    }));\nconst candidatesRelations = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_10__.relations)(candidates, ({ one, many })=>({\n        organization: one(organizations, {\n            fields: [\n                candidates.organizationId\n            ],\n            references: [\n                organizations.id\n            ]\n        }),\n        testRegistrations: many(testRegistrations),\n        paymentTransactions: many(paymentTransactions),\n        accessPermissions: many(accessPermissions)\n    }));\nconst testRegistrationsRelations = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_10__.relations)(testRegistrations, ({ one, many })=>({\n        candidate: one(candidates, {\n            fields: [\n                testRegistrations.candidateId\n            ],\n            references: [\n                candidates.id\n            ]\n        }),\n        testResults: many(testResults)\n    }));\nconst testResultsRelations = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_10__.relations)(testResults, ({ one })=>({\n        testRegistration: one(testRegistrations, {\n            fields: [\n                testResults.testRegistrationId\n            ],\n            references: [\n                testRegistrations.id\n            ]\n        }),\n        enteredByUser: one(users, {\n            fields: [\n                testResults.enteredBy\n            ],\n            references: [\n                users.id\n            ],\n            relationName: 'enteredBy'\n        }),\n        verifiedByUser: one(users, {\n            fields: [\n                testResults.verifiedBy\n            ],\n            references: [\n                users.id\n            ],\n            relationName: 'verifiedBy'\n        }),\n        aiFeedback: one(aiFeedback, {\n            fields: [\n                testResults.id\n            ],\n            references: [\n                aiFeedback.testResultId\n            ]\n        }),\n        certificate: one(certificateLifecycle, {\n            fields: [\n                testResults.id\n            ],\n            references: [\n                certificateLifecycle.resultId\n            ]\n        })\n    }));\nconst paymentTransactionsRelations = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_10__.relations)(paymentTransactions, ({ one })=>({\n        candidate: one(candidates, {\n            fields: [\n                paymentTransactions.candidateId\n            ],\n            references: [\n                candidates.id\n            ]\n        }),\n        organization: one(organizations, {\n            fields: [\n                paymentTransactions.organizationId\n            ],\n            references: [\n                organizations.id\n            ]\n        }),\n        result: one(testResults, {\n            fields: [\n                paymentTransactions.resultId\n            ],\n            references: [\n                testResults.id\n            ]\n        })\n    }));\nconst accessPermissionsRelations = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_10__.relations)(accessPermissions, ({ one })=>({\n        candidate: one(candidates, {\n            fields: [\n                accessPermissions.candidateId\n            ],\n            references: [\n                candidates.id\n            ]\n        }),\n        result: one(testResults, {\n            fields: [\n                accessPermissions.resultId\n            ],\n            references: [\n                testResults.id\n            ]\n        }),\n        grantedByUser: one(users, {\n            fields: [\n                accessPermissions.grantedBy\n            ],\n            references: [\n                users.id\n            ]\n        })\n    }));\nconst promotionalRulesRelations = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_10__.relations)(promotionalRules, ({ one })=>({\n        organization: one(organizations, {\n            fields: [\n                promotionalRules.organizationId\n            ],\n            references: [\n                organizations.id\n            ]\n        })\n    }));\nconst aiFeedbackRelations = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_10__.relations)(aiFeedback, ({ one })=>({\n        testResult: one(testResults, {\n            fields: [\n                aiFeedback.testResultId\n            ],\n            references: [\n                testResults.id\n            ]\n        })\n    }));\nconst certificateLifecycleRelations = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_10__.relations)(certificateLifecycle, ({ one })=>({\n        result: one(testResults, {\n            fields: [\n                certificateLifecycle.resultId\n            ],\n            references: [\n                testResults.id\n            ]\n        })\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/lib/db/schema.ts\n");

/***/ })

});