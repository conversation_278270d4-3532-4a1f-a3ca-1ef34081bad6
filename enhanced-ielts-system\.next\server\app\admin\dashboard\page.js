(()=>{var e={};e.id=5957,e.ids=[5957],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5148:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(26373).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},7766:(e,t,s)=>{"use strict";s.d(t,{cn:()=>i});var r=s(49384),a=s(82348);function i(...e){return(0,a.QP)((0,r.$)(e))}},10590:(e,t,s)=>{"use strict";s.d(t,{Header:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\layout\\header.tsx","Header")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19001:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(26373).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27219:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>l});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),d=s(30893),o={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);s.d(t,o);let l={children:["",{children:["admin",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,85423)),"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\admin\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,99111)),"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\admin\\dashboard\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/dashboard/page",pathname:"/admin/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,t,s)=>{"use strict";s.d(t,{$:()=>n});var r=s(60687),a=s(43210),i=s(7766);let n=a.forwardRef(({className:e,variant:t="default",size:s="default",...a},n)=>(0,r.jsx)("button",{className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{"bg-blue-600 text-white hover:bg-blue-700":"default"===t,"bg-red-600 text-white hover:bg-red-700":"destructive"===t,"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50":"outline"===t,"bg-gray-100 text-gray-900 hover:bg-gray-200":"secondary"===t,"hover:bg-gray-100 hover:text-gray-900":"ghost"===t,"text-blue-600 underline-offset-4 hover:underline":"link"===t},{"h-10 px-4 py-2":"default"===s,"h-9 rounded-md px-3":"sm"===s,"h-11 rounded-md px-8":"lg"===s,"h-10 w-10":"icon"===s},e),ref:n,...a}));n.displayName="Button"},30599:(e,t,s)=>{"use strict";s.d(t,{Sidebar:()=>p});var r=s(60687),a=s(85814),i=s.n(a),n=s(16189),d=s(7766),o=s(49625),l=s(17313),c=s(53411),m=s(41312),h=s(10022),u=s(85778),x=s(80428),g=s(6727);function p({userRole:e,isMasterAdmin:t}){let s=(0,n.usePathname)(),a=[{href:"/master/dashboard",label:"Master Dashboard",icon:o.A},{href:"/master/organizations",label:"Organizations",icon:l.A},{href:"/master/analytics",label:"Analytics",icon:c.A}],p=[{href:"/admin/dashboard",label:"Dashboard",icon:o.A},{href:"/admin/candidates",label:"Candidates",icon:m.A},{href:"/admin/results",label:"Test Results",icon:h.A},{href:"/admin/payments",label:"Payments",icon:u.A},{href:"/admin/promotions",label:"Promotions",icon:x.A}],b=[{href:"/checker/dashboard",label:"Dashboard",icon:o.A},{href:"/checker/entry",label:"Result Entry",icon:g.A},{href:"/checker/results",label:"My Results",icon:h.A}],y=t?[...a,...p]:"admin"===e?p:b;return(0,r.jsx)("aside",{className:"w-64 bg-white shadow-sm border-r border-gray-200 min-h-screen",children:(0,r.jsx)("nav",{className:"mt-8 px-4",children:(0,r.jsx)("ul",{className:"space-y-2",children:y.map(e=>{let t=e.icon,a=s===e.href||s.startsWith(e.href+"/");return(0,r.jsx)("li",{children:(0,r.jsxs)(i(),{href:e.href,className:(0,d.cn)("flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors",a?"bg-blue-50 text-blue-700 border-r-2 border-blue-700":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"),children:[(0,r.jsx)(t,{className:"mr-3 h-5 w-5"}),e.label]})},e.href)})})})})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37882:()=>{},43648:(e,t,s)=>{"use strict";s.d(t,{C:()=>a});var r=s(37413);function a({title:e,value:t,icon:s,description:a,trend:i}){return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e}),(0,r.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:t.toLocaleString()})]}),(0,r.jsx)("div",{className:"p-3 bg-blue-50 rounded-full",children:(0,r.jsx)(s,{className:"h-6 w-6 text-blue-600"})})]}),(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:a}),(0,r.jsx)("p",{className:"text-sm text-green-600 font-medium",children:i})]})]})}},50259:(e,t,s)=>{Promise.resolve().then(s.bind(s,74456)),Promise.resolve().then(s.bind(s,30599))},55511:e=>{"use strict";e.exports=require("crypto")},59105:(e,t,s)=>{"use strict";s.d(t,{Sidebar:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\layout\\sidebar.tsx","Sidebar")},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67973:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74456:(e,t,s)=>{"use strict";s.d(t,{Header:()=>l});var r=s(60687),a=s(99208),i=s(29523),n=s(58869),d=s(84027),o=s(40083);function l({user:e}){return(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"Enhanced IELTS System"}),e.masterAdmin&&(0,r.jsx)("span",{className:"ml-3 px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full",children:"Master Admin"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(n.A,{className:"h-4 w-4 text-gray-500"}),(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("div",{className:"font-medium text-gray-900",children:e.name}),(0,r.jsx)("div",{className:"text-gray-500 capitalize",children:e.role})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(i.$,{variant:"ghost",size:"sm",children:(0,r.jsx)(d.A,{className:"h-4 w-4"})}),(0,r.jsxs)(i.$,{variant:"ghost",size:"sm",onClick:()=>{(0,a.CI)({callbackUrl:"/login"})},children:[(0,r.jsx)(o.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"ml-1",children:"Sign Out"})]})]})]})]})})})}},74998:e=>{"use strict";e.exports=require("perf_hooks")},77598:e=>{"use strict";e.exports=require("node:crypto")},78245:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},83799:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(26373).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},85423:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>b});var r=s(37413),a=s(26326),i=s(71682),n=s(32767),d=s(1463),o=s(94634),l=s(43648),c=s(16048),m=s(35407);async function h({organizationId:e,type:t}){if("results"===t){let t=await i.db.select({id:n.testResults.id,overallBandScore:n.testResults.overallBandScore,status:n.testResults.status,createdAt:n.testResults.createdAt,candidateName:n.candidates.fullName,candidateNumber:n.testRegistrations.candidateNumber}).from(n.testResults).innerJoin(n.testRegistrations,(0,o.eq)(n.testResults.testRegistrationId,n.testRegistrations.id)).innerJoin(n.candidates,(0,o.eq)(n.testRegistrations.candidateId,n.candidates.id)).where((0,o.eq)(n.candidates.organizationId,e)).orderBy((0,c.i)(n.testResults.createdAt)).limit(5);return(0,r.jsx)("div",{className:"space-y-3",children:0===t.length?(0,r.jsx)("p",{className:"text-gray-500 text-sm",children:"No recent results"}):t.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:e.candidateName}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["#",e.candidateNumber]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("p",{className:"font-semibold text-blue-600",children:["Band ",e.overallBandScore]}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:(0,m.m)(new Date(e.createdAt),{addSuffix:!0})})]})]},e.id))})}if("registrations"===t){let t=await i.db.select({id:n.testRegistrations.id,candidateNumber:n.testRegistrations.candidateNumber,testDate:n.testRegistrations.testDate,status:n.testRegistrations.status,createdAt:n.testRegistrations.createdAt,candidateName:n.candidates.fullName}).from(n.testRegistrations).innerJoin(n.candidates,(0,o.eq)(n.testRegistrations.candidateId,n.candidates.id)).where((0,o.eq)(n.candidates.organizationId,e)).orderBy((0,c.i)(n.testRegistrations.createdAt)).limit(5);return(0,r.jsx)("div",{className:"space-y-3",children:0===t.length?(0,r.jsx)("p",{className:"text-gray-500 text-sm",children:"No recent registrations"}):t.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:e.candidateName}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["#",e.candidateNumber]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:new Date(e.testDate).toLocaleDateString()}),(0,r.jsx)("p",{className:"text-xs text-gray-500 capitalize",children:e.status})]})]},e.id))})}return null}var u=s(19001),x=s(88804),g=s(83799),p=s(5148);async function b(){let e=await (0,a.j2)();if(!e?.user?.organizationId)return(0,r.jsx)("div",{children:"Access denied"});let[t,s,c,m]=await Promise.all([i.db.select({count:(0,d.U9)()}).from(n.candidates).where((0,o.eq)(n.candidates.organizationId,e.user.organizationId)),i.db.select({count:(0,d.U9)()}).from(n.testRegistrations).innerJoin(n.candidates,(0,o.eq)(n.testRegistrations.candidateId,n.candidates.id)).where((0,o.eq)(n.candidates.organizationId,e.user.organizationId)),i.db.select({count:(0,d.U9)()}).from(n.testRegistrations).innerJoin(n.candidates,(0,o.eq)(n.testRegistrations.candidateId,n.candidates.id)).where((0,o.Uo)((0,o.eq)(n.candidates.organizationId,e.user.organizationId),(0,o.eq)(n.testRegistrations.status,"completed"),(0,o.RO)(n.testRegistrations.testDate,new Date(new Date().getFullYear(),new Date().getMonth(),1)))),i.db.select({count:(0,d.U9)()}).from(n.paymentTransactions).where((0,o.Uo)((0,o.eq)(n.paymentTransactions.organizationId,e.user.organizationId),(0,o.eq)(n.paymentTransactions.status,"completed"),(0,o.RO)(n.paymentTransactions.createdAt,new Date(Date.now()-2592e6))))]),b=[{title:"Total Candidates",value:t[0]?.count||0,icon:u.A,description:"Registered candidates",trend:"+12% from last month"},{title:"Total Tests",value:s[0]?.count||0,icon:x.A,description:"All test registrations",trend:"+8% from last month"},{title:"Tests This Month",value:c[0]?.count||0,icon:g.A,description:"Completed this month",trend:"+15% from last month"},{title:"Recent Payments",value:m[0]?.count||0,icon:p.A,description:"Last 30 days",trend:"+23% from last month"}];return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Dashboard"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Welcome back! Here's what's happening with your IELTS center."})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:b.map((e,t)=>(0,r.jsx)(l.C,{...e},t))}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Recent Test Results"}),(0,r.jsx)(h,{organizationId:e.user.organizationId,type:"results"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Recent Registrations"}),(0,r.jsx)(h,{organizationId:e.user.organizationId,type:"registrations"})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quick Actions"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("a",{href:"/admin/candidates",className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(u.A,{className:"h-8 w-8 text-blue-600 mb-2"}),(0,r.jsx)("h3",{className:"font-medium text-gray-900",children:"Add New Candidate"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Register a new test candidate"})]}),(0,r.jsxs)("a",{href:"/admin/results",className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(x.A,{className:"h-8 w-8 text-green-600 mb-2"}),(0,r.jsx)("h3",{className:"font-medium text-gray-900",children:"View Results"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Check recent test results"})]}),(0,r.jsxs)("a",{href:"/admin/payments",className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(p.A,{className:"h-8 w-8 text-purple-600 mb-2"}),(0,r.jsx)("h3",{className:"font-medium text-gray-900",children:"Payment Reports"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"View payment analytics"})]})]})]})]})}},91026:()=>{},91645:e=>{"use strict";e.exports=require("net")},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l,metadata:()=>o});var r=s(37413),a=s(22376),i=s.n(a),n=s(68726),d=s.n(n);s(61135);let o={title:"Create Next App",description:"Generated by create next app"};function l({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${i().variable} ${d().variable} antialiased`,children:e})})}},96487:()=>{},97115:(e,t,s)=>{Promise.resolve().then(s.bind(s,10590)),Promise.resolve().then(s.bind(s,59105))},99111:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(37413),a=s(26326),i=s(39916),n=s(59105),d=s(10590);async function o({children:e}){let t=await (0,a.j2)();return t?.user||(0,i.redirect)("/login"),"admin"===t.user.role||t.user.masterAdmin||(0,i.redirect)("/checker"),(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(d.Header,{user:t.user}),(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)(n.Sidebar,{userRole:t.user.role,isMasterAdmin:t.user.masterAdmin}),(0,r.jsx)("main",{className:"flex-1 p-6",children:e})]})]})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,5552,2190,1057,145,1658,9069,5814,892,6590,6326],()=>s(27219));module.exports=r})();