(()=>{var e={};e.id=84,e.ids=[84],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},78537:(e,t,r)=>{"use strict";r.d(t,{J:()=>l});var a=r(80137),i=r(71682),s=r(32767),n=r(94634),o=r(96657),c=r(51890);class l{static{this.CERTIFICATE_WIDTH=297}static{this.CERTIFICATE_HEIGHT=210}static async generateCertificate(e){let t=await i.db.select({testResult:s.testResults,candidate:s.candidates,organization:s.organizations,testRegistration:s.testRegistrations}).from(s.testResults).innerJoin(s.testRegistrations,(0,n.eq)(s.testResults.testRegistrationId,s.testRegistrations.id)).innerJoin(s.candidates,(0,n.eq)(s.testRegistrations.candidateId,s.candidates.id)).innerJoin(s.organizations,(0,n.eq)(s.candidates.organizationId,s.organizations.id)).where((0,n.eq)(s.testResults.id,e)).limit(1);if(0===t.length)throw Error("Test result not found");let{testResult:r,candidate:a,organization:o,testRegistration:c}=t[0],l=await i.db.select().from(s.certificateLifecycle).where((0,n.eq)(s.certificateLifecycle.resultId,e)).limit(1);if(l.length>0&&"active"===l[0].status)throw Error("Certificate already exists for this result");let d=await this.generateSerialNumber(),u=new Date(new Date(c.testDate));u.setMonth(u.getMonth()+6);let[p]=await i.db.insert(s.certificateLifecycle).values({resultId:e,serialNumber:d,expiresAt:u,metadata:{candidateName:a.fullName,testDate:c.testDate.toISOString().split("T")[0],overallBandScore:parseFloat(r.overallBandScore||"0"),organizationName:o.name,certificateType:"IELTS Academic"}}).returning(),g={candidateName:a.fullName,passportNumber:a.passportNumber,testDate:c.testDate.toISOString().split("T")[0],testCenter:o.name,listeningBandScore:parseFloat(r.listeningBandScore||"0"),readingBandScore:parseFloat(r.readingBandScore||"0"),writingBandScore:parseFloat(r.writingBandScore||"0"),speakingBandScore:parseFloat(r.speakingBandScore||"0"),overallBandScore:parseFloat(r.overallBandScore||"0"),organizationName:o.name,serialNumber:d,issueDate:new Date().toISOString().split("T")[0],expiryDate:u.toISOString().split("T")[0],candidatePhoto:a.photoData},T=await this.createPDF(g);return{certificateId:p.id,serialNumber:d,pdfBuffer:T}}static async generateSerialNumber(){let e=new Date().getFullYear(),t=String(new Date().getMonth()+1).padStart(2,"0"),r=new Date(e,new Date().getMonth(),1),a=new Date(e,new Date().getMonth()+1,0),c=await i.db.select({count:(0,o.ll)`count(*)`}).from(s.certificateLifecycle).where((0,n.Uo)((0,n.RO)(s.certificateLifecycle.generatedAt,r),(0,n.wJ)(s.certificateLifecycle.generatedAt,a))),l=String((c[0]?.count||0)+1).padStart(4,"0");return`IELTS-${e}${t}-${l}`}static async createPDF(e){let t=new a.Ay({orientation:"landscape",unit:"mm",format:"a4"});t.setFont("helvetica","normal"),t.setFillColor(248,250,252),t.rect(0,0,this.CERTIFICATE_WIDTH,this.CERTIFICATE_HEIGHT,"F"),t.setDrawColor(59,130,246),t.setLineWidth(2),t.rect(10,10,this.CERTIFICATE_WIDTH-20,this.CERTIFICATE_HEIGHT-20),t.setFontSize(24),t.setFont("helvetica","bold"),t.setTextColor(59,130,246),t.text("IELTS CERTIFICATE",this.CERTIFICATE_WIDTH/2,35,{align:"center"}),t.setFontSize(16),t.setFont("helvetica","normal"),t.setTextColor(75,85,99),t.text("International English Language Testing System",this.CERTIFICATE_WIDTH/2,45,{align:"center"}),t.setFontSize(14),t.setFont("helvetica","normal"),t.setTextColor(0,0,0);let r=70;t.setFont("helvetica","bold"),t.text("Candidate Name:",30,r),t.setFont("helvetica","normal"),t.text(e.candidateName,80,r),t.setFont("helvetica","bold"),t.text("Passport/ID Number:",180,r),t.setFont("helvetica","normal"),t.text(e.passportNumber,230,r),r+=15,t.setFont("helvetica","bold"),t.text("Test Date:",30,r),t.setFont("helvetica","normal"),t.text(e.testDate,60,r),t.setFont("helvetica","bold"),t.text("Test Center:",180,r),t.setFont("helvetica","normal"),t.text(e.testCenter,210,r),r+=25,t.setFont("helvetica","bold"),t.setFontSize(16),t.text("Test Results",this.CERTIFICATE_WIDTH/2,r,{align:"center"}),r+=15;let i=[["Skill","Band Score"],["Listening",e.listeningBandScore.toString()],["Reading",e.readingBandScore.toString()],["Writing",e.writingBandScore.toString()],["Speaking",e.speakingBandScore.toString()],["Overall Band Score",e.overallBandScore.toString()]],s=this.CERTIFICATE_WIDTH/2-40,n=r;i.forEach((e,r)=>{e.forEach((e,a)=>{let o=s+40*a,c=n+8*r;0===r?(t.setFillColor(59,130,246),t.setTextColor(255,255,255),t.setFont("helvetica","bold")):r===i.length-1?(t.setFillColor(34,197,94),t.setTextColor(255,255,255),t.setFont("helvetica","bold")):(t.setFillColor(248,250,252),t.setTextColor(0,0,0),t.setFont("helvetica","normal")),t.rect(o,c,40,8,"F"),t.setDrawColor(0,0,0),t.rect(o,c,40,8),t.text(e,o+20,c+4+1,{align:"center"})})}),r+=8*i.length+20,t.setTextColor(0,0,0),t.setFont("helvetica","normal"),t.setFontSize(10),t.text(`Certificate Serial Number: ${e.serialNumber}`,30,r),t.text(`Issue Date: ${e.issueDate}`,180,r),r+=8,t.text(`Valid Until: ${e.expiryDate}`,30,r),t.text(`Issued by: ${e.organizationName}`,180,r);let o=`${process.env.NEXT_PUBLIC_APP_URL||"http://localhost:3000"}/verify/${e.serialNumber}`,l=await c.toDataURL(o,{width:100,margin:1});return t.addImage(l,"PNG",this.CERTIFICATE_WIDTH-40,this.CERTIFICATE_HEIGHT-40,25,25),t.setFontSize(8),t.text("Scan to verify",this.CERTIFICATE_WIDTH-27,this.CERTIFICATE_HEIGHT-10,{align:"center"}),t.setFontSize(8),t.setTextColor(107,114,128),t.text("This certificate is valid for 6 months from the test date. For verification, visit our website or scan the QR code.",this.CERTIFICATE_WIDTH/2,this.CERTIFICATE_HEIGHT-5,{align:"center"}),Buffer.from(t.output("arraybuffer"))}}},79428:e=>{"use strict";e.exports=require("buffer")},82263:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>u,serverHooks:()=>T,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>g});var a={};r.r(a),r.d(a,{POST:()=>d});var i=r(96559),s=r(48088),n=r(37719),o=r(32190),c=r(26326),l=r(78537);async function d(e){let t=await (0,c.j2)();if(!t?.user?.organizationId)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{resultId:r}=await e.json();try{let e=await l.J.generateCertificate(r);return o.NextResponse.json({certificateId:e.certificateId,serialNumber:e.serialNumber,downloadUrl:`/api/certificates/download/${e.certificateId}`},{status:201})}catch(e){return console.error("Error generating certificate:",e),o.NextResponse.json({error:e instanceof Error?e.message:"Failed to generate certificate"},{status:500})}}let u=new i.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/certificates/generate/route",pathname:"/api/certificates/generate",filename:"route",bundlePath:"app/api/certificates/generate/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\certificates\\generate\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:g,serverHooks:T}=u;function h(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:g})}},91645:e=>{"use strict";e.exports=require("net")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,5552,2190,1057,2140,6326],()=>r(82263));module.exports=a})();