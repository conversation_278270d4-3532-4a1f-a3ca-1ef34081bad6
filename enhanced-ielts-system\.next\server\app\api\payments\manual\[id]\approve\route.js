"use strict";(()=>{var e={};e.id=5048,e.ids=[5048],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64017:(e,t,a)=>{a.r(t),a.d(t,{patchFetch:()=>h,routeModule:()=>g,serverHooks:()=>q,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>j});var r={};a.r(r),a.d(r,{GET:()=>f,POST:()=>x});var n=a(96559),s=a(48088),o=a(37719),i=a(32190),d=a(26326),p=a(71682),u=a(32767),c=a(94634),l=a(45697),m=a(18758);let y=l.z.object({action:l.z.enum(["approve","reject"]),notes:l.z.string().optional()});async function x(e,{params:t}){try{let a=await (0,d.j2)();if(!a?.user?.organizationId)return i.NextResponse.json({error:"Unauthorized"},{status:401});if("admin"!==a.user.role&&!a.user.masterAdmin)return i.NextResponse.json({error:"Insufficient permissions"},{status:403});let r=await e.json(),{action:n,notes:s}=y.parse(r),o=await p.db.select({transaction:u.paymentTransactions,candidate:u.candidates}).from(u.paymentTransactions).leftJoin(u.candidates,(0,c.eq)(u.paymentTransactions.candidateId,u.candidates.id)).where((0,c.Uo)((0,c.eq)(u.paymentTransactions.id,t.id),(0,c.eq)(u.paymentTransactions.gateway,"manual"),(0,c.eq)(u.candidates.organizationId,a.user.organizationId))).limit(1);if(0===o.length)return i.NextResponse.json({error:"Manual payment not found or does not belong to your organization"},{status:404});let l=o[0];if("pending"!==l.transaction.status)return i.NextResponse.json({error:`Transaction is already ${l.transaction.status}`},{status:400});let x="approve"===n?"completed":"failed",f={...l.transaction.metadata,approvalAction:n,approvalNotes:s,approvedBy:a.user.id,approvedAt:new Date().toISOString()};if(!await (0,m.ee)(t.id,x,void 0,f))return i.NextResponse.json({error:"Failed to update payment status"},{status:500});return i.NextResponse.json({transactionId:t.id,action:n,status:x,message:"approve"===n?"Payment approved successfully. Access has been granted.":"Payment rejected. Candidate has been notified."})}catch(e){if(console.error("Manual payment approval error:",e),e instanceof l.z.ZodError)return i.NextResponse.json({error:"Invalid data",details:e.errors},{status:400});return i.NextResponse.json({error:"Failed to process approval"},{status:500})}}async function f(e,{params:t}){try{let e=await (0,d.j2)();if(!e?.user?.organizationId)return i.NextResponse.json({error:"Unauthorized"},{status:401});if("admin"!==e.user.role&&!e.user.masterAdmin)return i.NextResponse.json({error:"Insufficient permissions"},{status:403});let a=await p.db.select({transaction:u.paymentTransactions,candidate:u.candidates}).from(u.paymentTransactions).leftJoin(u.candidates,(0,c.eq)(u.paymentTransactions.candidateId,u.candidates.id)).where((0,c.Uo)((0,c.eq)(u.paymentTransactions.id,t.id),(0,c.eq)(u.paymentTransactions.gateway,"manual"),(0,c.eq)(u.candidates.organizationId,e.user.organizationId))).limit(1);if(0===a.length)return i.NextResponse.json({error:"Manual payment not found or does not belong to your organization"},{status:404});let r=a[0];return i.NextResponse.json({id:r.transaction.id,candidate:{id:r.candidate?.id,fullName:r.candidate?.fullName,email:r.candidate?.email,passportNumber:r.candidate?.passportNumber},amount:parseFloat(r.transaction.amount),currency:r.transaction.currency,featureType:r.transaction.featureType,resultId:r.transaction.resultId,status:r.transaction.status,metadata:r.transaction.metadata,createdAt:r.transaction.createdAt,completedAt:r.transaction.completedAt})}catch(e){return console.error("Manual payment details fetch error:",e),i.NextResponse.json({error:"Failed to fetch payment details"},{status:500})}}let g=new n.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/payments/manual/[id]/approve/route",pathname:"/api/payments/manual/[id]/approve",filename:"route",bundlePath:"app/api/payments/manual/[id]/approve/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\payments\\manual\\[id]\\approve\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:v,workUnitAsyncStorage:j,serverHooks:q}=g;function h(){return(0,o.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:j})}},74998:e=>{e.exports=require("perf_hooks")},77598:e=>{e.exports=require("node:crypto")},91645:e=>{e.exports=require("net")}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[4447,5552,2190,1057,1595,6326,7346],()=>a(64017));module.exports=r})();