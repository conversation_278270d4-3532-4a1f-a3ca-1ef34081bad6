(()=>{var e={};e.id=1334,e.ids=[1334],e.modules={1463:(e,s,r)=>{"use strict";r.d(s,{U9:()=>a});var t=r(96657);function a(e){return(0,t.ll)`count(${e||t.ll.raw("*")})`.mapWith(Number)}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4536:(e,s,r)=>{let{createProxy:t}=r(39844);e.exports=t("C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\node_modules\\next\\dist\\client\\app-dir\\link.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14189:(e,s,r)=>{"use strict";r.d(s,{o:()=>u});var t=r(37413),a=r(30084),i=r(23469),n=r(32127),l=r(19001);let d=(0,r(26373).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var o=r(4536),c=r.n(o);function u({organization:e}){return(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"p-2 bg-blue-50 rounded-lg",children:(0,t.jsx)(n.A,{className:"h-6 w-6 text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900",children:e.name}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["@",e.slug]})]})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(a.E,{className:(e=>{switch(e){case"active":return"bg-green-100 text-green-800";case"suspended":return"bg-yellow-100 text-yellow-800";case"disabled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(e.status),children:e.status}),(0,t.jsx)(a.E,{className:(e=>{switch(e){case"enterprise":return"bg-purple-100 text-purple-800";case"premium":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}})(e.billingPlan),children:e.billingPlan})]})]}),(0,t.jsxs)("div",{className:"space-y-3 mb-4",children:[(0,t.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,t.jsx)(l.A,{className:"h-4 w-4 mr-2"}),(0,t.jsxs)("span",{children:["Created ",new Date(e.createdAt).toLocaleDateString()]})]}),e.features&&e.features.length>0&&(0,t.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.features.slice(0,3).map(e=>(0,t.jsx)("span",{className:"px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded",children:e.replace("_"," ")},e)),e.features.length>3&&(0,t.jsxs)("span",{className:"px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded",children:["+",e.features.length-3," more"]})]})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(c(),{href:`/master/organizations/${e.id}`,className:"flex-1",children:(0,t.jsxs)(i.$,{variant:"outline",size:"sm",className:"w-full",children:[(0,t.jsx)(d,{className:"h-4 w-4 mr-1"}),"Manage"]})}),(0,t.jsx)(c(),{href:`/master/organizations/${e.id}/analytics`,className:"flex-1",children:(0,t.jsx)(i.$,{variant:"outline",size:"sm",className:"w-full",children:"View Analytics"})})]})]})}},16032:(e,s,r)=>{"use strict";r.d(s,{CreateOrganizationModal:()=>x});var t=r(60687),a=r(43210),i=r(16189),n=r(27605),l=r(63442),d=r(9275),o=r(29523),c=r(89667),u=r(19352);let m=d.z.object({name:d.z.string().min(2,"Organization name must be at least 2 characters"),slug:d.z.string().min(2,"Slug must be at least 2 characters").regex(/^[a-z0-9-]+$/,"Slug can only contain lowercase letters, numbers, and hyphens"),adminEmail:d.z.string().email("Invalid email address"),adminPassword:d.z.string().min(8,"Password must be at least 8 characters"),adminName:d.z.string().min(2,"Admin name must be at least 2 characters"),billingPlan:d.z.enum(["basic","premium","enterprise"]),features:d.z.array(d.z.string()).default([])});function x({children:e}){let[s,r]=(0,a.useState)(!1),[d,x]=(0,a.useState)(!1),g=(0,i.useRouter)(),p=(0,n.mN)({resolver:(0,l.u)(m),defaultValues:{billingPlan:"basic",features:[]}}),h=async e=>{x(!0);try{let s=await fetch("/api/master/organizations",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok){let e=await s.json();throw Error(e.message||"Failed to create organization")}await s.json(),r(!1),p.reset(),g.refresh()}catch(e){console.error("Error creating organization:",e)}finally{x(!1)}},b=e=>{let s=e.target.value.toLowerCase().replace(/[^a-z0-9\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim();p.setValue("slug",s)};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{onClick:()=>r(!0),children:e}),(0,t.jsx)(u.a,{isOpen:s,onClose:()=>r(!1),title:"Create New Organization",children:(0,t.jsxs)("form",{onSubmit:p.handleSubmit(h),className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsx)(c.p,{...p.register("name"),placeholder:"Organization Name",onChange:e=>{p.register("name").onChange(e),b(e)},error:p.formState.errors.name?.message}),(0,t.jsx)(c.p,{...p.register("slug"),placeholder:"organization-slug",error:p.formState.errors.slug?.message})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"Admin User Details"}),(0,t.jsx)(c.p,{...p.register("adminName"),placeholder:"Admin Full Name",error:p.formState.errors.adminName?.message}),(0,t.jsx)(c.p,{...p.register("adminEmail"),type:"email",placeholder:"Admin Email",error:p.formState.errors.adminEmail?.message}),(0,t.jsx)(c.p,{...p.register("adminPassword"),type:"password",placeholder:"Admin Password",error:p.formState.errors.adminPassword?.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Billing Plan"}),(0,t.jsxs)("select",{...p.register("billingPlan"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"basic",children:"Basic"}),(0,t.jsx)("option",{value:"premium",children:"Premium"}),(0,t.jsx)("option",{value:"enterprise",children:"Enterprise"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Features"}),(0,t.jsx)("div",{className:"grid grid-cols-2 gap-2",children:[{id:"ai_feedback",label:"AI Feedback"},{id:"certificates",label:"Certificates"},{id:"progress_tracking",label:"Progress Tracking"},{id:"promotions",label:"Promotions"},{id:"analytics",label:"Advanced Analytics"}].map(e=>(0,t.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{type:"checkbox",value:e.id,...p.register("features"),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,t.jsx)("span",{className:"text-sm text-gray-700",children:e.label})]},e.id))})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,t.jsx)(o.$,{type:"button",variant:"outline",onClick:()=>r(!1),disabled:d,children:"Cancel"}),(0,t.jsx)(o.$,{type:"submit",disabled:d,children:d?"Creating...":"Create Organization"})]})]})})]})}},19001:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(26373).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30084:(e,s,r)=>{"use strict";r.d(s,{E:()=>i});var t=r(37413);r(61120);var a=r(72984);function i({className:e,variant:s="default",...r}){return(0,t.jsx)("div",{className:(0,a.cn)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{"border-transparent bg-primary text-primary-foreground hover:bg-primary/80":"default"===s,"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80":"secondary"===s,"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80":"destructive"===s,"text-foreground":"outline"===s,"border-transparent bg-blue-100 text-blue-800":"blue"===s,"border-transparent bg-green-100 text-green-800":"green"===s,"border-transparent bg-yellow-100 text-yellow-800":"yellow"===s,"border-transparent bg-red-100 text-red-800":"red"===s,"border-transparent bg-purple-100 text-purple-800":"purple"===s,"border-transparent bg-orange-100 text-orange-800":"orange"===s,"border-transparent bg-gray-100 text-gray-800":"gray"===s},e),...r})}},32127:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(26373).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},32359:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>o});var t=r(65239),a=r(48088),i=r(88170),n=r.n(i),l=r(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);r.d(s,d);let o={children:["",{children:["master",{children:["organizations",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,81994)),"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\master\\organizations\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,70090)),"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\master\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\master\\organizations\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/master/organizations/page",pathname:"/master/organizations",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},55511:e=>{"use strict";e.exports=require("crypto")},61348:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(26373).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68898:(e,s,r)=>{"use strict";r.d(s,{CreateOrganizationModal:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call CreateOrganizationModal() from the server but CreateOrganizationModal is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\specialized\\create-organization-modal.tsx","CreateOrganizationModal")},70090:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>d});var t=r(37413),a=r(26326),i=r(39916),n=r(59105),l=r(10590);async function d({children:e}){let s=await (0,a.j2)();return s?.user||(0,i.redirect)("/login"),s.user.masterAdmin||(0,i.redirect)("/admin"),(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)(l.Header,{user:s.user}),(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)(n.Sidebar,{userRole:s.user.role,isMasterAdmin:s.user.masterAdmin}),(0,t.jsx)("main",{className:"flex-1 p-6",children:e})]})]})}},74998:e=>{"use strict";e.exports=require("perf_hooks")},77598:e=>{"use strict";e.exports=require("node:crypto")},79202:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.bind(r,68898))},79551:e=>{"use strict";e.exports=require("url")},81994:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>p});var t=r(37413),a=r(26326),i=r(71682),n=r(32767),l=r(1463),d=r(94634),o=r(14189),c=r(23469),u=r(78593),m=r(68898),x=r(61348),g=r(78768);async function p(){let e=await (0,a.j2)();if(!e?.user?.masterAdmin)return(0,t.jsx)("div",{children:"Access denied"});let s=await i.db.select({id:n.organizations.id,name:n.organizations.name,slug:n.organizations.slug,status:n.organizations.status,billingPlan:n.organizations.billingPlan,features:n.organizations.features,settings:n.organizations.settings,createdAt:n.organizations.createdAt,updatedAt:n.organizations.updatedAt,userCount:(0,l.U9)(n.users.id)}).from(n.organizations).leftJoin(n.users,(0,d.eq)(n.organizations.id,n.users.organizationId)).groupBy(n.organizations.id).orderBy(n.organizations.createdAt);return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Organizations"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Manage test centers and their configurations"})]}),(0,t.jsx)(m.CreateOrganizationModal,{children:(0,t.jsxs)(c.$,{children:[(0,t.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Create Organization"]})})]}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,t.jsx)(u.p,{placeholder:"Search organizations...",className:"pl-10"})]})}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)("select",{className:"px-3 py-2 border border-gray-300 rounded-md text-sm",children:[(0,t.jsx)("option",{value:"",children:"All Status"}),(0,t.jsx)("option",{value:"active",children:"Active"}),(0,t.jsx)("option",{value:"suspended",children:"Suspended"}),(0,t.jsx)("option",{value:"disabled",children:"Disabled"})]}),(0,t.jsxs)("select",{className:"px-3 py-2 border border-gray-300 rounded-md text-sm",children:[(0,t.jsx)("option",{value:"",children:"All Plans"}),(0,t.jsx)("option",{value:"basic",children:"Basic"}),(0,t.jsx)("option",{value:"premium",children:"Premium"}),(0,t.jsx)("option",{value:"enterprise",children:"Enterprise"})]})]})]})}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:s.map(e=>(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(o.o,{organization:{id:e.id,name:e.name,slug:e.slug,status:e.status,billingPlan:e.billingPlan,features:e.features,settings:e.settings,createdAt:e.createdAt,updatedAt:e.updatedAt}}),(0,t.jsx)("div",{className:"absolute top-2 right-2",children:(0,t.jsxs)("span",{className:"bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full",children:[e.userCount," users"]})})]},e.id))}),0===s.length&&(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"text-gray-400 text-lg mb-2",children:"No organizations found"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Get started by creating your first organization"}),(0,t.jsx)(m.CreateOrganizationModal,{children:(0,t.jsxs)(c.$,{children:[(0,t.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Create First Organization"]})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Organization Statistics"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:s.filter(e=>"active"===e.status).length}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Active"})]}),(0,t.jsxs)("div",{className:"text-center p-4 bg-yellow-50 rounded-lg",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:s.filter(e=>"suspended"===e.status).length}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Suspended"})]}),(0,t.jsxs)("div",{className:"text-center p-4 bg-red-50 rounded-lg",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-red-600",children:s.filter(e=>"disabled"===e.status).length}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Disabled"})]}),(0,t.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:s.reduce((e,s)=>e+s.userCount,0)}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Total Users"})]})]})]})]})}},89667:(e,s,r)=>{"use strict";r.d(s,{p:()=>n});var t=r(60687),a=r(43210),i=r(7766);let n=a.forwardRef(({className:e,type:s,error:r,...a},n)=>(0,t.jsxs)("div",{className:"w-full",children:[(0,t.jsx)("input",{type:s,className:(0,i.cn)("flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:cursor-not-allowed disabled:opacity-50",r&&"border-red-500 focus:ring-red-500 focus:border-red-500",e),ref:n,...a}),r&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-500",children:r})]}));n.displayName="Input"},91645:e=>{"use strict";e.exports=require("net")},97354:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.bind(r,16032))}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4447,5552,2190,1057,145,1658,9069,5814,892,4017,367,6326,5807],()=>r(32359));module.exports=t})();