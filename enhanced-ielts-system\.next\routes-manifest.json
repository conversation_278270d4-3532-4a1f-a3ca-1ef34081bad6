{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/api/auth/[...next<PERSON>h]", "regex": "^/api/auth/(.+?)(?:/)?$", "routeKeys": {"nxtPnextauth": "nxtPnextauth"}, "namedRegex": "^/api/auth/(?<nxtPnextauth>.+?)(?:/)?$"}, {"page": "/api/certificates/download/[id]", "regex": "^/api/certificates/download/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/certificates/download/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/payments/manual/[id]/approve", "regex": "^/api/payments/manual/([^/]+?)/approve(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/payments/manual/(?<nxtPid>[^/]+?)/approve(?:/)?$"}, {"page": "/api/test-results/[id]", "regex": "^/api/test\\-results/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/test\\-results/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/verify/[serial]", "regex": "^/verify/([^/]+?)(?:/)?$", "routeKeys": {"nxtPserial": "nxtPserial"}, "namedRegex": "^/verify/(?<nxtPserial>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/admin/candidates", "regex": "^/admin/candidates(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/candidates(?:/)?$"}, {"page": "/admin/dashboard", "regex": "^/admin/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/dashboard(?:/)?$"}, {"page": "/admin/payments", "regex": "^/admin/payments(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/payments(?:/)?$"}, {"page": "/admin/results", "regex": "^/admin/results(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/results(?:/)?$"}, {"page": "/checker/dashboard", "regex": "^/checker/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/checker/dashboard(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/master/dashboard", "regex": "^/master/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/master/dashboard(?:/)?$"}, {"page": "/master/organizations", "regex": "^/master/organizations(?:/)?$", "routeKeys": {}, "namedRegex": "^/master/organizations(?:/)?$"}, {"page": "/search", "regex": "^/search(?:/)?$", "routeKeys": {}, "namedRegex": "^/search(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}