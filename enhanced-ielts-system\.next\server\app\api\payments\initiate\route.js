"use strict";(()=>{var e={};e.id=991,e.ids=[991],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{e.exports=require("os")},24006:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>j,routeModule:()=>y,serverHooks:()=>g,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>f});var n={};t.r(n),t.d(n,{GET:()=>m,POST:()=>l});var a=t(96559),s=t(48088),i=t(37719),o=t(32190),u=t(26326),p=t(45697),c=t(18758);let d=p.z.object({candidateId:p.z.string().min(1),featureType:p.z.enum(["feedback","certificate","progress"]),gateway:p.z.enum(["click","payme","manual"]),resultId:p.z.string().optional(),returnUrl:p.z.string().url()});async function l(e){try{let r=await (0,u.j2)();if(!r?.user?.organizationId)return o.NextResponse.json({error:"Unauthorized"},{status:401});let t=await e.json(),n=d.parse(t),a=(0,c.jc)(n.featureType),s=await (0,c.vi)({candidateId:n.candidateId,organizationId:r.user.organizationId,amount:a.amount,currency:a.currency,gateway:n.gateway,featureType:n.featureType,resultId:n.resultId,returnUrl:n.returnUrl,description:a.description});if("failed"===s.status)return o.NextResponse.json({error:s.error},{status:400});return o.NextResponse.json({transactionId:s.transactionId,paymentUrl:s.paymentUrl,status:s.status,amount:a.amount,currency:a.currency,description:a.description})}catch(e){if(console.error("Payment initiation error:",e),e instanceof p.z.ZodError)return o.NextResponse.json({error:"Invalid data",details:e.errors},{status:400});return o.NextResponse.json({error:"Payment initiation failed"},{status:500})}}async function m(e){try{let r=await (0,u.j2)();if(!r?.user?.organizationId)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),n=t.get("featureType");if(!n||!["feedback","certificate","progress"].includes(n))return o.NextResponse.json({error:"Invalid feature type"},{status:400});let a=(0,c.jc)(n);return o.NextResponse.json({featureType:n,amount:a.amount,currency:a.currency,description:a.description,availableGateways:[{id:"click",name:"Click",description:"Pay with Click - Fast and secure",processingTime:"Instant"},{id:"payme",name:"Payme",description:"Pay with Payme - Convenient mobile payments",processingTime:"Instant"},{id:"manual",name:"Manual Payment",description:"Bank transfer or cash payment with admin approval",processingTime:"1-3 business days"}]})}catch(e){return console.error("Payment info error:",e),o.NextResponse.json({error:"Failed to get payment information"},{status:500})}}let y=new a.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/payments/initiate/route",pathname:"/api/payments/initiate",filename:"route",bundlePath:"app/api/payments/initiate/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\payments\\initiate\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:x,workUnitAsyncStorage:f,serverHooks:g}=y;function j(){return(0,i.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:f})}},27910:e=>{e.exports=require("stream")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74998:e=>{e.exports=require("perf_hooks")},77598:e=>{e.exports=require("node:crypto")},91645:e=>{e.exports=require("net")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[4447,5552,2190,1057,1595,6326,7346],()=>t(24006));module.exports=n})();