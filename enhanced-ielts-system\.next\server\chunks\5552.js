exports.id=5552,exports.ids=[5552],exports.modules={316:(e,t,i)=>{"use strict";i.d(t,{w:()=>s});var r=i(5730),n=i(96657);class s extends n.Ss{static [r.i]="PgViewBase"}},3884:(e,t,i)=>{"use strict";i.d(t,{DZ:()=>w,I$:()=>function e(t,i,r,a,l=e=>e){let o={};for(let[c,h]of a.entries())if(h.isJson){let n=i.relations[h.tsKey],a=r[c],u="string"==typeof a?JSON.parse(a):a;o[h.tsKey]=(0,s.is)(n,f)?u&&e(t,t[h.relationTableTsKey],u,h.selection,l):u.map(i=>e(t,t[h.relationTableTsKey],i,h.selection,l))}else{let e,t=l(r[c]),i=h.field;e=(0,s.is)(i,n.V)?i:(0,s.is)(i,u.Xs)?i.decoder:i.sql.decoder,o[h.tsKey]=null===t?null:e.mapFromDriverValue(t)}return o},K1:()=>y,W0:()=>b,_k:()=>m,iv:()=>d,mm:()=>p,pD:()=>f,rl:()=>g});var r=i(24717),n=i(10007),s=i(5730),a=i(58152),l=i(94634),o=i(16048),u=i(96657);class c{constructor(e,t,i){this.sourceTable=e,this.referencedTable=t,this.relationName=i,this.referencedTableName=t[r.XI.Symbol.Name]}static [s.i]="Relation";referencedTableName;fieldName}class h{constructor(e,t){this.table=e,this.config=t}static [s.i]="Relations"}class f extends c{constructor(e,t,i,r){super(e,t,i?.relationName),this.config=i,this.isNullable=r}static [s.i]="One";withFieldName(e){let t=new f(this.sourceTable,this.referencedTable,this.config,this.isNullable);return t.fieldName=e,t}}class d extends c{constructor(e,t,i){super(e,t,i?.relationName),this.config=i}static [s.i]="Many";withFieldName(e){let t=new d(this.sourceTable,this.referencedTable,this.config);return t.fieldName=e,t}}function p(){return{and:l.Uo,between:l.Tq,eq:l.eq,exists:l.t2,gt:l.gt,gte:l.RO,ilike:l.B3,inArray:l.RV,isNull:l.kZ,isNotNull:l.Pe,like:l.mj,lt:l.lt,lte:l.wJ,ne:l.ne,not:l.AU,notBetween:l.o8,notExists:l.KJ,notLike:l.RK,notIlike:l.q1,notInArray:l.KL,or:l.or,sql:u.ll}}function g(){return{sql:u.ll,asc:o.Y,desc:o.i}}function m(e,t){1===Object.keys(e).length&&"default"in e&&!(0,s.is)(e.default,r.XI)&&(e=e.default);let i={},n={},l={};for(let[o,u]of Object.entries(e))if((0,s.is)(u,r.XI)){let e=(0,r.Lf)(u),t=n[e];for(let n of(i[e]=o,l[o]={tsName:o,dbName:u[r.XI.Symbol.Name],schema:u[r.XI.Symbol.Schema],columns:u[r.XI.Symbol.Columns],relations:t?.relations??{},primaryKey:t?.primaryKey??[]},Object.values(u[r.XI.Symbol.Columns])))n.primary&&l[o].primaryKey.push(n);let c=u[r.XI.Symbol.ExtraConfigBuilder]?.(u[r.XI.Symbol.ExtraConfigColumns]);if(c)for(let e of Object.values(c))(0,s.is)(e,a.hv)&&l[o].primaryKey.push(...e.columns)}else if((0,s.is)(u,h)){let e,s=(0,r.Lf)(u.table),a=i[s];for(let[i,r]of Object.entries(u.config(t(u.table))))if(a){let t=l[a];t.relations[i]=r,e&&t.primaryKey.push(...e)}else s in n||(n[s]={relations:{},primaryKey:e}),n[s].relations[i]=r}return{tables:l,tableNamesMap:i}}function y(e,t){return new h(e,e=>Object.fromEntries(Object.entries(t(e)).map(([e,t])=>[e,t.withFieldName(e)])))}function b(e,t,i){if((0,s.is)(i,f)&&i.config)return{fields:i.config.fields,references:i.config.references};let n=t[(0,r.Lf)(i.referencedTable)];if(!n)throw Error(`Table "${i.referencedTable[r.XI.Symbol.Name]}" not found in schema`);let a=e[n];if(!a)throw Error(`Table "${n}" not found in schema`);let l=i.sourceTable,o=t[(0,r.Lf)(l)];if(!o)throw Error(`Table "${l[r.XI.Symbol.Name]}" not found in schema`);let u=[];for(let e of Object.values(a.relations))(i.relationName&&i!==e&&e.relationName===i.relationName||!i.relationName&&e.referencedTable===i.sourceTable)&&u.push(e);if(u.length>1)throw i.relationName?Error(`There are multiple relations with name "${i.relationName}" in table "${n}"`):Error(`There are multiple relations between "${n}" and "${i.sourceTable[r.XI.Symbol.Name]}". Please specify relation name`);if(u[0]&&(0,s.is)(u[0],f)&&u[0].config)return{fields:u[0].config.references,references:u[0].config.fields};throw Error(`There is not enough information to infer relation "${o}.${i.fieldName}"`)}function w(e){return{one:function(t,i){return new f(e,t,i,i?.fields.reduce((e,t)=>e&&t.notNull,!0)??!1)},many:function(t,i){return new d(e,t,i)}}}},4147:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.toBig=t.shrSL=t.shrSH=t.rotrSL=t.rotrSH=t.rotrBL=t.rotrBH=t.rotr32L=t.rotr32H=t.rotlSL=t.rotlSH=t.rotlBL=t.rotlBH=t.add5L=t.add5H=t.add4L=t.add4H=t.add3L=t.add3H=void 0,t.add=w,t.fromBig=n,t.split=s;let i=BigInt(0x100000000-1),r=BigInt(32);function n(e,t=!1){return t?{h:Number(e&i),l:Number(e>>r&i)}:{h:0|Number(e>>r&i),l:0|Number(e&i)}}function s(e,t=!1){let i=e.length,r=new Uint32Array(i),a=new Uint32Array(i);for(let s=0;s<i;s++){let{h:i,l}=n(e[s],t);[r[s],a[s]]=[i,l]}return[r,a]}let a=(e,t)=>BigInt(e>>>0)<<r|BigInt(t>>>0);t.toBig=a;let l=(e,t,i)=>e>>>i;t.shrSH=l;let o=(e,t,i)=>e<<32-i|t>>>i;t.shrSL=o;let u=(e,t,i)=>e>>>i|t<<32-i;t.rotrSH=u;let c=(e,t,i)=>e<<32-i|t>>>i;t.rotrSL=c;let h=(e,t,i)=>e<<64-i|t>>>i-32;t.rotrBH=h;let f=(e,t,i)=>e>>>i-32|t<<64-i;t.rotrBL=f;let d=(e,t)=>t;t.rotr32H=d;let p=(e,t)=>e;t.rotr32L=p;let g=(e,t,i)=>e<<i|t>>>32-i;t.rotlSH=g;let m=(e,t,i)=>t<<i|e>>>32-i;t.rotlSL=m;let y=(e,t,i)=>t<<i-32|e>>>64-i;t.rotlBH=y;let b=(e,t,i)=>e<<i-32|t>>>64-i;function w(e,t,i,r){let n=(t>>>0)+(r>>>0);return{h:e+i+(n/0x100000000|0)|0,l:0|n}}t.rotlBL=b;let v=(e,t,i)=>(e>>>0)+(t>>>0)+(i>>>0);t.add3L=v;let S=(e,t,i,r)=>t+i+r+(e/0x100000000|0)|0;t.add3H=S;let x=(e,t,i,r)=>(e>>>0)+(t>>>0)+(i>>>0)+(r>>>0);t.add4L=x;let T=(e,t,i,r,n)=>t+i+r+n+(e/0x100000000|0)|0;t.add4H=T;let N=(e,t,i,r,n)=>(e>>>0)+(t>>>0)+(i>>>0)+(r>>>0)+(n>>>0);t.add5L=N;let P=(e,t,i,r,n,s)=>t+i+r+n+s+(e/0x100000000|0)|0;t.add5H=P,t.default={fromBig:n,split:s,toBig:a,shrSH:l,shrSL:o,rotrSH:u,rotrSL:c,rotrBH:h,rotrBL:f,rotr32H:d,rotr32L:p,rotlSH:g,rotlSL:m,rotlBH:y,rotlBL:b,add:w,add3L:v,add3H:S,add4L:x,add4H:T,add5H:P,add5L:N}},5730:(e,t,i)=>{"use strict";i.d(t,{i:()=>r,is:()=>n});let r=Symbol.for("drizzle:entityKind");function n(e,t){if(!e||"object"!=typeof e)return!1;if(e instanceof t)return!0;if(!Object.prototype.hasOwnProperty.call(t,r))throw Error(`Class "${t.name??"<unknown>"}" doesn't look like a Drizzle entity. If this is incorrect and the class is provided by Drizzle, please report this as a bug.`);let i=Object.getPrototypeOf(e).constructor;if(i)for(;i;){if(r in i&&i[r]===t[r])return!0;i=Object.getPrototypeOf(i)}return!1}Symbol.for("drizzle:hasOwnEntityKind")},9253:(e,t,i)=>{"use strict";i.d(t,{KM:()=>o,vE:()=>h,xQ:()=>c});var r=i(5730),n=i(79608),s=i(45944),a=i(65734);class l extends a.u{static [r.i]="PgTimestampBuilder";constructor(e,t,i){super(e,"date","PgTimestamp"),this.config.withTimezone=t,this.config.precision=i}build(e){return new o(e,this.config)}}class o extends s.Kl{static [r.i]="PgTimestamp";withTimezone;precision;constructor(e,t){super(e,t),this.withTimezone=t.withTimezone,this.precision=t.precision}getSQLType(){let e=void 0===this.precision?"":` (${this.precision})`;return`timestamp${e}${this.withTimezone?" with time zone":""}`}mapFromDriverValue=e=>new Date(this.withTimezone?e:e+"+0000");mapToDriverValue=e=>e.toISOString()}class u extends a.u{static [r.i]="PgTimestampStringBuilder";constructor(e,t,i){super(e,"string","PgTimestampString"),this.config.withTimezone=t,this.config.precision=i}build(e){return new c(e,this.config)}}class c extends s.Kl{static [r.i]="PgTimestampString";withTimezone;precision;constructor(e,t){super(e,t),this.withTimezone=t.withTimezone,this.precision=t.precision}getSQLType(){let e=void 0===this.precision?"":`(${this.precision})`;return`timestamp${e}${this.withTimezone?" with time zone":""}`}}function h(e,t={}){let{name:i,config:r}=(0,n.Ll)(e,t);return r?.mode==="string"?new u(i,r.withTimezone??!1,r.precision):new l(i,r?.withTimezone??!1,r?.precision)}},9528:(e,t,i)=>{"use strict";i.d(t,{Xd:()=>o,kB:()=>u});var r=i(5730),n=i(79608),s=i(45944),a=i(65734);class l extends a.u{constructor(e,t,i){super(e,"string","PgTime"),this.withTimezone=t,this.precision=i,this.config.withTimezone=t,this.config.precision=i}static [r.i]="PgTimeBuilder";build(e){return new o(e,this.config)}}class o extends s.Kl{static [r.i]="PgTime";withTimezone;precision;constructor(e,t){super(e,t),this.withTimezone=t.withTimezone,this.precision=t.precision}getSQLType(){let e=void 0===this.precision?"":`(${this.precision})`;return`time${e}${this.withTimezone?" with time zone":""}`}}function u(e,t={}){let{name:i,config:r}=(0,n.Ll)(e,t);return new l(i,r.withTimezone??!1,r.precision)}},9594:(e,t,i)=>{"use strict";i.d(t,{dL:()=>l,uR:()=>o});var r=i(5730),n=i(96657),s=i(45944);class a extends s.pe{static [r.i]="PgUUIDBuilder";constructor(e){super(e,"string","PgUUID")}defaultRandom(){return this.default((0,n.ll)`gen_random_uuid()`)}build(e){return new l(e,this.config)}}class l extends s.Kl{static [r.i]="PgUUID";getSQLType(){return"uuid"}}function o(e){return new a(e??"")}},10007:(e,t,i)=>{"use strict";i.d(t,{V:()=>n});var r=i(5730);class n{constructor(e,t){this.table=e,this.config=t,this.name=t.name,this.keyAsName=t.keyAsName,this.notNull=t.notNull,this.default=t.default,this.defaultFn=t.defaultFn,this.onUpdateFn=t.onUpdateFn,this.hasDefault=t.hasDefault,this.primary=t.primaryKey,this.isUnique=t.isUnique,this.uniqueName=t.uniqueName,this.uniqueType=t.uniqueType,this.dataType=t.dataType,this.columnType=t.columnType,this.generated=t.generated,this.generatedIdentity=t.generatedIdentity}static [r.i]="Column";name;keyAsName;primary;notNull;default;defaultFn;onUpdateFn;hasDefault;isUnique;uniqueName;uniqueType;dataType;columnType;enumValues=void 0;generated=void 0;generatedIdentity=void 0;config;mapFromDriverValue(e){return e}mapToDriverValue(e){return e}shouldDisableInsert(){return void 0!==this.config.generated&&"byDefault"!==this.config.generated.type}}},12772:(e,t,i)=>{"use strict";i.d(t,{n:()=>r});let r=Symbol.for("drizzle:ViewBaseConfig")},16048:(e,t,i)=>{"use strict";i.d(t,{Y:()=>n,i:()=>s});var r=i(96657);function n(e){return(0,r.ll)`${e} asc`}function s(e){return(0,r.ll)`${e} desc`}},24717:(e,t,i)=>{"use strict";i.d(t,{HE:()=>c,Io:()=>p,Lf:()=>g,Sj:()=>s,XI:()=>d,e:()=>a});var r=i(5730),n=i(86214);let s=Symbol.for("drizzle:Schema"),a=Symbol.for("drizzle:Columns"),l=Symbol.for("drizzle:ExtraConfigColumns"),o=Symbol.for("drizzle:OriginalName"),u=Symbol.for("drizzle:BaseName"),c=Symbol.for("drizzle:IsAlias"),h=Symbol.for("drizzle:ExtraConfigBuilder"),f=Symbol.for("drizzle:IsDrizzleTable");class d{static [r.i]="Table";static Symbol={Name:n.E,Schema:s,OriginalName:o,Columns:a,ExtraConfigColumns:l,BaseName:u,IsAlias:c,ExtraConfigBuilder:h};[n.E];[o];[s];[a];[l];[u];[c]=!1;[f]=!0;[h]=void 0;constructor(e,t,i){this[n.E]=this[o]=e,this[s]=t,this[u]=i}}function p(e){return e[n.E]}function g(e){return`${e[s]??"public"}.${e[n.E]}`}},29334:(e,t,i)=>{"use strict";i.d(t,{Qq:()=>o});var r=i(5730),n=i(79608),s=i(45944);class a extends s.pe{static [r.i]="PgTextBuilder";constructor(e,t){super(e,"string","PgText"),this.config.enumValues=t.enum}build(e){return new l(e,this.config)}}class l extends s.Kl{static [r.i]="PgText";enumValues=this.config.enumValues;getSQLType(){return"text"}}function o(e,t={}){let{name:i,config:r}=(0,n.Ll)(e,t);return new a(i,r)}},30686:(e,t,i)=>{"use strict";i.d(t,{f:()=>C});var r=i(43971),n=i(5730);class s{static [n.i]="ConsoleLogWriter";write(e){console.log(e)}}class a{static [n.i]="DefaultLogger";writer;constructor(e){this.writer=e?.writer??new s}logQuery(e,t){let i=t.map(e=>{try{return JSON.stringify(e)}catch{return String(e)}}),r=i.length?` -- params: [${i.join(", ")}]`:"";this.writer.write(`Query: ${e}${r}`)}}class l{static [n.i]="NoopLogger";logQuery(){}}var o=i(58779),u=i(50351),c=i(3884),h=i(79608);class f{static [n.i]="Cache"}class d extends f{strategy(){return"all"}static [n.i]="NoopCache";async get(e){}async put(e,t,i,r){}async onMutate(e){}}async function p(e,t){let i=`${e}-${JSON.stringify(t)}`,r=new TextEncoder().encode(i);return[...new Uint8Array(await crypto.subtle.digest("SHA-256",r))].map(e=>e.toString(16).padStart(2,"0")).join("")}var g=i(40774);class m extends Error{constructor(e,t,i){super(`Failed query: ${e}
params: ${t}`),this.query=e,this.params=t,this.cause=i,Error.captureStackTrace(this,m),i&&(this.cause=i)}}var y=i(96657),b=i(99511);class w{constructor(e,t,i,r){this.query=e,this.cache=t,this.queryMetadata=i,this.cacheConfig=r,t&&"all"===t.strategy()&&void 0===r&&(this.cacheConfig={enable:!0,autoInvalidate:!0}),this.cacheConfig?.enable||(this.cacheConfig=void 0)}authToken;getQuery(){return this.query}mapResult(e,t){return e}setToken(e){return this.authToken=e,this}static [n.i]="PgPreparedQuery";joinsNotNullableMap;async queryWithCache(e,t,i){if(void 0===this.cache||(0,n.is)(this.cache,d)||void 0===this.queryMetadata||this.cacheConfig&&!this.cacheConfig.enable)try{return await i()}catch(i){throw new m(e,t,i)}if(("insert"===this.queryMetadata.type||"update"===this.queryMetadata.type||"delete"===this.queryMetadata.type)&&this.queryMetadata.tables.length>0)try{let[e]=await Promise.all([i(),this.cache.onMutate({tables:this.queryMetadata.tables})]);return e}catch(i){throw new m(e,t,i)}if(!this.cacheConfig)try{return await i()}catch(i){throw new m(e,t,i)}if("select"===this.queryMetadata.type){let r=await this.cache.get(this.cacheConfig.tag??await p(e,t),this.queryMetadata.tables,void 0!==this.cacheConfig.tag,this.cacheConfig.autoInvalidate);if(void 0===r){let r;try{r=await i()}catch(i){throw new m(e,t,i)}return await this.cache.put(this.cacheConfig.tag??await p(e,t),r,this.cacheConfig.autoInvalidate?this.queryMetadata.tables:[],void 0!==this.cacheConfig.tag,this.cacheConfig.config),r}return r}try{return await i()}catch(i){throw new m(e,t,i)}}}class v{constructor(e){this.dialect=e}static [n.i]="PgSession";execute(e,t){return b.k.startActiveSpan("drizzle.operation",()=>b.k.startActiveSpan("drizzle.prepareQuery",()=>this.prepareQuery(this.dialect.sqlToQuery(e),void 0,void 0,!1)).setToken(t).execute(void 0,t))}all(e){return this.prepareQuery(this.dialect.sqlToQuery(e),void 0,void 0,!1).all()}async count(e,t){return Number((await this.execute(e,t))[0].count)}}class S extends o.p{constructor(e,t,i,r=0){super(e,t,i),this.schema=i,this.nestedIndex=r}static [n.i]="PgTransaction";rollback(){throw new g.j}getTransactionConfigSQL(e){let t=[];return e.isolationLevel&&t.push(`isolation level ${e.isolationLevel}`),e.accessMode&&t.push(e.accessMode),"boolean"==typeof e.deferrable&&t.push(e.deferrable?"deferrable":"not deferrable"),y.ll.raw(t.join(" "))}setTransaction(e){return this.session.execute((0,y.ll)`set transaction ${this.getTransactionConfigSQL(e)}`)}}class x extends w{constructor(e,t,i,r,n,s,a,l,o,u){super({sql:t,params:i},n,s,a),this.client=e,this.queryString=t,this.params=i,this.logger=r,this.fields=l,this._isResponseInArrayMode=o,this.customResultMapper=u}static [n.i]="PostgresJsPreparedQuery";async execute(e={}){return b.k.startActiveSpan("drizzle.execute",async t=>{let i=(0,y.Ct)(this.params,e);t?.setAttributes({"drizzle.query.text":this.queryString,"drizzle.query.params":JSON.stringify(i)}),this.logger.logQuery(this.queryString,i);let{fields:r,queryString:n,client:s,joinsNotNullableMap:a,customResultMapper:l}=this;if(!r&&!l)return b.k.startActiveSpan("drizzle.driver.execute",()=>this.queryWithCache(n,i,async()=>await s.unsafe(n,i)));let o=await b.k.startActiveSpan("drizzle.driver.execute",()=>(t?.setAttributes({"drizzle.query.text":n,"drizzle.query.params":JSON.stringify(i)}),this.queryWithCache(n,i,async()=>await s.unsafe(n,i).values())));return b.k.startActiveSpan("drizzle.mapResponse",()=>l?l(o):o.map(e=>(0,h.a6)(r,e,a)))})}all(e={}){return b.k.startActiveSpan("drizzle.execute",async t=>{let i=(0,y.Ct)(this.params,e);return t?.setAttributes({"drizzle.query.text":this.queryString,"drizzle.query.params":JSON.stringify(i)}),this.logger.logQuery(this.queryString,i),b.k.startActiveSpan("drizzle.driver.execute",()=>(t?.setAttributes({"drizzle.query.text":this.queryString,"drizzle.query.params":JSON.stringify(i)}),this.queryWithCache(this.queryString,i,async()=>this.client.unsafe(this.queryString,i))))})}isResponseInArrayMode(){return this._isResponseInArrayMode}}class T extends v{constructor(e,t,i,r={}){super(t),this.client=e,this.schema=i,this.options=r,this.logger=r.logger??new l,this.cache=r.cache??new d}static [n.i]="PostgresJsSession";logger;cache;prepareQuery(e,t,i,r,n,s,a){return new x(this.client,e.sql,e.params,this.logger,this.cache,s,a,t,r,n)}query(e,t){return this.logger.logQuery(e,t),this.client.unsafe(e,t).values()}queryObjects(e,t){return this.client.unsafe(e,t)}transaction(e,t){return this.client.begin(async i=>{let r=new T(i,this.dialect,this.schema,this.options),n=new N(this.dialect,r,this.schema);return t&&await n.setTransaction(t),e(n)})}}class N extends S{constructor(e,t,i,r=0){super(e,t,i,r),this.session=t}static [n.i]="PostgresJsTransaction";transaction(e){return this.session.client.savepoint(t=>{let i=new T(t,this.dialect,this.schema,this.session.options);return e(new N(this.dialect,i,this.schema))})}}class P extends o.p{static [n.i]="PostgresJsDatabase"}function $(e,t={}){let i,r,n=e=>e;for(let t of["1184","1082","1083","1114","1182","1185","1115","1231"])e.options.parsers[t]=n,e.options.serializers[t]=n;e.options.serializers["114"]=n,e.options.serializers["3802"]=n;let s=new u.s({casing:t.casing});if(!0===t.logger?i=new a:!1!==t.logger&&(i=t.logger),t.schema){let e=(0,c._k)(t.schema,c.DZ);r={fullSchema:t.schema,schema:e.tables,tableNamesMap:e.tableNamesMap}}let l=new T(e,s,r,{logger:i,cache:t.cache}),o=new P(s,l,r);return o.$client=e,o.$cache=t.cache,o.$cache&&(o.$cache.invalidate=t.cache?.onMutate),o}function C(...e){if("string"==typeof e[0])return $((0,r.A)(e[0]),e[1]);if((0,h.Lq)(e[0])){let{connection:t,client:i,...n}=e[0];if(i)return $(i,n);if("object"==typeof t&&void 0!==t.url){let{url:e,...i}=t;return $((0,r.A)(e,i),n)}return $((0,r.A)(t),n)}return $(e[0],e[1])}(C||(C={})).mock=function(e){return $({options:{parsers:{},serializers:{}}},e)}},34359:(e,t,i)=>{"use strict";i.d(t,{Pq:()=>l,iX:()=>a});var r=i(5730),n=i(45944);class s extends n.pe{static [r.i]="PgJsonBuilder";constructor(e){super(e,"json","PgJson")}build(e){return new a(e,this.config)}}class a extends n.Kl{static [r.i]="PgJson";constructor(e,t){super(e,t)}getSQLType(){return"json"}mapToDriverValue(e){return JSON.stringify(e)}mapFromDriverValue(e){if("string"==typeof e)try{return JSON.parse(e)}catch{}return e}}function l(e){return new s(e??"")}},34509:(e,t,i)=>{"use strict";i.d(t,{Fx:()=>l,kn:()=>a});var r=i(5730),n=i(45944);class s extends n.pe{static [r.i]="PgJsonbBuilder";constructor(e){super(e,"json","PgJsonb")}build(e){return new a(e,this.config)}}class a extends n.Kl{static [r.i]="PgJsonb";constructor(e,t){super(e,t)}getSQLType(){return"jsonb"}mapToDriverValue(e){return JSON.stringify(e)}mapFromDriverValue(e){if("string"==typeof e)try{return JSON.parse(e)}catch{}return e}}function l(e){return new s(e??"")}},37193:(e,t,i)=>{"use strict";i.d(t,{dw:()=>c,p6:()=>h,qw:()=>o});var r=i(5730),n=i(79608),s=i(45944),a=i(65734);class l extends a.u{static [r.i]="PgDateBuilder";constructor(e){super(e,"date","PgDate")}build(e){return new o(e,this.config)}}class o extends s.Kl{static [r.i]="PgDate";getSQLType(){return"date"}mapFromDriverValue(e){return new Date(e)}mapToDriverValue(e){return e.toISOString()}}class u extends a.u{static [r.i]="PgDateStringBuilder";constructor(e){super(e,"string","PgDateString")}build(e){return new c(e,this.config)}}class c extends s.Kl{static [r.i]="PgDateString";getSQLType(){return"date"}}function h(e,t){let{name:i,config:r}=(0,n.Ll)(e,t);return r?.mode==="date"?new l(i):new u(i)}},38949:(e,t,i)=>{"use strict";i.d(t,{Q:()=>n});var r=i(5730);class n{static [r.i]="ColumnBuilder";config;constructor(e,t,i){this.config={name:e,keyAsName:""===e,notNull:!1,default:void 0,hasDefault:!1,primaryKey:!1,isUnique:!1,uniqueName:void 0,uniqueType:void 0,dataType:t,columnType:i,generated:void 0}}$type(){return this}notNull(){return this.config.notNull=!0,this}default(e){return this.config.default=e,this.config.hasDefault=!0,this}$defaultFn(e){return this.config.defaultFn=e,this.config.hasDefault=!0,this}$default=this.$defaultFn;$onUpdateFn(e){return this.config.onUpdateFn=e,this.config.hasDefault=!0,this}$onUpdate=this.$onUpdateFn;primaryKey(){return this.config.primaryKey=!0,this.config.notNull=!0,this}setName(e){""===this.config.name&&(this.config.name=e)}}},40774:(e,t,i)=>{"use strict";i.d(t,{j:()=>s,n:()=>n});var r=i(5730);class n extends Error{static [r.i]="DrizzleError";constructor({message:e,cause:t}){super(e),this.name="DrizzleError",this.cause=t}}class s extends n{static [r.i]="TransactionRollbackError";constructor(){super({message:"Rollback"})}}},40888:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.crypto=void 0;let r=i(77598);t.crypto=r&&"object"==typeof r&&"webcrypto"in r?r.webcrypto:r&&"object"==typeof r&&"randomBytes"in r?r:void 0},41879:(e,t,i)=>{"use strict";i.d(t,{O:()=>n});var r=i(5730);class n{static [r.i]="TypedQueryBuilder";getSelectedFields(){return this._.selectedFields}}},43971:(e,t,i)=>{"use strict";i.d(t,{A:()=>ev});var r=i(21820),n=i(29021);let s=new Map,a=new Map,l=Symbol("OriginError"),o={};class u extends Promise{constructor(e,t,i,r,n={}){let a,o;super((e,t)=>{a=e,o=t}),this.tagged=Array.isArray(e.raw),this.strings=e,this.args=t,this.handler=i,this.canceller=r,this.options=n,this.state=null,this.statement=null,this.resolve=e=>(this.active=!1,a(e)),this.reject=e=>(this.active=!1,o(e)),this.active=!1,this.cancelled=null,this.executed=!1,this.signature="",this[l]=this.handler.debug?Error():this.tagged&&function(e){if(s.has(e))return s.get(e);let t=Error.stackTraceLimit;return Error.stackTraceLimit=4,s.set(e,Error()),Error.stackTraceLimit=t,s.get(e)}(this.strings)}get origin(){return(this.handler.debug?this[l].stack:this.tagged&&a.has(this.strings)?a.get(this.strings):a.set(this.strings,this[l].stack).get(this.strings))||""}static get[Symbol.species](){return Promise}cancel(){return this.canceller&&(this.canceller(this),this.canceller=null)}simple(){return this.options.simple=!0,this.options.prepare=!1,this}async readable(){return this.simple(),this.streaming=!0,this}async writable(){return this.simple(),this.streaming=!0,this}cursor(e=1,t){let i;return(this.options.simple=!1,"function"==typeof e&&(t=e,e=1),this.cursorRows=e,"function"==typeof t)?(this.cursorFn=t,this):{[Symbol.asyncIterator]:()=>({next:()=>{if(this.executed&&!this.active)return{done:!0};i&&i();let e=new Promise((e,t)=>{this.cursorFn=t=>(e({value:t,done:!1}),new Promise(e=>i=e)),this.resolve=()=>(this.active=!1,e({done:!0})),this.reject=e=>(this.active=!1,t(e))});return this.execute(),e},return:()=>(i&&i(o),{done:!0})})}}describe(){return this.options.simple=!1,this.onlyDescribe=this.options.prepare=!0,this}stream(){throw Error(".stream has been renamed to .forEach")}forEach(e){return this.forEachFn=e,this.handle(),this}raw(){return this.isRaw=!0,this}values(){return this.isRaw="values",this}async handle(){!this.executed&&(this.executed=!0)&&await 1&&this.handler(this)}execute(){return this.handle(),this}then(){return this.handle(),super.then.apply(this,arguments)}catch(){return this.handle(),super.catch.apply(this,arguments)}finally(){return this.handle(),super.finally.apply(this,arguments)}}class c extends Error{constructor(e){super(e.message),this.name=this.constructor.name,Object.assign(this,e)}}let h={connection:function e(t,i,r){let{host:n,port:s}=r||i,a=Object.assign(Error("write "+t+" "+(i.path||n+":"+s)),{code:t,errno:t,address:i.path||n},i.path?{}:{port:s});return Error.captureStackTrace(a,e),a},postgres:function e(t){let i=new c(t);return Error.captureStackTrace(i,e),i},generic:function e(t,i){let r=Object.assign(Error(t+": "+i),{code:t});return Error.captureStackTrace(r,e),r},notSupported:function e(t){let i=Object.assign(Error(t+" (B) is not supported"),{code:"MESSAGE_NOT_SUPPORTED",name:t});return Error.captureStackTrace(i,e),i}};class f{then(){P()}catch(){P()}finally(){P()}}class d extends f{constructor(e){super(),this.value=B(e)}}class p extends f{constructor(e,t,i){super(),this.value=e,this.type=t,this.array=i}}class g extends f{constructor(e,t){super(),this.first=e,this.rest=t}build(e,t,i,r){let n=N.map(([t,i])=>({fn:i,i:e.search(t)})).sort((e,t)=>e.i-t.i).pop();return -1===n.i?O(this.first,r):n.fn(this.first,this.rest,t,i,r)}}function m(e,t,i,r){let n=e instanceof p?e.value:e;if(void 0===n&&(e instanceof p?e.value=r.transform.undefined:n=e=r.transform.undefined,void 0===n))throw h.generic("UNDEFINED_VALUE","Undefined values are not allowed");return"$"+i.push(e instanceof p?(t.push(e.value),e.array?e.array[e.type||k(e.value)]||e.type||function e(t){return Array.isArray(t)?e(t[0]):1009*("string"==typeof t)}(e.value):e.type):(t.push(e),k(e)))}let y=I({string:{to:25,from:null,serialize:e=>""+e},number:{to:0,from:[21,23,26,700,701],serialize:e=>""+e,parse:e=>+e},json:{to:114,from:[114,3802],serialize:e=>JSON.stringify(e),parse:e=>JSON.parse(e)},boolean:{to:16,from:16,serialize:e=>!0===e?"t":"f",parse:e=>"t"===e},date:{to:1184,from:[1082,1114,1184],serialize:e=>(e instanceof Date?e:new Date(e)).toISOString(),parse:e=>new Date(e)},bytea:{to:17,from:17,serialize:e=>"\\x"+Buffer.from(e).toString("hex"),parse:e=>Buffer.from(e.slice(2),"hex")}});function b(e,t,i,r,n,s){for(let a=1;a<e.strings.length;a++)t+=w(t,i,r,n,s)+e.strings[a],i=e.args[a];return t}function w(e,t,i,r,n){return t instanceof g?t.build(e,i,r,n):t instanceof u?v(t,i,r,n):t instanceof d?t.value:t&&t[0]instanceof u?t.reduce((e,t)=>e+" "+v(t,i,r,n),""):m(t,i,r,n)}function v(e,t,i,r){return e.fragment=!0,b(e,e.strings[0],e.args[0],t,i,r)}function S(e,t,i,r,n){return e.map(e=>"("+r.map(r=>w("values",e[r],t,i,n)).join(",")+")").join(",")}function x(e,t,i,r,n){let s=Array.isArray(e[0]),a=t.length?t.flat():Object.keys(s?e[0]:e);return S(s?e:[e],i,r,a,n)}function T(e,t,i,r,n){let s;return("string"==typeof e&&(e=[e].concat(t)),Array.isArray(e))?O(e,n):(t.length?t.flat():Object.keys(e)).map(t=>((s=e[t])instanceof u?v(s,i,r,n):s instanceof d?s.value:m(s,i,r,n))+" as "+B(n.transform.column.to?n.transform.column.to(t):t)).join(",")}let N=Object.entries({values:x,in:(...e)=>{let t=x(...e);return"()"===t?"(null)":t},select:T,as:T,returning:T,"\\(":T,update:(e,t,i,r,n)=>(t.length?t.flat():Object.keys(e)).map(t=>B(n.transform.column.to?n.transform.column.to(t):t)+"="+w("values",e[t],i,r,n)),insert(e,t,i,r,n){let s=t.length?t.flat():Object.keys(Array.isArray(e)?e[0]:e);return"("+O(s,n)+")values"+S(Array.isArray(e)?e:[e],i,r,s,n)}}).map(([e,t])=>[RegExp("((?:^|[\\s(])"+e+"(?:$|[\\s(]))(?![\\s\\S]*\\1)","i"),t]);function P(){throw h.generic("NOT_TAGGED_CALL","Query not called as a tagged template literal")}let $=y.serializers,C=y.parsers,A=function(e){let t=I(e||{});return{serializers:Object.assign({},$,t.serializers),parsers:Object.assign({},C,t.parsers)}};function I(e){return Object.keys(e).reduce((t,i)=>(e[i].from&&[].concat(e[i].from).forEach(r=>t.parsers[r]=e[i].parse),e[i].serialize&&(t.serializers[e[i].to]=e[i].serialize,e[i].from&&[].concat(e[i].from).forEach(r=>t.serializers[r]=e[i].serialize)),t),{parsers:{},serializers:{}})}function O(e,{transform:{column:t}}){return e.map(e=>B(t.to?t.to(e):e)).join(",")}let B=function(e){return'"'+e.replace(/"/g,'""').replace(/\./g,'"."')+'"'},k=function e(t){return t instanceof p?t.type:t instanceof Date?1184:t instanceof Uint8Array?17:!0===t||!1===t?16:"bigint"==typeof t?20:Array.isArray(t)?e(t[0]):0},_=/\\/g,L=/"/g,E=function e(t,i,r,n){if(!1===Array.isArray(t))return t;if(!t.length)return"{}";let s=t[0],a=1020===n?";":",";return Array.isArray(s)&&!s.type?"{"+t.map(t=>e(t,i,r,n)).join(a)+"}":"{"+t.map(e=>{if(void 0===e&&void 0===(e=r.transform.undefined))throw h.generic("UNDEFINED_VALUE","Undefined values are not allowed");return null===e?"null":'"'+(i?i(e.type?e.value:e):""+e).replace(_,"\\\\").replace(L,'\\"')+'"'}).join(a)+"}"},q={i:0,char:null,str:"",quoted:!1,last:0},j=e=>{let t=e[0];for(let i=1;i<e.length;i++)t+="_"===e[i]?e[++i].toUpperCase():e[i];return t},D=e=>{let t=e[0].toUpperCase();for(let i=1;i<e.length;i++)t+="_"===e[i]?e[++i].toUpperCase():e[i];return t},z=e=>e.replace(/_/g,"-"),Q=e=>e.replace(/([A-Z])/g,"_$1").toLowerCase(),F=e=>(e.slice(0,1)+e.slice(1).replace(/([A-Z])/g,"_$1")).toLowerCase(),U=e=>e.replace(/-/g,"_");function V(e){return function t(i,r){return"object"==typeof i&&null!==i&&(114===r.type||3802===r.type)?Array.isArray(i)?i.map(e=>t(e,r)):Object.entries(i).reduce((i,[n,s])=>Object.assign(i,{[e(n)]:t(s,r)}),{}):i}}j.column={from:j},j.value={from:V(j)},Q.column={to:Q};let X={...j};X.column.to=Q,D.column={from:D},D.value={from:V(D)},F.column={to:F};let M={...D};M.column.to=F,z.column={from:z},z.value={from:V(z)},U.column={to:U};let K={...z};K.column.to=U;var R=i(91645),J=i(34631),H=i(55511),W=i(27910),G=i(74998);class Y extends Array{constructor(){super(),Object.defineProperties(this,{count:{value:null,writable:!0},state:{value:null,writable:!0},command:{value:null,writable:!0},columns:{value:null,writable:!0},statement:{value:null,writable:!0}})}static get[Symbol.species](){return Array}}let Z=function(e=[]){let t=e.slice(),i=0;return{get length(){return t.length-i},remove:e=>{let i=t.indexOf(e);return -1===i?null:(t.splice(i,1),e)},push:e=>(t.push(e),e),shift:()=>{let e=t[i++];return i===t.length?(i=0,t=[]):t[i-1]=void 0,e}}},ee=Buffer.allocUnsafe(256),et=Object.assign(function(){return et.i=0,et},"BCcDdEFfHPpQSX".split("").reduce((e,t)=>{let i=t.charCodeAt(0);return e[t]=()=>(ee[0]=i,et.i=5,et),e},{}),{N:"\0",i:0,inc:e=>(et.i+=e,et),str(e){let t=Buffer.byteLength(e);return ei(t),et.i+=ee.write(e,et.i,t,"utf8"),et},i16:e=>(ei(2),ee.writeUInt16BE(e,et.i),et.i+=2,et),i32:(e,t)=>(t||0===t?ee.writeUInt32BE(e,t):(ei(4),ee.writeUInt32BE(e,et.i),et.i+=4),et),z:e=>(ei(e),ee.fill(0,et.i,et.i+e),et.i+=e,et),raw:e=>(ee=Buffer.concat([ee.subarray(0,et.i),e]),et.i=ee.length,et),end(e=1){ee.writeUInt32BE(et.i-e,e);let t=ee.subarray(0,et.i);return et.i=0,ee=Buffer.allocUnsafe(256),t}});function ei(e){if(ee.length-et.i<e){let t=ee,i=t.length;ee=Buffer.allocUnsafe(i+(i>>1)+e),t.copy(ee)}}let er=function e(t,i={},{onopen:r=ec,onend:n=ec,onclose:s=ec}={}){let{ssl:a,max:l,user:c,host:f,port:d,database:p,parsers:g,transform:y,onnotice:w,onnotify:v,onparameter:S,max_pipeline:x,keep_alive:T,backoff:N,target_session_attrs:P}=t,$=Z(),C=en++,A={pid:null,secret:null},I=em(eK,t.idle_timeout),O=em(eK,t.max_lifetime),B=em(function(){eX(h.connection("CONNECT_TIMEOUT",t,k)),k.destroy()},t.connect_timeout),k=null,_,L=new Y,j=Buffer.alloc(0),D=t.fetch_types,z={},Q={},F=Math.random().toString(36).slice(2),U=1,V=0,X=0,M=0,K=0,ee=0,ei=0,er=0,ef=null,ey=null,eb=!1,ew=null,ev=null,eS=null,ex=null,eT=null,eN=null,eP=null,e$=null,eC=null,eA=null,eI={queue:i.closed,idleTimer:I,connect(e){eS=e,eF()},terminate:eR,execute:ek,cancel:eB,end:eK,count:0,id:C};return i.closed&&i.closed.push(eI),eI;async function eO(){let e;try{e=t.socket?await Promise.resolve(t.socket(t)):new R.Socket}catch(e){eV(e);return}return e.on("error",eV),e.on("close",eJ),e.on("drain",eD),e}async function eB({pid:e,secret:t},i,r){try{_=et().i32(16).i32(0x4d2162e).i32(e).i32(t).end(16),await eQ(),k.once("error",r),k.once("close",i)}catch(e){r(e)}}function ek(e){if(eb)return eM(e,h.connection("CONNECTION_DESTROYED",t));if(!e.cancelled)try{return e.state=A,eC?$.push(e):(eC=e).active=!0,function(e){let i=[],r=[],n=b(e,e.strings[0],e.args[0],i,r,t);e.tagged||e.args.forEach(e=>m(e,i,r,t)),e.prepare=t.prepare&&(!("prepare"in e.options)||e.options.prepare),e.string=n,e.signature=e.prepare&&r+n,e.onlyDescribe&&delete Q[e.signature],e.parameters=e.parameters||i,e.prepared=e.prepare&&e.signature in Q,e.describeFirst=e.onlyDescribe||i.length&&!e.prepared,e.statement=e.prepared?Q[e.signature]:{string:n,types:r,name:e.prepare?F+U++:""},"function"==typeof t.debug&&t.debug(C,n,i,r)}(e),eE(function(e){var t;if(e.parameters.length>=65534)throw h.generic("MAX_PARAMETERS_EXCEEDED","Max number of parameters (65534) exceeded");return e.options.simple?et().Q().str(e.statement.string+et.N).end():e.describeFirst?Buffer.concat([e_(e),ea]):e.prepare?e.prepared?eL(e):Buffer.concat([e_(e),eL(e)]):(t=e,Buffer.concat([e4(t.statement.string,t.parameters,t.statement.types),eu,eL(t)]))}(e))&&!e.describeFirst&&!e.cursorFn&&$.length<x&&(!e.options.onexecute||e.options.onexecute(eI))}catch(e){return 0===$.length&&eE(es),eX(e),!0}}function e_(e){return Buffer.concat([e4(e.statement.string,e.parameters,e.statement.types,e.statement.name),function(e,t=""){return et().D().str("S").str(t+et.N).end()}("S",e.statement.name)])}function eL(e){return Buffer.concat([function(e,i,r="",n=""){let s,a;return et().B().str(n+et.N).str(r+et.N).i16(0).i16(e.length),e.forEach((r,n)=>{if(null===r)return et.i32(0xffffffff);a=i[n],e[n]=r=a in t.serializers?t.serializers[a](r):""+r,s=et.i,et.inc(4).str(r).i32(et.i-s-4,s)}),et.i16(0),et.end()}(e.parameters,e.statement.types,e.statement.name,e.cursorName),e.cursorFn?e5("",e.cursorRows):eo])}function eE(e,t){return(eN=eN?Buffer.concat([eN,e]):Buffer.from(e),t||eN.length>=1024)?eq(t):(null===ey&&(ey=setImmediate(eq)),!0)}function eq(e){let t=k.write(eN,e);return null!==ey&&clearImmediate(ey),eN=ey=null,t}async function ej(){if(eE(el),!await new Promise(e=>k.once("data",t=>e(83===t[0])))&&"prefer"===a)return eU();k.removeAllListeners(),(k=J.connect({socket:k,servername:R.isIP(k.host)?void 0:k.host,..."require"===a||"allow"===a||"prefer"===a?{rejectUnauthorized:!1}:"verify-full"===a?{}:"object"==typeof a?a:{}})).on("secureConnect",eU),k.on("error",eV),k.on("close",eJ),k.on("drain",eD)}function eD(){eC||r(eI)}function ez(i){if(!ew||(ew.push(i),!((X-=i.length)>0)))for(j=ew?Buffer.concat(ew,ee-X):0===j.length?i:Buffer.concat([j,i],j.length+i.length);j.length>4;){if((ee=j.readUInt32BE(1))>=j.length){X=ee-j.length,ew=[j];break}try{!function(i,n=i[0]){(68===n?function(e){let t,i,r,n=7,s=eC.isRaw?Array(eC.statement.columns.length):{};for(let a=0;a<eC.statement.columns.length;a++)i=eC.statement.columns[a],t=e.readInt32BE(n),n+=4,r=-1===t?null:!0===eC.isRaw?e.subarray(n,n+=t):void 0===i.parser?e.toString("utf8",n,n+=t):!0===i.parser.array?i.parser(e.toString("utf8",n+1,n+=t)):i.parser(e.toString("utf8",n,n+=t)),eC.isRaw?s[a]=!0===eC.isRaw?r:y.value.from?y.value.from(r,i):r:s[i.name]=y.value.from?y.value.from(r,i):r;eC.forEachFn?eC.forEachFn(y.row.from?y.row.from(s):s,L):L[er++]=y.row.from?y.row.from(s):s}:100===n?function(e){eT&&(eT.push(e.subarray(5))||k.pause())}:65===n?function(e){if(!v)return;let t=9;for(;0!==e[t++];);v(e.toString("utf8",9,t-1),e.toString("utf8",t,e.length-1))}:83===n?function(e){let[i,r]=e.toString("utf8",5,e.length-1).split(et.N);z[i]=r,t.parameters[i]!==r&&(t.parameters[i]=r,S&&S(i,r))}:90===n?function(i){if(eC&&eC.options.simple&&eC.resolve(ev||L),eC=ev=null,L=new Y,B.cancel(),eS){if(P)if(z.in_hot_standby&&z.default_transaction_read_only){var n,s;if(n=P,s=z,"read-write"===n&&"on"===s.default_transaction_read_only||"read-only"===n&&"off"===s.default_transaction_read_only||"primary"===n&&"on"===s.in_hot_standby||"standby"===n&&"off"===s.in_hot_standby||"prefer-standby"===n&&"off"===s.in_hot_standby&&t.host[K])return eR()}else{let e=new u([`
      show transaction_read_only;
      select pg_catalog.pg_is_in_recovery()
    `],[],ek,null,{simple:!0});e.resolve=([[e],[t]])=>{z.default_transaction_read_only=e.transaction_read_only,z.in_hot_standby=t.pg_is_in_recovery?"on":"off"},e.execute();return}return D?(eS.reserve&&(eS=null),e2()):(eS&&!eS.reserve&&ek(eS),t.shared.retries=K=0,void(eS=null))}for(;$.length&&(eC=$.shift())&&(eC.active=!0,eC.cancelled);)e(t).cancel(eC.state,eC.cancelled.resolve,eC.cancelled.reject);eC||(eI.reserved?eI.reserved.release||73!==i[5]?eI.reserved():ex?eR():(eI.reserved=null,r(eI)):ex?eR():r(eI))}:67===n?function(e){er=0;for(let t=e.length-1;t>0;t--)if(32===e[t]&&e[t+1]<58&&null===L.count&&(L.count=+e.toString("utf8",t+1,e.length-1)),e[t-1]>=65){L.command=e.toString("utf8",5,t),L.state=A;break}return(eA&&(eA(),eA=null),"BEGIN"!==L.command||1===l||eI.reserved)?eC.options.simple?eH():void(eC.cursorFn&&(L.count&&eC.cursorFn(L),eE(es)),eC.resolve(L)):eX(h.generic("UNSAFE_TRANSACTION","Only use sql.begin, sql.reserved or max: 1"))}:50===n?eH:49===n?function(){eC.parsing=!1}:116===n?function(e){let t=e.readUInt16BE(5);for(let i=0;i<t;++i)eC.statement.types[i]||(eC.statement.types[i]=e.readUInt32BE(7+4*i));eC.prepare&&(Q[eC.signature]=eC.statement),eC.describeFirst&&!eC.onlyDescribe&&(eE(eL(eC)),eC.describeFirst=!1)}:84===n?function(e){let t;L.command&&((ev=ev||[L]).push(L=new Y),L.count=null,eC.statement.columns=null);let i=e.readUInt16BE(5),r=7;eC.statement.columns=Array(i);for(let n=0;n<i;++n){for(t=r;0!==e[r++];);let i=e.readUInt32BE(r),s=e.readUInt16BE(r+4),a=e.readUInt32BE(r+6);eC.statement.columns[n]={name:y.column.from?y.column.from(e.toString("utf8",t,r-1)):e.toString("utf8",t,r-1),parser:g[a],table:i,number:s,type:a},r+=18}if(L.statement=eC.statement,eC.onlyDescribe)return eC.resolve(eC.statement),eE(es)}:82===n?eW:110===n?function(){if(L.statement=eC.statement,L.statement.columns=[],eC.onlyDescribe)return eC.resolve(eC.statement),eE(es)}:75===n?function(e){A.pid=e.readUInt32BE(5),A.secret=e.readUInt32BE(9)}:69===n?function(e){var t,i;eC&&(eC.cursorFn||eC.describeFirst)&&eE(es);let r=h.postgres(ed(e));eC&&eC.retried?eX(eC.retried):eC&&eC.prepared&&eh.has(r.routine)?(t=eC,i=r,delete Q[t.signature],t.retried=i,ek(t)):eX(r)}:115===n?e3:51===n?function(){L.count&&eC.cursorFn(L),eC.resolve(L)}:71===n?function(){eT=new W.Writable({autoDestroy:!0,write(e,t,i){k.write(et().d().raw(e).end(),i)},destroy(e,t){t(e),k.write(et().f().str(e+et.N).end()),eT=null},final(e){k.write(et().c().end()),eA=e}}),eC.resolve(eT)}:78===n?function(e){w?w(ed(e)):console.log(ed(e))}:72===n?function(){eT=new W.Readable({read(){k.resume()}}),eC.resolve(eT)}:99===n?function(){eT&&eT.push(null),eT=null}:73===n?function(){}:86===n?function(){eX(h.notSupported("FunctionCallResponse"))}:118===n?function(){eX(h.notSupported("NegotiateProtocolVersion"))}:87===n?function(){eT=new W.Duplex({autoDestroy:!0,read(){k.resume()},write(e,t,i){k.write(et().d().raw(e).end(),i)},destroy(e,t){t(e),k.write(et().f().str(e+et.N).end()),eT=null},final(e){k.write(et().c().end()),eA=e}}),eC.resolve(eT)}:function(e){console.error("Postgres.js : Unknown Message:",e[0])})(i)}(j.subarray(0,ee+1))}catch(e){eC&&(eC.cursorFn||eC.describeFirst)&&eE(es),eX(e)}j=j.subarray(ee+1),X=0,ew=null}}async function eQ(){if(eb=!1,z={},k||(k=await eO()),k){if(B.start(),t.socket)return a?ej():eU();if(k.on("connect",a?ej:eU),t.path)return k.connect(t.path);k.ssl=a,k.connect(d[M],f[M]),k.host=f[M],k.port=d[M],M=(M+1)%d.length}}function eF(){setTimeout(eQ,V?V+ei-G.performance.now():0)}function eU(){try{Q={},D=t.fetch_types,F=Math.random().toString(36).slice(2),U=1,O.start(),k.on("data",ez),T&&k.setKeepAlive&&k.setKeepAlive(!0,1e3*T);let e=_||et().inc(4).i16(3).z(2).str(Object.entries(Object.assign({user:c,database:p,client_encoding:"UTF8"},t.connection)).filter(([,e])=>e).map(([e,t])=>e+et.N+t).join(et.N)).z(2).end(0);eE(e)}catch(e){eV(e)}}function eV(e){if(eI.queue!==i.connecting||!t.host[K+1])for(eX(e);$.length;)eM($.shift(),e)}function eX(e){eT&&(eT.destroy(e),eT=null),eC&&eM(eC,e),eS&&(eM(eS,e),eS=null)}function eM(e,i){if(e.reserve)return e.reject(i);i&&"object"==typeof i||(i=Error(i)),"query"in i||"parameters"in i||Object.defineProperties(i,{stack:{value:i.stack+e.origin.replace(/.*\n/,"\n"),enumerable:t.debug},query:{value:e.string,enumerable:t.debug},parameters:{value:e.parameters,enumerable:t.debug},args:{value:e.args,enumerable:t.debug},types:{value:e.statement&&e.statement.types,enumerable:t.debug}}),e.reject(i)}function eK(){return ex||(eI.reserved||n(eI),eI.reserved||eS||eC||0!==$.length?ex=new Promise(e=>eP=e):(eR(),new Promise(e=>k&&"closed"!==k.readyState?k.once("close",e):e())))}function eR(){eb=!0,(eT||eC||eS||$.length)&&eV(h.connection("CONNECTION_DESTROYED",t)),clearImmediate(ey),k&&(k.removeListener("data",ez),k.removeListener("connect",eU),"open"===k.readyState&&k.end(et().X().end())),eP&&(eP(),ex=eP=null)}async function eJ(e){if(j=Buffer.alloc(0),X=0,ew=null,clearImmediate(ey),k.removeListener("data",ez),k.removeListener("connect",eU),I.cancel(),O.cancel(),B.cancel(),k.removeAllListeners(),k=null,eS)return eF();!e&&(eC||$.length)&&eV(h.connection("CONNECTION_CLOSED",t,k)),V=G.performance.now(),e&&t.shared.retries++,ei=("function"==typeof N?N(t.shared.retries):N)*1e3,s(eI,h.connection("CONNECTION_CLOSED",t,k))}function eH(){L.statement||(L.statement=eC.statement),L.columns=eC.statement.columns}async function eW(e,t=e.readUInt32BE(5)){(3===t?eG:5===t?eY:10===t?eZ:11===t?e0:12===t?function(e){e.toString("utf8",9).split(et.N,1)[0].slice(2)!==ef&&(eX(h.generic("SASL_SIGNATURE_MISMATCH","The server did not return the correct signature")),k.destroy())}:0!==t?function(e,t){console.error("Postgres.js : Unknown Auth:",t)}:ec)(e,t)}async function eG(){let e=await e1();eE(et().p().str(e).z(1).end())}async function eY(e){let t="md5"+await ep(Buffer.concat([Buffer.from(await ep(await e1()+c)),e.subarray(9)]));eE(et().p().str(t).z(1).end())}async function eZ(){e$=(await H.randomBytes(18)).toString("base64"),et().p().str("SCRAM-SHA-256"+et.N);let e=et.i;eE(et.inc(4).str("n,,n=*,r="+e$).i32(et.i-e-4,e).end())}async function e0(e){var t;let i=e.toString("utf8",9).split(",").reduce((e,t)=>(e[t[0]]=t.slice(2),e),{}),r=await H.pbkdf2Sync(await e1(),Buffer.from(i.s,"base64"),parseInt(i.i),32,"sha256"),n=await eg(r,"Client Key"),s="n=*,r="+e$+",r="+i.r+",s="+i.s+",i="+i.i+",c=biws,r="+i.r;ef=(await eg(await eg(r,"Server Key"),s)).toString("base64");let a="c=biws,r="+i.r+",p="+(function(e,t){let i=Math.max(e.length,t.length),r=Buffer.allocUnsafe(i);for(let n=0;n<i;n++)r[n]=e[n]^t[n];return r})(n,Buffer.from(await eg(await (t=n,H.createHash("sha256").update(t).digest()),s))).toString("base64");eE(et().p().str(a).end())}function e1(){return Promise.resolve("function"==typeof t.pass?t.pass():t.pass)}async function e2(){D=!1,(await new u([`
      select b.oid, b.typarray
      from pg_catalog.pg_type a
      left join pg_catalog.pg_type b on b.oid = a.typelem
      where a.typcategory = 'A'
      group by b.oid, b.typarray
      order by b.oid
    `],[],ek)).forEach(({oid:e,typarray:i})=>(function(e,i){if(t.parsers[i]&&t.serializers[i])return;let r=t.parsers[e];t.shared.typeArrayMap[e]=i,t.parsers[i]=e=>(q.i=q.last=0,function e(t,i,r,n){let s=[],a=1020===n?";":",";for(;t.i<i.length;t.i++){if(t.char=i[t.i],t.quoted)"\\"===t.char?t.str+=i[++t.i]:'"'===t.char?(s.push(r?r(t.str):t.str),t.str="",t.quoted='"'===i[t.i+1],t.last=t.i+2):t.str+=t.char;else if('"'===t.char)t.quoted=!0;else if("{"===t.char)t.last=++t.i,s.push(e(t,i,r,n));else if("}"===t.char){t.quoted=!1,t.last<t.i&&s.push(r?r(i.slice(t.last,t.i)):i.slice(t.last,t.i)),t.last=t.i+1;break}else t.char===a&&"}"!==t.p&&'"'!==t.p&&(s.push(r?r(i.slice(t.last,t.i)):i.slice(t.last,t.i)),t.last=t.i+1);t.p=t.char}return t.last<t.i&&s.push(r?r(i.slice(t.last,t.i+1)):i.slice(t.last,t.i+1)),s}(q,e,r,i)),t.parsers[i].array=!0,t.serializers[i]=r=>E(r,t.serializers[e],t,i)})(e,i))}async function e3(){try{let e=await Promise.resolve(eC.cursorFn(L));er=0,e===o?eE(function(e=""){return Buffer.concat([et().C().str("P").str(e+et.N).end(),et().S().end()])}(eC.portal)):(L=new Y,eE(e5("",eC.cursorRows)))}catch(e){eE(es),eC.reject(e)}}function e4(e,t,i,r=""){return et().P().str(r+et.N).str(e+et.N).i16(t.length),t.forEach((e,t)=>et.i32(i[t]||0)),et.end()}function e5(e="",t=0){return Buffer.concat([et().E().str(e+et.N).i32(t).end(),ea])}},en=1,es=et().S().end(),ea=et().H().end(),el=et().i32(8).i32(0x4d2162f).end(8),eo=Buffer.concat([et().E().str(et.N).i32(0).end(),es]),eu=et().D().str("S").str(et.N).end(),ec=()=>{},eh=new Set(["FetchPreparedStatement","RevalidateCachedQuery","transformAssignedExpr"]),ef={83:"severity_local",86:"severity",67:"code",77:"message",68:"detail",72:"hint",80:"position",112:"internal_position",113:"internal_query",87:"where",115:"schema_name",116:"table_name",99:"column_name",100:"data type_name",110:"constraint_name",70:"file",76:"line",82:"routine"};function ed(e){let t={},i=5;for(let r=5;r<e.length-1;r++)0===e[r]&&(t[ef[e[i]]]=e.toString("utf8",i+1,r),i=r+1);return t}function ep(e){return H.createHash("md5").update(e).digest("hex")}function eg(e,t){return H.createHmac("sha256",e).update(t).digest()}function em(e,t){let i;if(!(t="function"==typeof t?t():t))return{cancel:ec,start:ec};return{cancel(){i&&(clearTimeout(i),i=null)},start(){i&&clearTimeout(i),i=setTimeout(r,1e3*t,arguments)}};function r(t){e.apply(null,t),i=null}}let ey=()=>{};function eb(e,t,i,r){let n,s,a,l=r.raw?Array(t.length):{};for(let o=0;o<t.length;o++)n=e[i++],s=t[o],a=110===n?null:117===n?void 0:void 0===s.parser?e.toString("utf8",i+4,i+=4+e.readUInt32BE(i)):!0===s.parser.array?s.parser(e.toString("utf8",i+5,i+=4+e.readUInt32BE(i))):s.parser(e.toString("utf8",i+4,i+=4+e.readUInt32BE(i))),r.raw?l[o]=!0===r.raw?a:r.value.from?r.value.from(a,s):a:l[s.name]=r.value.from?r.value.from(a,s):a;return{i:i,row:r.row.from?r.row.from(l):l}}function ew(e,t,i=393216){return new Promise(async(r,n)=>{await e.begin(async e=>{let n;t||([{oid:t}]=await e`select lo_creat(-1) as oid`);let[{fd:s}]=await e`select lo_open(${t}, ${i}) as fd`,a={writable:o,readable:l,close:()=>e`select lo_close(${s})`.then(n),tell:()=>e`select lo_tell64(${s})`,read:t=>e`select loread(${s}, ${t}) as data`,write:t=>e`select lowrite(${s}, ${t})`,truncate:t=>e`select lo_truncate64(${s}, ${t})`,seek:(t,i=0)=>e`select lo_lseek64(${s}, ${t}, ${i})`,size:()=>e`
          select
            lo_lseek64(${s}, location, 0) as position,
            seek.size
          from (
            select
              lo_lseek64($1, 0, 2) as size,
              tell.location
            from (select lo_tell64($1) as location) tell
          ) seek
        `};return r(a),new Promise(async e=>n=e);async function l({highWaterMark:e=16384,start:t=0,end:i=1/0}={}){let r=i-t;return t&&await a.seek(t),new W.Readable({highWaterMark:e,async read(e){let t=e>r?e-r:e;r-=e;let[{data:i}]=await a.read(t);this.push(i),i.length<e&&this.push(null)}})}async function o({highWaterMark:e=16384,start:t=0}={}){return t&&await a.seek(t),new W.Writable({highWaterMark:e,write(e,t,i){a.write(e).then(()=>i(),i)}})}}).catch(n)})}Object.assign(eS,{PostgresError:c,toPascal:D,pascal:M,toCamel:j,camel:X,toKebab:z,kebab:K,fromPascal:F,fromCamel:Q,fromKebab:U,BigInt:{to:20,from:[20],parse:e=>BigInt(e),serialize:e=>e.toString()}});let ev=eS;function eS(e,t){let i=function(e,t){var i;if(e&&e.shared)return e;let n=process.env,s=(e&&"string"!=typeof e?e:t)||{},{url:a,multihost:l}=function(e){if(!e||"string"!=typeof e)return{url:{searchParams:new Map}};let t=e;t=decodeURIComponent((t=t.slice(t.indexOf("://")+3).split(/[?/]/)[0]).slice(t.indexOf("@")+1));let i=new URL(e.replace(t,t.split(",")[0]));return{url:{username:decodeURIComponent(i.username),password:decodeURIComponent(i.password),host:i.host,hostname:i.hostname,port:i.port,pathname:i.pathname,searchParams:i.searchParams},multihost:t.indexOf(",")>-1&&t}}(e),o=[...a.searchParams].reduce((e,[t,i])=>(e[t]=i,e),{}),u=s.hostname||s.host||l||a.hostname||n.PGHOST||"localhost",c=s.port||a.port||n.PGPORT||5432,h=s.user||s.username||a.username||n.PGUSERNAME||n.PGUSER||function(){try{return r.userInfo().username}catch(e){return process.env.USERNAME||process.env.USER||process.env.LOGNAME}}();s.no_prepare&&(s.prepare=!1),o.sslmode&&(o.ssl=o.sslmode,delete o.sslmode),"timeout"in s&&(console.log("The timeout option is deprecated, use idle_timeout instead"),s.idle_timeout=s.timeout),"system"===o.sslrootcert&&(o.ssl="verify-full");let f=["idle_timeout","connect_timeout","max_lifetime","max_pipeline","backoff","keep_alive"],d={max:10,ssl:!1,idle_timeout:null,connect_timeout:30,max_lifetime:eT,max_pipeline:100,backoff:ex,keep_alive:60,prepare:!0,debug:!1,fetch_types:!0,publications:"alltables",target_session_attrs:null};return{host:Array.isArray(u)?u:u.split(",").map(e=>e.split(":")[0]),port:Array.isArray(c)?c:u.split(",").map(e=>parseInt(e.split(":")[1]||c)),path:s.path||u.indexOf("/")>-1&&u+"/.s.PGSQL."+c,database:s.database||s.db||(a.pathname||"").slice(1)||n.PGDATABASE||h,user:h,pass:s.pass||s.password||a.password||n.PGPASSWORD||"",...Object.entries(d).reduce((e,[t,i])=>{let r=t in s?s[t]:t in o?"disable"!==o[t]&&"false"!==o[t]&&o[t]:n["PG"+t.toUpperCase()]||i;return e[t]="string"==typeof r&&f.includes(t)?+r:r,e},{}),connection:{application_name:n.PGAPPNAME||"postgres.js",...s.connection,...Object.entries(o).reduce((e,[t,i])=>(t in d||(e[t]=i),e),{})},types:s.types||{},target_session_attrs:function(e,t,i){let r=e.target_session_attrs||t.searchParams.get("target_session_attrs")||i.PGTARGETSESSIONATTRS;if(!r||["read-write","read-only","primary","standby","prefer-standby"].includes(r))return r;throw Error("target_session_attrs "+r+" is not supported")}(s,a,n),onnotice:s.onnotice,onnotify:s.onnotify,onclose:s.onclose,onparameter:s.onparameter,socket:s.socket,transform:{undefined:(i=s.transform||{undefined:void 0}).undefined,column:{from:"function"==typeof i.column?i.column:i.column&&i.column.from,to:i.column&&i.column.to},value:{from:"function"==typeof i.value?i.value:i.value&&i.value.from,to:i.value&&i.value.to},row:{from:"function"==typeof i.row?i.row:i.row&&i.row.from,to:i.row&&i.row.to}},parameters:{},shared:{retries:0,typeArrayMap:{}},...A(s.types)}}(e,t),s=i.no_subscribe||function(e,t){let i=new Map,r="postgresjs_"+Math.random().toString(36).slice(2),n={},s,a,l=!1,o=h.sql=e({...t,transform:{column:{},value:{},row:{}},max:1,fetch_types:!1,idle_timeout:null,max_lifetime:null,connection:{...t.connection,replication:"database"},onclose:async function(){l||(a=null,n.pid=n.secret=void 0,f(await d(o,r,t.publications)),i.forEach(e=>e.forEach(({onsubscribe:e})=>e())))},no_subscribe:!0}),u=o.end,c=o.close;return o.end=async()=>(l=!0,a&&await new Promise(e=>(a.once("close",e),a.end())),u()),o.close=async()=>(a&&await new Promise(e=>(a.once("close",e),a.end())),c()),h;async function h(e,l,u=ey,c=ey){e=function(e){let t=e.match(/^(\*|insert|update|delete)?:?([^.]+?\.?[^=]+)?=?(.+)?/i)||[];if(!t)throw Error("Malformed subscribe pattern: "+e);let[,i,r,n]=t;return(i||"*")+(r?":"+(-1===r.indexOf(".")?"public."+r:r):"")+(n?"="+n:"")}(e),s||(s=d(o,r,t.publications));let p={fn:l,onsubscribe:u},g=i.has(e)?i.get(e).add(p):i.set(e,new Set([p])).get(e),m=()=>{g.delete(p),0===g.size&&i.delete(e)};return s.then(e=>(f(e),u(),a&&a.on("error",c),{unsubscribe:m,state:n,sql:o}))}function f(e){a=e.stream,n.pid=e.state.pid,n.secret=e.state.secret}async function d(e,i,r){if(!r)throw Error("Missing publication names");let n=await e.unsafe(`CREATE_REPLICATION_SLOT ${i} TEMPORARY LOGICAL pgoutput NOEXPORT_SNAPSHOT`),[s]=n,a=await e.unsafe(`START_REPLICATION SLOT ${i} LOGICAL ${s.consistent_point} (proto_version '1', publication_names '${r}')`).writable(),l={lsn:Buffer.concat(s.consistent_point.split("/").map(e=>Buffer.from(("00000000"+e).slice(-8),"hex")))};return a.on("data",function(i){var r,n,s,u,c;119===i[0]?(r=i.subarray(25),n=l,s=e.options.parsers,u=o,c=t.transform,Object.entries({R:e=>{let t=1,i=n[e.readUInt32BE(t)]={schema:e.toString("utf8",t+=4,t=e.indexOf(0,t))||"pg_catalog",table:e.toString("utf8",t+1,t=e.indexOf(0,t+1)),columns:Array(e.readUInt16BE(t+=2)),keys:[]};t+=2;let r=0,a;for(;t<e.length;)(a=i.columns[r++]={key:e[t++],name:c.column.from?c.column.from(e.toString("utf8",t,t=e.indexOf(0,t))):e.toString("utf8",t,t=e.indexOf(0,t)),type:e.readUInt32BE(t+=1),parser:s[e.readUInt32BE(t)],atttypmod:e.readUInt32BE(t+=4)}).key&&i.keys.push(a),t+=4},Y:()=>{},O:()=>{},B:e=>{var t;t=e.readBigInt64BE(9),n.date=new Date(Date.UTC(2e3,0,1)+Number(t/BigInt(1e3))),n.lsn=e.subarray(1,9)},I:e=>{let t=1,i=n[e.readUInt32BE(t)],{row:r}=eb(e,i.columns,t+=7,c);u(r,{command:"insert",relation:i})},D:e=>{let t=1,i=n[e.readUInt32BE(t)],r=75===e[t+=4];u(r||79===e[t]?eb(e,i.columns,t+=3,c).row:null,{command:"delete",relation:i,key:r})},U:e=>{let t=1,i=n[e.readUInt32BE(t)],r=75===e[t+=4],s=r||79===e[t]?eb(e,i.columns,t+=3,c):null;s&&(t=s.i);let{row:a}=eb(e,i.columns,t+3,c);u(a,{command:"update",relation:i,key:r,old:s&&s.row})},T:()=>{},C:()=>{}}).reduce((e,[t,i])=>(e[t.charCodeAt(0)]=i,e),{})[r[0]](r)):107===i[0]&&i[17]&&(l.lsn=i.subarray(1,9),function(){let e=Buffer.alloc(34);e[0]=114,e.fill(l.lsn,1),e.writeBigInt64BE(BigInt(Date.now()-Date.UTC(2e3,0,1))*BigInt(1e3),25),a.write(e)}())}),a.on("error",function(e){console.error("Unexpected error during logical streaming - reconnecting",e)}),a.on("close",e.close),{stream:a,state:n.state};function o(e,t){let i=t.relation.schema+"."+t.relation.table;p("*",e,t),p("*:"+i,e,t),t.relation.keys.length&&p("*:"+i+"="+t.relation.keys.map(t=>e[t.name]),e,t),p(t.command,e,t),p(t.command+":"+i,e,t),t.relation.keys.length&&p(t.command+":"+i+"="+t.relation.keys.map(t=>e[t.name]),e,t)}}function p(e,t,r){i.has(e)&&i.get(e).forEach(({fn:i})=>i(t,r,e))}}(eS,{...i}),a=!1,l=Z(),f=Z(),m=Z(),y=Z(),b=Z(),w=Z(),v=Z(),S=Z(),x={connecting:f,reserved:m,closed:y,ended:b,open:w,busy:v,full:S},T=[...Array(i.max)].map(()=>er(i,x,{onopen:F,onend:Q,onclose:U})),N=P(function(e){return a?e.reject(h.connection("CONNECTION_ENDED",i,i)):w.length?L(w.shift(),e):y.length?z(y.shift(),e):void(v.length?L(v.shift(),e):l.push(e))});return Object.assign(N,{get parameters(){return i.parameters},largeObject:ew.bind(null,N),subscribe:s,CLOSE:o,END:o,PostgresError:c,options:i,reserve:I,listen:$,begin:O,close:j,end:q}),N;function P(e){return e.debug=i.debug,Object.entries(i.types).reduce((e,[t,i])=>(e[t]=e=>new p(e,i.to),e),t),Object.assign(r,{types:t,typed:t,unsafe:function(t,i=[],r={}){return 2!=arguments.length||Array.isArray(i)||(r=i,i=[]),new u([t],i,e,E,{prepare:!1,...r,simple:"simple"in r?r.simple:0===i.length})},notify:C,array:function e(t,r){return Array.isArray(t)?new p(t,r||(t.length?k(t)||25:0),i.shared.typeArrayMap):e(Array.from(arguments))},json:_,file:function(t,i=[],r={}){return 2!=arguments.length||Array.isArray(i)||(r=i,i=[]),new u([],i,i=>{n.readFile(t,"utf8",(t,r)=>{if(t)return i.reject(t);i.strings=[r],e(i)})},E,{...r,simple:"simple"in r?r.simple:0===i.length})}}),r;function t(e,t){return new p(e,t)}function r(t,...n){return t&&Array.isArray(t.raw)?new u(t,n,e,E):"string"!=typeof t||n.length?new g(t,n):new d(i.transform.column.to?i.transform.column.to(t):t)}}async function $(e,t,r){let n={fn:t,onlisten:r},s=$.sql||($.sql=eS({...i,max:1,idle_timeout:null,max_lifetime:null,fetch_types:!1,onclose(){Object.entries($.channels).forEach(([e,{listeners:t}])=>{delete $.channels[e],Promise.all(t.map(t=>$(e,t.fn,t.onlisten).catch(()=>{})))})},onnotify(e,t){e in $.channels&&$.channels[e].listeners.forEach(e=>e.fn(t))}})),a=$.channels||($.channels={});if(e in a){a[e].listeners.push(n);let t=await a[e].result;return n.onlisten&&n.onlisten(),{state:t.state,unlisten:o}}a[e]={result:s`listen ${s.unsafe('"'+e.replace(/"/g,'""')+'"')}`,listeners:[n]};let l=await a[e].result;return n.onlisten&&n.onlisten(),{state:l.state,unlisten:o};async function o(){if(e in a!=!1&&(a[e].listeners=a[e].listeners.filter(e=>e!==n),!a[e].listeners.length))return delete a[e],s`unlisten ${s.unsafe('"'+e.replace(/"/g,'""')+'"')}`}}async function C(e,t){return await N`select pg_notify(${e}, ${""+t})`}async function I(){let e=Z(),t=w.length?w.shift():await new Promise((e,t)=>{let i={reserve:e,reject:t};l.push(i),y.length&&z(y.shift(),i)});B(t,m),t.reserved=()=>e.length?t.execute(e.shift()):B(t,m),t.reserved.release=!0;let i=P(function(i){t.queue===S?e.push(i):t.execute(i)||B(t,S)});return i.release=()=>{t.reserved=null,F(t)},i}async function O(e,t){t||(t=e,e="");let i=Z(),r=0,n,s=null;try{return await N.unsafe("begin "+e.replace(/[^a-z ]/ig,""),[],{onexecute:function(e){n=e,B(e,m),e.reserved=()=>i.length?e.execute(i.shift()):B(e,m)}}).execute(),await Promise.race([a(n,t),new Promise((e,t)=>n.onclose=t)])}catch(e){throw e}async function a(e,t,n){let l,o,u=P(function(t){t.catch(e=>l||(l=e)),e.queue===S?i.push(t):e.execute(t)||B(e,S)});u.savepoint=function t(i,n){return i&&Array.isArray(i.raw)?t(e=>e.apply(e,arguments)):(1==arguments.length&&(n=i,i=null),a(e,n,"s"+r+++(i?"_"+i:"")))},u.prepare=e=>s=e.replace(/[^a-z0-9$-_. ]/gi),n&&await u`savepoint ${u(n)}`;try{if(o=await new Promise((e,i)=>{let r=t(u);Promise.resolve(Array.isArray(r)?Promise.all(r):r).then(e,i)}),l)throw l}catch(e){throw await (n?u`rollback to ${u(n)}`:u`rollback`),e instanceof c&&"25P02"===e.code&&l||e}return n||(s?await u`prepare transaction '${u.unsafe(s)}'`:await u`commit`),o}}function B(e,t){return e.queue.remove(e),t.push(e),e.queue=t,t===w?e.idleTimer.start():e.idleTimer.cancel(),e}function _(e){return new p(e,3802)}function L(e,t){return e.execute(t)?B(e,v):B(e,S)}function E(e){return new Promise((t,r)=>{e.state?e.active?er(i).cancel(e.state,t,r):e.cancelled={resolve:t,reject:r}:(l.remove(e),e.cancelled=!0,e.reject(h.generic("57014","canceling statement due to user request")),t())})}async function q({timeout:e=null}={}){let t;return a||(await 1,a=Promise.race([new Promise(i=>null!==e&&(t=setTimeout(D,1e3*e,i))),Promise.all(T.map(e=>e.end()).concat($.sql?$.sql.end({timeout:0}):[],s.sql?s.sql.end({timeout:0}):[]))]).then(()=>clearTimeout(t)))}async function j(){await Promise.all(T.map(e=>e.end()))}async function D(e){for(await Promise.all(T.map(e=>e.terminate()));l.length;)l.shift().reject(h.connection("CONNECTION_DESTROYED",i));e()}function z(e,t){return B(e,f),e.connect(t),e}function Q(e){B(e,b)}function F(e){if(0===l.length)return B(e,w);let t=Math.ceil(l.length/(f.length+1)),i=!0;for(;i&&l.length&&t-- >0;){let t=l.shift();if(t.reserve)return t.reserve(e);i=e.execute(t)}i?B(e,v):B(e,S)}function U(e,t){B(e,y),e.reserved=null,e.onclose&&(e.onclose(t),e.onclose=null),i.onclose&&i.onclose(e.id),l.length&&z(e,l.shift())}}function ex(e){return(.5+Math.random()/2)*Math.min(3**e/100,20)}function eT(){return 60*(30+30*Math.random())}},45944:(e,t,i)=>{"use strict";i.d(t,{ae:()=>g,Kl:()=>d,pe:()=>f});var r=i(38949),n=i(10007),s=i(5730),a=i(86214);class l{static [s.i]="PgForeignKeyBuilder";reference;_onUpdate="no action";_onDelete="no action";constructor(e,t){this.reference=()=>{let{name:t,columns:i,foreignColumns:r}=e();return{name:t,columns:i,foreignTable:r[0].table,foreignColumns:r}},t&&(this._onUpdate=t.onUpdate,this._onDelete=t.onDelete)}onUpdate(e){return this._onUpdate=void 0===e?"no action":e,this}onDelete(e){return this._onDelete=void 0===e?"no action":e,this}build(e){return new o(e,this)}}class o{constructor(e,t){this.table=e,this.reference=t.reference,this.onUpdate=t._onUpdate,this.onDelete=t._onDelete}static [s.i]="PgForeignKey";reference;onUpdate;onDelete;getName(){let{name:e,columns:t,foreignColumns:i}=this.reference(),r=t.map(e=>e.name),n=i.map(e=>e.name),s=[this.table[a.E],...r,i[0].table[a.E],...n];return e??`${s.join("_")}_fk`}}var u=i(69167),c=i(91036);function h(e,t,i){for(let r=t;r<e.length;r++){let n=e[r];if("\\"===n){r++;continue}if('"'===n)return[e.slice(t,r).replace(/\\/g,""),r+1];if(!i&&(","===n||"}"===n))return[e.slice(t,r).replace(/\\/g,""),r]}return[e.slice(t).replace(/\\/g,""),e.length]}class f extends r.Q{foreignKeyConfigs=[];static [s.i]="PgColumnBuilder";array(e){return new m(this.config.name,this,e)}references(e,t={}){return this.foreignKeyConfigs.push({ref:e,actions:t}),this}unique(e,t){return this.config.isUnique=!0,this.config.uniqueName=e,this.config.uniqueType=t?.nulls,this}generatedAlwaysAs(e){return this.config.generated={as:e,type:"always",mode:"stored"},this}buildForeignKeys(e,t){return this.foreignKeyConfigs.map(({ref:i,actions:r})=>(0,u.i)((i,r)=>{let n=new l(()=>({columns:[e],foreignColumns:[i()]}));return r.onUpdate&&n.onUpdate(r.onUpdate),r.onDelete&&n.onDelete(r.onDelete),n.build(t)},i,r))}buildExtraConfigColumn(e){return new p(e,this.config)}}class d extends n.V{constructor(e,t){t.uniqueName||(t.uniqueName=(0,c.Wx)(e,[t.name])),super(e,t),this.table=e}static [s.i]="PgColumn"}class p extends d{static [s.i]="ExtraConfigColumn";getSQLType(){return this.getSQLType()}indexConfig={order:this.config.order??"asc",nulls:this.config.nulls??"last",opClass:this.config.opClass};defaultConfig={order:"asc",nulls:"last",opClass:void 0};asc(){return this.indexConfig.order="asc",this}desc(){return this.indexConfig.order="desc",this}nullsFirst(){return this.indexConfig.nulls="first",this}nullsLast(){return this.indexConfig.nulls="last",this}op(e){return this.indexConfig.opClass=e,this}}class g{static [s.i]="IndexedColumn";constructor(e,t,i,r){this.name=e,this.keyAsName=t,this.type=i,this.indexConfig=r}name;keyAsName;type;indexConfig}class m extends f{static [s.i]="PgArrayBuilder";constructor(e,t,i){super(e,"array","PgArray"),this.config.baseBuilder=t,this.config.size=i}build(e){let t=this.config.baseBuilder.build(e);return new y(e,this.config,t)}}class y extends d{constructor(e,t,i,r){super(e,t),this.baseColumn=i,this.range=r,this.size=t.size}size;static [s.i]="PgArray";getSQLType(){return`${this.baseColumn.getSQLType()}[${"number"==typeof this.size?this.size:""}]`}mapFromDriverValue(e){return"string"==typeof e&&(e=function(e){let[t]=function e(t,i=0){let r=[],n=i,s=!1;for(;n<t.length;){let a=t[n];if(","===a){(s||n===i)&&r.push(""),s=!0,n++;continue}if(s=!1,"\\"===a){n+=2;continue}if('"'===a){let[e,i]=h(t,n+1,!0);r.push(e),n=i;continue}if("}"===a)return[r,n+1];if("{"===a){let[i,s]=e(t,n+1);r.push(i),n=s;continue}let[l,o]=h(t,n,!1);r.push(l),n=o}return[r,n]}(e,1);return t}(e)),e.map(e=>this.baseColumn.mapFromDriverValue(e))}mapToDriverValue(e,t=!1){let i=e.map(e=>null===e?null:(0,s.is)(this.baseColumn,y)?this.baseColumn.mapToDriverValue(e,!0):this.baseColumn.mapToDriverValue(e));return t?i:function e(t){return`{${t.map(t=>Array.isArray(t)?e(t):"string"==typeof t?`"${t.replace(/\\/g,"\\\\").replace(/"/g,'\\"')}"`:`${t}`).join(",")}}`}(i)}}},45958:(e,t,i)=>{"use strict";i.d(t,{b:()=>u});var r=i(89685),n=i(10007),s=i(5730),a=i(96657),l=i(67083),o=i(12772);class u{static [s.i]="SelectionProxyHandler";config;constructor(e){this.config={...e}}get(e,t){if("_"===t)return{...e._,selectedFields:new Proxy(e._.selectedFields,this)};if(t===o.n)return{...e[o.n],selectedFields:new Proxy(e[o.n].selectedFields,this)};if("symbol"==typeof t)return e[t];let i=((0,s.is)(e,l.n)?e._.selectedFields:(0,s.is)(e,a.Ss)?e[o.n].selectedFields:e)[t];if((0,s.is)(i,a.Xs.Aliased)){if("sql"===this.config.sqlAliasedBehavior&&!i.isSelectionField)return i.sql;let e=i.clone();return e.isSelectionField=!0,e}if((0,s.is)(i,a.Xs)){if("sql"===this.config.sqlBehavior)return i;throw Error(`You tried to reference "${t}" field from a subquery, which is a raw SQL field, but it doesn't have an alias declared. Please add an alias to the field using ".as('alias')" method.`)}return(0,s.is)(i,n.V)?this.config.alias?new Proxy(i,new r.Ht(new Proxy(i.table,new r.h_(this.config.alias,this.config.replaceOriginalName??!1)))):i:"object"!=typeof i||null===i?i:new Proxy(i,new u(this.config))}}},49197:(e,t,i)=>{"use strict";i.d(t,{k:()=>n});var r=i(5730);class n{static [r.i]="QueryPromise";[Symbol.toStringTag]="QueryPromise";catch(e){return this.then(void 0,e)}finally(e){return this.then(t=>(e?.(),t),t=>{throw e?.(),t})}then(e,t){return this.execute().then(e,t)}}},50351:(e,t,i)=>{"use strict";i.d(t,{s:()=>P});var r=i(89685),n=i(86890),s=i(10007),a=i(5730),l=i(40774),o=i(45944),u=i(34509),c=i(34359),h=i(72170),f=i(9528),d=i(9253),p=i(37193),g=i(9594),m=i(92768),y=i(3884),b=i(96657),w=i(94634),v=i(67083),S=i(24717),x=i(79608),T=i(12772),N=i(316);class P{static [a.i]="PgDialect";casing;constructor(e){this.casing=new n.Yn(e?.casing)}async migrate(e,t,i){let r="string"==typeof i?"__drizzle_migrations":i.migrationsTable??"__drizzle_migrations",n="string"==typeof i?"drizzle":i.migrationsSchema??"drizzle",s=(0,b.ll)`
			CREATE TABLE IF NOT EXISTS ${b.ll.identifier(n)}.${b.ll.identifier(r)} (
				id SERIAL PRIMARY KEY,
				hash text NOT NULL,
				created_at bigint
			)
		`;await t.execute((0,b.ll)`CREATE SCHEMA IF NOT EXISTS ${b.ll.identifier(n)}`),await t.execute(s);let a=(await t.all((0,b.ll)`select id, hash, created_at from ${b.ll.identifier(n)}.${b.ll.identifier(r)} order by created_at desc limit 1`))[0];await t.transaction(async t=>{for await(let i of e)if(!a||Number(a.created_at)<i.folderMillis){for(let e of i.sql)await t.execute(b.ll.raw(e));await t.execute((0,b.ll)`insert into ${b.ll.identifier(n)}.${b.ll.identifier(r)} ("hash", "created_at") values(${i.hash}, ${i.folderMillis})`)}})}escapeName(e){return`"${e}"`}escapeParam(e){return`$${e+1}`}escapeString(e){return`'${e.replace(/'/g,"''")}'`}buildWithCTE(e){if(!e?.length)return;let t=[(0,b.ll)`with `];for(let[i,r]of e.entries())t.push((0,b.ll)`${b.ll.identifier(r._.alias)} as (${r._.sql})`),i<e.length-1&&t.push((0,b.ll)`, `);return t.push((0,b.ll)` `),b.ll.join(t)}buildDeleteQuery({table:e,where:t,returning:i,withList:r}){let n=this.buildWithCTE(r),s=i?(0,b.ll)` returning ${this.buildSelection(i,{isSingleTable:!0})}`:void 0,a=t?(0,b.ll)` where ${t}`:void 0;return(0,b.ll)`${n}delete from ${e}${a}${s}`}buildUpdateSet(e,t){let i=e[S.XI.Symbol.Columns],r=Object.keys(i).filter(e=>void 0!==t[e]||i[e]?.onUpdateFn!==void 0),n=r.length;return b.ll.join(r.flatMap((e,r)=>{let s=i[e],a=t[e]??b.ll.param(s.onUpdateFn(),s),l=(0,b.ll)`${b.ll.identifier(this.casing.getColumnCasing(s))} = ${a}`;return r<n-1?[l,b.ll.raw(", ")]:[l]}))}buildUpdateQuery({table:e,set:t,where:i,returning:r,withList:n,from:s,joins:a}){let l=this.buildWithCTE(n),o=e[m.mu.Symbol.Name],u=e[m.mu.Symbol.Schema],c=e[m.mu.Symbol.OriginalName],h=o===c?void 0:o,f=(0,b.ll)`${u?(0,b.ll)`${b.ll.identifier(u)}.`:void 0}${b.ll.identifier(c)}${h&&(0,b.ll)` ${b.ll.identifier(h)}`}`,d=this.buildUpdateSet(e,t),p=s&&b.ll.join([b.ll.raw(" from "),this.buildFromTable(s)]),g=this.buildJoins(a),y=r?(0,b.ll)` returning ${this.buildSelection(r,{isSingleTable:!s})}`:void 0,w=i?(0,b.ll)` where ${i}`:void 0;return(0,b.ll)`${l}update ${f} set ${d}${p}${g}${w}${y}`}buildSelection(e,{isSingleTable:t=!1}={}){let i=e.length,r=e.flatMap(({field:e},r)=>{let n=[];if((0,a.is)(e,b.Xs.Aliased)&&e.isSelectionField)n.push(b.ll.identifier(e.fieldAlias));else if((0,a.is)(e,b.Xs.Aliased)||(0,a.is)(e,b.Xs)){let i=(0,a.is)(e,b.Xs.Aliased)?e.sql:e;t?n.push(new b.Xs(i.queryChunks.map(e=>(0,a.is)(e,o.Kl)?b.ll.identifier(this.casing.getColumnCasing(e)):e))):n.push(i),(0,a.is)(e,b.Xs.Aliased)&&n.push((0,b.ll)` as ${b.ll.identifier(e.fieldAlias)}`)}else(0,a.is)(e,s.V)&&(t?n.push(b.ll.identifier(this.casing.getColumnCasing(e))):n.push(e));return r<i-1&&n.push((0,b.ll)`, `),n});return b.ll.join(r)}buildJoins(e){if(!e||0===e.length)return;let t=[];for(let[i,r]of e.entries()){0===i&&t.push((0,b.ll)` `);let n=r.table,s=r.lateral?(0,b.ll)` lateral`:void 0,l=r.on?(0,b.ll)` on ${r.on}`:void 0;if((0,a.is)(n,m.mu)){let e=n[m.mu.Symbol.Name],i=n[m.mu.Symbol.Schema],a=n[m.mu.Symbol.OriginalName],o=e===a?void 0:r.alias;t.push((0,b.ll)`${b.ll.raw(r.joinType)} join${s} ${i?(0,b.ll)`${b.ll.identifier(i)}.`:void 0}${b.ll.identifier(a)}${o&&(0,b.ll)` ${b.ll.identifier(o)}`}${l}`)}else if((0,a.is)(n,b.Ss)){let e=n[T.n].name,i=n[T.n].schema,a=n[T.n].originalName,o=e===a?void 0:r.alias;t.push((0,b.ll)`${b.ll.raw(r.joinType)} join${s} ${i?(0,b.ll)`${b.ll.identifier(i)}.`:void 0}${b.ll.identifier(a)}${o&&(0,b.ll)` ${b.ll.identifier(o)}`}${l}`)}else t.push((0,b.ll)`${b.ll.raw(r.joinType)} join${s} ${n}${l}`);i<e.length-1&&t.push((0,b.ll)` `)}return b.ll.join(t)}buildFromTable(e){if((0,a.is)(e,S.XI)&&e[S.XI.Symbol.IsAlias]){let t=(0,b.ll)`${b.ll.identifier(e[S.XI.Symbol.OriginalName])}`;return e[S.XI.Symbol.Schema]&&(t=(0,b.ll)`${b.ll.identifier(e[S.XI.Symbol.Schema])}.${t}`),(0,b.ll)`${t} ${b.ll.identifier(e[S.XI.Symbol.Name])}`}return e}buildSelectQuery({withList:e,fields:t,fieldsFlat:i,where:r,having:n,table:l,joins:o,orderBy:u,groupBy:c,limit:h,offset:f,lockingClause:d,distinct:p,setOperators:g}){let m,y,w,P=i??(0,x.He)(t);for(let e of P){let t;if((0,a.is)(e.field,s.V)&&(0,S.Io)(e.field.table)!==((0,a.is)(l,v.n)?l._.alias:(0,a.is)(l,N.w)?l[T.n].name:(0,a.is)(l,b.Xs)?void 0:(0,S.Io)(l))&&(t=e.field.table,!o?.some(({alias:e})=>e===(t[S.XI.Symbol.IsAlias]?(0,S.Io)(t):t[S.XI.Symbol.BaseName])))){let t=(0,S.Io)(e.field.table);throw Error(`Your "${e.path.join("->")}" field references a column "${t}"."${e.field.name}", but the table "${t}" is not part of the query! Did you forget to join it?`)}}let $=!o||0===o.length,C=this.buildWithCTE(e);p&&(m=!0===p?(0,b.ll)` distinct`:(0,b.ll)` distinct on (${b.ll.join(p.on,(0,b.ll)`, `)})`);let A=this.buildSelection(P,{isSingleTable:$}),I=this.buildFromTable(l),O=this.buildJoins(o),B=r?(0,b.ll)` where ${r}`:void 0,k=n?(0,b.ll)` having ${n}`:void 0;u&&u.length>0&&(y=(0,b.ll)` order by ${b.ll.join(u,(0,b.ll)`, `)}`),c&&c.length>0&&(w=(0,b.ll)` group by ${b.ll.join(c,(0,b.ll)`, `)}`);let _="object"==typeof h||"number"==typeof h&&h>=0?(0,b.ll)` limit ${h}`:void 0,L=f?(0,b.ll)` offset ${f}`:void 0,E=b.ll.empty();if(d){let e=(0,b.ll)` for ${b.ll.raw(d.strength)}`;d.config.of&&e.append((0,b.ll)` of ${b.ll.join(Array.isArray(d.config.of)?d.config.of:[d.config.of],(0,b.ll)`, `)}`),d.config.noWait?e.append((0,b.ll)` nowait`):d.config.skipLocked&&e.append((0,b.ll)` skip locked`),E.append(e)}let q=(0,b.ll)`${C}select${m} ${A} from ${I}${O}${B}${w}${k}${y}${_}${L}${E}`;return g.length>0?this.buildSetOperations(q,g):q}buildSetOperations(e,t){let[i,...r]=t;if(!i)throw Error("Cannot pass undefined values to any set operator");return 0===r.length?this.buildSetOperationQuery({leftSelect:e,setOperator:i}):this.buildSetOperations(this.buildSetOperationQuery({leftSelect:e,setOperator:i}),r)}buildSetOperationQuery({leftSelect:e,setOperator:{type:t,isAll:i,rightSelect:r,limit:n,orderBy:s,offset:l}}){let u,c=(0,b.ll)`(${e.getSQL()}) `,h=(0,b.ll)`(${r.getSQL()})`;if(s&&s.length>0){let e=[];for(let t of s)if((0,a.is)(t,o.Kl))e.push(b.ll.identifier(t.name));else if((0,a.is)(t,b.Xs)){for(let e=0;e<t.queryChunks.length;e++){let i=t.queryChunks[e];(0,a.is)(i,o.Kl)&&(t.queryChunks[e]=b.ll.identifier(i.name))}e.push((0,b.ll)`${t}`)}else e.push((0,b.ll)`${t}`);u=(0,b.ll)` order by ${b.ll.join(e,(0,b.ll)`, `)} `}let f="object"==typeof n||"number"==typeof n&&n>=0?(0,b.ll)` limit ${n}`:void 0,d=b.ll.raw(`${t} ${i?"all ":""}`),p=l?(0,b.ll)` offset ${l}`:void 0;return(0,b.ll)`${c}${d}${h}${u}${f}${p}`}buildInsertQuery({table:e,values:t,onConflict:i,returning:r,withList:n,select:s,overridingSystemValue_:l}){let o=[],u=Object.entries(e[S.XI.Symbol.Columns]).filter(([e,t])=>!t.shouldDisableInsert()),c=u.map(([,e])=>b.ll.identifier(this.casing.getColumnCasing(e)));if(s)(0,a.is)(t,b.Xs)?o.push(t):o.push(t.getSQL());else for(let[e,i]of(o.push(b.ll.raw("values ")),t.entries())){let r=[];for(let[e,t]of u){let n=i[e];if(void 0===n||(0,a.is)(n,b.Iw)&&void 0===n.value)if(void 0!==t.defaultFn){let e=t.defaultFn(),i=(0,a.is)(e,b.Xs)?e:b.ll.param(e,t);r.push(i)}else if(t.default||void 0===t.onUpdateFn)r.push((0,b.ll)`default`);else{let e=t.onUpdateFn(),i=(0,a.is)(e,b.Xs)?e:b.ll.param(e,t);r.push(i)}else r.push(n)}o.push(r),e<t.length-1&&o.push((0,b.ll)`, `)}let h=this.buildWithCTE(n),f=b.ll.join(o),d=r?(0,b.ll)` returning ${this.buildSelection(r,{isSingleTable:!0})}`:void 0,p=i?(0,b.ll)` on conflict ${i}`:void 0,g=!0===l?(0,b.ll)`overriding system value `:void 0;return(0,b.ll)`${h}insert into ${e} ${c} ${g}${f}${p}${d}`}buildRefreshMaterializedViewQuery({view:e,concurrently:t,withNoData:i}){let r=t?(0,b.ll)` concurrently`:void 0,n=i?(0,b.ll)` with no data`:void 0;return(0,b.ll)`refresh materialized view${r} ${e}${n}`}prepareTyping(e){if((0,a.is)(e,u.kn)||(0,a.is)(e,c.iX))return"json";if((0,a.is)(e,h.Z5))return"decimal";if((0,a.is)(e,f.Xd))return"time";if((0,a.is)(e,d.KM)||(0,a.is)(e,d.xQ))return"timestamp";if((0,a.is)(e,p.qw)||(0,a.is)(e,p.dw))return"date";else if((0,a.is)(e,g.dL))return"uuid";else return"none"}sqlToQuery(e,t){return e.toQuery({casing:this.casing,escapeName:this.escapeName,escapeParam:this.escapeParam,escapeString:this.escapeString,prepareTyping:this.prepareTyping,invokeSource:t})}buildRelationalQueryWithoutPK({fullSchema:e,schema:t,tableNamesMap:i,table:n,tableConfig:o,queryConfig:u,tableAlias:c,nestedQueryRelation:h,joinOn:f}){let d,p=[],g,x,T=[],N,P=[];if(!0===u)p=Object.entries(o.columns).map(([e,t])=>({dbKey:t.name,tsKey:e,field:(0,r.ug)(t,c),relationTableTsKey:void 0,isJson:!1,selection:[]}));else{let n=Object.fromEntries(Object.entries(o.columns).map(([e,t])=>[e,(0,r.ug)(t,c)]));if(u.where){let e="function"==typeof u.where?u.where(n,(0,y.mm)()):u.where;N=e&&(0,r.yY)(e,c)}let l=[],h=[];if(u.columns){let e=!1;for(let[t,i]of Object.entries(u.columns))void 0!==i&&t in o.columns&&(e||!0!==i||(e=!0),h.push(t));h.length>0&&(h=e?h.filter(e=>u.columns?.[e]===!0):Object.keys(o.columns).filter(e=>!h.includes(e)))}else h=Object.keys(o.columns);for(let e of h){let t=o.columns[e];l.push({tsKey:e,value:t})}let f=[];if(u.with&&(f=Object.entries(u.with).filter(e=>!!e[1]).map(([e,t])=>({tsKey:e,queryConfig:t,relation:o.relations[e]}))),u.extras)for(let[e,t]of Object.entries("function"==typeof u.extras?u.extras(n,{sql:b.ll}):u.extras))l.push({tsKey:e,value:(0,r.Hs)(t,c)});for(let{tsKey:e,value:t}of l)p.push({dbKey:(0,a.is)(t,b.Xs.Aliased)?t.fieldAlias:o.columns[e].name,tsKey:e,field:(0,a.is)(t,s.V)?(0,r.ug)(t,c):t,relationTableTsKey:void 0,isJson:!1,selection:[]});let d="function"==typeof u.orderBy?u.orderBy(n,(0,y.rl)()):u.orderBy??[];for(let{tsKey:n,queryConfig:l,relation:o}of(Array.isArray(d)||(d=[d]),T=d.map(e=>(0,a.is)(e,s.V)?(0,r.ug)(e,c):(0,r.yY)(e,c)),g=u.limit,x=u.offset,f)){let s=(0,y.W0)(t,i,o),u=i[(0,S.Lf)(o.referencedTable)],h=`${c}_${n}`,f=(0,w.Uo)(...s.fields.map((e,t)=>(0,w.eq)((0,r.ug)(s.references[t],h),(0,r.ug)(e,c)))),d=this.buildRelationalQueryWithoutPK({fullSchema:e,schema:t,tableNamesMap:i,table:e[u],tableConfig:t[u],queryConfig:(0,a.is)(o,y.pD)?!0===l?{limit:1}:{...l,limit:1}:l,tableAlias:h,joinOn:f,nestedQueryRelation:o}),g=(0,b.ll)`${b.ll.identifier(h)}.${b.ll.identifier("data")}`.as(n);P.push({on:(0,b.ll)`true`,table:new v.n(d.sql,{},h),alias:h,joinType:"left",lateral:!0}),p.push({dbKey:n,tsKey:n,field:g,relationTableTsKey:u,isJson:!0,selection:d.selection})}}if(0===p.length)throw new l.n({message:`No fields selected for table "${o.tsName}" ("${c}")`});if(N=(0,w.Uo)(f,N),h){let e=(0,b.ll)`json_build_array(${b.ll.join(p.map(({field:e,tsKey:t,isJson:i})=>i?(0,b.ll)`${b.ll.identifier(`${c}_${t}`)}.${b.ll.identifier("data")}`:(0,a.is)(e,b.Xs.Aliased)?e.sql:e),(0,b.ll)`, `)})`;(0,a.is)(h,y.iv)&&(e=(0,b.ll)`coalesce(json_agg(${e}${T.length>0?(0,b.ll)` order by ${b.ll.join(T,(0,b.ll)`, `)}`:void 0}), '[]'::json)`);let t=[{dbKey:"data",tsKey:"data",field:e.as("data"),isJson:!0,relationTableTsKey:o.tsName,selection:p}];void 0!==g||void 0!==x||T.length>0?(d=this.buildSelectQuery({table:(0,r.oG)(n,c),fields:{},fieldsFlat:[{path:[],field:b.ll.raw("*")}],where:N,limit:g,offset:x,orderBy:T,setOperators:[]}),N=void 0,g=void 0,x=void 0,T=[]):d=(0,r.oG)(n,c),d=this.buildSelectQuery({table:(0,a.is)(d,m.mu)?d:new v.n(d,{},c),fields:{},fieldsFlat:t.map(({field:e})=>({path:[],field:(0,a.is)(e,s.V)?(0,r.ug)(e,c):e})),joins:P,where:N,limit:g,offset:x,orderBy:T,setOperators:[]})}else d=this.buildSelectQuery({table:(0,r.oG)(n,c),fields:{},fieldsFlat:p.map(({field:e})=>({path:[],field:(0,a.is)(e,s.V)?(0,r.ug)(e,c):e})),joins:P,where:N,limit:g,offset:x,orderBy:T,setOperators:[]});return{tableTsKey:o.tsName,sql:d,selection:p}}}},51928:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.wrapXOFConstructorWithOpts=t.wrapConstructorWithOpts=t.wrapConstructor=t.Hash=t.nextTick=t.swap32IfBE=t.byteSwapIfBE=t.swap8IfBE=t.isLE=void 0,t.isBytes=n,t.anumber=s,t.abytes=a,t.ahash=function(e){if("function"!=typeof e||"function"!=typeof e.create)throw Error("Hash should be wrapped by utils.createHasher");s(e.outputLen),s(e.blockLen)},t.aexists=function(e,t=!0){if(e.destroyed)throw Error("Hash instance has been destroyed");if(t&&e.finished)throw Error("Hash#digest() has already been called")},t.aoutput=function(e,t){a(e);let i=t.outputLen;if(e.length<i)throw Error("digestInto() expects output buffer of length at least "+i)},t.u8=function(e){return new Uint8Array(e.buffer,e.byteOffset,e.byteLength)},t.u32=function(e){return new Uint32Array(e.buffer,e.byteOffset,Math.floor(e.byteLength/4))},t.clean=function(...e){for(let t=0;t<e.length;t++)e[t].fill(0)},t.createView=function(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)},t.rotr=function(e,t){return e<<32-t|e>>>t},t.rotl=function(e,t){return e<<t|e>>>32-t>>>0},t.byteSwap=l,t.byteSwap32=o,t.bytesToHex=function(e){if(a(e),u)return e.toHex();let t="";for(let i=0;i<e.length;i++)t+=c[e[i]];return t},t.hexToBytes=function(e){if("string"!=typeof e)throw Error("hex string expected, got "+typeof e);if(u)return Uint8Array.fromHex(e);let t=e.length,i=t/2;if(t%2)throw Error("hex string expected, got unpadded hex of length "+t);let r=new Uint8Array(i);for(let t=0,n=0;t<i;t++,n+=2){let i=f(e.charCodeAt(n)),s=f(e.charCodeAt(n+1));if(void 0===i||void 0===s)throw Error('hex string expected, got non-hex character "'+(e[n]+e[n+1])+'" at index '+n);r[t]=16*i+s}return r},t.asyncLoop=d,t.utf8ToBytes=p,t.bytesToUtf8=function(e){return new TextDecoder().decode(e)},t.toBytes=g,t.kdfInputToBytes=function(e){return"string"==typeof e&&(e=p(e)),a(e),e},t.concatBytes=function(...e){let t=0;for(let i=0;i<e.length;i++){let r=e[i];a(r),t+=r.length}let i=new Uint8Array(t);for(let t=0,r=0;t<e.length;t++){let n=e[t];i.set(n,r),r+=n.length}return i},t.checkOpts=function(e,t){if(void 0!==t&&"[object Object]"!==({}).toString.call(t))throw Error("options should be object or undefined");return Object.assign(e,t)},t.createHasher=y,t.createOptHasher=b,t.createXOFer=w,t.randomBytes=function(e=32){if(r.crypto&&"function"==typeof r.crypto.getRandomValues)return r.crypto.getRandomValues(new Uint8Array(e));if(r.crypto&&"function"==typeof r.crypto.randomBytes)return Uint8Array.from(r.crypto.randomBytes(e));throw Error("crypto.getRandomValues must be defined")};let r=i(40888);function n(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&"Uint8Array"===e.constructor.name}function s(e){if(!Number.isSafeInteger(e)||e<0)throw Error("positive integer expected, got "+e)}function a(e,...t){if(!n(e))throw Error("Uint8Array expected");if(t.length>0&&!t.includes(e.length))throw Error("Uint8Array expected of length "+t+", got length="+e.length)}function l(e){return e<<24&0xff000000|e<<8&0xff0000|e>>>8&65280|e>>>24&255}function o(e){for(let t=0;t<e.length;t++)e[t]=l(e[t]);return e}t.isLE=68===new Uint8Array(new Uint32Array([0x11223344]).buffer)[0],t.swap8IfBE=t.isLE?e=>e:e=>l(e),t.byteSwapIfBE=t.swap8IfBE,t.swap32IfBE=t.isLE?e=>e:o;let u="function"==typeof Uint8Array.from([]).toHex&&"function"==typeof Uint8Array.fromHex,c=Array.from({length:256},(e,t)=>t.toString(16).padStart(2,"0")),h={_0:48,_9:57,A:65,F:70,a:97,f:102};function f(e){return e>=h._0&&e<=h._9?e-h._0:e>=h.A&&e<=h.F?e-(h.A-10):e>=h.a&&e<=h.f?e-(h.a-10):void 0}async function d(e,i,r){let n=Date.now();for(let s=0;s<e;s++){r(s);let e=Date.now()-n;e>=0&&e<i||(await (0,t.nextTick)(),n+=e)}}function p(e){if("string"!=typeof e)throw Error("string expected");return new Uint8Array(new TextEncoder().encode(e))}function g(e){return"string"==typeof e&&(e=p(e)),a(e),e}t.nextTick=async()=>{};class m{}function y(e){let t=t=>e().update(g(t)).digest(),i=e();return t.outputLen=i.outputLen,t.blockLen=i.blockLen,t.create=()=>e(),t}function b(e){let t=(t,i)=>e(i).update(g(t)).digest(),i=e({});return t.outputLen=i.outputLen,t.blockLen=i.blockLen,t.create=t=>e(t),t}function w(e){let t=(t,i)=>e(i).update(g(t)).digest(),i=e({});return t.outputLen=i.outputLen,t.blockLen=i.blockLen,t.create=t=>e(t),t}t.Hash=m,t.wrapConstructor=y,t.wrapConstructorWithOpts=b,t.wrapXOFConstructorWithOpts=w},52175:(e,t,i)=>{let{createId:r,init:n,getConstants:s,isCuid:a}=i(66246);e.exports.sX=r},54693:(e,t,i)=>{"use strict";i.d(t,{zM:()=>l});var r=i(5730),n=i(45944);class s extends n.pe{static [r.i]="PgBooleanBuilder";constructor(e){super(e,"boolean","PgBoolean")}build(e){return new a(e,this.config)}}class a extends n.Kl{static [r.i]="PgBoolean";getSQLType(){return"boolean"}}function l(e){return new s(e??"")}},58152:(e,t,i)=>{"use strict";i.d(t,{hv:()=>a,ie:()=>s});var r=i(5730),n=i(92768);function s(...e){return e[0].columns?new a(e[0].columns,e[0].name):new a(e)}class a{static [r.i]="PgPrimaryKeyBuilder";columns;name;constructor(e,t){this.columns=e,this.name=t}build(e){return new l(e,this.columns,this.name)}}class l{constructor(e,t,i){this.table=e,this.columns=t,this.name=i}static [r.i]="PgPrimaryKey";columns;name;getName(){return this.name??`${this.table[n.mu.Symbol.Name]}_${this.columns.map(e=>e.name).join("_")}_pk`}}},58779:(e,t,i)=>{"use strict";i.d(t,{p:()=>Q});var r=i(5730),n=i(50351),s=i(45958),a=i(67083),l=i(316),o=i(41879),u=i(49197),c=i(96657),h=i(24717),f=i(99511),d=i(79608),p=i(12772),g=i(92768);function m(e){return(0,r.is)(e,g.mu)?[e[h.Sj]?`${e[h.Sj]}.${e[h.XI.Symbol.BaseName]}`:e[h.XI.Symbol.BaseName]]:(0,r.is)(e,a.n)?e._.usedTables??[]:(0,r.is)(e,c.Xs)?e.usedTables??[]:[]}class y{static [r.i]="PgSelectBuilder";fields;session;dialect;withList=[];distinct;constructor(e){this.fields=e.fields,this.session=e.session,this.dialect=e.dialect,e.withList&&(this.withList=e.withList),this.distinct=e.distinct}authToken;setToken(e){return this.authToken=e,this}from(e){let t,i=!!this.fields;return t=this.fields?this.fields:(0,r.is)(e,a.n)?Object.fromEntries(Object.keys(e._.selectedFields).map(t=>[t,e[t]])):(0,r.is)(e,l.w)?e[p.n].selectedFields:(0,r.is)(e,c.Xs)?{}:(0,d.YD)(e),new w({table:e,fields:t,isPartialSelect:i,session:this.session,dialect:this.dialect,withList:this.withList,distinct:this.distinct}).setToken(this.authToken)}}class b extends o.O{static [r.i]="PgSelectQueryBuilder";_;config;joinsNotNullableMap;tableName;isPartialSelect;session;dialect;cacheConfig=void 0;usedTables=new Set;constructor({table:e,fields:t,isPartialSelect:i,session:r,dialect:n,withList:s,distinct:a}){for(let l of(super(),this.config={withList:s,table:e,fields:{...t},distinct:a,setOperators:[]},this.isPartialSelect=i,this.session=r,this.dialect=n,this._={selectedFields:t,config:this.config},this.tableName=(0,d.zN)(e),this.joinsNotNullableMap="string"==typeof this.tableName?{[this.tableName]:!0}:{},m(e)))this.usedTables.add(l)}getUsedTables(){return[...this.usedTables]}createJoin(e,t){return(i,n)=>{let l=this.tableName,o=(0,d.zN)(i);for(let e of m(i))this.usedTables.add(e);if("string"==typeof o&&this.config.joins?.some(e=>e.alias===o))throw Error(`Alias "${o}" is already used in this query`);if(!this.isPartialSelect&&(1===Object.keys(this.joinsNotNullableMap).length&&"string"==typeof l&&(this.config.fields={[l]:this.config.fields}),"string"==typeof o&&!(0,r.is)(i,c.Xs))){let e=(0,r.is)(i,a.n)?i._.selectedFields:(0,r.is)(i,c.Ss)?i[p.n].selectedFields:i[h.XI.Symbol.Columns];this.config.fields[o]=e}if("function"==typeof n&&(n=n(new Proxy(this.config.fields,new s.b({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.joins||(this.config.joins=[]),this.config.joins.push({on:n,table:i,joinType:e,alias:o,lateral:t}),"string"==typeof o)switch(e){case"left":this.joinsNotNullableMap[o]=!1;break;case"right":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[o]=!0;break;case"cross":case"inner":this.joinsNotNullableMap[o]=!0;break;case"full":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[o]=!1}return this}}leftJoin=this.createJoin("left",!1);leftJoinLateral=this.createJoin("left",!0);rightJoin=this.createJoin("right",!1);innerJoin=this.createJoin("inner",!1);innerJoinLateral=this.createJoin("inner",!0);fullJoin=this.createJoin("full",!1);crossJoin=this.createJoin("cross",!1);crossJoinLateral=this.createJoin("cross",!0);createSetOperator(e,t){return i=>{let r="function"==typeof i?i(S()):i;if(!(0,d.DV)(this.getSelectedFields(),r.getSelectedFields()))throw Error("Set operator error (union / intersect / except): selected fields are not the same or are in a different order");return this.config.setOperators.push({type:e,isAll:t,rightSelect:r}),this}}union=this.createSetOperator("union",!1);unionAll=this.createSetOperator("union",!0);intersect=this.createSetOperator("intersect",!1);intersectAll=this.createSetOperator("intersect",!0);except=this.createSetOperator("except",!1);exceptAll=this.createSetOperator("except",!0);addSetOperators(e){return this.config.setOperators.push(...e),this}where(e){return"function"==typeof e&&(e=e(new Proxy(this.config.fields,new s.b({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.where=e,this}having(e){return"function"==typeof e&&(e=e(new Proxy(this.config.fields,new s.b({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.having=e,this}groupBy(...e){if("function"==typeof e[0]){let t=e[0](new Proxy(this.config.fields,new s.b({sqlAliasedBehavior:"alias",sqlBehavior:"sql"})));this.config.groupBy=Array.isArray(t)?t:[t]}else this.config.groupBy=e;return this}orderBy(...e){if("function"==typeof e[0]){let t=e[0](new Proxy(this.config.fields,new s.b({sqlAliasedBehavior:"alias",sqlBehavior:"sql"}))),i=Array.isArray(t)?t:[t];this.config.setOperators.length>0?this.config.setOperators.at(-1).orderBy=i:this.config.orderBy=i}else this.config.setOperators.length>0?this.config.setOperators.at(-1).orderBy=e:this.config.orderBy=e;return this}limit(e){return this.config.setOperators.length>0?this.config.setOperators.at(-1).limit=e:this.config.limit=e,this}offset(e){return this.config.setOperators.length>0?this.config.setOperators.at(-1).offset=e:this.config.offset=e,this}for(e,t={}){return this.config.lockingClause={strength:e,config:t},this}getSQL(){return this.dialect.buildSelectQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}as(e){let t=[];if(t.push(...m(this.config.table)),this.config.joins)for(let e of this.config.joins)t.push(...m(e.table));return new Proxy(new a.n(this.getSQL(),this.config.fields,e,!1,[...new Set(t)]),new s.b({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"}))}getSelectedFields(){return new Proxy(this.config.fields,new s.b({alias:this.tableName,sqlAliasedBehavior:"alias",sqlBehavior:"error"}))}$dynamic(){return this}$withCache(e){return this.cacheConfig=void 0===e?{config:{},enable:!0,autoInvalidate:!0}:!1===e?{enable:!1}:{enable:!0,autoInvalidate:!0,...e},this}}class w extends b{static [r.i]="PgSelect";_prepare(e){let{session:t,config:i,dialect:r,joinsNotNullableMap:n,authToken:s,cacheConfig:a,usedTables:l}=this;if(!t)throw Error("Cannot execute a query on a query builder. Please use a database instance instead.");let{fields:o}=i;return f.k.startActiveSpan("drizzle.prepareQuery",()=>{let i=(0,d.He)(o),u=t.prepareQuery(r.sqlToQuery(this.getSQL()),i,e,!0,void 0,{type:"select",tables:[...l]},a);return u.joinsNotNullableMap=n,u.setToken(s)})}prepare(e){return this._prepare(e)}authToken;setToken(e){return this.authToken=e,this}execute=e=>f.k.startActiveSpan("drizzle.operation",()=>this._prepare().execute(e,this.authToken))}function v(e,t){return(i,r,...n)=>{let s=[r,...n].map(i=>({type:e,isAll:t,rightSelect:i}));for(let e of s)if(!(0,d.DV)(i.getSelectedFields(),e.rightSelect.getSelectedFields()))throw Error("Set operator error (union / intersect / except): selected fields are not the same or are in a different order");return i.addSetOperators(s)}}(0,d.XJ)(w,[u.k]);let S=()=>({union:x,unionAll:T,intersect:N,intersectAll:P,except:$,exceptAll:C}),x=v("union",!1),T=v("union",!0),N=v("intersect",!1),P=v("intersect",!0),$=v("except",!1),C=v("except",!0);class A{static [r.i]="PgQueryBuilder";dialect;dialectConfig;constructor(e){this.dialect=(0,r.is)(e,n.s)?e:void 0,this.dialectConfig=(0,r.is)(e,n.s)?void 0:e}$with=(e,t)=>{let i=this;return{as:r=>("function"==typeof r&&(r=r(i)),new Proxy(new a.J(r.getSQL(),t??("getSelectedFields"in r?r.getSelectedFields()??{}:{}),e,!0),new s.b({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"})))}};with(...e){let t=this;return{select:function(i){return new y({fields:i??void 0,session:void 0,dialect:t.getDialect(),withList:e})},selectDistinct:function(e){return new y({fields:e??void 0,session:void 0,dialect:t.getDialect(),distinct:!0})},selectDistinctOn:function(e,i){return new y({fields:i??void 0,session:void 0,dialect:t.getDialect(),distinct:{on:e}})}}}select(e){return new y({fields:e??void 0,session:void 0,dialect:this.getDialect()})}selectDistinct(e){return new y({fields:e??void 0,session:void 0,dialect:this.getDialect(),distinct:!0})}selectDistinctOn(e,t){return new y({fields:t??void 0,session:void 0,dialect:this.getDialect(),distinct:{on:e}})}getDialect(){return this.dialect||(this.dialect=new n.s(this.dialectConfig)),this.dialect}}class I{constructor(e,t,i,r){this.table=e,this.session=t,this.dialect=i,this.withList=r}static [r.i]="PgUpdateBuilder";authToken;setToken(e){return this.authToken=e,this}set(e){return new O(this.table,(0,d.q)(this.table,e),this.session,this.dialect,this.withList).setToken(this.authToken)}}class O extends u.k{constructor(e,t,i,r,n){super(),this.session=i,this.dialect=r,this.config={set:t,table:e,withList:n,joins:[]},this.tableName=(0,d.zN)(e),this.joinsNotNullableMap="string"==typeof this.tableName?{[this.tableName]:!0}:{}}static [r.i]="PgUpdate";config;tableName;joinsNotNullableMap;cacheConfig;from(e){let t=(0,d.zN)(e);return"string"==typeof t&&(this.joinsNotNullableMap[t]=!0),this.config.from=e,this}getTableLikeFields(e){return(0,r.is)(e,g.mu)?e[h.XI.Symbol.Columns]:(0,r.is)(e,a.n)?e._.selectedFields:e[p.n].selectedFields}createJoin(e){return(t,i)=>{let n=(0,d.zN)(t);if("string"==typeof n&&this.config.joins.some(e=>e.alias===n))throw Error(`Alias "${n}" is already used in this query`);if("function"==typeof i){let e=this.config.from&&!(0,r.is)(this.config.from,c.Xs)?this.getTableLikeFields(this.config.from):void 0;i=i(new Proxy(this.config.table[h.XI.Symbol.Columns],new s.b({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})),e&&new Proxy(e,new s.b({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))}if(this.config.joins.push({on:i,table:t,joinType:e,alias:n}),"string"==typeof n)switch(e){case"left":this.joinsNotNullableMap[n]=!1;break;case"right":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[n]=!0;break;case"inner":this.joinsNotNullableMap[n]=!0;break;case"full":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[n]=!1}return this}}leftJoin=this.createJoin("left");rightJoin=this.createJoin("right");innerJoin=this.createJoin("inner");fullJoin=this.createJoin("full");where(e){return this.config.where=e,this}returning(e){if(!e&&(e=Object.assign({},this.config.table[h.XI.Symbol.Columns]),this.config.from)){let t=(0,d.zN)(this.config.from);if("string"==typeof t&&this.config.from&&!(0,r.is)(this.config.from,c.Xs)){let i=this.getTableLikeFields(this.config.from);e[t]=i}for(let t of this.config.joins){let i=(0,d.zN)(t.table);if("string"==typeof i&&!(0,r.is)(t.table,c.Xs)){let r=this.getTableLikeFields(t.table);e[i]=r}}}return this.config.returningFields=e,this.config.returning=(0,d.He)(e),this}getSQL(){return this.dialect.buildUpdateQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e){let t=this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),this.config.returning,e,!0,void 0,{type:"insert",tables:m(this.config.table)},this.cacheConfig);return t.joinsNotNullableMap=this.joinsNotNullableMap,t}prepare(e){return this._prepare(e)}authToken;setToken(e){return this.authToken=e,this}execute=e=>this._prepare().execute(e,this.authToken);getSelectedFields(){return this.config.returningFields?new Proxy(this.config.returningFields,new s.b({alias:(0,h.Io)(this.config.table),sqlAliasedBehavior:"alias",sqlBehavior:"error"})):void 0}$dynamic(){return this}}class B{constructor(e,t,i,r,n){this.table=e,this.session=t,this.dialect=i,this.withList=r,this.overridingSystemValue_=n}static [r.i]="PgInsertBuilder";authToken;setToken(e){return this.authToken=e,this}overridingSystemValue(){return this.overridingSystemValue_=!0,this}values(e){if(0===(e=Array.isArray(e)?e:[e]).length)throw Error("values() must be called with at least one value");let t=e.map(e=>{let t={},i=this.table[h.XI.Symbol.Columns];for(let n of Object.keys(e)){let s=e[n];t[n]=(0,r.is)(s,c.Xs)?s:new c.Iw(s,i[n])}return t});return new k(this.table,t,this.session,this.dialect,this.withList,!1,this.overridingSystemValue_).setToken(this.authToken)}select(e){let t="function"==typeof e?e(new A):e;if(!(0,r.is)(t,c.Xs)&&!(0,d.DV)(this.table[h.e],t._.selectedFields))throw Error("Insert select error: selected fields are not the same or are in a different order compared to the table definition");return new k(this.table,t,this.session,this.dialect,this.withList,!0)}}class k extends u.k{constructor(e,t,i,r,n,s,a){super(),this.session=i,this.dialect=r,this.config={table:e,values:t,withList:n,select:s,overridingSystemValue_:a}}static [r.i]="PgInsert";config;cacheConfig;returning(e=this.config.table[h.XI.Symbol.Columns]){return this.config.returningFields=e,this.config.returning=(0,d.He)(e),this}onConflictDoNothing(e={}){if(void 0===e.target)this.config.onConflict=(0,c.ll)`do nothing`;else{let t="";t=Array.isArray(e.target)?e.target.map(e=>this.dialect.escapeName(this.dialect.casing.getColumnCasing(e))).join(","):this.dialect.escapeName(this.dialect.casing.getColumnCasing(e.target));let i=e.where?(0,c.ll)` where ${e.where}`:void 0;this.config.onConflict=(0,c.ll)`(${c.ll.raw(t)})${i} do nothing`}return this}onConflictDoUpdate(e){if(e.where&&(e.targetWhere||e.setWhere))throw Error('You cannot use both "where" and "targetWhere"/"setWhere" at the same time - "where" is deprecated, use "targetWhere" or "setWhere" instead.');let t=e.where?(0,c.ll)` where ${e.where}`:void 0,i=e.targetWhere?(0,c.ll)` where ${e.targetWhere}`:void 0,r=e.setWhere?(0,c.ll)` where ${e.setWhere}`:void 0,n=this.dialect.buildUpdateSet(this.config.table,(0,d.q)(this.config.table,e.set)),s="";return s=Array.isArray(e.target)?e.target.map(e=>this.dialect.escapeName(this.dialect.casing.getColumnCasing(e))).join(","):this.dialect.escapeName(this.dialect.casing.getColumnCasing(e.target)),this.config.onConflict=(0,c.ll)`(${c.ll.raw(s)})${i} do update set ${n}${t}${r}`,this}getSQL(){return this.dialect.buildInsertQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e){return f.k.startActiveSpan("drizzle.prepareQuery",()=>this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),this.config.returning,e,!0,void 0,{type:"insert",tables:m(this.config.table)},this.cacheConfig))}prepare(e){return this._prepare(e)}authToken;setToken(e){return this.authToken=e,this}execute=e=>f.k.startActiveSpan("drizzle.operation",()=>this._prepare().execute(e,this.authToken));getSelectedFields(){return this.config.returningFields?new Proxy(this.config.returningFields,new s.b({alias:(0,h.Io)(this.config.table),sqlAliasedBehavior:"alias",sqlBehavior:"error"})):void 0}$dynamic(){return this}}class _ extends u.k{constructor(e,t,i,r){super(),this.session=t,this.dialect=i,this.config={table:e,withList:r}}static [r.i]="PgDelete";config;cacheConfig;where(e){return this.config.where=e,this}returning(e=this.config.table[h.XI.Symbol.Columns]){return this.config.returningFields=e,this.config.returning=(0,d.He)(e),this}getSQL(){return this.dialect.buildDeleteQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e){return f.k.startActiveSpan("drizzle.prepareQuery",()=>this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),this.config.returning,e,!0,void 0,{type:"delete",tables:m(this.config.table)},this.cacheConfig))}prepare(e){return this._prepare(e)}authToken;setToken(e){return this.authToken=e,this}execute=e=>f.k.startActiveSpan("drizzle.operation",()=>this._prepare().execute(e,this.authToken));getSelectedFields(){return this.config.returningFields?new Proxy(this.config.returningFields,new s.b({alias:(0,h.Io)(this.config.table),sqlAliasedBehavior:"alias",sqlBehavior:"error"})):void 0}$dynamic(){return this}}class L extends c.Xs{constructor(e){super(L.buildEmbeddedCount(e.source,e.filters).queryChunks),this.params=e,this.mapWith(Number),this.session=e.session,this.sql=L.buildCount(e.source,e.filters)}sql;token;static [r.i]="PgCountBuilder";[Symbol.toStringTag]="PgCountBuilder";session;static buildEmbeddedCount(e,t){return(0,c.ll)`(select count(*) from ${e}${c.ll.raw(" where ").if(t)}${t})`}static buildCount(e,t){return(0,c.ll)`select count(*) as count from ${e}${c.ll.raw(" where ").if(t)}${t};`}setToken(e){return this.token=e,this}then(e,t){return Promise.resolve(this.session.count(this.sql,this.token)).then(e,t)}catch(e){return this.then(void 0,e)}finally(e){return this.then(t=>(e?.(),t),t=>{throw e?.(),t})}}var E=i(3884);class q{constructor(e,t,i,r,n,s,a){this.fullSchema=e,this.schema=t,this.tableNamesMap=i,this.table=r,this.tableConfig=n,this.dialect=s,this.session=a}static [r.i]="PgRelationalQueryBuilder";findMany(e){return new j(this.fullSchema,this.schema,this.tableNamesMap,this.table,this.tableConfig,this.dialect,this.session,e||{},"many")}findFirst(e){return new j(this.fullSchema,this.schema,this.tableNamesMap,this.table,this.tableConfig,this.dialect,this.session,e?{...e,limit:1}:{limit:1},"first")}}class j extends u.k{constructor(e,t,i,r,n,s,a,l,o){super(),this.fullSchema=e,this.schema=t,this.tableNamesMap=i,this.table=r,this.tableConfig=n,this.dialect=s,this.session=a,this.config=l,this.mode=o}static [r.i]="PgRelationalQuery";_prepare(e){return f.k.startActiveSpan("drizzle.prepareQuery",()=>{let{query:t,builtQuery:i}=this._toSQL();return this.session.prepareQuery(i,void 0,e,!0,(e,i)=>{let r=e.map(e=>(0,E.I$)(this.schema,this.tableConfig,e,t.selection,i));return"first"===this.mode?r[0]:r})})}prepare(e){return this._prepare(e)}_getQuery(){return this.dialect.buildRelationalQueryWithoutPK({fullSchema:this.fullSchema,schema:this.schema,tableNamesMap:this.tableNamesMap,table:this.table,tableConfig:this.tableConfig,queryConfig:this.config,tableAlias:this.tableConfig.tsName})}getSQL(){return this._getQuery().sql}_toSQL(){let e=this._getQuery(),t=this.dialect.sqlToQuery(e.sql);return{query:e,builtQuery:t}}toSQL(){return this._toSQL().builtQuery}authToken;setToken(e){return this.authToken=e,this}execute(){return f.k.startActiveSpan("drizzle.operation",()=>this._prepare().execute(void 0,this.authToken))}}class D extends u.k{constructor(e,t,i,r){super(),this.execute=e,this.sql=t,this.query=i,this.mapBatchResult=r}static [r.i]="PgRaw";getSQL(){return this.sql}getQuery(){return this.query}mapResult(e,t){return t?this.mapBatchResult(e):e}_prepare(){return this}isResponseInArrayMode(){return!1}}class z extends u.k{constructor(e,t,i){super(),this.session=t,this.dialect=i,this.config={view:e}}static [r.i]="PgRefreshMaterializedView";config;concurrently(){if(void 0!==this.config.withNoData)throw Error("Cannot use concurrently and withNoData together");return this.config.concurrently=!0,this}withNoData(){if(void 0!==this.config.concurrently)throw Error("Cannot use concurrently and withNoData together");return this.config.withNoData=!0,this}getSQL(){return this.dialect.buildRefreshMaterializedViewQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e){return f.k.startActiveSpan("drizzle.prepareQuery",()=>this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),void 0,e,!0))}prepare(e){return this._prepare(e)}authToken;setToken(e){return this.authToken=e,this}execute=e=>f.k.startActiveSpan("drizzle.operation",()=>this._prepare().execute(e,this.authToken))}class Q{constructor(e,t,i){if(this.dialect=e,this.session=t,this._=i?{schema:i.schema,fullSchema:i.fullSchema,tableNamesMap:i.tableNamesMap,session:t}:{schema:void 0,fullSchema:{},tableNamesMap:{},session:t},this.query={},this._.schema)for(let[r,n]of Object.entries(this._.schema))this.query[r]=new q(i.fullSchema,this._.schema,this._.tableNamesMap,i.fullSchema[r],n,e,t);this.$cache={invalidate:async e=>{}}}static [r.i]="PgDatabase";query;$with=(e,t)=>{let i=this;return{as:r=>("function"==typeof r&&(r=r(new A(i.dialect))),new Proxy(new a.J(r.getSQL(),t??("getSelectedFields"in r?r.getSelectedFields()??{}:{}),e,!0),new s.b({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"})))}};$count(e,t){return new L({source:e,filters:t,session:this.session})}$cache;with(...e){let t=this;return{select:function(i){return new y({fields:i??void 0,session:t.session,dialect:t.dialect,withList:e})},selectDistinct:function(i){return new y({fields:i??void 0,session:t.session,dialect:t.dialect,withList:e,distinct:!0})},selectDistinctOn:function(i,r){return new y({fields:r??void 0,session:t.session,dialect:t.dialect,withList:e,distinct:{on:i}})},update:function(i){return new I(i,t.session,t.dialect,e)},insert:function(i){return new B(i,t.session,t.dialect,e)},delete:function(i){return new _(i,t.session,t.dialect,e)}}}select(e){return new y({fields:e??void 0,session:this.session,dialect:this.dialect})}selectDistinct(e){return new y({fields:e??void 0,session:this.session,dialect:this.dialect,distinct:!0})}selectDistinctOn(e,t){return new y({fields:t??void 0,session:this.session,dialect:this.dialect,distinct:{on:e}})}update(e){return new I(e,this.session,this.dialect)}insert(e){return new B(e,this.session,this.dialect)}delete(e){return new _(e,this.session,this.dialect)}refreshMaterializedView(e){return new z(e,this.session,this.dialect)}authToken;execute(e){let t="string"==typeof e?c.ll.raw(e):e.getSQL(),i=this.dialect.sqlToQuery(t),r=this.session.prepareQuery(i,void 0,void 0,!1);return new D(()=>r.execute(void 0,this.authToken),t,i,e=>r.mapResult(e,!0))}transaction(e,t){return this.session.transaction(e,t)}}},63431:(e,t,i)=>{"use strict";i.d(t,{nd:()=>o});var r=i(5730),n=i(45944),s=i(68449);class a extends s.p{static [r.i]="PgIntegerBuilder";constructor(e){super(e,"number","PgInteger")}build(e){return new l(e,this.config)}}class l extends n.Kl{static [r.i]="PgInteger";getSQLType(){return"integer"}mapFromDriverValue(e){return"string"==typeof e?Number.parseInt(e):e}}function o(e){return new a(e??"")}},65734:(e,t,i)=>{"use strict";i.d(t,{u:()=>a});var r=i(5730),n=i(96657),s=i(45944);class a extends s.pe{static [r.i]="PgDateColumnBaseBuilder";defaultNow(){return this.default((0,n.ll)`now()`)}}},66246:(e,t,i)=>{let{sha3_512:r}=i(85706),n=24,s=32,a=(e=4,t=Math.random)=>{let i="";for(;i.length<e;)i+=Math.floor(36*t()).toString(36);return i};function l(e){let t=0n;for(let i of e.values())t=(t<<8n)+BigInt(i);return t}let o=(e="")=>l(r(e)).toString(36).slice(1),u=Array.from({length:26},(e,t)=>String.fromCharCode(t+97)),c=e=>u[Math.floor(e()*u.length)],h=({globalObj:e="undefined"!=typeof global?global:"undefined"!=typeof window?window:{},random:t=Math.random}={})=>{let i=Object.keys(e).toString();return o(i.length?i+a(s,t):a(s,t)).substring(0,s)},f=e=>()=>e++,d=0x1c6b1f1f,p=({random:e=Math.random,counter:t=f(Math.floor(e()*d)),length:i=n,fingerprint:r=h({random:e})}={})=>function(){let n=c(e),s=Date.now().toString(36),l=t().toString(36),u=a(i,e),h=`${s+u+l+r}`;return`${n+o(h).substring(1,i)}`},g=p();e.exports.getConstants=()=>({defaultLength:n,bigLength:s}),e.exports.init=p,e.exports.createId=g,e.exports.bufToBigInt=l,e.exports.createCounter=f,e.exports.createFingerprint=h,e.exports.isCuid=(e,{minLength:t=2,maxLength:i=s}={})=>{let r=e.length;return!!("string"==typeof e&&r>=t&&r<=i&&/^[0-9a-z]+$/.test(e))}},67083:(e,t,i)=>{"use strict";i.d(t,{J:()=>s,n:()=>n});var r=i(5730);class n{static [r.i]="Subquery";constructor(e,t,i,r=!1,n=[]){this._={brand:"Subquery",sql:e,selectedFields:t,alias:i,isWith:r,usedTables:n}}}class s extends n{static [r.i]="WithSubquery"}},68449:(e,t,i)=>{"use strict";i.d(t,{p:()=>s});var r=i(5730),n=i(45944);class s extends n.pe{static [r.i]="PgIntColumnBaseBuilder";generatedAlwaysAsIdentity(e){if(e){let{name:t,...i}=e;this.config.generatedIdentity={type:"always",sequenceName:t,sequenceOptions:i}}else this.config.generatedIdentity={type:"always"};return this.config.hasDefault=!0,this.config.notNull=!0,this}generatedByDefaultAsIdentity(e){if(e){let{name:t,...i}=e;this.config.generatedIdentity={type:"byDefault",sequenceName:t,sequenceOptions:i}}else this.config.generatedIdentity={type:"byDefault"};return this.config.hasDefault=!0,this.config.notNull=!0,this}}},69167:(e,t,i)=>{"use strict";function r(e,...t){return e(...t)}i.d(t,{i:()=>r})},72170:(e,t,i)=>{"use strict";i.d(t,{Z5:()=>l,_:()=>d,sH:()=>f});var r=i(5730),n=i(79608),s=i(45944);class a extends s.pe{static [r.i]="PgNumericBuilder";constructor(e,t,i){super(e,"string","PgNumeric"),this.config.precision=t,this.config.scale=i}build(e){return new l(e,this.config)}}class l extends s.Kl{static [r.i]="PgNumeric";precision;scale;constructor(e,t){super(e,t),this.precision=t.precision,this.scale=t.scale}mapFromDriverValue(e){return"string"==typeof e?e:String(e)}getSQLType(){return void 0!==this.precision&&void 0!==this.scale?`numeric(${this.precision}, ${this.scale})`:void 0===this.precision?"numeric":`numeric(${this.precision})`}}class o extends s.pe{static [r.i]="PgNumericNumberBuilder";constructor(e,t,i){super(e,"number","PgNumericNumber"),this.config.precision=t,this.config.scale=i}build(e){return new u(e,this.config)}}class u extends s.Kl{static [r.i]="PgNumericNumber";precision;scale;constructor(e,t){super(e,t),this.precision=t.precision,this.scale=t.scale}mapFromDriverValue(e){return"number"==typeof e?e:Number(e)}mapToDriverValue=String;getSQLType(){return void 0!==this.precision&&void 0!==this.scale?`numeric(${this.precision}, ${this.scale})`:void 0===this.precision?"numeric":`numeric(${this.precision})`}}class c extends s.pe{static [r.i]="PgNumericBigIntBuilder";constructor(e,t,i){super(e,"bigint","PgNumericBigInt"),this.config.precision=t,this.config.scale=i}build(e){return new h(e,this.config)}}class h extends s.Kl{static [r.i]="PgNumericBigInt";precision;scale;constructor(e,t){super(e,t),this.precision=t.precision,this.scale=t.scale}mapFromDriverValue=BigInt;mapToDriverValue=String;getSQLType(){return void 0!==this.precision&&void 0!==this.scale?`numeric(${this.precision}, ${this.scale})`:void 0===this.precision?"numeric":`numeric(${this.precision})`}}function f(e,t){let{name:i,config:r}=(0,n.Ll)(e,t),s=r?.mode;return"number"===s?new o(i,r?.precision,r?.scale):"bigint"===s?new c(i,r?.precision,r?.scale):new a(i,r?.precision,r?.scale)}let d=f},79608:(e,t,i)=>{"use strict";i.d(t,{DV:()=>c,He:()=>function e(t,i){return Object.entries(t).reduce((t,[a,o])=>{if("string"!=typeof a)return t;let u=i?[...i,a]:[a];return(0,n.is)(o,r.V)||(0,n.is)(o,s.Xs)||(0,n.is)(o,s.Xs.Aliased)?t.push({path:u,field:o}):(0,n.is)(o,l.XI)?t.push(...e(o[l.XI.Symbol.Columns],u)):t.push(...e(o,u)),t},[])},Ll:()=>g,Lq:()=>m,XJ:()=>f,YD:()=>d,a6:()=>u,q:()=>h,zN:()=>p});var r=i(10007),n=i(5730),s=i(96657),a=i(67083),l=i(24717),o=i(12772);function u(e,t,i){let a={},o=e.reduce((e,{path:o,field:u},c)=>{let h;h=(0,n.is)(u,r.V)?u:(0,n.is)(u,s.Xs)?u.decoder:u.sql.decoder;let f=e;for(let[e,s]of o.entries())if(e<o.length-1)s in f||(f[s]={}),f=f[s];else{let e=t[c],d=f[s]=null===e?null:h.mapFromDriverValue(e);if(i&&(0,n.is)(u,r.V)&&2===o.length){let e=o[0];e in a?"string"==typeof a[e]&&a[e]!==(0,l.Io)(u.table)&&(a[e]=!1):a[e]=null===d&&(0,l.Io)(u.table)}}return e},{});if(i&&Object.keys(a).length>0)for(let[e,t]of Object.entries(a))"string"!=typeof t||i[t]||(o[e]=null);return o}function c(e,t){let i=Object.keys(e),r=Object.keys(t);if(i.length!==r.length)return!1;for(let[e,t]of i.entries())if(t!==r[e])return!1;return!0}function h(e,t){let i=Object.entries(t).filter(([,e])=>void 0!==e).map(([t,i])=>(0,n.is)(i,s.Xs)||(0,n.is)(i,r.V)?[t,i]:[t,new s.Iw(i,e[l.XI.Symbol.Columns][t])]);if(0===i.length)throw Error("No values to set");return Object.fromEntries(i)}function f(e,t){for(let i of t)for(let t of Object.getOwnPropertyNames(i.prototype))"constructor"!==t&&Object.defineProperty(e.prototype,t,Object.getOwnPropertyDescriptor(i.prototype,t)||Object.create(null))}function d(e){return e[l.XI.Symbol.Columns]}function p(e){return(0,n.is)(e,a.n)?e._.alias:(0,n.is)(e,s.Ss)?e[o.n].name:(0,n.is)(e,s.Xs)?void 0:e[l.XI.Symbol.IsAlias]?e[l.XI.Symbol.Name]:e[l.XI.Symbol.BaseName]}function g(e,t){return{name:"string"==typeof e&&e.length>0?e:"",config:"object"==typeof e?e:t}}function m(e){if("object"!=typeof e||null===e||"Object"!==e.constructor.name)return!1;if("logger"in e){let t=typeof e.logger;return"boolean"===t||"object"===t&&"function"==typeof e.logger.logQuery||"undefined"===t}if("schema"in e){let t=typeof e.schema;return"object"===t||"undefined"===t}if("casing"in e){let t=typeof e.casing;return"string"===t||"undefined"===t}if("mode"in e)return"default"===e.mode&&"planetscale"===e.mode&&void 0===e.mode;if("connection"in e){let t=typeof e.connection;return"string"===t||"object"===t||"undefined"===t}if("client"in e){let t=typeof e.client;return"object"===t||"function"===t||"undefined"===t}return 0===Object.keys(e).length}},85706:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.shake256=t.shake128=t.keccak_512=t.keccak_384=t.keccak_256=t.keccak_224=t.sha3_512=t.sha3_384=t.sha3_256=t.sha3_224=t.Keccak=void 0,t.keccakP=w;let r=i(4147),n=i(51928),s=BigInt(0),a=BigInt(1),l=BigInt(2),o=BigInt(7),u=BigInt(256),c=BigInt(113),h=[],f=[],d=[];for(let e=0,t=a,i=1,r=0;e<24;e++){[i,r]=[r,(2*i+3*r)%5],h.push(2*(5*r+i)),f.push((e+1)*(e+2)/2%64);let n=s;for(let e=0;e<7;e++)(t=(t<<a^(t>>o)*c)%u)&l&&(n^=a<<(a<<BigInt(e))-a);d.push(n)}let p=(0,r.split)(d,!0),g=p[0],m=p[1],y=(e,t,i)=>i>32?(0,r.rotlBH)(e,t,i):(0,r.rotlSH)(e,t,i),b=(e,t,i)=>i>32?(0,r.rotlBL)(e,t,i):(0,r.rotlSL)(e,t,i);function w(e,t=24){let i=new Uint32Array(10);for(let r=24-t;r<24;r++){for(let t=0;t<10;t++)i[t]=e[t]^e[t+10]^e[t+20]^e[t+30]^e[t+40];for(let t=0;t<10;t+=2){let r=(t+8)%10,n=(t+2)%10,s=i[n],a=i[n+1],l=y(s,a,1)^i[r],o=b(s,a,1)^i[r+1];for(let i=0;i<50;i+=10)e[t+i]^=l,e[t+i+1]^=o}let t=e[2],n=e[3];for(let i=0;i<24;i++){let r=f[i],s=y(t,n,r),a=b(t,n,r),l=h[i];t=e[l],n=e[l+1],e[l]=s,e[l+1]=a}for(let t=0;t<50;t+=10){for(let r=0;r<10;r++)i[r]=e[t+r];for(let r=0;r<10;r++)e[t+r]^=~i[(r+2)%10]&i[(r+4)%10]}e[0]^=g[r],e[1]^=m[r]}(0,n.clean)(i)}class v extends n.Hash{constructor(e,t,i,r=!1,s=24){if(super(),this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,this.enableXOF=!1,this.blockLen=e,this.suffix=t,this.outputLen=i,this.enableXOF=r,this.rounds=s,(0,n.anumber)(i),!(0<e&&e<200))throw Error("only keccak-f1600 function is supported");this.state=new Uint8Array(200),this.state32=(0,n.u32)(this.state)}clone(){return this._cloneInto()}keccak(){(0,n.swap32IfBE)(this.state32),w(this.state32,this.rounds),(0,n.swap32IfBE)(this.state32),this.posOut=0,this.pos=0}update(e){(0,n.aexists)(this),e=(0,n.toBytes)(e),(0,n.abytes)(e);let{blockLen:t,state:i}=this,r=e.length;for(let n=0;n<r;){let s=Math.min(t-this.pos,r-n);for(let t=0;t<s;t++)i[this.pos++]^=e[n++];this.pos===t&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;let{state:e,suffix:t,pos:i,blockLen:r}=this;e[i]^=t,(128&t)!=0&&i===r-1&&this.keccak(),e[r-1]^=128,this.keccak()}writeInto(e){(0,n.aexists)(this,!1),(0,n.abytes)(e),this.finish();let t=this.state,{blockLen:i}=this;for(let r=0,n=e.length;r<n;){this.posOut>=i&&this.keccak();let s=Math.min(i-this.posOut,n-r);e.set(t.subarray(this.posOut,this.posOut+s),r),this.posOut+=s,r+=s}return e}xofInto(e){if(!this.enableXOF)throw Error("XOF is not possible for this instance");return this.writeInto(e)}xof(e){return(0,n.anumber)(e),this.xofInto(new Uint8Array(e))}digestInto(e){if((0,n.aoutput)(e,this),this.finished)throw Error("digest() was already called");return this.writeInto(e),this.destroy(),e}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,(0,n.clean)(this.state)}_cloneInto(e){let{blockLen:t,suffix:i,outputLen:r,rounds:n,enableXOF:s}=this;return e||(e=new v(t,i,r,s,n)),e.state32.set(this.state32),e.pos=this.pos,e.posOut=this.posOut,e.finished=this.finished,e.rounds=n,e.suffix=i,e.outputLen=r,e.enableXOF=s,e.destroyed=this.destroyed,e}}t.Keccak=v;let S=(e,t,i)=>(0,n.createHasher)(()=>new v(t,e,i));t.sha3_224=S(6,144,28),t.sha3_256=S(6,136,32),t.sha3_384=S(6,104,48),t.sha3_512=S(6,72,64),t.keccak_224=S(1,144,28),t.keccak_256=S(1,136,32),t.keccak_384=S(1,104,48),t.keccak_512=S(1,72,64);let x=(e,t,i)=>(0,n.createXOFer)((r={})=>new v(t,e,void 0===r.dkLen?i:r.dkLen,!0));t.shake128=x(31,168,16),t.shake256=x(31,136,32)},86214:(e,t,i)=>{"use strict";i.d(t,{E:()=>r});let r=Symbol.for("drizzle:Name")},86890:(e,t,i)=>{"use strict";i.d(t,{Yn:()=>o});var r=i(5730),n=i(24717);function s(e){return(e.replace(/['\u2019]/g,"").match(/[\da-z]+|[A-Z]+(?![a-z])|[A-Z][\da-z]+/g)??[]).map(e=>e.toLowerCase()).join("_")}function a(e){return(e.replace(/['\u2019]/g,"").match(/[\da-z]+|[A-Z]+(?![a-z])|[A-Z][\da-z]+/g)??[]).reduce((e,t,i)=>e+(0===i?t.toLowerCase():`${t[0].toUpperCase()}${t.slice(1)}`),"")}function l(e){return e}class o{static [r.i]="CasingCache";cache={};cachedTables={};convert;constructor(e){this.convert="snake_case"===e?s:"camelCase"===e?a:l}getColumnCasing(e){if(!e.keyAsName)return e.name;let t=e.table[n.XI.Symbol.Schema]??"public",i=e.table[n.XI.Symbol.OriginalName],r=`${t}.${i}.${e.name}`;return this.cache[r]||this.cacheTable(e.table),this.cache[r]}cacheTable(e){let t=e[n.XI.Symbol.Schema]??"public",i=e[n.XI.Symbol.OriginalName],r=`${t}.${i}`;if(!this.cachedTables[r]){for(let t of Object.values(e[n.XI.Symbol.Columns])){let e=`${r}.${t.name}`;this.cache[e]=this.convert(t.name)}this.cachedTables[r]=!0}}clearCache(){this.cache={},this.cachedTables={}}}},89685:(e,t,i)=>{"use strict";i.d(t,{Hs:()=>d,Ht:()=>o,h_:()=>u,oG:()=>h,ug:()=>f,yY:()=>p});var r=i(10007),n=i(5730),s=i(96657),a=i(24717),l=i(12772);class o{constructor(e){this.table=e}static [n.i]="ColumnAliasProxyHandler";get(e,t){return"table"===t?this.table:e[t]}}class u{constructor(e,t){this.alias=e,this.replaceOriginalName=t}static [n.i]="TableAliasProxyHandler";get(e,t){if(t===a.XI.Symbol.IsAlias)return!0;if(t===a.XI.Symbol.Name||this.replaceOriginalName&&t===a.XI.Symbol.OriginalName)return this.alias;if(t===l.n)return{...e[l.n],name:this.alias,isAlias:!0};if(t===a.XI.Symbol.Columns){let t=e[a.XI.Symbol.Columns];if(!t)return t;let i={};return Object.keys(t).map(r=>{i[r]=new Proxy(t[r],new o(new Proxy(e,this)))}),i}let i=e[t];return(0,n.is)(i,r.V)?new Proxy(i,new o(new Proxy(e,this))):i}}class c{constructor(e){this.alias=e}static [n.i]=null;get(e,t){return"sourceTable"===t?h(e.sourceTable,this.alias):e[t]}}function h(e,t){return new Proxy(e,new u(t,!1))}function f(e,t){return new Proxy(e,new o(new Proxy(e.table,new u(t,!1))))}function d(e,t){return new s.Xs.Aliased(p(e.sql,t),e.fieldAlias)}function p(e,t){return s.ll.join(e.queryChunks.map(e=>(0,n.is)(e,r.V)?f(e,t):(0,n.is)(e,s.Xs)?p(e,t):(0,n.is)(e,s.Xs.Aliased)?d(e,t):e))}},89697:(e,t,i)=>{"use strict";i.d(t,{Pe:()=>u});var r=i(96657),n=i(5730),s=i(45944);class a{constructor(e,t){this.unique=e,this.name=t}static [n.i]="PgIndexBuilderOn";on(...e){return new l(e.map(e=>{if((0,n.is)(e,r.Xs))return e;let t=new s.ae(e.name,!!e.keyAsName,e.columnType,e.indexConfig);return e.indexConfig=JSON.parse(JSON.stringify(e.defaultConfig)),t}),this.unique,!1,this.name)}onOnly(...e){return new l(e.map(e=>{if((0,n.is)(e,r.Xs))return e;let t=new s.ae(e.name,!!e.keyAsName,e.columnType,e.indexConfig);return e.indexConfig=e.defaultConfig,t}),this.unique,!0,this.name)}using(e,...t){return new l(t.map(e=>{if((0,n.is)(e,r.Xs))return e;let t=new s.ae(e.name,!!e.keyAsName,e.columnType,e.indexConfig);return e.indexConfig=JSON.parse(JSON.stringify(e.defaultConfig)),t}),this.unique,!0,this.name,e)}}class l{static [n.i]="PgIndexBuilder";config;constructor(e,t,i,r,n="btree"){this.config={name:r,columns:e,unique:t,only:i,method:n}}concurrently(){return this.config.concurrently=!0,this}with(e){return this.config.with=e,this}where(e){return this.config.where=e,this}build(e){return new o(this.config,e)}}class o{static [n.i]="PgIndex";config;constructor(e,t){this.config={...e,table:t}}}function u(e){return new a(!1,e)}},91036:(e,t,i)=>{"use strict";i.d(t,{Am:()=>s,Wx:()=>a});var r=i(5730),n=i(86214);function s(e){return new o(e)}function a(e,t){return`${e[n.E]}_${t.join("_")}_unique`}class l{constructor(e,t){this.name=t,this.columns=e}static [r.i]="PgUniqueConstraintBuilder";columns;nullsNotDistinctConfig=!1;nullsNotDistinct(){return this.nullsNotDistinctConfig=!0,this}build(e){return new u(e,this.columns,this.nullsNotDistinctConfig,this.name)}}class o{static [r.i]="PgUniqueOnConstraintBuilder";name;constructor(e){this.name=e}on(...e){return new l(e,this.name)}}class u{constructor(e,t,i,r){this.table=e,this.columns=t,this.name=r??a(this.table,this.columns.map(e=>e.name)),this.nullsNotDistinct=i}static [r.i]="PgUniqueConstraint";columns;name;nullsNotDistinct=!1;getName(){return this.name}}},92768:(e,t,i)=>{"use strict";i.d(t,{mu:()=>eX,cJ:()=>eM});var r=i(5730),n=i(24717),s=i(79608),a=i(45944),l=i(68449);class o extends l.p{static [r.i]="PgBigInt53Builder";constructor(e){super(e,"number","PgBigInt53")}build(e){return new u(e,this.config)}}class u extends a.Kl{static [r.i]="PgBigInt53";getSQLType(){return"bigint"}mapFromDriverValue(e){return"number"==typeof e?e:Number(e)}}class c extends l.p{static [r.i]="PgBigInt64Builder";constructor(e){super(e,"bigint","PgBigInt64")}build(e){return new h(e,this.config)}}class h extends a.Kl{static [r.i]="PgBigInt64";getSQLType(){return"bigint"}mapFromDriverValue(e){return BigInt(e)}}function f(e,t){let{name:i,config:r}=(0,s.Ll)(e,t);return"number"===r.mode?new o(i):new c(i)}class d extends a.pe{static [r.i]="PgBigSerial53Builder";constructor(e){super(e,"number","PgBigSerial53"),this.config.hasDefault=!0,this.config.notNull=!0}build(e){return new p(e,this.config)}}class p extends a.Kl{static [r.i]="PgBigSerial53";getSQLType(){return"bigserial"}mapFromDriverValue(e){return"number"==typeof e?e:Number(e)}}class g extends a.pe{static [r.i]="PgBigSerial64Builder";constructor(e){super(e,"bigint","PgBigSerial64"),this.config.hasDefault=!0}build(e){return new m(e,this.config)}}class m extends a.Kl{static [r.i]="PgBigSerial64";getSQLType(){return"bigserial"}mapFromDriverValue(e){return BigInt(e)}}function y(e,t){let{name:i,config:r}=(0,s.Ll)(e,t);return"number"===r.mode?new d(i):new g(i)}var b=i(54693);class w extends a.pe{static [r.i]="PgCharBuilder";constructor(e,t){super(e,"string","PgChar"),this.config.length=t.length,this.config.enumValues=t.enum}build(e){return new v(e,this.config)}}class v extends a.Kl{static [r.i]="PgChar";length=this.config.length;enumValues=this.config.enumValues;getSQLType(){return void 0===this.length?"char":`char(${this.length})`}}function S(e,t={}){let{name:i,config:r}=(0,s.Ll)(e,t);return new w(i,r)}class x extends a.pe{static [r.i]="PgCidrBuilder";constructor(e){super(e,"string","PgCidr")}build(e){return new T(e,this.config)}}class T extends a.Kl{static [r.i]="PgCidr";getSQLType(){return"cidr"}}function N(e){return new x(e??"")}class P extends a.pe{static [r.i]="PgCustomColumnBuilder";constructor(e,t,i){super(e,"custom","PgCustomColumn"),this.config.fieldConfig=t,this.config.customTypeParams=i}build(e){return new $(e,this.config)}}class $ extends a.Kl{static [r.i]="PgCustomColumn";sqlName;mapTo;mapFrom;constructor(e,t){super(e,t),this.sqlName=t.customTypeParams.dataType(t.fieldConfig),this.mapTo=t.customTypeParams.toDriver,this.mapFrom=t.customTypeParams.fromDriver}getSQLType(){return this.sqlName}mapFromDriverValue(e){return"function"==typeof this.mapFrom?this.mapFrom(e):e}mapToDriverValue(e){return"function"==typeof this.mapTo?this.mapTo(e):e}}function C(e){return(t,i)=>{let{name:r,config:n}=(0,s.Ll)(t,i);return new P(r,n,e)}}var A=i(37193);class I extends a.pe{static [r.i]="PgDoublePrecisionBuilder";constructor(e){super(e,"number","PgDoublePrecision")}build(e){return new O(e,this.config)}}class O extends a.Kl{static [r.i]="PgDoublePrecision";getSQLType(){return"double precision"}mapFromDriverValue(e){return"string"==typeof e?Number.parseFloat(e):e}}function B(e){return new I(e??"")}class k extends a.pe{static [r.i]="PgInetBuilder";constructor(e){super(e,"string","PgInet")}build(e){return new _(e,this.config)}}class _ extends a.Kl{static [r.i]="PgInet";getSQLType(){return"inet"}}function L(e){return new k(e??"")}var E=i(63431);class q extends a.pe{static [r.i]="PgIntervalBuilder";constructor(e,t){super(e,"string","PgInterval"),this.config.intervalConfig=t}build(e){return new j(e,this.config)}}class j extends a.Kl{static [r.i]="PgInterval";fields=this.config.intervalConfig.fields;precision=this.config.intervalConfig.precision;getSQLType(){let e=this.fields?` ${this.fields}`:"",t=this.precision?`(${this.precision})`:"";return`interval${e}${t}`}}function D(e,t={}){let{name:i,config:r}=(0,s.Ll)(e,t);return new q(i,r)}var z=i(34359),Q=i(34509);class F extends a.pe{static [r.i]="PgLineBuilder";constructor(e){super(e,"array","PgLine")}build(e){return new U(e,this.config)}}class U extends a.Kl{static [r.i]="PgLine";getSQLType(){return"line"}mapFromDriverValue(e){let[t,i,r]=e.slice(1,-1).split(",");return[Number.parseFloat(t),Number.parseFloat(i),Number.parseFloat(r)]}mapToDriverValue(e){return`{${e[0]},${e[1]},${e[2]}}`}}class V extends a.pe{static [r.i]="PgLineABCBuilder";constructor(e){super(e,"json","PgLineABC")}build(e){return new X(e,this.config)}}class X extends a.Kl{static [r.i]="PgLineABC";getSQLType(){return"line"}mapFromDriverValue(e){let[t,i,r]=e.slice(1,-1).split(",");return{a:Number.parseFloat(t),b:Number.parseFloat(i),c:Number.parseFloat(r)}}mapToDriverValue(e){return`{${e.a},${e.b},${e.c}}`}}function M(e,t){let{name:i,config:r}=(0,s.Ll)(e,t);return r?.mode&&"tuple"!==r.mode?new V(i):new F(i)}class K extends a.pe{static [r.i]="PgMacaddrBuilder";constructor(e){super(e,"string","PgMacaddr")}build(e){return new R(e,this.config)}}class R extends a.Kl{static [r.i]="PgMacaddr";getSQLType(){return"macaddr"}}function J(e){return new K(e??"")}class H extends a.pe{static [r.i]="PgMacaddr8Builder";constructor(e){super(e,"string","PgMacaddr8")}build(e){return new W(e,this.config)}}class W extends a.Kl{static [r.i]="PgMacaddr8";getSQLType(){return"macaddr8"}}function G(e){return new H(e??"")}var Y=i(72170);class Z extends a.pe{static [r.i]="PgPointTupleBuilder";constructor(e){super(e,"array","PgPointTuple")}build(e){return new ee(e,this.config)}}class ee extends a.Kl{static [r.i]="PgPointTuple";getSQLType(){return"point"}mapFromDriverValue(e){if("string"==typeof e){let[t,i]=e.slice(1,-1).split(",");return[Number.parseFloat(t),Number.parseFloat(i)]}return[e.x,e.y]}mapToDriverValue(e){return`(${e[0]},${e[1]})`}}class et extends a.pe{static [r.i]="PgPointObjectBuilder";constructor(e){super(e,"json","PgPointObject")}build(e){return new ei(e,this.config)}}class ei extends a.Kl{static [r.i]="PgPointObject";getSQLType(){return"point"}mapFromDriverValue(e){if("string"==typeof e){let[t,i]=e.slice(1,-1).split(",");return{x:Number.parseFloat(t),y:Number.parseFloat(i)}}return e}mapToDriverValue(e){return`(${e.x},${e.y})`}}function er(e,t){let{name:i,config:r}=(0,s.Ll)(e,t);return r?.mode&&"tuple"!==r.mode?new et(i):new Z(i)}function en(e,t){let i=new DataView(new ArrayBuffer(8));for(let r=0;r<8;r++)i.setUint8(r,e[t+r]);return i.getFloat64(0,!0)}function es(e){let t=function(e){let t=[];for(let i=0;i<e.length;i+=2)t.push(Number.parseInt(e.slice(i,i+2),16));return new Uint8Array(t)}(e),i=0,r=t[0];i+=1;let n=new DataView(t.buffer),s=n.getUint32(i,1===r);if(i+=4,0x20000000&s&&(n.getUint32(i,1===r),i+=4),(65535&s)==1){let e=en(t,i),r=en(t,i+=8);return i+=8,[e,r]}throw Error("Unsupported geometry type")}class ea extends a.pe{static [r.i]="PgGeometryBuilder";constructor(e){super(e,"array","PgGeometry")}build(e){return new el(e,this.config)}}class el extends a.Kl{static [r.i]="PgGeometry";getSQLType(){return"geometry(point)"}mapFromDriverValue(e){return es(e)}mapToDriverValue(e){return`point(${e[0]} ${e[1]})`}}class eo extends a.pe{static [r.i]="PgGeometryObjectBuilder";constructor(e){super(e,"json","PgGeometryObject")}build(e){return new eu(e,this.config)}}class eu extends a.Kl{static [r.i]="PgGeometryObject";getSQLType(){return"geometry(point)"}mapFromDriverValue(e){let t=es(e);return{x:t[0],y:t[1]}}mapToDriverValue(e){return`point(${e.x} ${e.y})`}}function ec(e,t){let{name:i,config:r}=(0,s.Ll)(e,t);return r?.mode&&"tuple"!==r.mode?new eo(i):new ea(i)}class eh extends a.pe{static [r.i]="PgRealBuilder";constructor(e,t){super(e,"number","PgReal"),this.config.length=t}build(e){return new ef(e,this.config)}}class ef extends a.Kl{static [r.i]="PgReal";constructor(e,t){super(e,t)}getSQLType(){return"real"}mapFromDriverValue=e=>"string"==typeof e?Number.parseFloat(e):e}function ed(e){return new eh(e??"")}class ep extends a.pe{static [r.i]="PgSerialBuilder";constructor(e){super(e,"number","PgSerial"),this.config.hasDefault=!0,this.config.notNull=!0}build(e){return new eg(e,this.config)}}class eg extends a.Kl{static [r.i]="PgSerial";getSQLType(){return"serial"}}function em(e){return new ep(e??"")}class ey extends l.p{static [r.i]="PgSmallIntBuilder";constructor(e){super(e,"number","PgSmallInt")}build(e){return new eb(e,this.config)}}class eb extends a.Kl{static [r.i]="PgSmallInt";getSQLType(){return"smallint"}mapFromDriverValue=e=>"string"==typeof e?Number(e):e}function ew(e){return new ey(e??"")}class ev extends a.pe{static [r.i]="PgSmallSerialBuilder";constructor(e){super(e,"number","PgSmallSerial"),this.config.hasDefault=!0,this.config.notNull=!0}build(e){return new eS(e,this.config)}}class eS extends a.Kl{static [r.i]="PgSmallSerial";getSQLType(){return"smallserial"}}function ex(e){return new ev(e??"")}var eT=i(29334),eN=i(9528),eP=i(9253),e$=i(9594);class eC extends a.pe{static [r.i]="PgVarcharBuilder";constructor(e,t){super(e,"string","PgVarchar"),this.config.length=t.length,this.config.enumValues=t.enum}build(e){return new eA(e,this.config)}}class eA extends a.Kl{static [r.i]="PgVarchar";length=this.config.length;enumValues=this.config.enumValues;getSQLType(){return void 0===this.length?"varchar":`varchar(${this.length})`}}function eI(e,t={}){let{name:i,config:r}=(0,s.Ll)(e,t);return new eC(i,r)}class eO extends a.pe{static [r.i]="PgBinaryVectorBuilder";constructor(e,t){super(e,"string","PgBinaryVector"),this.config.dimensions=t.dimensions}build(e){return new eB(e,this.config)}}class eB extends a.Kl{static [r.i]="PgBinaryVector";dimensions=this.config.dimensions;getSQLType(){return`bit(${this.dimensions})`}}function ek(e,t){let{name:i,config:r}=(0,s.Ll)(e,t);return new eO(i,r)}class e_ extends a.pe{static [r.i]="PgHalfVectorBuilder";constructor(e,t){super(e,"array","PgHalfVector"),this.config.dimensions=t.dimensions}build(e){return new eL(e,this.config)}}class eL extends a.Kl{static [r.i]="PgHalfVector";dimensions=this.config.dimensions;getSQLType(){return`halfvec(${this.dimensions})`}mapToDriverValue(e){return JSON.stringify(e)}mapFromDriverValue(e){return e.slice(1,-1).split(",").map(e=>Number.parseFloat(e))}}function eE(e,t){let{name:i,config:r}=(0,s.Ll)(e,t);return new e_(i,r)}class eq extends a.pe{static [r.i]="PgSparseVectorBuilder";constructor(e,t){super(e,"string","PgSparseVector"),this.config.dimensions=t.dimensions}build(e){return new ej(e,this.config)}}class ej extends a.Kl{static [r.i]="PgSparseVector";dimensions=this.config.dimensions;getSQLType(){return`sparsevec(${this.dimensions})`}}function eD(e,t){let{name:i,config:r}=(0,s.Ll)(e,t);return new eq(i,r)}class ez extends a.pe{static [r.i]="PgVectorBuilder";constructor(e,t){super(e,"array","PgVector"),this.config.dimensions=t.dimensions}build(e){return new eQ(e,this.config)}}class eQ extends a.Kl{static [r.i]="PgVector";dimensions=this.config.dimensions;getSQLType(){return`vector(${this.dimensions})`}mapToDriverValue(e){return JSON.stringify(e)}mapFromDriverValue(e){return e.slice(1,-1).split(",").map(e=>Number.parseFloat(e))}}function eF(e,t){let{name:i,config:r}=(0,s.Ll)(e,t);return new ez(i,r)}let eU=Symbol.for("drizzle:PgInlineForeignKeys"),eV=Symbol.for("drizzle:EnableRLS");class eX extends n.XI{static [r.i]="PgTable";static Symbol=Object.assign({},n.XI.Symbol,{InlineForeignKeys:eU,EnableRLS:eV});[eU]=[];[eV]=!1;[n.XI.Symbol.ExtraConfigBuilder]=void 0;[n.XI.Symbol.ExtraConfigColumns]={}}let eM=(e,t,i)=>(function(e,t,i,r,s=e){let a=new eX(e,r,s),l="function"==typeof t?t({bigint:f,bigserial:y,boolean:b.zM,char:S,cidr:N,customType:C,date:A.p6,doublePrecision:B,inet:L,integer:E.nd,interval:D,json:z.Pq,jsonb:Q.Fx,line:M,macaddr:J,macaddr8:G,numeric:Y.sH,point:er,geometry:ec,real:ed,serial:em,smallint:ew,smallserial:ex,text:eT.Qq,time:eN.kB,timestamp:eP.vE,uuid:e$.uR,varchar:eI,bit:ek,halfvec:eE,sparsevec:eD,vector:eF}):t,o=Object.fromEntries(Object.entries(l).map(([e,t])=>{t.setName(e);let i=t.build(a);return a[eU].push(...t.buildForeignKeys(i,a)),[e,i]})),u=Object.fromEntries(Object.entries(l).map(([e,t])=>(t.setName(e),[e,t.buildExtraConfigColumn(a)]))),c=Object.assign(a,o);return c[n.XI.Symbol.Columns]=o,c[n.XI.Symbol.ExtraConfigColumns]=u,i&&(c[eX.Symbol.ExtraConfigBuilder]=i),Object.assign(c,{enableRLS:()=>(c[eX.Symbol.EnableRLS]=!0,c)})})(e,t,i,void 0)},94634:(e,t,i)=>{"use strict";i.d(t,{AU:()=>f,B3:()=>C,KJ:()=>x,KL:()=>b,Pe:()=>v,RK:()=>$,RO:()=>p,RV:()=>y,Tq:()=>T,Uo:()=>c,eq:()=>o,gt:()=>d,kZ:()=>w,lt:()=>g,mj:()=>P,ne:()=>u,o8:()=>N,or:()=>h,q1:()=>A,t2:()=>S,wJ:()=>m});var r=i(10007),n=i(5730),s=i(24717),a=i(96657);function l(e,t){return!(0,a.eG)(t)||(0,a.qt)(e)||(0,n.is)(e,a.Iw)||(0,n.is)(e,a.Or)||(0,n.is)(e,r.V)||(0,n.is)(e,s.XI)||(0,n.is)(e,a.Ss)?e:new a.Iw(e,t)}let o=(e,t)=>(0,a.ll)`${e} = ${l(t,e)}`,u=(e,t)=>(0,a.ll)`${e} <> ${l(t,e)}`;function c(...e){let t=e.filter(e=>void 0!==e);if(0!==t.length)return new a.Xs(1===t.length?t:[new a.DJ("("),a.ll.join(t,new a.DJ(" and ")),new a.DJ(")")])}function h(...e){let t=e.filter(e=>void 0!==e);if(0!==t.length)return new a.Xs(1===t.length?t:[new a.DJ("("),a.ll.join(t,new a.DJ(" or ")),new a.DJ(")")])}function f(e){return(0,a.ll)`not ${e}`}let d=(e,t)=>(0,a.ll)`${e} > ${l(t,e)}`,p=(e,t)=>(0,a.ll)`${e} >= ${l(t,e)}`,g=(e,t)=>(0,a.ll)`${e} < ${l(t,e)}`,m=(e,t)=>(0,a.ll)`${e} <= ${l(t,e)}`;function y(e,t){return Array.isArray(t)?0===t.length?(0,a.ll)`false`:(0,a.ll)`${e} in ${t.map(t=>l(t,e))}`:(0,a.ll)`${e} in ${l(t,e)}`}function b(e,t){return Array.isArray(t)?0===t.length?(0,a.ll)`true`:(0,a.ll)`${e} not in ${t.map(t=>l(t,e))}`:(0,a.ll)`${e} not in ${l(t,e)}`}function w(e){return(0,a.ll)`${e} is null`}function v(e){return(0,a.ll)`${e} is not null`}function S(e){return(0,a.ll)`exists ${e}`}function x(e){return(0,a.ll)`not exists ${e}`}function T(e,t,i){return(0,a.ll)`${e} between ${l(t,e)} and ${l(i,e)}`}function N(e,t,i){return(0,a.ll)`${e} not between ${l(t,e)} and ${l(i,e)}`}function P(e,t){return(0,a.ll)`${e} like ${t}`}function $(e,t){return(0,a.ll)`${e} not like ${t}`}function C(e,t){return(0,a.ll)`${e} ilike ${t}`}function A(e,t){return(0,a.ll)`${e} not ilike ${t}`}},96657:(e,t,i)=>{"use strict";i.d(t,{Iw:()=>T,Or:()=>P,Xs:()=>b,DJ:()=>y,Ss:()=>A,Ct:()=>$,eG:()=>v,qt:()=>m,ll:()=>N});var r=i(5730),n=i(45944);class s extends n.pe{static [r.i]="PgEnumObjectColumnBuilder";constructor(e,t){super(e,"string","PgEnumObjectColumn"),this.config.enum=t}build(e){return new a(e,this.config)}}class a extends n.Kl{static [r.i]="PgEnumObjectColumn";enum;enumValues=this.config.enum.enumValues;constructor(e,t){super(e,t),this.enum=t.enum}getSQLType(){return this.enum.enumName}}let l=Symbol.for("drizzle:isPgEnum");class o extends n.pe{static [r.i]="PgEnumColumnBuilder";constructor(e,t){super(e,"string","PgEnumColumn"),this.config.enum=t}build(e){return new u(e,this.config)}}class u extends n.Kl{static [r.i]="PgEnumColumn";enum=this.config.enum;enumValues=this.config.enum.enumValues;constructor(e,t){super(e,t),this.enum=t.enum}getSQLType(){return this.enum.enumName}}var c=i(67083),h=i(99511),f=i(12772),d=i(10007),p=i(24717);class g{static [r.i]=null}function m(e){return null!=e&&"function"==typeof e.getSQL}class y{static [r.i]="StringChunk";value;constructor(e){this.value=Array.isArray(e)?e:[e]}getSQL(){return new b([this])}}class b{constructor(e){for(let t of(this.queryChunks=e,e))if((0,r.is)(t,p.XI)){let e=t[p.XI.Symbol.Schema];this.usedTables.push(void 0===e?t[p.XI.Symbol.Name]:e+"."+t[p.XI.Symbol.Name])}}static [r.i]="SQL";decoder=S;shouldInlineParams=!1;usedTables=[];append(e){return this.queryChunks.push(...e.queryChunks),this}toQuery(e){return h.k.startActiveSpan("drizzle.buildSQL",t=>{let i=this.buildQueryFromSourceParams(this.queryChunks,e);return t?.setAttributes({"drizzle.query.text":i.sql,"drizzle.query.params":JSON.stringify(i.params)}),i})}buildQueryFromSourceParams(e,t){let i=Object.assign({},t,{inlineParams:t.inlineParams||this.shouldInlineParams,paramStartIndex:t.paramStartIndex||{value:0}}),{casing:n,escapeName:s,escapeParam:a,prepareTyping:o,inlineParams:u,paramStartIndex:h}=i;var g=e.map(e=>{if((0,r.is)(e,y))return{sql:e.value.join(""),params:[]};if((0,r.is)(e,w))return{sql:s(e.value),params:[]};if(void 0===e)return{sql:"",params:[]};if(Array.isArray(e)){let t=[new y("(")];for(let[i,r]of e.entries())t.push(r),i<e.length-1&&t.push(new y(", "));return t.push(new y(")")),this.buildQueryFromSourceParams(t,i)}if((0,r.is)(e,b))return this.buildQueryFromSourceParams(e.queryChunks,{...i,inlineParams:u||e.shouldInlineParams});if((0,r.is)(e,p.XI)){let t=e[p.XI.Symbol.Schema],i=e[p.XI.Symbol.Name];return{sql:void 0===t||e[p.HE]?s(i):s(t)+"."+s(i),params:[]}}if((0,r.is)(e,d.V)){let i=n.getColumnCasing(e);if("indexes"===t.invokeSource)return{sql:s(i),params:[]};let r=e.table[p.XI.Symbol.Schema];return{sql:e.table[p.HE]||void 0===r?s(e.table[p.XI.Symbol.Name])+"."+s(i):s(r)+"."+s(e.table[p.XI.Symbol.Name])+"."+s(i),params:[]}}if((0,r.is)(e,A)){let t=e[f.n].schema,i=e[f.n].name;return{sql:void 0===t||e[f.n].isAlias?s(i):s(t)+"."+s(i),params:[]}}if((0,r.is)(e,T)){if((0,r.is)(e.value,P))return{sql:a(h.value++,e),params:[e],typings:["none"]};let t=null===e.value?null:e.encoder.mapToDriverValue(e.value);if((0,r.is)(t,b))return this.buildQueryFromSourceParams([t],i);if(u)return{sql:this.mapInlineParam(t,i),params:[]};let n=["none"];return o&&(n=[o(e.encoder)]),{sql:a(h.value++,t),params:[t],typings:n}}return(0,r.is)(e,P)?{sql:a(h.value++,e),params:[e],typings:["none"]}:(0,r.is)(e,b.Aliased)&&void 0!==e.fieldAlias?{sql:s(e.fieldAlias),params:[]}:(0,r.is)(e,c.n)?e._.isWith?{sql:s(e._.alias),params:[]}:this.buildQueryFromSourceParams([new y("("),e._.sql,new y(") "),new w(e._.alias)],i):e&&"function"==typeof e&&l in e&&!0===e[l]?e.schema?{sql:s(e.schema)+"."+s(e.enumName),params:[]}:{sql:s(e.enumName),params:[]}:m(e)?e.shouldOmitSQLParens?.()?this.buildQueryFromSourceParams([e.getSQL()],i):this.buildQueryFromSourceParams([new y("("),e.getSQL(),new y(")")],i):u?{sql:this.mapInlineParam(e,i),params:[]}:{sql:a(h.value++,e),params:[e],typings:["none"]}});let v={sql:"",params:[]};for(let e of g)v.sql+=e.sql,v.params.push(...e.params),e.typings?.length&&(v.typings||(v.typings=[]),v.typings.push(...e.typings));return v}mapInlineParam(e,{escapeString:t}){if(null===e)return"null";if("number"==typeof e||"boolean"==typeof e)return e.toString();if("string"==typeof e)return t(e);if("object"==typeof e){let i=e.toString();return"[object Object]"===i?t(JSON.stringify(e)):t(i)}throw Error("Unexpected param value: "+e)}getSQL(){return this}as(e){return void 0===e?this:new b.Aliased(this,e)}mapWith(e){return this.decoder="function"==typeof e?{mapFromDriverValue:e}:e,this}inlineParams(){return this.shouldInlineParams=!0,this}if(e){return e?this:void 0}}class w{constructor(e){this.value=e}static [r.i]="Name";brand;getSQL(){return new b([this])}}function v(e){return"object"==typeof e&&null!==e&&"mapToDriverValue"in e&&"function"==typeof e.mapToDriverValue}let S={mapFromDriverValue:e=>e},x={mapToDriverValue:e=>e};({...S,...x});class T{constructor(e,t=x){this.value=e,this.encoder=t}static [r.i]="Param";brand;getSQL(){return new b([this])}}function N(e,...t){let i=[];for(let[r,n]of((t.length>0||e.length>0&&""!==e[0])&&i.push(new y(e[0])),t.entries()))i.push(n,new y(e[r+1]));return new b(i)}(e=>{e.empty=function(){return new b([])},e.fromList=function(e){return new b(e)},e.raw=function(e){return new b([new y(e)])},e.join=function(e,t){let i=[];for(let[r,n]of e.entries())r>0&&void 0!==t&&i.push(t),i.push(n);return new b(i)},e.identifier=function(e){return new w(e)},e.placeholder=function(e){return new P(e)},e.param=function(e,t){return new T(e,t)}})(N||(N={})),(e=>{class t{constructor(e,t){this.sql=e,this.fieldAlias=t}static [r.i]="SQL.Aliased";isSelectionField=!1;getSQL(){return this.sql}clone(){return new t(this.sql,this.fieldAlias)}}e.Aliased=t})(b||(b={}));class P{constructor(e){this.name=e}static [r.i]="Placeholder";getSQL(){return new b([this])}}function $(e,t){return e.map(e=>{if((0,r.is)(e,P)){if(!(e.name in t))throw Error(`No value for placeholder "${e.name}" was provided`);return t[e.name]}if((0,r.is)(e,T)&&(0,r.is)(e.value,P)){if(!(e.value.name in t))throw Error(`No value for placeholder "${e.value.name}" was provided`);return e.encoder.mapToDriverValue(t[e.value.name])}return e})}let C=Symbol.for("drizzle:IsDrizzleView");class A{static [r.i]="View";[f.n];[C]=!0;constructor({name:e,schema:t,selectedFields:i,query:r}){this[f.n]={name:e,originalName:e,schema:t,selectedFields:i,query:r,isExisting:!r,isAlias:!1}}getSQL(){return new b([this])}}d.V.prototype.getSQL=function(){return new b([this])},p.XI.prototype.getSQL=function(){return new b([this])},c.n.prototype.getSQL=function(){return new b([this])}},99511:(e,t,i)=>{"use strict";let r,n;i.d(t,{k:()=>a});var s=i(69167);let a={startActiveSpan:(e,t)=>r?(n||(n=r.trace.getTracer("drizzle-orm","0.44.0")),(0,s.i)((i,r)=>r.startActiveSpan(e,e=>{try{return t(e)}catch(t){throw e.setStatus({code:i.SpanStatusCode.ERROR,message:t instanceof Error?t.message:"Unknown error"}),t}finally{e.end()}}),r,n)):t()}}};