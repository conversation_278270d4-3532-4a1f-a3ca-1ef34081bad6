"use strict";exports.id=1057,exports.ids=[1057],exports.modules={163:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return i}});let i=r(71042).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8704:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTTPAccessErrorStatus:function(){return r},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return n},getAccessFallbackErrorTypeByStatus:function(){return o},getAccessFallbackHTTPStatus:function(){return a},isHTTPAccessFallbackError:function(){return s}});let r={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},i=new Set(Object.values(r)),n="NEXT_HTTP_ERROR_FALLBACK";function s(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===n&&i.has(Number(r))}function a(e){return Number(e.digest.split(";")[1])}function o(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},10189:(e,t,r)=>{r.d(t,{A:()=>i});function i(e){return{id:"credentials",name:"Credentials",type:"credentials",credentials:{},authorize:()=>null,options:e}}},19443:(e,t,r)=>{let i,n,s,a,o;r.d(t,{Ay:()=>ad});var l={};r.r(l),r.d(l,{q:()=>tW,l:()=>tF});var c=function(e,t,r,i,n){if("m"===i)throw TypeError("Private method is not writable");if("a"===i&&!n)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===i?n.call(e,r):n?n.value=r:t.set(e,r),r},u=function(e,t,r,i){if("a"===r&&!i)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!i:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?i:"a"===r?i.call(e):i?i.value:t.get(e)};function d(e){let t=e?"__Secure-":"";return{sessionToken:{name:`${t}authjs.session-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},callbackUrl:{name:`${t}authjs.callback-url`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},csrfToken:{name:`${e?"__Host-":""}authjs.csrf-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},pkceCodeVerifier:{name:`${t}authjs.pkce.code_verifier`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},state:{name:`${t}authjs.state`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},nonce:{name:`${t}authjs.nonce`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},webauthnChallenge:{name:`${t}authjs.challenge`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}}}}class f{constructor(e,t,r){if(rb.add(this),rw.set(this,{}),rx.set(this,void 0),rv.set(this,void 0),c(this,rv,r,"f"),c(this,rx,e,"f"),!t)return;let{name:i}=e;for(let[e,r]of Object.entries(t))e.startsWith(i)&&r&&(u(this,rw,"f")[e]=r)}get value(){return Object.keys(u(this,rw,"f")).sort((e,t)=>parseInt(e.split(".").pop()||"0")-parseInt(t.split(".").pop()||"0")).map(e=>u(this,rw,"f")[e]).join("")}chunk(e,t){let r=u(this,rb,"m",rS).call(this);for(let i of u(this,rb,"m",r_).call(this,{name:u(this,rx,"f").name,value:e,options:{...u(this,rx,"f").options,...t}}))r[i.name]=i;return Object.values(r)}clean(){return Object.values(u(this,rb,"m",rS).call(this))}}rw=new WeakMap,rx=new WeakMap,rv=new WeakMap,rb=new WeakSet,r_=function(e){let t=Math.ceil(e.value.length/3936);if(1===t)return u(this,rw,"f")[e.name]=e.value,[e];let r=[];for(let i=0;i<t;i++){let t=`${e.name}.${i}`,n=e.value.substr(3936*i,3936);r.push({...e,name:t,value:n}),u(this,rw,"f")[t]=n}return u(this,rv,"f").debug("CHUNKING_SESSION_COOKIE",{message:"Session cookie exceeds allowed 4096 bytes.",emptyCookieSize:160,valueSize:e.value.length,chunks:r.map(e=>e.value.length+160)}),r},rS=function(){let e={};for(let t in u(this,rw,"f"))delete u(this,rw,"f")?.[t],e[t]={name:t,value:"",options:{...u(this,rx,"f").options,maxAge:0}};return e};class h extends Error{constructor(e,t){e instanceof Error?super(void 0,{cause:{err:e,...e.cause,...t}}):"string"==typeof e?(t instanceof Error&&(t={err:t,...t.cause}),super(e,t)):super(void 0,e),this.name=this.constructor.name,this.type=this.constructor.type??"AuthError",this.kind=this.constructor.kind??"error",Error.captureStackTrace?.(this,this.constructor);let r=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${r}`}}class p extends h{}p.kind="signIn";class y extends h{}y.type="AdapterError";class m extends h{}m.type="AccessDenied";class g extends h{}g.type="CallbackRouteError";class b extends h{}b.type="ErrorPageLoop";class w extends h{}w.type="EventError";class x extends h{}x.type="InvalidCallbackUrl";class v extends p{constructor(){super(...arguments),this.code="credentials"}}v.type="CredentialsSignin";class _ extends h{}_.type="InvalidEndpoints";class S extends h{}S.type="InvalidCheck";class k extends h{}k.type="JWTSessionError";class A extends h{}A.type="MissingAdapter";class E extends h{}E.type="MissingAdapterMethods";class T extends h{}T.type="MissingAuthorize";class C extends h{}C.type="MissingSecret";class $ extends p{}$.type="OAuthAccountNotLinked";class O extends p{}O.type="OAuthCallbackError";class R extends h{}R.type="OAuthProfileParseError";class I extends h{}I.type="SessionTokenError";class P extends p{}P.type="OAuthSignInError";class j extends p{}j.type="EmailSignInError";class U extends h{}U.type="SignOutError";class q extends h{}q.type="UnknownAction";class D extends h{}D.type="UnsupportedStrategy";class L extends h{}L.type="InvalidProvider";class N extends h{}N.type="UntrustedHost";class M extends h{}M.type="Verification";class B extends p{}B.type="MissingCSRF";let H=new Set(["CredentialsSignin","OAuthAccountNotLinked","OAuthCallbackError","AccessDenied","Verification","MissingCSRF","AccountNotLinked","WebAuthnVerificationError"]);class K extends h{}K.type="DuplicateConditionalUI";class W extends h{}W.type="MissingWebAuthnAutocomplete";class Q extends h{}Q.type="WebAuthnVerificationError";class J extends p{}J.type="AccountNotLinked";class F extends h{}F.type="ExperimentalFeatureNotEnabled";let V=!1;function X(e,t){try{return/^https?:/.test(new URL(e,e.startsWith("/")?t:void 0).protocol)}catch{return!1}}let z=!1,G=!1,Y=!1,Z=["createVerificationToken","useVerificationToken","getUserByEmail"],ee=["createUser","getUser","getUserByEmail","getUserByAccount","updateUser","linkAccount","createSession","getSessionAndUser","updateSession","deleteSession"],et=["createUser","getUser","linkAccount","getAccount","getAuthenticator","createAuthenticator","listAuthenticatorsByUserId","updateAuthenticatorCounter"];var er=r(55511);let ei=(e,t,r,i,n)=>{let s=parseInt(e.substr(3),10)>>3||20,a=(0,er.createHmac)(e,r.byteLength?r:new Uint8Array(s)).update(t).digest(),o=Math.ceil(n/s),l=new Uint8Array(s*o+i.byteLength+1),c=0,u=0;for(let t=1;t<=o;t++)l.set(i,u),l[u+i.byteLength]=t,l.set((0,er.createHmac)(e,a).update(l.subarray(c,u+i.byteLength+1)).digest(),u),c=u,u+=s;return l.slice(0,n)};"function"!=typeof er.hkdf||process.versions.electron||(i=async(...e)=>new Promise((t,r)=>{er.hkdf(...e,(e,i)=>{e?r(e):t(new Uint8Array(i))})}));let en=async(e,t,r,n,s)=>(i||ei)(e,t,r,n,s);function es(e,t){if("string"==typeof e)return new TextEncoder().encode(e);if(!(e instanceof Uint8Array))throw TypeError(`"${t}"" must be an instance of Uint8Array or a string`);return e}async function ea(e,t,r,i,n){return en(function(e){switch(e){case"sha256":case"sha384":case"sha512":case"sha1":return e;default:throw TypeError('unsupported "digest" value')}}(e),function(e){let t=es(e,"ikm");if(!t.byteLength)throw TypeError('"ikm" must be at least one byte in length');return t}(t),es(r,"salt"),function(e){let t=es(e,"info");if(t.byteLength>1024)throw TypeError('"info" must not contain more than 1024 bytes');return t}(i),function(e,t){if("number"!=typeof e||!Number.isInteger(e)||e<1)throw TypeError('"keylen" must be a positive integer');if(e>255*(parseInt(t.substr(3),10)>>3||20))throw TypeError('"keylen" too large');return e}(n,e))}let eo=async(e,t)=>{let r=`SHA-${e.slice(-3)}`;return new Uint8Array(await crypto.subtle.digest(r,t))},el=new TextEncoder,ec=new TextDecoder;function eu(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;for(let i of e)t.set(i,r),r+=i.length;return t}function ed(e,t,r){if(t<0||t>=0x100000000)throw RangeError(`value must be >= 0 and <= ${0x100000000-1}. Received ${t}`);e.set([t>>>24,t>>>16,t>>>8,255&t],r)}function ef(e){let t=Math.floor(e/0x100000000),r=new Uint8Array(8);return ed(r,t,0),ed(r,e%0x100000000,4),r}function eh(e){let t=new Uint8Array(4);return ed(t,e),t}function ep(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64("string"==typeof e?e:ec.decode(e),{alphabet:"base64url"});let t=e;t instanceof Uint8Array&&(t=ec.decode(t)),t=t.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{var r=t;if(Uint8Array.fromBase64)return Uint8Array.fromBase64(r);let e=atob(r),i=new Uint8Array(e.length);for(let t=0;t<e.length;t++)i[t]=e.charCodeAt(t);return i}catch{throw TypeError("The input to be decoded is not correctly encoded.")}}function ey(e){let t=e;return("string"==typeof t&&(t=el.encode(t)),Uint8Array.prototype.toBase64)?t.toBase64({alphabet:"base64url",omitPadding:!0}):(function(e){if(Uint8Array.prototype.toBase64)return e.toBase64();let t=[];for(let r=0;r<e.length;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join(""))})(t).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}class em extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(e,t){super(e,t),this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}class eg extends em{static code="ERR_JWT_CLAIM_VALIDATION_FAILED";code="ERR_JWT_CLAIM_VALIDATION_FAILED";claim;reason;payload;constructor(e,t,r="unspecified",i="unspecified"){super(e,{cause:{claim:r,reason:i,payload:t}}),this.claim=r,this.reason=i,this.payload=t}}class eb extends em{static code="ERR_JWT_EXPIRED";code="ERR_JWT_EXPIRED";claim;reason;payload;constructor(e,t,r="unspecified",i="unspecified"){super(e,{cause:{claim:r,reason:i,payload:t}}),this.claim=r,this.reason=i,this.payload=t}}class ew extends em{static code="ERR_JOSE_ALG_NOT_ALLOWED";code="ERR_JOSE_ALG_NOT_ALLOWED"}class ex extends em{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class ev extends em{static code="ERR_JWE_DECRYPTION_FAILED";code="ERR_JWE_DECRYPTION_FAILED";constructor(e="decryption operation failed",t){super(e,t)}}class e_ extends em{static code="ERR_JWE_INVALID";code="ERR_JWE_INVALID"}class eS extends em{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class ek extends em{static code="ERR_JWK_INVALID";code="ERR_JWK_INVALID"}class eA extends em{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(e="multiple matching keys found in the JSON Web Key Set",t){super(e,t)}}function eE(e){if(!eT(e))throw Error("CryptoKey instance expected")}function eT(e){return e?.[Symbol.toStringTag]==="CryptoKey"}function eC(e){return e?.[Symbol.toStringTag]==="KeyObject"}let e$=e=>eT(e)||eC(e),eO=e=>{if(!function(e){return"object"==typeof e&&null!==e}(e)||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t};function eR(e){return eO(e)&&"string"==typeof e.kty}function eI(e,t,...r){if((r=r.filter(Boolean)).length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor?.name&&(e+=` Received an instance of ${t.constructor.name}`),e}let eP=(e,...t)=>eI("Key must be ",e,...t);function ej(e,t,...r){return eI(`Key for the ${e} algorithm must be `,t,...r)}async function eU(e){if(eC(e))if("secret"!==e.type)return e.export({format:"jwk"});else e=e.export();if(e instanceof Uint8Array)return{kty:"oct",k:ey(e)};if(!eT(e))throw TypeError(eP(e,"CryptoKey","KeyObject","Uint8Array"));if(!e.extractable)throw TypeError("non-extractable CryptoKey cannot be exported as a JWK");let{ext:t,key_ops:r,alg:i,use:n,...s}=await crypto.subtle.exportKey("jwk",e);return s}async function eq(e){return eU(e)}let eD=(e,t)=>{if("string"!=typeof e||!e)throw new ek(`${t} missing or invalid`)};async function eL(e,t){let r,i;if(eR(e))r=e;else if(e$(e))r=await eq(e);else throw TypeError(eP(e,"CryptoKey","KeyObject","JSON Web Key"));if("sha256"!==(t??="sha256")&&"sha384"!==t&&"sha512"!==t)throw TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"');switch(r.kty){case"EC":eD(r.crv,'"crv" (Curve) Parameter'),eD(r.x,'"x" (X Coordinate) Parameter'),eD(r.y,'"y" (Y Coordinate) Parameter'),i={crv:r.crv,kty:r.kty,x:r.x,y:r.y};break;case"OKP":eD(r.crv,'"crv" (Subtype of Key Pair) Parameter'),eD(r.x,'"x" (Public Key) Parameter'),i={crv:r.crv,kty:r.kty,x:r.x};break;case"RSA":eD(r.e,'"e" (Exponent) Parameter'),eD(r.n,'"n" (Modulus) Parameter'),i={e:r.e,kty:r.kty,n:r.n};break;case"oct":eD(r.k,'"k" (Key Value) Parameter'),i={k:r.k,kty:r.kty};break;default:throw new ex('"kty" (Key Type) Parameter missing or unsupported')}let n=el.encode(JSON.stringify(i));return ey(await eo(t,n))}let eN=Symbol();function eM(e){switch(e){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new ex(`Unsupported JWE Algorithm: ${e}`)}}let eB=e=>crypto.getRandomValues(new Uint8Array(eM(e)>>3)),eH=(e,t)=>{if(t.length<<3!==eM(e))throw new e_("Invalid Initialization Vector length")},eK=(e,t)=>{let r=e.byteLength<<3;if(r!==t)throw new e_(`Invalid Content Encryption Key length. Expected ${t} bits, got ${r} bits`)};function eW(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function eQ(e,t){return e.name===t}function eJ(e,t,r){switch(t){case"A128GCM":case"A192GCM":case"A256GCM":{if(!eQ(e.algorithm,"AES-GCM"))throw eW("AES-GCM");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw eW(r,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!eQ(e.algorithm,"AES-KW"))throw eW("AES-KW");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw eW(r,"algorithm.length");break}case"ECDH":switch(e.algorithm.name){case"ECDH":case"X25519":break;default:throw eW("ECDH or X25519")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!eQ(e.algorithm,"PBKDF2"))throw eW("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!eQ(e.algorithm,"RSA-OAEP"))throw eW("RSA-OAEP");let r=parseInt(t.slice(9),10)||1;if(parseInt(e.algorithm.hash.name.slice(4),10)!==r)throw eW(`SHA-${r}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}var i=e,n=r;if(n&&!i.usages.includes(n))throw TypeError(`CryptoKey does not support this operation, its usages must include ${n}.`)}async function eF(e,t,r,i,n){if(!(r instanceof Uint8Array))throw TypeError(eP(r,"Uint8Array"));let s=parseInt(e.slice(1,4),10),a=await crypto.subtle.importKey("raw",r.subarray(s>>3),"AES-CBC",!1,["encrypt"]),o=await crypto.subtle.importKey("raw",r.subarray(0,s>>3),{hash:`SHA-${s<<1}`,name:"HMAC"},!1,["sign"]),l=new Uint8Array(await crypto.subtle.encrypt({iv:i,name:"AES-CBC"},a,t)),c=eu(n,i,l,ef(n.length<<3));return{ciphertext:l,tag:new Uint8Array((await crypto.subtle.sign("HMAC",o,c)).slice(0,s>>3)),iv:i}}async function eV(e,t,r,i,n){let s;r instanceof Uint8Array?s=await crypto.subtle.importKey("raw",r,"AES-GCM",!1,["encrypt"]):(eJ(r,e,"encrypt"),s=r);let a=new Uint8Array(await crypto.subtle.encrypt({additionalData:n,iv:i,name:"AES-GCM",tagLength:128},s,t)),o=a.slice(-16);return{ciphertext:a.slice(0,-16),tag:o,iv:i}}let eX=async(e,t,r,i,n)=>{if(!eT(r)&&!(r instanceof Uint8Array))throw TypeError(eP(r,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"));switch(i?eH(e,i):i=eB(e),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return r instanceof Uint8Array&&eK(r,parseInt(e.slice(-3),10)),eF(e,t,r,i,n);case"A128GCM":case"A192GCM":case"A256GCM":return r instanceof Uint8Array&&eK(r,parseInt(e.slice(1,4),10)),eV(e,t,r,i,n);default:throw new ex("Unsupported JWE Content Encryption Algorithm")}};function ez(e,t){if(e.algorithm.length!==parseInt(t.slice(1,4),10))throw TypeError(`Invalid key size for alg: ${t}`)}function eG(e,t,r){return e instanceof Uint8Array?crypto.subtle.importKey("raw",e,"AES-KW",!0,[r]):(eJ(e,t,r),e)}async function eY(e,t,r){let i=await eG(t,e,"wrapKey");ez(i,e);let n=await crypto.subtle.importKey("raw",r,{hash:"SHA-256",name:"HMAC"},!0,["sign"]);return new Uint8Array(await crypto.subtle.wrapKey("raw",n,i,"AES-KW"))}async function eZ(e,t,r){let i=await eG(t,e,"unwrapKey");ez(i,e);let n=await crypto.subtle.unwrapKey("raw",r,i,"AES-KW",{hash:"SHA-256",name:"HMAC"},!0,["sign"]);return new Uint8Array(await crypto.subtle.exportKey("raw",n))}function e0(e){return eu(eh(e.length),e)}async function e1(e,t,r){let i=Math.ceil((t>>3)/32),n=new Uint8Array(32*i);for(let t=0;t<i;t++){let i=new Uint8Array(4+e.length+r.length);i.set(eh(t+1)),i.set(e,4),i.set(r,4+e.length),n.set(await eo("sha256",i),32*t)}return n.slice(0,t>>3)}async function e2(e,t,r,i,n=new Uint8Array(0),s=new Uint8Array(0)){let a;eJ(e,"ECDH"),eJ(t,"ECDH","deriveBits");let o=eu(e0(el.encode(r)),e0(n),e0(s),eh(i));return a="X25519"===e.algorithm.name?256:Math.ceil(parseInt(e.algorithm.namedCurve.slice(-3),10)/8)<<3,e1(new Uint8Array(await crypto.subtle.deriveBits({name:e.algorithm.name,public:e},t,a)),i,o)}function e5(e){switch(e.algorithm.namedCurve){case"P-256":case"P-384":case"P-521":return!0;default:return"X25519"===e.algorithm.name}}let e6=(e,t)=>eu(el.encode(e),new Uint8Array([0]),t);async function e3(e,t,r,i){if(!(e instanceof Uint8Array)||e.length<8)throw new e_("PBES2 Salt Input must be 8 or more octets");let n=e6(t,e),s=parseInt(t.slice(13,16),10),a={hash:`SHA-${t.slice(8,11)}`,iterations:r,name:"PBKDF2",salt:n},o=await (i instanceof Uint8Array?crypto.subtle.importKey("raw",i,"PBKDF2",!1,["deriveBits"]):(eJ(i,t,"deriveBits"),i));return new Uint8Array(await crypto.subtle.deriveBits(a,o,s))}async function e8(e,t,r,i=2048,n=crypto.getRandomValues(new Uint8Array(16))){let s=await e3(n,e,i,t);return{encryptedKey:await eY(e.slice(-6),s,r),p2c:i,p2s:ey(n)}}async function e4(e,t,r,i,n){let s=await e3(n,e,i,t);return eZ(e.slice(-6),s,r)}let e9=(e,t)=>{if(e.startsWith("RS")||e.startsWith("PS")){let{modulusLength:r}=t.algorithm;if("number"!=typeof r||r<2048)throw TypeError(`${e} requires key modulusLength to be 2048 bits or larger`)}},e7=e=>{switch(e){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return"RSA-OAEP";default:throw new ex(`alg ${e} is not supported either by JOSE or your javascript runtime`)}};async function te(e,t,r){return eJ(t,e,"encrypt"),e9(e,t),new Uint8Array(await crypto.subtle.encrypt(e7(e),t,r))}async function tt(e,t,r){return eJ(t,e,"decrypt"),e9(e,t),new Uint8Array(await crypto.subtle.decrypt(e7(e),t,r))}let tr=async e=>{if(!e.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:t,keyUsages:r}=function(e){let t,r;switch(e.kty){case"RSA":switch(e.alg){case"PS256":case"PS384":case"PS512":t={name:"RSA-PSS",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":t={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":t={name:"RSA-OAEP",hash:`SHA-${parseInt(e.alg.slice(-3),10)||1}`},r=e.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new ex('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(e.alg){case"ES256":t={name:"ECDSA",namedCurve:"P-256"},r=e.d?["sign"]:["verify"];break;case"ES384":t={name:"ECDSA",namedCurve:"P-384"},r=e.d?["sign"]:["verify"];break;case"ES512":t={name:"ECDSA",namedCurve:"P-521"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:"ECDH",namedCurve:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new ex('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(e.alg){case"Ed25519":case"EdDSA":t={name:"Ed25519"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new ex('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new ex('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:t,keyUsages:r}}(e),i={...e};return delete i.alg,delete i.use,crypto.subtle.importKey("jwk",i,t,e.ext??!e.d,e.key_ops??r)},ti=async(e,t,r,i=!1)=>{let s=(n||=new WeakMap).get(e);if(s?.[r])return s[r];let a=await tr({...t,alg:r});return i&&Object.freeze(e),s?s[r]=a:n.set(e,{[r]:a}),a},tn=(e,t)=>{let r,i=(n||=new WeakMap).get(e);if(i?.[t])return i[t];let s="public"===e.type,a=!!s;if("x25519"===e.asymmetricKeyType){switch(t){case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}r=e.toCryptoKey(e.asymmetricKeyType,a,s?[]:["deriveBits"])}if("ed25519"===e.asymmetricKeyType){if("EdDSA"!==t&&"Ed25519"!==t)throw TypeError("given KeyObject instance cannot be used for this algorithm");r=e.toCryptoKey(e.asymmetricKeyType,a,[s?"verify":"sign"])}if("rsa"===e.asymmetricKeyType){let i;switch(t){case"RSA-OAEP":i="SHA-1";break;case"RS256":case"PS256":case"RSA-OAEP-256":i="SHA-256";break;case"RS384":case"PS384":case"RSA-OAEP-384":i="SHA-384";break;case"RS512":case"PS512":case"RSA-OAEP-512":i="SHA-512";break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}if(t.startsWith("RSA-OAEP"))return e.toCryptoKey({name:"RSA-OAEP",hash:i},a,s?["encrypt"]:["decrypt"]);r=e.toCryptoKey({name:t.startsWith("PS")?"RSA-PSS":"RSASSA-PKCS1-v1_5",hash:i},a,[s?"verify":"sign"])}if("ec"===e.asymmetricKeyType){let i=new Map([["prime256v1","P-256"],["secp384r1","P-384"],["secp521r1","P-521"]]).get(e.asymmetricKeyDetails?.namedCurve);if(!i)throw TypeError("given KeyObject instance cannot be used for this algorithm");"ES256"===t&&"P-256"===i&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:i},a,[s?"verify":"sign"])),"ES384"===t&&"P-384"===i&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:i},a,[s?"verify":"sign"])),"ES512"===t&&"P-521"===i&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:i},a,[s?"verify":"sign"])),t.startsWith("ECDH-ES")&&(r=e.toCryptoKey({name:"ECDH",namedCurve:i},a,s?[]:["deriveBits"]))}if(!r)throw TypeError("given KeyObject instance cannot be used for this algorithm");return i?i[t]=r:n.set(e,{[t]:r}),r},ts=async(e,t)=>{if(e instanceof Uint8Array||eT(e))return e;if(eC(e)){if("secret"===e.type)return e.export();if("toCryptoKey"in e&&"function"==typeof e.toCryptoKey)try{return tn(e,t)}catch(e){if(e instanceof TypeError)throw e}let r=e.export({format:"jwk"});return ti(e,r,t)}if(eR(e))return e.k?ep(e.k):ti(e,e,t,!0);throw Error("unreachable")};function ta(e){switch(e){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new ex(`Unsupported JWE Algorithm: ${e}`)}}let to=e=>crypto.getRandomValues(new Uint8Array(ta(e)>>3));async function tl(e,t){if(!(e instanceof Uint8Array))throw TypeError("First argument must be a buffer");if(!(t instanceof Uint8Array))throw TypeError("Second argument must be a buffer");let r={name:"HMAC",hash:"SHA-256"},i=await crypto.subtle.generateKey(r,!1,["sign"]),n=new Uint8Array(await crypto.subtle.sign(r,i,e)),s=new Uint8Array(await crypto.subtle.sign(r,i,t)),a=0,o=-1;for(;++o<32;)a|=n[o]^s[o];return 0===a}async function tc(e,t,r,i,n,s){let a,o;if(!(t instanceof Uint8Array))throw TypeError(eP(t,"Uint8Array"));let l=parseInt(e.slice(1,4),10),c=await crypto.subtle.importKey("raw",t.subarray(l>>3),"AES-CBC",!1,["decrypt"]),u=await crypto.subtle.importKey("raw",t.subarray(0,l>>3),{hash:`SHA-${l<<1}`,name:"HMAC"},!1,["sign"]),d=eu(s,i,r,ef(s.length<<3)),f=new Uint8Array((await crypto.subtle.sign("HMAC",u,d)).slice(0,l>>3));try{a=await tl(n,f)}catch{}if(!a)throw new ev;try{o=new Uint8Array(await crypto.subtle.decrypt({iv:i,name:"AES-CBC"},c,r))}catch{}if(!o)throw new ev;return o}async function tu(e,t,r,i,n,s){let a;t instanceof Uint8Array?a=await crypto.subtle.importKey("raw",t,"AES-GCM",!1,["decrypt"]):(eJ(t,e,"decrypt"),a=t);try{return new Uint8Array(await crypto.subtle.decrypt({additionalData:s,iv:i,name:"AES-GCM",tagLength:128},a,eu(r,n)))}catch{throw new ev}}let td=async(e,t,r,i,n,s)=>{if(!eT(t)&&!(t instanceof Uint8Array))throw TypeError(eP(t,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"));if(!i)throw new e_("JWE Initialization Vector missing");if(!n)throw new e_("JWE Authentication Tag missing");switch(eH(e,i),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return t instanceof Uint8Array&&eK(t,parseInt(e.slice(-3),10)),tc(e,t,r,i,n,s);case"A128GCM":case"A192GCM":case"A256GCM":return t instanceof Uint8Array&&eK(t,parseInt(e.slice(1,4),10)),tu(e,t,r,i,n,s);default:throw new ex("Unsupported JWE Content Encryption Algorithm")}};async function tf(e,t,r,i){let n=e.slice(0,7),s=await eX(n,r,t,i,new Uint8Array(0));return{encryptedKey:s.ciphertext,iv:ey(s.iv),tag:ey(s.tag)}}async function th(e,t,r,i,n){return td(e.slice(0,7),t,r,i,n,new Uint8Array(0))}let tp=async(e,t,r,i,n={})=>{let s,a,o;switch(e){case"dir":o=r;break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let l;if(eE(r),!e5(r))throw new ex("ECDH with the provided key is not allowed or not supported by your javascript runtime");let{apu:c,apv:u}=n;l=n.epk?await ts(n.epk,e):(await crypto.subtle.generateKey(r.algorithm,!0,["deriveBits"])).privateKey;let{x:d,y:f,crv:h,kty:p}=await eq(l),y=await e2(r,l,"ECDH-ES"===e?t:e,"ECDH-ES"===e?ta(t):parseInt(e.slice(-5,-2),10),c,u);if(a={epk:{x:d,crv:h,kty:p}},"EC"===p&&(a.epk.y=f),c&&(a.apu=ey(c)),u&&(a.apv=ey(u)),"ECDH-ES"===e){o=y;break}o=i||to(t);let m=e.slice(-6);s=await eY(m,y,o);break}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":o=i||to(t),eE(r),s=await te(e,r,o);break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{o=i||to(t);let{p2c:l,p2s:c}=n;({encryptedKey:s,...a}=await e8(e,r,o,l,c));break}case"A128KW":case"A192KW":case"A256KW":o=i||to(t),s=await eY(e,r,o);break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{o=i||to(t);let{iv:l}=n;({encryptedKey:s,...a}=await tf(e,r,o,l));break}default:throw new ex('Invalid or unsupported "alg" (JWE Algorithm) header value')}return{cek:o,encryptedKey:s,parameters:a}},ty=(...e)=>{let t,r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0},tm=(e,t,r,i,n)=>{let s;if(void 0!==n.crit&&i?.crit===void 0)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!i||void 0===i.crit)return new Set;if(!Array.isArray(i.crit)||0===i.crit.length||i.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let a of(s=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,i.crit)){if(!s.has(a))throw new ex(`Extension Header Parameter "${a}" is not recognized`);if(void 0===n[a])throw new e(`Extension Header Parameter "${a}" is missing`);if(s.get(a)&&void 0===i[a])throw new e(`Extension Header Parameter "${a}" MUST be integrity protected`)}return new Set(i.crit)},tg=e=>e?.[Symbol.toStringTag],tb=(e,t,r)=>{if(void 0!==t.use){let e;switch(r){case"sign":case"verify":e="sig";break;case"encrypt":case"decrypt":e="enc"}if(t.use!==e)throw TypeError(`Invalid key for this operation, its "use" must be "${e}" when present`)}if(void 0!==t.alg&&t.alg!==e)throw TypeError(`Invalid key for this operation, its "alg" must be "${e}" when present`);if(Array.isArray(t.key_ops)){let i;switch(!0){case"sign"===r||"verify"===r:case"dir"===e:case e.includes("CBC-HS"):i=r;break;case e.startsWith("PBES2"):i="deriveBits";break;case/^A\d{3}(?:GCM)?(?:KW)?$/.test(e):i=!e.includes("GCM")&&e.endsWith("KW")?"encrypt"===r?"wrapKey":"unwrapKey":r;break;case"encrypt"===r&&e.startsWith("RSA"):i="wrapKey";break;case"decrypt"===r:i=e.startsWith("RSA")?"unwrapKey":"deriveBits"}if(i&&t.key_ops?.includes?.(i)===!1)throw TypeError(`Invalid key for this operation, its "key_ops" must include "${i}" when present`)}return!0},tw=(e,t,r)=>{if(!(t instanceof Uint8Array)){if(eR(t)){if(function(e){return"oct"===e.kty&&"string"==typeof e.k}(t)&&tb(e,t,r))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!e$(t))throw TypeError(ej(e,t,"CryptoKey","KeyObject","JSON Web Key","Uint8Array"));if("secret"!==t.type)throw TypeError(`${tg(t)} instances for symmetric algorithms must be of type "secret"`)}},tx=(e,t,r)=>{if(eR(t))switch(r){case"decrypt":case"sign":if(function(e){return"oct"!==e.kty&&"string"==typeof e.d}(t)&&tb(e,t,r))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"encrypt":case"verify":if(function(e){return"oct"!==e.kty&&void 0===e.d}(t)&&tb(e,t,r))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!e$(t))throw TypeError(ej(e,t,"CryptoKey","KeyObject","JSON Web Key"));if("secret"===t.type)throw TypeError(`${tg(t)} instances for asymmetric algorithms must not be of type "secret"`);if("public"===t.type)switch(r){case"sign":throw TypeError(`${tg(t)} instances for asymmetric algorithm signing must be of type "private"`);case"decrypt":throw TypeError(`${tg(t)} instances for asymmetric algorithm decryption must be of type "private"`)}if("private"===t.type)switch(r){case"verify":throw TypeError(`${tg(t)} instances for asymmetric algorithm verifying must be of type "public"`);case"encrypt":throw TypeError(`${tg(t)} instances for asymmetric algorithm encryption must be of type "public"`)}},tv=(e,t,r)=>{e.startsWith("HS")||"dir"===e||e.startsWith("PBES2")||/^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(e)||/^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(e)?tw(e,t,r):tx(e,t,r)};class t_{#e;#t;#r;#i;#n;#s;#a;#o;constructor(e){if(!(e instanceof Uint8Array))throw TypeError("plaintext must be an instance of Uint8Array");this.#e=e}setKeyManagementParameters(e){if(this.#o)throw TypeError("setKeyManagementParameters can only be called once");return this.#o=e,this}setProtectedHeader(e){if(this.#t)throw TypeError("setProtectedHeader can only be called once");return this.#t=e,this}setSharedUnprotectedHeader(e){if(this.#r)throw TypeError("setSharedUnprotectedHeader can only be called once");return this.#r=e,this}setUnprotectedHeader(e){if(this.#i)throw TypeError("setUnprotectedHeader can only be called once");return this.#i=e,this}setAdditionalAuthenticatedData(e){return this.#n=e,this}setContentEncryptionKey(e){if(this.#s)throw TypeError("setContentEncryptionKey can only be called once");return this.#s=e,this}setInitializationVector(e){if(this.#a)throw TypeError("setInitializationVector can only be called once");return this.#a=e,this}async encrypt(e,t){let r,i,n,s,a;if(!this.#t&&!this.#i&&!this.#r)throw new e_("either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()");if(!ty(this.#t,this.#i,this.#r))throw new e_("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let o={...this.#t,...this.#i,...this.#r};if(tm(e_,new Map,t?.crit,this.#t,o),void 0!==o.zip)throw new ex('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:l,enc:c}=o;if("string"!=typeof l||!l)throw new e_('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("string"!=typeof c||!c)throw new e_('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if(this.#s&&("dir"===l||"ECDH-ES"===l))throw TypeError(`setContentEncryptionKey cannot be called with JWE "alg" (Algorithm) Header ${l}`);tv("dir"===l?c:l,e,"encrypt");{let n,s=await ts(e,l);({cek:i,encryptedKey:r,parameters:n}=await tp(l,c,s,this.#s,this.#o)),n&&(t&&eN in t?this.#i?this.#i={...this.#i,...n}:this.setUnprotectedHeader(n):this.#t?this.#t={...this.#t,...n}:this.setProtectedHeader(n))}s=this.#t?el.encode(ey(JSON.stringify(this.#t))):el.encode(""),this.#n?(a=ey(this.#n),n=eu(s,el.encode("."),el.encode(a))):n=s;let{ciphertext:u,tag:d,iv:f}=await eX(c,this.#e,i,this.#a,n),h={ciphertext:ey(u)};return f&&(h.iv=ey(f)),d&&(h.tag=ey(d)),r&&(h.encrypted_key=ey(r)),a&&(h.aad=a),this.#t&&(h.protected=ec.decode(s)),this.#r&&(h.unprotected=this.#r),this.#i&&(h.header=this.#i),h}}class tS{#l;constructor(e){this.#l=new t_(e)}setContentEncryptionKey(e){return this.#l.setContentEncryptionKey(e),this}setInitializationVector(e){return this.#l.setInitializationVector(e),this}setProtectedHeader(e){return this.#l.setProtectedHeader(e),this}setKeyManagementParameters(e){return this.#l.setKeyManagementParameters(e),this}async encrypt(e,t){let r=await this.#l.encrypt(e,t);return[r.protected,r.encrypted_key,r.iv,r.ciphertext,r.tag].join(".")}}let tk=e=>Math.floor(e.getTime()/1e3),tA=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,tE=e=>{let t,r=tA.exec(e);if(!r||r[4]&&r[1])throw TypeError("Invalid time period format");let i=parseFloat(r[2]);switch(r[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":t=Math.round(i);break;case"minute":case"minutes":case"min":case"mins":case"m":t=Math.round(60*i);break;case"hour":case"hours":case"hr":case"hrs":case"h":t=Math.round(3600*i);break;case"day":case"days":case"d":t=Math.round(86400*i);break;case"week":case"weeks":case"w":t=Math.round(604800*i);break;default:t=Math.round(0x1e187e0*i)}return"-"===r[1]||"ago"===r[4]?-t:t};function tT(e,t){if(!Number.isFinite(t))throw TypeError(`Invalid ${e} input`);return t}let tC=e=>e.includes("/")?e.toLowerCase():`application/${e.toLowerCase()}`,t$=(e,t)=>"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e)));class tO{#c;constructor(e){if(!eO(e))throw TypeError("JWT Claims Set MUST be an object");this.#c=structuredClone(e)}data(){return el.encode(JSON.stringify(this.#c))}get iss(){return this.#c.iss}set iss(e){this.#c.iss=e}get sub(){return this.#c.sub}set sub(e){this.#c.sub=e}get aud(){return this.#c.aud}set aud(e){this.#c.aud=e}set jti(e){this.#c.jti=e}set nbf(e){"number"==typeof e?this.#c.nbf=tT("setNotBefore",e):e instanceof Date?this.#c.nbf=tT("setNotBefore",tk(e)):this.#c.nbf=tk(new Date)+tE(e)}set exp(e){"number"==typeof e?this.#c.exp=tT("setExpirationTime",e):e instanceof Date?this.#c.exp=tT("setExpirationTime",tk(e)):this.#c.exp=tk(new Date)+tE(e)}set iat(e){void 0===e?this.#c.iat=tk(new Date):e instanceof Date?this.#c.iat=tT("setIssuedAt",tk(e)):"string"==typeof e?this.#c.iat=tT("setIssuedAt",tk(new Date)+tE(e)):this.#c.iat=tT("setIssuedAt",e)}}class tR{#s;#a;#o;#t;#u;#d;#f;#h;constructor(e={}){this.#h=new tO(e)}setIssuer(e){return this.#h.iss=e,this}setSubject(e){return this.#h.sub=e,this}setAudience(e){return this.#h.aud=e,this}setJti(e){return this.#h.jti=e,this}setNotBefore(e){return this.#h.nbf=e,this}setExpirationTime(e){return this.#h.exp=e,this}setIssuedAt(e){return this.#h.iat=e,this}setProtectedHeader(e){if(this.#t)throw TypeError("setProtectedHeader can only be called once");return this.#t=e,this}setKeyManagementParameters(e){if(this.#o)throw TypeError("setKeyManagementParameters can only be called once");return this.#o=e,this}setContentEncryptionKey(e){if(this.#s)throw TypeError("setContentEncryptionKey can only be called once");return this.#s=e,this}setInitializationVector(e){if(this.#a)throw TypeError("setInitializationVector can only be called once");return this.#a=e,this}replicateIssuerAsHeader(){return this.#u=!0,this}replicateSubjectAsHeader(){return this.#d=!0,this}replicateAudienceAsHeader(){return this.#f=!0,this}async encrypt(e,t){let r=new tS(this.#h.data());return this.#t&&(this.#u||this.#d||this.#f)&&(this.#t={...this.#t,iss:this.#u?this.#h.iss:void 0,sub:this.#d?this.#h.sub:void 0,aud:this.#f?this.#h.aud:void 0}),r.setProtectedHeader(this.#t),this.#a&&r.setInitializationVector(this.#a),this.#s&&r.setContentEncryptionKey(this.#s),this.#o&&r.setKeyManagementParameters(this.#o),r.encrypt(e,t)}}async function tI(e,t,r){let i;if(!eO(e))throw TypeError("JWK must be an object");switch(t??=e.alg,i??=r?.extractable??e.ext,e.kty){case"oct":if("string"!=typeof e.k||!e.k)throw TypeError('missing "k" (Key Value) Parameter value');return ep(e.k);case"RSA":if("oth"in e&&void 0!==e.oth)throw new ex('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":return tr({...e,alg:t,ext:i});default:throw new ex('Unsupported "kty" (Key Type) Parameter value')}}let tP=async(e,t,r,i,n)=>{switch(e){case"dir":if(void 0!==r)throw new e_("Encountered unexpected JWE Encrypted Key");return t;case"ECDH-ES":if(void 0!==r)throw new e_("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let n,s;if(!eO(i.epk))throw new e_('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(eE(t),!e5(t))throw new ex("ECDH with the provided key is not allowed or not supported by your javascript runtime");let a=await tI(i.epk,e);if(eE(a),void 0!==i.apu){if("string"!=typeof i.apu)throw new e_('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{n=ep(i.apu)}catch{throw new e_("Failed to base64url decode the apu")}}if(void 0!==i.apv){if("string"!=typeof i.apv)throw new e_('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{s=ep(i.apv)}catch{throw new e_("Failed to base64url decode the apv")}}let o=await e2(a,t,"ECDH-ES"===e?i.enc:e,"ECDH-ES"===e?ta(i.enc):parseInt(e.slice(-5,-2),10),n,s);if("ECDH-ES"===e)return o;if(void 0===r)throw new e_("JWE Encrypted Key missing");return eZ(e.slice(-6),o,r)}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===r)throw new e_("JWE Encrypted Key missing");return eE(t),tt(e,t,r);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let s;if(void 0===r)throw new e_("JWE Encrypted Key missing");if("number"!=typeof i.p2c)throw new e_('JOSE Header "p2c" (PBES2 Count) missing or invalid');let a=n?.maxPBES2Count||1e4;if(i.p2c>a)throw new e_('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof i.p2s)throw new e_('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{s=ep(i.p2s)}catch{throw new e_("Failed to base64url decode the p2s")}return e4(e,t,r,i.p2c,s)}case"A128KW":case"A192KW":case"A256KW":if(void 0===r)throw new e_("JWE Encrypted Key missing");return eZ(e,t,r);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let n,s;if(void 0===r)throw new e_("JWE Encrypted Key missing");if("string"!=typeof i.iv)throw new e_('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof i.tag)throw new e_('JOSE Header "tag" (Authentication Tag) missing or invalid');try{n=ep(i.iv)}catch{throw new e_("Failed to base64url decode the iv")}try{s=ep(i.tag)}catch{throw new e_("Failed to base64url decode the tag")}return th(e,t,r,n,s)}default:throw new ex('Invalid or unsupported "alg" (JWE Algorithm) header value')}},tj=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError(`"${e}" option must be an array of strings`);if(t)return new Set(t)};async function tU(e,t,r){let i,n,s,a,o,l,c;if(!eO(e))throw new e_("Flattened JWE must be an object");if(void 0===e.protected&&void 0===e.header&&void 0===e.unprotected)throw new e_("JOSE Header missing");if(void 0!==e.iv&&"string"!=typeof e.iv)throw new e_("JWE Initialization Vector incorrect type");if("string"!=typeof e.ciphertext)throw new e_("JWE Ciphertext missing or incorrect type");if(void 0!==e.tag&&"string"!=typeof e.tag)throw new e_("JWE Authentication Tag incorrect type");if(void 0!==e.protected&&"string"!=typeof e.protected)throw new e_("JWE Protected Header incorrect type");if(void 0!==e.encrypted_key&&"string"!=typeof e.encrypted_key)throw new e_("JWE Encrypted Key incorrect type");if(void 0!==e.aad&&"string"!=typeof e.aad)throw new e_("JWE AAD incorrect type");if(void 0!==e.header&&!eO(e.header))throw new e_("JWE Shared Unprotected Header incorrect type");if(void 0!==e.unprotected&&!eO(e.unprotected))throw new e_("JWE Per-Recipient Unprotected Header incorrect type");if(e.protected)try{let t=ep(e.protected);i=JSON.parse(ec.decode(t))}catch{throw new e_("JWE Protected Header is invalid")}if(!ty(i,e.header,e.unprotected))throw new e_("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let u={...i,...e.header,...e.unprotected};if(tm(e_,new Map,r?.crit,i,u),void 0!==u.zip)throw new ex('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:d,enc:f}=u;if("string"!=typeof d||!d)throw new e_("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof f||!f)throw new e_("missing JWE Encryption Algorithm (enc) in JWE Header");let h=r&&tj("keyManagementAlgorithms",r.keyManagementAlgorithms),p=r&&tj("contentEncryptionAlgorithms",r.contentEncryptionAlgorithms);if(h&&!h.has(d)||!h&&d.startsWith("PBES2"))throw new ew('"alg" (Algorithm) Header Parameter value not allowed');if(p&&!p.has(f))throw new ew('"enc" (Encryption Algorithm) Header Parameter value not allowed');if(void 0!==e.encrypted_key)try{n=ep(e.encrypted_key)}catch{throw new e_("Failed to base64url decode the encrypted_key")}let y=!1;"function"==typeof t&&(t=await t(i,e),y=!0),tv("dir"===d?f:d,t,"decrypt");let m=await ts(t,d);try{s=await tP(d,m,n,u,r)}catch(e){if(e instanceof TypeError||e instanceof e_||e instanceof ex)throw e;s=to(f)}if(void 0!==e.iv)try{a=ep(e.iv)}catch{throw new e_("Failed to base64url decode the iv")}if(void 0!==e.tag)try{o=ep(e.tag)}catch{throw new e_("Failed to base64url decode the tag")}let g=el.encode(e.protected??"");l=void 0!==e.aad?eu(g,el.encode("."),el.encode(e.aad)):g;try{c=ep(e.ciphertext)}catch{throw new e_("Failed to base64url decode the ciphertext")}let b={plaintext:await td(f,s,c,a,o,l)};if(void 0!==e.protected&&(b.protectedHeader=i),void 0!==e.aad)try{b.additionalAuthenticatedData=ep(e.aad)}catch{throw new e_("Failed to base64url decode the aad")}return(void 0!==e.unprotected&&(b.sharedUnprotectedHeader=e.unprotected),void 0!==e.header&&(b.unprotectedHeader=e.header),y)?{...b,key:m}:b}async function tq(e,t,r){if(e instanceof Uint8Array&&(e=ec.decode(e)),"string"!=typeof e)throw new e_("Compact JWE must be a string or Uint8Array");let{0:i,1:n,2:s,3:a,4:o,length:l}=e.split(".");if(5!==l)throw new e_("Invalid Compact JWE");let c=await tU({ciphertext:a,iv:s||void 0,protected:i,tag:o||void 0,encrypted_key:n||void 0},t,r),u={plaintext:c.plaintext,protectedHeader:c.protectedHeader};return"function"==typeof t?{...u,key:c.key}:u}async function tD(e,t,r){let i=await tq(e,t,r),n=function(e,t,r={}){let i,n;try{i=JSON.parse(ec.decode(t))}catch{}if(!eO(i))throw new eS("JWT Claims Set must be a top-level JSON object");let{typ:s}=r;if(s&&("string"!=typeof e.typ||tC(e.typ)!==tC(s)))throw new eg('unexpected "typ" JWT header value',i,"typ","check_failed");let{requiredClaims:a=[],issuer:o,subject:l,audience:c,maxTokenAge:u}=r,d=[...a];for(let e of(void 0!==u&&d.push("iat"),void 0!==c&&d.push("aud"),void 0!==l&&d.push("sub"),void 0!==o&&d.push("iss"),new Set(d.reverse())))if(!(e in i))throw new eg(`missing required "${e}" claim`,i,e,"missing");if(o&&!(Array.isArray(o)?o:[o]).includes(i.iss))throw new eg('unexpected "iss" claim value',i,"iss","check_failed");if(l&&i.sub!==l)throw new eg('unexpected "sub" claim value',i,"sub","check_failed");if(c&&!t$(i.aud,"string"==typeof c?[c]:c))throw new eg('unexpected "aud" claim value',i,"aud","check_failed");switch(typeof r.clockTolerance){case"string":n=tE(r.clockTolerance);break;case"number":n=r.clockTolerance;break;case"undefined":n=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:f}=r,h=tk(f||new Date);if((void 0!==i.iat||u)&&"number"!=typeof i.iat)throw new eg('"iat" claim must be a number',i,"iat","invalid");if(void 0!==i.nbf){if("number"!=typeof i.nbf)throw new eg('"nbf" claim must be a number',i,"nbf","invalid");if(i.nbf>h+n)throw new eg('"nbf" claim timestamp check failed',i,"nbf","check_failed")}if(void 0!==i.exp){if("number"!=typeof i.exp)throw new eg('"exp" claim must be a number',i,"exp","invalid");if(i.exp<=h-n)throw new eb('"exp" claim timestamp check failed',i,"exp","check_failed")}if(u){let e=h-i.iat;if(e-n>("number"==typeof u?u:tE(u)))throw new eb('"iat" claim timestamp check failed (too far in the past)',i,"iat","check_failed");if(e<0-n)throw new eg('"iat" claim timestamp check failed (it should be in the past)',i,"iat","check_failed")}return i}(i.protectedHeader,i.plaintext,r),{protectedHeader:s}=i;if(void 0!==s.iss&&s.iss!==n.iss)throw new eg('replicated "iss" claim header parameter mismatch',n,"iss","mismatch");if(void 0!==s.sub&&s.sub!==n.sub)throw new eg('replicated "sub" claim header parameter mismatch',n,"sub","mismatch");if(void 0!==s.aud&&JSON.stringify(s.aud)!==JSON.stringify(n.aud))throw new eg('replicated "aud" claim header parameter mismatch',n,"aud","mismatch");let a={payload:n,protectedHeader:s};return"function"==typeof t?{...a,key:i.key}:a}let tL=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,tN=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,tM=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,tB=/^[\u0020-\u003A\u003D-\u007E]*$/,tH=Object.prototype.toString,tK=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function tW(e,t){let r=new tK,i=e.length;if(i<2)return r;let n=t?.decode||tV,s=0;do{let t=e.indexOf("=",s);if(-1===t)break;let a=e.indexOf(";",s),o=-1===a?i:a;if(t>o){s=e.lastIndexOf(";",t-1)+1;continue}let l=tQ(e,s,t),c=tJ(e,t,l),u=e.slice(l,c);if(void 0===r[u]){let i=tQ(e,t+1,o),s=tJ(e,o,i),a=n(e.slice(i,s));r[u]=a}s=o+1}while(s<i);return r}function tQ(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function tJ(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function tF(e,t,r){let i=r?.encode||encodeURIComponent;if(!tL.test(e))throw TypeError(`argument name is invalid: ${e}`);let n=i(t);if(!tN.test(n))throw TypeError(`argument val is invalid: ${t}`);let s=e+"="+n;if(!r)return s;if(void 0!==r.maxAge){if(!Number.isInteger(r.maxAge))throw TypeError(`option maxAge is invalid: ${r.maxAge}`);s+="; Max-Age="+r.maxAge}if(r.domain){if(!tM.test(r.domain))throw TypeError(`option domain is invalid: ${r.domain}`);s+="; Domain="+r.domain}if(r.path){if(!tB.test(r.path))throw TypeError(`option path is invalid: ${r.path}`);s+="; Path="+r.path}if(r.expires){var a;if(a=r.expires,"[object Date]"!==tH.call(a)||!Number.isFinite(r.expires.valueOf()))throw TypeError(`option expires is invalid: ${r.expires}`);s+="; Expires="+r.expires.toUTCString()}if(r.httpOnly&&(s+="; HttpOnly"),r.secure&&(s+="; Secure"),r.partitioned&&(s+="; Partitioned"),r.priority)switch("string"==typeof r.priority?r.priority.toLowerCase():void 0){case"low":s+="; Priority=Low";break;case"medium":s+="; Priority=Medium";break;case"high":s+="; Priority=High";break;default:throw TypeError(`option priority is invalid: ${r.priority}`)}if(r.sameSite)switch("string"==typeof r.sameSite?r.sameSite.toLowerCase():r.sameSite){case!0:case"strict":s+="; SameSite=Strict";break;case"lax":s+="; SameSite=Lax";break;case"none":s+="; SameSite=None";break;default:throw TypeError(`option sameSite is invalid: ${r.sameSite}`)}return s}function tV(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}let{q:tX}=l,tz=()=>Date.now()/1e3|0,tG="A256CBC-HS512";async function tY(e){let{token:t={},secret:r,maxAge:i=2592e3,salt:n}=e,s=Array.isArray(r)?r:[r],a=await t0(tG,s[0],n),o=await eL({kty:"oct",k:ey(a)},`sha${a.byteLength<<3}`);return await new tR(t).setProtectedHeader({alg:"dir",enc:tG,kid:o}).setIssuedAt().setExpirationTime(tz()+i).setJti(crypto.randomUUID()).encrypt(a)}async function tZ(e){let{token:t,secret:r,salt:i}=e,n=Array.isArray(r)?r:[r];if(!t)return null;let{payload:s}=await tD(t,async({kid:e,enc:t})=>{for(let r of n){let n=await t0(t,r,i);if(void 0===e||e===await eL({kty:"oct",k:ey(n)},`sha${n.byteLength<<3}`))return n}throw Error("no matching decryption secret")},{clockTolerance:15,keyManagementAlgorithms:["dir"],contentEncryptionAlgorithms:[tG,"A256GCM"]});return s}async function t0(e,t,r){let i;switch(e){case"A256CBC-HS512":i=64;break;case"A256GCM":i=32;break;default:throw Error("Unsupported JWT Content Encryption Algorithm")}return await ea("sha256",t,r,`Auth.js Generated Encryption Key (${r})`,i)}async function t1({options:e,paramValue:t,cookieValue:r}){let{url:i,callbacks:n}=e,s=i.origin;return t?s=await n.redirect({url:t,baseUrl:i.origin}):r&&(s=await n.redirect({url:r,baseUrl:i.origin})),{callbackUrl:s,callbackUrlCookie:s!==r?s:void 0}}let t2="\x1b[31m",t5="\x1b[0m",t6={error(e){let t=e instanceof h?e.type:e.name;if(console.error(`${t2}[auth][error]${t5} ${t}: ${e.message}`),e.cause&&"object"==typeof e.cause&&"err"in e.cause&&e.cause.err instanceof Error){let{err:t,...r}=e.cause;console.error(`${t2}[auth][cause]${t5}:`,t.stack),r&&console.error(`${t2}[auth][details]${t5}:`,JSON.stringify(r,null,2))}else e.stack&&console.error(e.stack.replace(/.*/,"").substring(1))},warn(e){console.warn(`\x1b[33m[auth][warn][${e}]${t5}`,"Read more: https://warnings.authjs.dev")},debug(e,t){console.log(`\x1b[90m[auth][debug]:${t5} ${e}`,JSON.stringify(t,null,2))}};function t3(e){let t={...t6};return e.debug||(t.debug=()=>{}),e.logger?.error&&(t.error=e.logger.error),e.logger?.warn&&(t.warn=e.logger.warn),e.logger?.debug&&(t.debug=e.logger.debug),e.logger??(e.logger=t),t}let t8=["providers","session","csrf","signin","signout","callback","verify-request","error","webauthn-options"],{q:t4,l:t9}=l;async function t7(e){if(!("body"in e)||!e.body||"POST"!==e.method)return;let t=e.headers.get("content-type");return t?.includes("application/json")?await e.json():t?.includes("application/x-www-form-urlencoded")?Object.fromEntries(new URLSearchParams(await e.text())):void 0}async function re(e,t){try{if("GET"!==e.method&&"POST"!==e.method)throw new q("Only GET and POST requests are supported");t.basePath??(t.basePath="/auth");let r=new URL(e.url),{action:i,providerId:n}=function(e,t){let r=e.match(RegExp(`^${t}(.+)`));if(null===r)throw new q(`Cannot parse action at ${e}`);let i=r.at(-1).replace(/^\//,"").split("/").filter(Boolean);if(1!==i.length&&2!==i.length)throw new q(`Cannot parse action at ${e}`);let[n,s]=i;if(!t8.includes(n)||s&&!["signin","callback","webauthn-options"].includes(n))throw new q(`Cannot parse action at ${e}`);return{action:n,providerId:s}}(r.pathname,t.basePath);return{url:r,action:i,providerId:n,method:e.method,headers:Object.fromEntries(e.headers),body:e.body?await t7(e):void 0,cookies:t4(e.headers.get("cookie")??"")??{},error:r.searchParams.get("error")??void 0,query:Object.fromEntries(r.searchParams)}}catch(i){let r=t3(t);r.error(i),r.debug("request",e)}}function rt(e){let t=new Headers(e.headers);e.cookies?.forEach(e=>{let{name:r,value:i,options:n}=e,s=t9(r,i,n);t.has("Set-Cookie")?t.append("Set-Cookie",s):t.set("Set-Cookie",s)});let r=e.body;"application/json"===t.get("content-type")?r=JSON.stringify(e.body):"application/x-www-form-urlencoded"===t.get("content-type")&&(r=new URLSearchParams(e.body).toString());let i=new Response(r,{headers:t,status:e.redirect?302:e.status??200});return e.redirect&&i.headers.set("Location",e.redirect),i}async function rr(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("").toString()}function ri(e){let t=e=>("0"+e.toString(16)).slice(-2);return Array.from(crypto.getRandomValues(new Uint8Array(e))).reduce((e,r)=>e+t(r),"")}async function rn({options:e,cookieValue:t,isPost:r,bodyValue:i}){if(t){let[n,s]=t.split("|");if(s===await rr(`${n}${e.secret}`))return{csrfTokenVerified:r&&n===i,csrfToken:n}}let n=ri(32),s=await rr(`${n}${e.secret}`);return{cookie:`${n}|${s}`,csrfToken:n}}function rs(e,t){if(!t)throw new B(`CSRF token was missing during an action ${e}`)}function ra(e){return null!==e&&"object"==typeof e}function ro(e,...t){if(!t.length)return e;let r=t.shift();if(ra(e)&&ra(r))for(let t in r)ra(r[t])?(ra(e[t])||(e[t]=Array.isArray(r[t])?[]:{}),ro(e[t],r[t])):void 0!==r[t]&&(e[t]=r[t]);return ro(e,...t)}let rl=Symbol("skip-csrf-check"),rc=Symbol("return-type-raw"),ru=Symbol("custom-fetch"),rd=Symbol("conform-internal"),rf=e=>rp({id:e.sub??e.id??crypto.randomUUID(),name:e.name??e.nickname??e.preferred_username,email:e.email,image:e.picture}),rh=e=>rp({access_token:e.access_token,id_token:e.id_token,refresh_token:e.refresh_token,expires_at:e.expires_at,scope:e.scope,token_type:e.token_type,session_state:e.session_state});function rp(e){let t={};for(let[r,i]of Object.entries(e))void 0!==i&&(t[r]=i);return t}function ry(e,t){if(!e&&t)return;if("string"==typeof e)return{url:new URL(e)};let r=new URL(e?.url??"https://authjs.dev");if(e?.params!=null)for(let[t,i]of Object.entries(e.params))"claims"===t&&(i=JSON.stringify(i)),r.searchParams.set(t,String(i));return{url:r,request:e?.request,conform:e?.conform,...e?.clientPrivateKey?{clientPrivateKey:e?.clientPrivateKey}:null}}let rm={signIn:()=>!0,redirect:({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:({session:e})=>({user:{name:e.user?.name,email:e.user?.email,image:e.user?.image},expires:e.expires?.toISOString?.()??e.expires}),jwt:({token:e})=>e};async function rg({authOptions:e,providerId:t,action:r,url:i,cookies:n,callbackUrl:s,csrfToken:a,csrfDisabled:o,isPost:l}){var c,u;let f=t3(e),{providers:h,provider:p}=function(e){let{providerId:t,config:r}=e,i=new URL(r.basePath??"/auth",e.url.origin),n=r.providers.map(e=>{let t="function"==typeof e?e():e,{options:n,...s}=t,a=n?.id??s.id,o=ro(s,n,{signinUrl:`${i}/signin/${a}`,callbackUrl:`${i}/callback/${a}`});if("oauth"===t.type||"oidc"===t.type){o.redirectProxyUrl??(o.redirectProxyUrl=n?.redirectProxyUrl??r.redirectProxyUrl);let e=function(e){e.issuer&&(e.wellKnown??(e.wellKnown=`${e.issuer}/.well-known/openid-configuration`));let t=ry(e.authorization,e.issuer);t&&!t.url?.searchParams.has("scope")&&t.url.searchParams.set("scope","openid profile email");let r=ry(e.token,e.issuer),i=ry(e.userinfo,e.issuer),n=e.checks??["pkce"];return e.redirectProxyUrl&&(n.includes("state")||n.push("state"),e.redirectProxyUrl=`${e.redirectProxyUrl}/callback/${e.id}`),{...e,authorization:t,token:r,checks:n,userinfo:i,profile:e.profile??rf,account:e.account??rh}}(o);return e.authorization?.url.searchParams.get("response_mode")==="form_post"&&delete e.redirectProxyUrl,e[ru]??(e[ru]=n?.[ru]),e}return o}),s=n.find(({id:e})=>e===t);if(t&&!s){let e=n.map(e=>e.id).join(", ");throw Error(`Provider with id "${t}" not found. Available providers: [${e}].`)}return{providers:n,provider:s}}({url:i,providerId:t,config:e}),m=!1;if((p?.type==="oauth"||p?.type==="oidc")&&p.redirectProxyUrl)try{m=new URL(p.redirectProxyUrl).origin===i.origin}catch{throw TypeError(`redirectProxyUrl must be a valid URL. Received: ${p.redirectProxyUrl}`)}let g={debug:!1,pages:{},theme:{colorScheme:"auto",logo:"",brandColor:"",buttonText:""},...e,url:i,action:r,provider:p,cookies:ro(d(e.useSecureCookies??"https:"===i.protocol),e.cookies),providers:h,session:{strategy:e.adapter?"database":"jwt",maxAge:2592e3,updateAge:86400,generateSessionToken:()=>crypto.randomUUID(),...e.session},jwt:{secret:e.secret,maxAge:e.session?.maxAge??2592e3,encode:tY,decode:tZ,...e.jwt},events:(c=e.events??{},u=f,Object.keys(c).reduce((e,t)=>(e[t]=async(...e)=>{try{let r=c[t];return await r(...e)}catch(e){u.error(new w(e))}},e),{})),adapter:function(e,t){if(e)return Object.keys(e).reduce((r,i)=>(r[i]=async(...r)=>{try{t.debug(`adapter_${i}`,{args:r});let n=e[i];return await n(...r)}catch(r){let e=new y(r);throw t.error(e),e}},r),{})}(e.adapter,f),callbacks:{...rm,...e.callbacks},logger:f,callbackUrl:i.origin,isOnRedirectProxy:m,experimental:{...e.experimental}},b=[];if(o)g.csrfTokenVerified=!0;else{let{csrfToken:e,cookie:t,csrfTokenVerified:r}=await rn({options:g,cookieValue:n?.[g.cookies.csrfToken.name],isPost:l,bodyValue:a});g.csrfToken=e,g.csrfTokenVerified=r,t&&b.push({name:g.cookies.csrfToken.name,value:t,options:g.cookies.csrfToken.options})}let{callbackUrl:x,callbackUrlCookie:v}=await t1({options:g,cookieValue:n?.[g.cookies.callbackUrl.name],paramValue:s});return g.callbackUrl=x,v&&b.push({name:g.cookies.callbackUrl.name,value:v,options:g.cookies.callbackUrl.options}),{options:g,cookies:b}}var rb,rw,rx,rv,r_,rS,rk,rA,rE,rT,rC,r$,rO,rR,rI,rP,rj={},rU=[],rq=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,rD=Array.isArray;function rL(e,t){for(var r in t)e[r]=t[r];return e}function rN(e){e&&e.parentNode&&e.parentNode.removeChild(e)}function rM(e,t,r){var i,n,s,a={};for(s in t)"key"==s?i=t[s]:"ref"==s?n=t[s]:a[s]=t[s];if(arguments.length>2&&(a.children=arguments.length>3?rk.call(arguments,2):r),"function"==typeof e&&null!=e.defaultProps)for(s in e.defaultProps)void 0===a[s]&&(a[s]=e.defaultProps[s]);return rB(e,a,i,n,null)}function rB(e,t,r,i,n){var s={type:e,props:t,key:r,ref:i,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==n?++rE:n,__i:-1,__u:0};return null==n&&null!=rA.vnode&&rA.vnode(s),s}function rH(e){return e.children}function rK(e,t){this.props=e,this.context=t}function rW(e,t){if(null==t)return e.__?rW(e.__,e.__i+1):null;for(var r;t<e.__k.length;t++)if(null!=(r=e.__k[t])&&null!=r.__e)return r.__e;return"function"==typeof e.type?rW(e):null}function rQ(e){(!e.__d&&(e.__d=!0)&&rT.push(e)&&!rJ.__r++||rC!==rA.debounceRendering)&&((rC=rA.debounceRendering)||r$)(rJ)}function rJ(){var e,t,r,i,n,s,a,o;for(rT.sort(rO);e=rT.shift();)e.__d&&(t=rT.length,i=void 0,s=(n=(r=e).__v).__e,a=[],o=[],r.__P&&((i=rL({},n)).__v=n.__v+1,rA.vnode&&rA.vnode(i),rG(r.__P,i,n,r.__n,r.__P.namespaceURI,32&n.__u?[s]:null,a,null==s?rW(n):s,!!(32&n.__u),o),i.__v=n.__v,i.__.__k[i.__i]=i,rY(a,i,o),i.__e!=s&&function e(t){var r,i;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,r=0;r<t.__k.length;r++)if(null!=(i=t.__k[r])&&null!=i.__e){t.__e=t.__c.base=i.__e;break}return e(t)}}(i)),rT.length>t&&rT.sort(rO));rJ.__r=0}function rF(e,t,r,i,n,s,a,o,l,c,u){var d,f,h,p,y,m=i&&i.__k||rU,g=t.length;for(r.__d=l,function(e,t,r){var i,n,s,a,o,l=t.length,c=r.length,u=c,d=0;for(e.__k=[],i=0;i<l;i++)null!=(n=t[i])&&"boolean"!=typeof n&&"function"!=typeof n?(a=i+d,(n=e.__k[i]="string"==typeof n||"number"==typeof n||"bigint"==typeof n||n.constructor==String?rB(null,n,null,null,null):rD(n)?rB(rH,{children:n},null,null,null):void 0===n.constructor&&n.__b>0?rB(n.type,n.props,n.key,n.ref?n.ref:null,n.__v):n).__=e,n.__b=e.__b+1,s=null,-1!==(o=n.__i=function(e,t,r,i){var n=e.key,s=e.type,a=r-1,o=r+1,l=t[r];if(null===l||l&&n==l.key&&s===l.type&&0==(131072&l.__u))return r;if(i>+(null!=l&&0==(131072&l.__u)))for(;a>=0||o<t.length;){if(a>=0){if((l=t[a])&&0==(131072&l.__u)&&n==l.key&&s===l.type)return a;a--}if(o<t.length){if((l=t[o])&&0==(131072&l.__u)&&n==l.key&&s===l.type)return o;o++}}return -1}(n,r,a,u))&&(u--,(s=r[o])&&(s.__u|=131072)),null==s||null===s.__v?(-1==o&&d--,"function"!=typeof n.type&&(n.__u|=65536)):o!==a&&(o==a-1?d--:o==a+1?d++:(o>a?d--:d++,n.__u|=65536))):n=e.__k[i]=null;if(u)for(i=0;i<c;i++)null!=(s=r[i])&&0==(131072&s.__u)&&(s.__e==e.__d&&(e.__d=rW(s)),function e(t,r,i){var n,s;if(rA.unmount&&rA.unmount(t),(n=t.ref)&&(n.current&&n.current!==t.__e||rZ(n,null,r)),null!=(n=t.__c)){if(n.componentWillUnmount)try{n.componentWillUnmount()}catch(e){rA.__e(e,r)}n.base=n.__P=null}if(n=t.__k)for(s=0;s<n.length;s++)n[s]&&e(n[s],r,i||"function"!=typeof t.type);i||rN(t.__e),t.__c=t.__=t.__e=t.__d=void 0}(s,s))}(r,t,m),l=r.__d,d=0;d<g;d++)null!=(h=r.__k[d])&&(f=-1===h.__i?rj:m[h.__i]||rj,h.__i=d,rG(e,h,f,n,s,a,o,l,c,u),p=h.__e,h.ref&&f.ref!=h.ref&&(f.ref&&rZ(f.ref,null,h),u.push(h.ref,h.__c||p,h)),null==y&&null!=p&&(y=p),65536&h.__u||f.__k===h.__k?l=function e(t,r,i){var n,s;if("function"==typeof t.type){for(n=t.__k,s=0;n&&s<n.length;s++)n[s]&&(n[s].__=t,r=e(n[s],r,i));return r}t.__e!=r&&(r&&t.type&&!i.contains(r)&&(r=rW(t)),i.insertBefore(t.__e,r||null),r=t.__e);do r=r&&r.nextSibling;while(null!=r&&8===r.nodeType);return r}(h,l,e):"function"==typeof h.type&&void 0!==h.__d?l=h.__d:p&&(l=p.nextSibling),h.__d=void 0,h.__u&=-196609);r.__d=l,r.__e=y}function rV(e,t,r){"-"===t[0]?e.setProperty(t,null==r?"":r):e[t]=null==r?"":"number"!=typeof r||rq.test(t)?r:r+"px"}function rX(e,t,r,i,n){var s;e:if("style"===t)if("string"==typeof r)e.style.cssText=r;else{if("string"==typeof i&&(e.style.cssText=i=""),i)for(t in i)r&&t in r||rV(e.style,t,"");if(r)for(t in r)i&&r[t]===i[t]||rV(e.style,t,r[t])}else if("o"===t[0]&&"n"===t[1])s=t!==(t=t.replace(/(PointerCapture)$|Capture$/i,"$1")),t=t.toLowerCase()in e||"onFocusOut"===t||"onFocusIn"===t?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+s]=r,r?i?r.u=i.u:(r.u=rR,e.addEventListener(t,s?rP:rI,s)):e.removeEventListener(t,s?rP:rI,s);else{if("http://www.w3.org/2000/svg"==n)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=t&&"height"!=t&&"href"!=t&&"list"!=t&&"form"!=t&&"tabIndex"!=t&&"download"!=t&&"rowSpan"!=t&&"colSpan"!=t&&"role"!=t&&"popover"!=t&&t in e)try{e[t]=null==r?"":r;break e}catch(e){}"function"==typeof r||(null==r||!1===r&&"-"!==t[4]?e.removeAttribute(t):e.setAttribute(t,"popover"==t&&1==r?"":r))}}function rz(e){return function(t){if(this.l){var r=this.l[t.type+e];if(null==t.t)t.t=rR++;else if(t.t<r.u)return;return r(rA.event?rA.event(t):t)}}}function rG(e,t,r,i,n,s,a,o,l,c){var u,d,f,h,p,y,m,g,b,w,x,v,_,S,k,A,E=t.type;if(void 0!==t.constructor)return null;128&r.__u&&(l=!!(32&r.__u),s=[o=t.__e=r.__e]),(u=rA.__b)&&u(t);e:if("function"==typeof E)try{if(g=t.props,b="prototype"in E&&E.prototype.render,w=(u=E.contextType)&&i[u.__c],x=u?w?w.props.value:u.__:i,r.__c?m=(d=t.__c=r.__c).__=d.__E:(b?t.__c=d=new E(g,x):(t.__c=d=new rK(g,x),d.constructor=E,d.render=r0),w&&w.sub(d),d.props=g,d.state||(d.state={}),d.context=x,d.__n=i,f=d.__d=!0,d.__h=[],d._sb=[]),b&&null==d.__s&&(d.__s=d.state),b&&null!=E.getDerivedStateFromProps&&(d.__s==d.state&&(d.__s=rL({},d.__s)),rL(d.__s,E.getDerivedStateFromProps(g,d.__s))),h=d.props,p=d.state,d.__v=t,f)b&&null==E.getDerivedStateFromProps&&null!=d.componentWillMount&&d.componentWillMount(),b&&null!=d.componentDidMount&&d.__h.push(d.componentDidMount);else{if(b&&null==E.getDerivedStateFromProps&&g!==h&&null!=d.componentWillReceiveProps&&d.componentWillReceiveProps(g,x),!d.__e&&(null!=d.shouldComponentUpdate&&!1===d.shouldComponentUpdate(g,d.__s,x)||t.__v===r.__v)){for(t.__v!==r.__v&&(d.props=g,d.state=d.__s,d.__d=!1),t.__e=r.__e,t.__k=r.__k,t.__k.some(function(e){e&&(e.__=t)}),v=0;v<d._sb.length;v++)d.__h.push(d._sb[v]);d._sb=[],d.__h.length&&a.push(d);break e}null!=d.componentWillUpdate&&d.componentWillUpdate(g,d.__s,x),b&&null!=d.componentDidUpdate&&d.__h.push(function(){d.componentDidUpdate(h,p,y)})}if(d.context=x,d.props=g,d.__P=e,d.__e=!1,_=rA.__r,S=0,b){for(d.state=d.__s,d.__d=!1,_&&_(t),u=d.render(d.props,d.state,d.context),k=0;k<d._sb.length;k++)d.__h.push(d._sb[k]);d._sb=[]}else do d.__d=!1,_&&_(t),u=d.render(d.props,d.state,d.context),d.state=d.__s;while(d.__d&&++S<25);d.state=d.__s,null!=d.getChildContext&&(i=rL(rL({},i),d.getChildContext())),b&&!f&&null!=d.getSnapshotBeforeUpdate&&(y=d.getSnapshotBeforeUpdate(h,p)),rF(e,rD(A=null!=u&&u.type===rH&&null==u.key?u.props.children:u)?A:[A],t,r,i,n,s,a,o,l,c),d.base=t.__e,t.__u&=-161,d.__h.length&&a.push(d),m&&(d.__E=d.__=null)}catch(e){if(t.__v=null,l||null!=s){for(t.__u|=l?160:128;o&&8===o.nodeType&&o.nextSibling;)o=o.nextSibling;s[s.indexOf(o)]=null,t.__e=o}else t.__e=r.__e,t.__k=r.__k;rA.__e(e,t,r)}else null==s&&t.__v===r.__v?(t.__k=r.__k,t.__e=r.__e):t.__e=function(e,t,r,i,n,s,a,o,l){var c,u,d,f,h,p,y,m=r.props,g=t.props,b=t.type;if("svg"===b?n="http://www.w3.org/2000/svg":"math"===b?n="http://www.w3.org/1998/Math/MathML":n||(n="http://www.w3.org/1999/xhtml"),null!=s){for(c=0;c<s.length;c++)if((h=s[c])&&"setAttribute"in h==!!b&&(b?h.localName===b:3===h.nodeType)){e=h,s[c]=null;break}}if(null==e){if(null===b)return document.createTextNode(g);e=document.createElementNS(n,b,g.is&&g),o&&(rA.__m&&rA.__m(t,s),o=!1),s=null}if(null===b)m===g||o&&e.data===g||(e.data=g);else{if(s=s&&rk.call(e.childNodes),m=r.props||rj,!o&&null!=s)for(m={},c=0;c<e.attributes.length;c++)m[(h=e.attributes[c]).name]=h.value;for(c in m)if(h=m[c],"children"==c);else if("dangerouslySetInnerHTML"==c)d=h;else if(!(c in g)){if("value"==c&&"defaultValue"in g||"checked"==c&&"defaultChecked"in g)continue;rX(e,c,null,h,n)}for(c in g)h=g[c],"children"==c?f=h:"dangerouslySetInnerHTML"==c?u=h:"value"==c?p=h:"checked"==c?y=h:o&&"function"!=typeof h||m[c]===h||rX(e,c,h,m[c],n);if(u)o||d&&(u.__html===d.__html||u.__html===e.innerHTML)||(e.innerHTML=u.__html),t.__k=[];else if(d&&(e.innerHTML=""),rF(e,rD(f)?f:[f],t,r,i,"foreignObject"===b?"http://www.w3.org/1999/xhtml":n,s,a,s?s[0]:r.__k&&rW(r,0),o,l),null!=s)for(c=s.length;c--;)rN(s[c]);o||(c="value","progress"===b&&null==p?e.removeAttribute("value"):void 0===p||p===e[c]&&("progress"!==b||p)&&("option"!==b||p===m[c])||rX(e,c,p,m[c],n),c="checked",void 0!==y&&y!==e[c]&&rX(e,c,y,m[c],n))}return e}(r.__e,t,r,i,n,s,a,l,c);(u=rA.diffed)&&u(t)}function rY(e,t,r){t.__d=void 0;for(var i=0;i<r.length;i++)rZ(r[i],r[++i],r[++i]);rA.__c&&rA.__c(t,e),e.some(function(t){try{e=t.__h,t.__h=[],e.some(function(e){e.call(t)})}catch(e){rA.__e(e,t.__v)}})}function rZ(e,t,r){try{if("function"==typeof e){var i="function"==typeof e.__u;i&&e.__u(),i&&null==t||(e.__u=e(t))}else e.current=t}catch(e){rA.__e(e,r)}}function r0(e,t,r){return this.constructor(e,r)}function r1(e,t){var r,i,n,s,a;r=e,rA.__&&rA.__(r,t),n=(i="function"==typeof r1)?null:r1&&r1.__k||t.__k,s=[],a=[],rG(t,r=(!i&&r1||t).__k=rM(rH,null,[r]),n||rj,rj,t.namespaceURI,!i&&r1?[r1]:n?null:t.firstChild?rk.call(t.childNodes):null,s,!i&&r1?r1:n?n.__e:t.firstChild,i,a),rY(s,r,a)}rk=rU.slice,rA={__e:function(e,t,r,i){for(var n,s,a;t=t.__;)if((n=t.__c)&&!n.__)try{if((s=n.constructor)&&null!=s.getDerivedStateFromError&&(n.setState(s.getDerivedStateFromError(e)),a=n.__d),null!=n.componentDidCatch&&(n.componentDidCatch(e,i||{}),a=n.__d),a)return n.__E=n}catch(t){e=t}throw e}},rE=0,rK.prototype.setState=function(e,t){var r;r=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=rL({},this.state),"function"==typeof e&&(e=e(rL({},r),this.props)),e&&rL(r,e),null!=e&&this.__v&&(t&&this._sb.push(t),rQ(this))},rK.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),rQ(this))},rK.prototype.render=rH,rT=[],r$="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,rO=function(e,t){return e.__v.__b-t.__v.__b},rJ.__r=0,rR=0,rI=rz(!1),rP=rz(!0);var r2=/[\s\n\\/='"\0<>]/,r5=/^(xlink|xmlns|xml)([A-Z])/,r6=/^accessK|^auto[A-Z]|^cell|^ch|^col|cont|cross|dateT|encT|form[A-Z]|frame|hrefL|inputM|maxL|minL|noV|playsI|popoverT|readO|rowS|src[A-Z]|tabI|useM|item[A-Z]/,r3=/^ac|^ali|arabic|basel|cap|clipPath$|clipRule$|color|dominant|enable|fill|flood|font|glyph[^R]|horiz|image|letter|lighting|marker[^WUH]|overline|panose|pointe|paint|rendering|shape|stop|strikethrough|stroke|text[^L]|transform|underline|unicode|units|^v[^i]|^w|^xH/,r8=new Set(["draggable","spellcheck"]),r4=/["&<]/;function r9(e){if(0===e.length||!1===r4.test(e))return e;for(var t=0,r=0,i="",n="";r<e.length;r++){switch(e.charCodeAt(r)){case 34:n="&quot;";break;case 38:n="&amp;";break;case 60:n="&lt;";break;default:continue}r!==t&&(i+=e.slice(t,r)),i+=n,t=r+1}return r!==t&&(i+=e.slice(t,r)),i}var r7={},ie=new Set(["animation-iteration-count","border-image-outset","border-image-slice","border-image-width","box-flex","box-flex-group","box-ordinal-group","column-count","fill-opacity","flex","flex-grow","flex-negative","flex-order","flex-positive","flex-shrink","flood-opacity","font-weight","grid-column","grid-row","line-clamp","line-height","opacity","order","orphans","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-miterlimit","stroke-opacity","stroke-width","tab-size","widows","z-index","zoom"]),it=/[A-Z]/g;function ir(){this.__d=!0}var ii=null,is,ia,io,il,ic={},iu=[],id=Array.isArray,ih=Object.assign;function ip(e,t){var r,i=e.type,n=!0;return e.__c?(n=!1,(r=e.__c).state=r.__s):r=new i(e.props,t),e.__c=r,r.__v=e,r.props=e.props,r.context=t,r.__d=!0,null==r.state&&(r.state=ic),null==r.__s&&(r.__s=r.state),i.getDerivedStateFromProps?r.state=ih({},r.state,i.getDerivedStateFromProps(r.props,r.state)):n&&r.componentWillMount?(r.componentWillMount(),r.state=r.__s!==r.state?r.__s:r.state):!n&&r.componentWillUpdate&&r.componentWillUpdate(),io&&io(e),r.render(r.props,r.state,t)}var iy=new Set(["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]),im=/["&<]/,ig=0;function ib(e,t,r,i,n,s){t||(t={});var a,o,l=t;"ref"in t&&(a=t.ref,delete t.ref);var c={type:e,props:l,key:r,ref:a,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:--ig,__i:-1,__u:0,__source:n,__self:s};if("function"==typeof e&&(a=e.defaultProps))for(o in a)void 0===l[o]&&(l[o]=a[o]);return rA.vnode&&rA.vnode(c),c}async function iw(e,t){let r=window.SimpleWebAuthnBrowser;async function i(r){let i=new URL(`${e}/webauthn-options/${t}`);r&&i.searchParams.append("action",r),s().forEach(e=>{i.searchParams.append(e.name,e.value)});let n=await fetch(i);return n.ok?n.json():void console.error("Failed to fetch options",n)}function n(){let e=`#${t}-form`,r=document.querySelector(e);if(!r)throw Error(`Form '${e}' not found`);return r}function s(){return Array.from(n().querySelectorAll("input[data-form-field]"))}async function a(e,t){let r=n();if(e){let t=document.createElement("input");t.type="hidden",t.name="action",t.value=e,r.appendChild(t)}if(t){let e=document.createElement("input");e.type="hidden",e.name="data",e.value=JSON.stringify(t),r.appendChild(e)}return r.submit()}async function o(e,t){let i=await r.startAuthentication(e,t);return await a("authenticate",i)}async function l(e){s().forEach(e=>{if(e.required&&!e.value)throw Error(`Missing required field: ${e.name}`)});let t=await r.startRegistration(e);return await a("register",t)}async function c(){if(!r.browserSupportsWebAuthnAutofill())return;let e=await i("authenticate");if(!e)return void console.error("Failed to fetch option for autofill authentication");try{await o(e.options,!0)}catch(e){console.error(e)}}(async function(){let e=n();if(!r.browserSupportsWebAuthn()){e.style.display="none";return}e&&e.addEventListener("submit",async e=>{e.preventDefault();let t=await i(void 0);if(!t)return void console.error("Failed to fetch options for form submission");if("authenticate"===t.action)try{await o(t.options,!1)}catch(e){console.error(e)}else if("register"===t.action)try{await l(t.options)}catch(e){console.error(e)}})})(),c()}let ix={default:"Unable to sign in.",Signin:"Try signing in with a different account.",OAuthSignin:"Try signing in with a different account.",OAuthCallbackError:"Try signing in with a different account.",OAuthCreateAccount:"Try signing in with a different account.",EmailCreateAccount:"Try signing in with a different account.",Callback:"Try signing in with a different account.",OAuthAccountNotLinked:"To confirm your identity, sign in with the same account you used originally.",EmailSignin:"The e-mail could not be sent.",CredentialsSignin:"Sign in failed. Check the details you provided are correct.",SessionRequired:"Please sign in to access this page."},iv=`:root {
  --border-width: 1px;
  --border-radius: 0.5rem;
  --color-error: #c94b4b;
  --color-info: #157efb;
  --color-info-hover: #0f6ddb;
  --color-info-text: #fff;
}

.__next-auth-theme-auto,
.__next-auth-theme-light {
  --color-background: #ececec;
  --color-background-hover: rgba(236, 236, 236, 0.8);
  --color-background-card: #fff;
  --color-text: #000;
  --color-primary: #444;
  --color-control-border: #bbb;
  --color-button-active-background: #f9f9f9;
  --color-button-active-border: #aaa;
  --color-separator: #ccc;
  --provider-bg: #fff;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #fff
  );
}

.__next-auth-theme-dark {
  --color-background: #161b22;
  --color-background-hover: rgba(22, 27, 34, 0.8);
  --color-background-card: #0d1117;
  --color-text: #fff;
  --color-primary: #ccc;
  --color-control-border: #555;
  --color-button-active-background: #060606;
  --color-button-active-border: #666;
  --color-separator: #444;
  --provider-bg: #161b22;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #000
  );
}

.__next-auth-theme-dark img[src$="42-school.svg"],
  .__next-auth-theme-dark img[src$="apple.svg"],
  .__next-auth-theme-dark img[src$="boxyhq-saml.svg"],
  .__next-auth-theme-dark img[src$="eveonline.svg"],
  .__next-auth-theme-dark img[src$="github.svg"],
  .__next-auth-theme-dark img[src$="mailchimp.svg"],
  .__next-auth-theme-dark img[src$="medium.svg"],
  .__next-auth-theme-dark img[src$="okta.svg"],
  .__next-auth-theme-dark img[src$="patreon.svg"],
  .__next-auth-theme-dark img[src$="ping-id.svg"],
  .__next-auth-theme-dark img[src$="roblox.svg"],
  .__next-auth-theme-dark img[src$="threads.svg"],
  .__next-auth-theme-dark img[src$="wikimedia.svg"] {
    filter: invert(1);
  }

.__next-auth-theme-dark #submitButton {
    background-color: var(--provider-bg, var(--color-info));
  }

@media (prefers-color-scheme: dark) {
  .__next-auth-theme-auto {
    --color-background: #161b22;
    --color-background-hover: rgba(22, 27, 34, 0.8);
    --color-background-card: #0d1117;
    --color-text: #fff;
    --color-primary: #ccc;
    --color-control-border: #555;
    --color-button-active-background: #060606;
    --color-button-active-border: #666;
    --color-separator: #444;
    --provider-bg: #161b22;
    --provider-bg-hover: color-mix(
      in srgb,
      var(--provider-brand-color) 30%,
      #000
    );
  }
    .__next-auth-theme-auto img[src$="42-school.svg"],
    .__next-auth-theme-auto img[src$="apple.svg"],
    .__next-auth-theme-auto img[src$="boxyhq-saml.svg"],
    .__next-auth-theme-auto img[src$="eveonline.svg"],
    .__next-auth-theme-auto img[src$="github.svg"],
    .__next-auth-theme-auto img[src$="mailchimp.svg"],
    .__next-auth-theme-auto img[src$="medium.svg"],
    .__next-auth-theme-auto img[src$="okta.svg"],
    .__next-auth-theme-auto img[src$="patreon.svg"],
    .__next-auth-theme-auto img[src$="ping-id.svg"],
    .__next-auth-theme-auto img[src$="roblox.svg"],
    .__next-auth-theme-auto img[src$="threads.svg"],
    .__next-auth-theme-auto img[src$="wikimedia.svg"] {
      filter: invert(1);
    }
    .__next-auth-theme-auto #submitButton {
      background-color: var(--provider-bg, var(--color-info));
    }
}

html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
  margin: 0;
  padding: 0;
}

body {
  background-color: var(--color-background);
  margin: 0;
  padding: 0;
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
}

h1 {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  font-weight: 400;
  color: var(--color-text);
}

p {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  color: var(--color-text);
}

form {
  margin: 0;
  padding: 0;
}

label {
  font-weight: 500;
  text-align: left;
  margin-bottom: 0.25rem;
  display: block;
  color: var(--color-text);
}

input[type] {
  box-sizing: border-box;
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  border: var(--border-width) solid var(--color-control-border);
  background: var(--color-background-card);
  font-size: 1rem;
  border-radius: var(--border-radius);
  color: var(--color-text);
}

p {
  font-size: 1.1rem;
  line-height: 2rem;
}

a.button {
  text-decoration: none;
  line-height: 1rem;
}

a.button:link,
  a.button:visited {
    background-color: var(--color-background);
    color: var(--color-primary);
  }

button,
a.button {
  padding: 0.75rem 1rem;
  color: var(--provider-color, var(--color-primary));
  background-color: var(--provider-bg, var(--color-background));
  border: 1px solid #00000031;
  font-size: 0.9rem;
  height: 50px;
  border-radius: var(--border-radius);
  transition: background-color 250ms ease-in-out;
  font-weight: 300;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:is(button,a.button):hover {
    background-color: var(--provider-bg-hover, var(--color-background-hover));
    cursor: pointer;
  }

:is(button,a.button):active {
    cursor: pointer;
  }

:is(button,a.button) span {
    color: var(--provider-bg);
  }

#submitButton {
  color: var(--button-text-color, var(--color-info-text));
  background-color: var(--brand-color, var(--color-info));
  width: 100%;
}

#submitButton:hover {
    background-color: var(
      --button-hover-bg,
      var(--color-info-hover)
    ) !important;
  }

a.site {
  color: var(--color-primary);
  text-decoration: none;
  font-size: 1rem;
  line-height: 2rem;
}

a.site:hover {
    text-decoration: underline;
  }

.page {
  position: absolute;
  width: 100%;
  height: 100%;
  display: grid;
  place-items: center;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.page > div {
    text-align: center;
  }

.error a.button {
    padding-left: 2rem;
    padding-right: 2rem;
    margin-top: 0.5rem;
  }

.error .message {
    margin-bottom: 1.5rem;
  }

.signin input[type="text"] {
    margin-left: auto;
    margin-right: auto;
    display: block;
  }

.signin hr {
    display: block;
    border: 0;
    border-top: 1px solid var(--color-separator);
    margin: 2rem auto 1rem auto;
    overflow: visible;
  }

.signin hr::before {
      content: "or";
      background: var(--color-background-card);
      color: #888;
      padding: 0 0.4rem;
      position: relative;
      top: -0.7rem;
    }

.signin .error {
    background: #f5f5f5;
    font-weight: 500;
    border-radius: 0.3rem;
    background: var(--color-error);
  }

.signin .error p {
      text-align: left;
      padding: 0.5rem 1rem;
      font-size: 0.9rem;
      line-height: 1.2rem;
      color: var(--color-info-text);
    }

.signin > div,
  .signin form {
    display: block;
  }

.signin > div input[type], .signin form input[type] {
      margin-bottom: 0.5rem;
    }

.signin > div button, .signin form button {
      width: 100%;
    }

.signin .provider + .provider {
    margin-top: 1rem;
  }

.logo {
  display: inline-block;
  max-width: 150px;
  margin: 1.25rem 0;
  max-height: 70px;
}

.card {
  background-color: var(--color-background-card);
  border-radius: 1rem;
  padding: 1.25rem 2rem;
}

.card .header {
    color: var(--color-primary);
  }

.card input[type]::-moz-placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type]::placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type] {
    background: color-mix(in srgb, var(--color-background-card) 95%, black);
  }

.section-header {
  color: var(--color-text);
}

@media screen and (min-width: 450px) {
  .card {
    margin: 2rem 0;
    width: 368px;
  }
}

@media screen and (max-width: 450px) {
  .card {
    margin: 1rem 0;
    width: 343px;
  }
}
`;function i_({html:e,title:t,status:r,cookies:i,theme:n,headTags:s}){return{cookies:i,status:r,headers:{"Content-Type":"text/html"},body:`<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><style>${iv}</style><title>${t}</title>${s??""}</head><body class="__next-auth-theme-${n?.colorScheme??"auto"}"><div class="page">${function(e,t,r){var i=rA.__s;rA.__s=!0,is=rA.__b,ia=rA.diffed,io=rA.__r,il=rA.unmount;var n=rM(rH,null);n.__k=[e];try{var s=function e(t,r,i,n,s,a,o){if(null==t||!0===t||!1===t||""===t)return"";var l=typeof t;if("object"!=l)return"function"==l?"":"string"==l?r9(t):t+"";if(id(t)){var c,u="";s.__k=t;for(var d=0;d<t.length;d++){var f=t[d];if(null!=f&&"boolean"!=typeof f){var h,p=e(f,r,i,n,s,a,o);"string"==typeof p?u+=p:(c||(c=[]),u&&c.push(u),u="",id(p)?(h=c).push.apply(h,p):c.push(p))}}return c?(u&&c.push(u),c):u}if(void 0!==t.constructor)return"";t.__=s,is&&is(t);var y=t.type,m=t.props;if("function"==typeof y){var g,b,w,x=r;if(y===rH){if("tpl"in m){for(var v="",_=0;_<m.tpl.length;_++)if(v+=m.tpl[_],m.exprs&&_<m.exprs.length){var S=m.exprs[_];if(null==S)continue;"object"==typeof S&&(void 0===S.constructor||id(S))?v+=e(S,r,i,n,t,a,o):v+=S}return v}if("UNSTABLE_comment"in m)return"\x3c!--"+r9(m.UNSTABLE_comment)+"--\x3e";b=m.children}else{if(null!=(g=y.contextType)){var k=r[g.__c];x=k?k.props.value:g.__}var A=y.prototype&&"function"==typeof y.prototype.render;if(A)b=ip(t,x),w=t.__c;else{t.__c=w={__v:t,context:x,props:t.props,setState:ir,forceUpdate:ir,__d:!0,__h:[]};for(var E=0;w.__d&&E++<25;)w.__d=!1,io&&io(t),b=y.call(w,m,x);w.__d=!0}if(null!=w.getChildContext&&(r=ih({},r,w.getChildContext())),A&&rA.errorBoundaries&&(y.getDerivedStateFromError||w.componentDidCatch)){b=null!=b&&b.type===rH&&null==b.key&&null==b.props.tpl?b.props.children:b;try{return e(b,r,i,n,t,a,o)}catch(s){return y.getDerivedStateFromError&&(w.__s=y.getDerivedStateFromError(s)),w.componentDidCatch&&w.componentDidCatch(s,ic),w.__d?(b=ip(t,r),null!=(w=t.__c).getChildContext&&(r=ih({},r,w.getChildContext())),e(b=null!=b&&b.type===rH&&null==b.key&&null==b.props.tpl?b.props.children:b,r,i,n,t,a,o)):""}finally{ia&&ia(t),t.__=null,il&&il(t)}}}b=null!=b&&b.type===rH&&null==b.key&&null==b.props.tpl?b.props.children:b;try{var T=e(b,r,i,n,t,a,o);return ia&&ia(t),t.__=null,rA.unmount&&rA.unmount(t),T}catch(s){if(!a&&o&&o.onError){var C=o.onError(s,t,function(s){return e(s,r,i,n,t,a,o)});if(void 0!==C)return C;var $=rA.__e;return $&&$(s,t),""}if(!a||!s||"function"!=typeof s.then)throw s;return s.then(function s(){try{return e(b,r,i,n,t,a,o)}catch(l){if(!l||"function"!=typeof l.then)throw l;return l.then(function(){return e(b,r,i,n,t,a,o)},s)}})}}var O,R="<"+y,I="";for(var P in m){var j=m[P];if("function"!=typeof j||"class"===P||"className"===P){switch(P){case"children":O=j;continue;case"key":case"ref":case"__self":case"__source":continue;case"htmlFor":if("for"in m)continue;P="for";break;case"className":if("class"in m)continue;P="class";break;case"defaultChecked":P="checked";break;case"defaultSelected":P="selected";break;case"defaultValue":case"value":switch(P="value",y){case"textarea":O=j;continue;case"select":n=j;continue;case"option":n!=j||"selected"in m||(R+=" selected")}break;case"dangerouslySetInnerHTML":I=j&&j.__html;continue;case"style":"object"==typeof j&&(j=function(e){var t="";for(var r in e){var i=e[r];if(null!=i&&""!==i){var n="-"==r[0]?r:r7[r]||(r7[r]=r.replace(it,"-$&").toLowerCase()),s=";";"number"!=typeof i||n.startsWith("--")||ie.has(n)||(s="px;"),t=t+n+":"+i+s}}return t||void 0}(j));break;case"acceptCharset":P="accept-charset";break;case"httpEquiv":P="http-equiv";break;default:if(r5.test(P))P=P.replace(r5,"$1:$2").toLowerCase();else{if(r2.test(P))continue;("-"===P[4]||r8.has(P))&&null!=j?j+="":i?r3.test(P)&&(P="panose1"===P?"panose-1":P.replace(/([A-Z])/g,"-$1").toLowerCase()):r6.test(P)&&(P=P.toLowerCase())}}null!=j&&!1!==j&&(R=!0===j||""===j?R+" "+P:R+" "+P+'="'+("string"==typeof j?r9(j):j+"")+'"')}}if(r2.test(y))throw Error(y+" is not a valid HTML tag name in "+R+">");if(I||("string"==typeof O?I=r9(O):null!=O&&!1!==O&&!0!==O&&(I=e(O,r,"svg"===y||"foreignObject"!==y&&i,n,t,a,o))),ia&&ia(t),t.__=null,il&&il(t),!I&&iy.has(y))return R+"/>";var U="</"+y+">",q=R+">";return id(I)?[q].concat(I,[U]):"string"!=typeof I?[q,I,U]:q+I+U}(e,ic,!1,void 0,n,!1,void 0);return id(s)?s.join(""):s}catch(e){if(e.then)throw Error('Use "renderToStringAsync" for suspenseful rendering.');throw e}finally{rA.__c&&rA.__c(e,iu),rA.__s=i,iu.length=0}}(e)}</div></body></html>`}}function iS(e){let{url:t,theme:r,query:i,cookies:n,pages:s,providers:a}=e;return{csrf:(e,t,r)=>e?(t.logger.warn("csrf-disabled"),r.push({name:t.cookies.csrfToken.name,value:"",options:{...t.cookies.csrfToken.options,maxAge:0}}),{status:404,cookies:r}):{headers:{"Content-Type":"application/json","Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"},body:{csrfToken:t.csrfToken},cookies:r},providers:e=>({headers:{"Content-Type":"application/json"},body:e.reduce((e,{id:t,name:r,type:i,signinUrl:n,callbackUrl:s})=>(e[t]={id:t,name:r,type:i,signinUrl:n,callbackUrl:s},e),{})}),signin(t,o){if(t)throw new q("Unsupported action");if(s?.signIn){let t=`${s.signIn}${s.signIn.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:e.callbackUrl??"/"})}`;return o&&(t=`${t}&${new URLSearchParams({error:o})}`),{redirect:t,cookies:n}}let l=a?.find(e=>"webauthn"===e.type&&e.enableConditionalUI&&!!e.simpleWebAuthnBrowserVersion),c="";if(l){let{simpleWebAuthnBrowserVersion:e}=l;c=`<script src="https://unpkg.com/@simplewebauthn/browser@${e}/dist/bundle/index.umd.min.js" crossorigin="anonymous"></script>`}return i_({cookies:n,theme:r,html:function(e){let{csrfToken:t,providers:r=[],callbackUrl:i,theme:n,email:s,error:a}=e;"undefined"!=typeof document&&n?.brandColor&&document.documentElement.style.setProperty("--brand-color",n.brandColor),"undefined"!=typeof document&&n?.buttonText&&document.documentElement.style.setProperty("--button-text-color",n.buttonText);let o=a&&(ix[a]??ix.default),l=r.find(e=>"webauthn"===e.type&&e.enableConditionalUI)?.id;return ib("div",{className:"signin",children:[n?.brandColor&&ib("style",{dangerouslySetInnerHTML:{__html:`:root {--brand-color: ${n.brandColor}}`}}),n?.buttonText&&ib("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${n.buttonText}
        }
      `}}),ib("div",{className:"card",children:[o&&ib("div",{className:"error",children:ib("p",{children:o})}),n?.logo&&ib("img",{src:n.logo,alt:"Logo",className:"logo"}),r.map((e,n)=>{let a,o,l;("oauth"===e.type||"oidc"===e.type)&&({bg:a="#fff",brandColor:o,logo:l=`https://authjs.dev/img/providers/${e.id}.svg`}=e.style??{});let c=o??a??"#fff";return ib("div",{className:"provider",children:["oauth"===e.type||"oidc"===e.type?ib("form",{action:e.signinUrl,method:"POST",children:[ib("input",{type:"hidden",name:"csrfToken",value:t}),i&&ib("input",{type:"hidden",name:"callbackUrl",value:i}),ib("button",{type:"submit",className:"button",style:{"--provider-brand-color":c},tabIndex:0,children:[ib("span",{style:{filter:"invert(1) grayscale(1) brightness(1.3) contrast(9000)","mix-blend-mode":"luminosity",opacity:.95},children:["Sign in with ",e.name]}),l&&ib("img",{loading:"lazy",height:24,src:l})]})]}):null,("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&n>0&&"email"!==r[n-1].type&&"credentials"!==r[n-1].type&&"webauthn"!==r[n-1].type&&ib("hr",{}),"email"===e.type&&ib("form",{action:e.signinUrl,method:"POST",children:[ib("input",{type:"hidden",name:"csrfToken",value:t}),ib("label",{className:"section-header",htmlFor:`input-email-for-${e.id}-provider`,children:"Email"}),ib("input",{id:`input-email-for-${e.id}-provider`,autoFocus:!0,type:"email",name:"email",value:s,placeholder:"<EMAIL>",required:!0}),ib("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"credentials"===e.type&&ib("form",{action:e.callbackUrl,method:"POST",children:[ib("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.credentials).map(t=>ib("div",{children:[ib("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.credentials[t].label??t}),ib("input",{name:t,id:`input-${t}-for-${e.id}-provider`,type:e.credentials[t].type??"text",placeholder:e.credentials[t].placeholder??"",...e.credentials[t]})]},`input-group-${e.id}`)),ib("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"webauthn"===e.type&&ib("form",{action:e.callbackUrl,method:"POST",id:`${e.id}-form`,children:[ib("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.formFields).map(t=>ib("div",{children:[ib("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.formFields[t].label??t}),ib("input",{name:t,"data-form-field":!0,id:`input-${t}-for-${e.id}-provider`,type:e.formFields[t].type??"text",placeholder:e.formFields[t].placeholder??"",...e.formFields[t]})]},`input-group-${e.id}`)),ib("button",{id:`submitButton-${e.id}`,type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&n+1<r.length&&ib("hr",{})]},e.id)})]}),l&&ib(rH,{children:ib("script",{dangerouslySetInnerHTML:{__html:`
const currentURL = window.location.href;
const authURL = currentURL.substring(0, currentURL.lastIndexOf('/'));
(${iw})(authURL, "${l}");
`}})})]})}({csrfToken:e.csrfToken,providers:e.providers?.filter(e=>["email","oauth","oidc"].includes(e.type)||"credentials"===e.type&&e.credentials||"webauthn"===e.type&&e.formFields||!1),callbackUrl:e.callbackUrl,theme:e.theme,error:o,...i}),title:"Sign In",headTags:c})},signout:()=>s?.signOut?{redirect:s.signOut,cookies:n}:i_({cookies:n,theme:r,html:function(e){let{url:t,csrfToken:r,theme:i}=e;return ib("div",{className:"signout",children:[i?.brandColor&&ib("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${i.brandColor}
        }
      `}}),i?.buttonText&&ib("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${i.buttonText}
        }
      `}}),ib("div",{className:"card",children:[i?.logo&&ib("img",{src:i.logo,alt:"Logo",className:"logo"}),ib("h1",{children:"Signout"}),ib("p",{children:"Are you sure you want to sign out?"}),ib("form",{action:t?.toString(),method:"POST",children:[ib("input",{type:"hidden",name:"csrfToken",value:r}),ib("button",{id:"submitButton",type:"submit",children:"Sign out"})]})]})]})}({csrfToken:e.csrfToken,url:t,theme:r}),title:"Sign Out"}),verifyRequest:e=>s?.verifyRequest?{redirect:`${s.verifyRequest}${t?.search??""}`,cookies:n}:i_({cookies:n,theme:r,html:function(e){let{url:t,theme:r}=e;return ib("div",{className:"verify-request",children:[r.brandColor&&ib("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${r.brandColor}
        }
      `}}),ib("div",{className:"card",children:[r.logo&&ib("img",{src:r.logo,alt:"Logo",className:"logo"}),ib("h1",{children:"Check your email"}),ib("p",{children:"A sign in link has been sent to your email address."}),ib("p",{children:ib("a",{className:"site",href:t.origin,children:t.host})})]})]})}({url:t,theme:r,...e}),title:"Verify Request"}),error:e=>s?.error?{redirect:`${s.error}${s.error.includes("?")?"&":"?"}error=${e}`,cookies:n}:i_({cookies:n,theme:r,...function(e){let{url:t,error:r="default",theme:i}=e,n=`${t}/signin`,s={default:{status:200,heading:"Error",message:ib("p",{children:ib("a",{className:"site",href:t?.origin,children:t?.host})})},Configuration:{status:500,heading:"Server error",message:ib("div",{children:[ib("p",{children:"There is a problem with the server configuration."}),ib("p",{children:"Check the server logs for more information."})]})},AccessDenied:{status:403,heading:"Access Denied",message:ib("div",{children:[ib("p",{children:"You do not have permission to sign in."}),ib("p",{children:ib("a",{className:"button",href:n,children:"Sign in"})})]})},Verification:{status:403,heading:"Unable to sign in",message:ib("div",{children:[ib("p",{children:"The sign in link is no longer valid."}),ib("p",{children:"It may have been used already or it may have expired."})]}),signin:ib("a",{className:"button",href:n,children:"Sign in"})}},{status:a,heading:o,message:l,signin:c}=s[r]??s.default;return{status:a,html:ib("div",{className:"error",children:[i?.brandColor&&ib("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${i?.brandColor}
        }
      `}}),ib("div",{className:"card",children:[i?.logo&&ib("img",{src:i?.logo,alt:"Logo",className:"logo"}),ib("h1",{children:o}),ib("div",{className:"message",children:l}),c]})]})}}({url:t,theme:r,error:e}),title:"Error"})}}function ik(e,t=Date.now()){return new Date(t+1e3*e)}async function iA(e,t,r,i){if(!r?.providerAccountId||!r.type)throw Error("Missing or invalid provider account");if(!["email","oauth","oidc","webauthn"].includes(r.type))throw Error("Provider not supported");let{adapter:n,jwt:s,events:a,session:{strategy:o,generateSessionToken:l}}=i;if(!n)return{user:t,account:r};let c=r,{createUser:u,updateUser:d,getUser:f,getUserByAccount:h,getUserByEmail:p,linkAccount:y,createSession:m,getSessionAndUser:g,deleteSession:b}=n,w=null,x=null,v=!1,_="jwt"===o;if(e)if(_)try{let t=i.cookies.sessionToken.name;(w=await s.decode({...s,token:e,salt:t}))&&"sub"in w&&w.sub&&(x=await f(w.sub))}catch{}else{let t=await g(e);t&&(w=t.session,x=t.user)}if("email"===c.type){let r=await p(t.email);return r?(x?.id!==r.id&&!_&&e&&await b(e),x=await d({id:r.id,emailVerified:new Date}),await a.updateUser?.({user:x})):(x=await u({...t,emailVerified:new Date}),await a.createUser?.({user:x}),v=!0),{session:w=_?{}:await m({sessionToken:l(),userId:x.id,expires:ik(i.session.maxAge)}),user:x,isNewUser:v}}if("webauthn"===c.type){let e=await h({providerAccountId:c.providerAccountId,provider:c.provider});if(e){if(x){if(e.id===x.id){let e={...c,userId:x.id};return{session:w,user:x,isNewUser:v,account:e}}throw new J("The account is already associated with another user",{provider:c.provider})}w=_?{}:await m({sessionToken:l(),userId:e.id,expires:ik(i.session.maxAge)});let t={...c,userId:e.id};return{session:w,user:e,isNewUser:v,account:t}}{if(x){await y({...c,userId:x.id}),await a.linkAccount?.({user:x,account:c,profile:t});let e={...c,userId:x.id};return{session:w,user:x,isNewUser:v,account:e}}if(t.email?await p(t.email):null)throw new J("Another account already exists with the same e-mail address",{provider:c.provider});x=await u({...t}),await a.createUser?.({user:x}),await y({...c,userId:x.id}),await a.linkAccount?.({user:x,account:c,profile:t}),w=_?{}:await m({sessionToken:l(),userId:x.id,expires:ik(i.session.maxAge)});let e={...c,userId:x.id};return{session:w,user:x,isNewUser:!0,account:e}}}let S=await h({providerAccountId:c.providerAccountId,provider:c.provider});if(S){if(x){if(S.id===x.id)return{session:w,user:x,isNewUser:v};throw new $("The account is already associated with another user",{provider:c.provider})}return{session:w=_?{}:await m({sessionToken:l(),userId:S.id,expires:ik(i.session.maxAge)}),user:S,isNewUser:v}}{let{provider:e}=i,{type:r,provider:n,providerAccountId:s,userId:o,...d}=c;if(c=Object.assign(e.account(d)??{},{providerAccountId:s,provider:n,type:r,userId:o}),x)return await y({...c,userId:x.id}),await a.linkAccount?.({user:x,account:c,profile:t}),{session:w,user:x,isNewUser:v};let f=t.email?await p(t.email):null;if(f){let e=i.provider;if(e?.allowDangerousEmailAccountLinking)x=f,v=!1;else throw new $("Another account already exists with the same e-mail address",{provider:c.provider})}else x=await u({...t,emailVerified:null}),v=!0;return await a.createUser?.({user:x}),await y({...c,userId:x.id}),await a.linkAccount?.({user:x,account:c,profile:t}),{session:w=_?{}:await m({sessionToken:l(),userId:x.id,expires:ik(i.session.maxAge)}),user:x,isNewUser:v}}}function iE(e,t){if(null==e)return!1;try{return e instanceof t||Object.getPrototypeOf(e)[Symbol.toStringTag]===t.prototype[Symbol.toStringTag]}catch{return!1}}"undefined"!=typeof navigator&&navigator.userAgent?.startsWith?.("Mozilla/5.0 ")||(s="oauth4webapi/v3.5.1");let iT="ERR_INVALID_ARG_VALUE",iC="ERR_INVALID_ARG_TYPE";function i$(e,t,r){let i=TypeError(e,{cause:r});return Object.assign(i,{code:t}),i}let iO=Symbol(),iR=Symbol(),iI=Symbol(),iP=Symbol(),ij=Symbol(),iU=Symbol(),iq=Symbol(),iD=new TextEncoder,iL=new TextDecoder;function iN(e){return"string"==typeof e?iD.encode(e):iL.decode(e)}function iM(e){if("string"==typeof e)try{let t=atob(e.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"")),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r}catch(e){throw i$("The input to be decoded is not correctly encoded.",iT,e)}var t=e;t instanceof ArrayBuffer&&(t=new Uint8Array(t));let r=[];for(let e=0;e<t.byteLength;e+=32768)r.push(String.fromCharCode.apply(null,t.subarray(e,e+32768)));return btoa(r.join("")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}class iB extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=nz,Error.captureStackTrace?.(this,this.constructor)}}class iH extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,t?.code&&(this.code=t?.code),Error.captureStackTrace?.(this,this.constructor)}}function iK(e,t,r){return new iH(e,{code:t,cause:r})}function iW(e,t){if(!(e instanceof CryptoKey))throw i$(`${t} must be a CryptoKey`,iC)}function iQ(e,t){if(iW(e,t),"private"!==e.type)throw i$(`${t} must be a private CryptoKey`,iT)}function iJ(e){return!(null===e||"object"!=typeof e||Array.isArray(e))}function iF(e){iE(e,Headers)&&(e=Object.fromEntries(e.entries()));let t=new Headers(e);if(s&&!t.has("user-agent")&&t.set("user-agent",s),t.has("authorization"))throw i$('"options.headers" must not include the "authorization" header name',iT);return t}function iV(e){if("function"==typeof e&&(e=e()),!(e instanceof AbortSignal))throw i$('"options.signal" must return or be an instance of AbortSignal',iC);return e}function iX(e){return e.includes("//")?e.replace("//","/"):e}async function iz(e,t,r,i){if(!(e instanceof URL))throw i$(`"${t}" must be an instance of URL`,iC);na(e,i?.[iO]!==!0);let n=r(new URL(e.href)),s=iF(i?.headers);return s.set("accept","application/json"),(i?.[iP]||fetch)(n.href,{body:void 0,headers:Object.fromEntries(s.entries()),method:"GET",redirect:"manual",signal:i?.signal?iV(i.signal):void 0})}async function iG(e,t){return iz(e,"issuerIdentifier",e=>{switch(t?.algorithm){case void 0:case"oidc":e.pathname=iX(`${e.pathname}/.well-known/openid-configuration`);break;case"oauth2":var r,i;i=".well-known/oauth-authorization-server","/"===(r=e).pathname?r.pathname=i:r.pathname=iX(`${i}/${r.pathname}`);break;default:throw i$('"options.algorithm" must be "oidc" (default), or "oauth2"',iT)}return e},t)}function iY(e,t,r,i,n){try{if("number"!=typeof e||!Number.isFinite(e))throw i$(`${r} must be a number`,iC,n);if(e>0)return;if(t){if(0!==e)throw i$(`${r} must be a non-negative number`,iT,n);return}throw i$(`${r} must be a positive number`,iT,n)}catch(e){if(i)throw iK(e.message,i,n);throw e}}function iZ(e,t,r,i){try{if("string"!=typeof e)throw i$(`${t} must be a string`,iC,i);if(0===e.length)throw i$(`${t} must not be empty`,iT,i)}catch(e){if(r)throw iK(e.message,r,i);throw e}}async function i0(e,t){if(!(e instanceof URL)&&e!==sk)throw i$('"expectedIssuerIdentifier" must be an instance of URL',iC);if(!iE(t,Response))throw i$('"response" must be an instance of Response',iC);if(200!==t.status)throw iK('"response" is not a conform Authorization Server Metadata response (unexpected HTTP status code)',n5,t);si(t);let r=await s_(t);if(iZ(r.issuer,'"response" body "issuer" property',n0,{body:r}),e!==sk&&new URL(r.issuer).href!==e.href)throw iK('"response" body "issuer" property does not match the expected value',n9,{expected:e.href,body:r,attribute:"issuer"});return r}function i1(e){var t=e,r="application/json";if(nE(t)!==r)throw i2(t,r)}function i2(e,...t){let r='"response" content-type must be ';if(t.length>2){let e=t.pop();r+=`${t.join(", ")}, or ${e}`}else 2===t.length?r+=`${t[0]} or ${t[1]}`:r+=t[0];return iK(r,n2,e)}function i5(){return iM(crypto.getRandomValues(new Uint8Array(32)))}async function i6(e){return iZ(e,"codeVerifier"),iM(await crypto.subtle.digest("SHA-256",iN(e)))}function i3(e){switch(e.algorithm.name){case"RSA-PSS":switch(e.algorithm.hash.name){case"SHA-256":return"PS256";case"SHA-384":return"PS384";case"SHA-512":return"PS512";default:throw new iB("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}case"RSASSA-PKCS1-v1_5":switch(e.algorithm.hash.name){case"SHA-256":return"RS256";case"SHA-384":return"RS384";case"SHA-512":return"RS512";default:throw new iB("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}case"ECDSA":switch(e.algorithm.namedCurve){case"P-256":return"ES256";case"P-384":return"ES384";case"P-521":return"ES512";default:throw new iB("unsupported EcKeyAlgorithm namedCurve",{cause:e})}case"Ed25519":case"EdDSA":return"Ed25519";default:throw new iB("unsupported CryptoKey algorithm name",{cause:e})}}function i8(e){let t=e?.[iR];return"number"==typeof t&&Number.isFinite(t)?t:0}function i4(e){let t=e?.[iI];return"number"==typeof t&&Number.isFinite(t)&&-1!==Math.sign(t)?t:30}function i9(){return Math.floor(Date.now()/1e3)}function i7(e){if("object"!=typeof e||null===e)throw i$('"as" must be an object',iC);iZ(e.issuer,'"as.issuer"')}function ne(e){if("object"!=typeof e||null===e)throw i$('"client" must be an object',iC);iZ(e.client_id,'"client.client_id"')}function nt(e,t){let r=i9()+i8(t);return{jti:i5(),aud:e.issuer,exp:r+60,iat:r,nbf:r,iss:t.client_id,sub:t.client_id}}async function nr(e,t,r){if(!r.usages.includes("sign"))throw i$('CryptoKey instances used for signing assertions must include "sign" in their "usages"',iT);let i=`${iM(iN(JSON.stringify(e)))}.${iM(iN(JSON.stringify(t)))}`,n=iM(await crypto.subtle.sign(sl(r),r,iN(i)));return`${i}.${n}`}async function ni(e){let{kty:t,e:r,n:i,x:n,y:s,crv:o}=await crypto.subtle.exportKey("jwk",e),l={kty:t,e:r,n:i,x:n,y:s,crv:o};return a.set(e,l),l}async function nn(e){return(a||=new WeakMap).get(e)||ni(e)}let ns=URL.parse?(e,t)=>URL.parse(e,t):(e,t)=>{try{return new URL(e,t)}catch{return null}};function na(e,t){if(t&&"https:"!==e.protocol)throw iK("only requests to HTTPS are allowed",n6,e);if("https:"!==e.protocol&&"http:"!==e.protocol)throw iK("only HTTP and HTTPS requests are allowed",n3,e)}function no(e,t,r,i){let n;if("string"!=typeof e||!(n=ns(e)))throw iK(`authorization server metadata does not contain a valid ${r?`"as.mtls_endpoint_aliases.${t}"`:`"as.${t}"`}`,void 0===e?se:st,{attribute:r?`mtls_endpoint_aliases.${t}`:t});return na(n,i),n}function nl(e,t,r,i){return r&&e.mtls_endpoint_aliases&&t in e.mtls_endpoint_aliases?no(e.mtls_endpoint_aliases[t],t,r,i):no(e[t],t,r,i)}class nc extends Error{cause;code;error;status;error_description;response;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=nX,this.cause=t.cause,this.error=t.cause.error,this.status=t.response.status,this.error_description=t.cause.error_description,Object.defineProperty(this,"response",{enumerable:!1,value:t.response}),Error.captureStackTrace?.(this,this.constructor)}}class nu extends Error{cause;code;error;error_description;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=nG,this.cause=t.cause,this.error=t.cause.get("error"),this.error_description=t.cause.get("error_description")??void 0,Error.captureStackTrace?.(this,this.constructor)}}class nd extends Error{cause;code;response;status;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=nV,this.cause=t.cause,this.status=t.response.status,this.response=t.response,Object.defineProperty(this,"response",{enumerable:!1}),Error.captureStackTrace?.(this,this.constructor)}}let nf="[a-zA-Z0-9!#$%&\\'\\*\\+\\-\\.\\^_`\\|~]+",nh=RegExp("^[,\\s]*("+nf+")\\s(.*)"),np=RegExp("^[,\\s]*("+nf+')\\s*=\\s*"((?:[^"\\\\]|\\\\.)*)"[,\\s]*(.*)'),ny=RegExp("^[,\\s]*"+("("+nf+")\\s*=\\s*(")+nf+")[,\\s]*(.*)"),nm=RegExp("^([a-zA-Z0-9\\-\\._\\~\\+\\/]+[=]{0,2})(?:$|[,\\s])(.*)");async function ng(e){if(e.status>399&&e.status<500){si(e),i1(e);try{let t=await e.clone().json();if(iJ(t)&&"string"==typeof t.error&&t.error.length)return t}catch{}}}async function nb(e,t,r){if(e.status!==t){let t;if(t=await ng(e))throw await e.body?.cancel(),new nc("server responded with an error in the response body",{cause:t,response:e});throw iK(`"response" is not a conform ${r} response (unexpected HTTP status code)`,n5,e)}}function nw(e){if(!nN.has(e))throw i$('"options.DPoP" is not a valid DPoPHandle',iT)}async function nx(e,t,r,i,n,s){if(iZ(e,'"accessToken"'),!(r instanceof URL))throw i$('"url" must be an instance of URL',iC);na(r,s?.[iO]!==!0),i=iF(i),s?.DPoP&&(nw(s.DPoP),await s.DPoP.addProof(r,i,t.toUpperCase(),e)),i.set("authorization",`${i.has("dpop")?"DPoP":"Bearer"} ${e}`);let a=await (s?.[iP]||fetch)(r.href,{body:n,headers:Object.fromEntries(i.entries()),method:t,redirect:"manual",signal:s?.signal?iV(s.signal):void 0});return s?.DPoP?.cacheNonce(a),a}async function nv(e,t,r,i){i7(e),ne(t);let n=nl(e,"userinfo_endpoint",t.use_mtls_endpoint_aliases,i?.[iO]!==!0),s=iF(i?.headers);return t.userinfo_signed_response_alg?s.set("accept","application/jwt"):(s.set("accept","application/json"),s.append("accept","application/jwt")),nx(r,"GET",n,s,null,{...i,[iR]:i8(t)})}function n_(e,t,r,i){(o||=new WeakMap).set(e,{jwks:t,uat:r,get age(){return i9()-this.uat}}),i&&Object.assign(i,{jwks:structuredClone(t),uat:r})}function nS(e,t){o?.delete(e),delete t?.jwks,delete t?.uat}async function nk(e,t,r){var i;let n,s,a,{alg:l,kid:c}=r;if(function(e){if(!sa(e.alg))throw new iB('unsupported JWS "alg" identifier',{cause:{alg:e.alg}})}(r),!o?.has(e)&&!("object"!=typeof(i=t?.[iq])||null===i||!("uat"in i)||"number"!=typeof i.uat||i9()-i.uat>=300)&&"jwks"in i&&iJ(i.jwks)&&Array.isArray(i.jwks.keys)&&Array.prototype.every.call(i.jwks.keys,iJ)&&n_(e,t?.[iq].jwks,t?.[iq].uat),o?.has(e)){if({jwks:n,age:s}=o.get(e),s>=300)return nS(e,t?.[iq]),nk(e,t,r)}else n=await sn(e,t).then(ss),s=0,n_(e,n,i9(),t?.[iq]);switch(l.slice(0,2)){case"RS":case"PS":a="RSA";break;case"ES":a="EC";break;case"Ed":a="OKP";break;default:throw new iB("unsupported JWS algorithm",{cause:{alg:l}})}let u=n.keys.filter(e=>{if(e.kty!==a||void 0!==c&&c!==e.kid||void 0!==e.alg&&l!==e.alg||void 0!==e.use&&"sig"!==e.use||e.key_ops?.includes("verify")===!1)return!1;switch(!0){case"ES256"===l&&"P-256"!==e.crv:case"ES384"===l&&"P-384"!==e.crv:case"ES512"===l&&"P-521"!==e.crv:case"Ed25519"===l&&"Ed25519"!==e.crv:case"EdDSA"===l&&"Ed25519"!==e.crv:return!1}return!0}),{0:d,length:f}=u;if(!f){if(s>=60)return nS(e,t?.[iq]),nk(e,t,r);throw iK("error when selecting a JWT verification key, no applicable keys found",n7,{header:r,candidates:u,jwks_uri:new URL(e.jwks_uri)})}if(1!==f)throw iK('error when selecting a JWT verification key, multiple applicable keys found, a "kid" JWT Header Parameter is required',n7,{header:r,candidates:u,jwks_uri:new URL(e.jwks_uri)});return sx(l,d)}let nA=Symbol();function nE(e){return e.headers.get("content-type")?.split(";")[0]}async function nT(e,t,r,i,n){let s;if(i7(e),ne(t),!iE(i,Response))throw i$('"response" must be an instance of Response',iC);if(nj(i),200!==i.status)throw iK('"response" is not a conform UserInfo Endpoint response (unexpected HTTP status code)',n5,i);if(si(i),"application/jwt"===nE(i)){let{claims:r,jwt:a}=await su(await i.text(),sy.bind(void 0,t.userinfo_signed_response_alg,e.userinfo_signing_alg_values_supported,void 0),i8(t),i4(t),n?.[iU]).then(nU.bind(void 0,t.client_id)).then(nD.bind(void 0,e));nR.set(i,a),s=r}else{if(t.userinfo_signed_response_alg)throw iK("JWT UserInfo Response expected",nY,i);s=await s_(i)}if(iZ(s.sub,'"response" body "sub" property',n0,{body:s}),r===nA);else if(iZ(r,'"expectedSubject"'),s.sub!==r)throw iK('unexpected "response" body "sub" property value',n9,{expected:r,body:s,attribute:"sub"});return s}async function nC(e,t,r,i,n,s,a){return await r(e,t,n,s),s.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),(a?.[iP]||fetch)(i.href,{body:n,headers:Object.fromEntries(s.entries()),method:"POST",redirect:"manual",signal:a?.signal?iV(a.signal):void 0})}async function n$(e,t,r,i,n,s){let a=nl(e,"token_endpoint",t.use_mtls_endpoint_aliases,s?.[iO]!==!0);n.set("grant_type",i);let o=iF(s?.headers);o.set("accept","application/json"),s?.DPoP!==void 0&&(nw(s.DPoP),await s.DPoP.addProof(a,o,"POST"));let l=await nC(e,t,r,a,n,o,s);return s?.DPoP?.cacheNonce(l),l}let nO=new WeakMap,nR=new WeakMap;function nI(e){if(!e.id_token)return;let t=nO.get(e);if(!t)throw i$('"ref" was already garbage collected or did not resolve from the proper sources',iT);return t}async function nP(e,t,r,i,n){if(i7(e),ne(t),!iE(r,Response))throw i$('"response" must be an instance of Response',iC);nj(r),await nb(r,200,"Token Endpoint"),si(r);let s=await s_(r);if(iZ(s.access_token,'"response" body "access_token" property',n0,{body:s}),iZ(s.token_type,'"response" body "token_type" property',n0,{body:s}),s.token_type=s.token_type.toLowerCase(),"dpop"!==s.token_type&&"bearer"!==s.token_type)throw new iB("unsupported `token_type` value",{cause:{body:s}});if(void 0!==s.expires_in){let e="number"!=typeof s.expires_in?parseFloat(s.expires_in):s.expires_in;iY(e,!1,'"response" body "expires_in" property',n0,{body:s}),s.expires_in=e}if(void 0!==s.refresh_token&&iZ(s.refresh_token,'"response" body "refresh_token" property',n0,{body:s}),void 0!==s.scope&&"string"!=typeof s.scope)throw iK('"response" body "scope" property must be a string',n0,{body:s});if(void 0!==s.id_token){iZ(s.id_token,'"response" body "id_token" property',n0,{body:s});let a=["aud","exp","iat","iss","sub"];!0===t.require_auth_time&&a.push("auth_time"),void 0!==t.default_max_age&&(iY(t.default_max_age,!1,'"client.default_max_age"'),a.push("auth_time")),i?.length&&a.push(...i);let{claims:o,jwt:l}=await su(s.id_token,sy.bind(void 0,t.id_token_signed_response_alg,e.id_token_signing_alg_values_supported,"RS256"),i8(t),i4(t),n?.[iU]).then(nH.bind(void 0,a)).then(nL.bind(void 0,e)).then(nq.bind(void 0,t.client_id));if(Array.isArray(o.aud)&&1!==o.aud.length){if(void 0===o.azp)throw iK('ID Token "aud" (audience) claim includes additional untrusted audiences',n4,{claims:o,claim:"aud"});if(o.azp!==t.client_id)throw iK('unexpected ID Token "azp" (authorized party) claim value',n4,{expected:t.client_id,claims:o,claim:"azp"})}void 0!==o.auth_time&&iY(o.auth_time,!1,'ID Token "auth_time" (authentication time)',n0,{claims:o}),nR.set(r,l),nO.set(s,o)}return s}function nj(e){let t;if(t=function(e){if(!iE(e,Response))throw i$('"response" must be an instance of Response',iC);let t=e.headers.get("www-authenticate");if(null===t)return;let r=[],i=t;for(;i;){let e,t=i.match(nh),n=t?.["1"].toLowerCase();if(i=t?.["2"],!n)return;let s={};for(;i;){let r,n;if(t=i.match(np)){if([,r,n,i]=t,n.includes("\\"))try{n=JSON.parse(`"${n}"`)}catch{}s[r.toLowerCase()]=n;continue}if(t=i.match(ny)){[,r,n,i]=t,s[r.toLowerCase()]=n;continue}if(t=i.match(nm)){if(Object.keys(s).length)break;[,e,i]=t;break}return}let a={scheme:n,parameters:s};e&&(a.token68=e),r.push(a)}if(r.length)return r}(e))throw new nd("server responded with a challenge in the WWW-Authenticate HTTP Header",{cause:t,response:e})}function nU(e,t){return void 0!==t.claims.aud?nq(e,t):t}function nq(e,t){if(Array.isArray(t.claims.aud)){if(!t.claims.aud.includes(e))throw iK('unexpected JWT "aud" (audience) claim value',n4,{expected:e,claims:t.claims,claim:"aud"})}else if(t.claims.aud!==e)throw iK('unexpected JWT "aud" (audience) claim value',n4,{expected:e,claims:t.claims,claim:"aud"});return t}function nD(e,t){return void 0!==t.claims.iss?nL(e,t):t}function nL(e,t){let r=e[sA]?.(t)??e.issuer;if(t.claims.iss!==r)throw iK('unexpected JWT "iss" (issuer) claim value',n4,{expected:r,claims:t.claims,claim:"iss"});return t}let nN=new WeakSet;async function nM(e,t,r,i,n,s,a){if(i7(e),ne(t),!nN.has(i))throw i$('"callbackParameters" must be an instance of URLSearchParams obtained from "validateAuthResponse()", or "validateJwtAuthResponse()',iT);iZ(n,'"redirectUri"');let o=sm(i,"code");if(!o)throw iK('no authorization code in "callbackParameters"',n0);let l=new URLSearchParams(a?.additionalParameters);return l.set("redirect_uri",n),l.set("code",o),s!==sS&&(iZ(s,'"codeVerifier"'),l.set("code_verifier",s)),n$(e,t,r,"authorization_code",l,a)}let nB={aud:"audience",c_hash:"code hash",client_id:"client id",exp:"expiration time",iat:"issued at",iss:"issuer",jti:"jwt id",nonce:"nonce",s_hash:"state hash",sub:"subject",ath:"access token hash",htm:"http method",htu:"http uri",cnf:"confirmation",auth_time:"authentication time"};function nH(e,t){for(let r of e)if(void 0===t.claims[r])throw iK(`JWT "${r}" (${nB[r]}) claim missing`,n0,{claims:t.claims});return t}let nK=Symbol(),nW=Symbol();async function nQ(e,t,r,i){return"string"==typeof i?.expectedNonce||"number"==typeof i?.maxAge||i?.requireIdToken?nJ(e,t,r,i.expectedNonce,i.maxAge,{[iU]:i[iU]}):nF(e,t,r,i)}async function nJ(e,t,r,i,n,s){let a=[];switch(i){case void 0:i=nK;break;case nK:break;default:iZ(i,'"expectedNonce" argument'),a.push("nonce")}switch(n??=t.default_max_age){case void 0:n=nW;break;case nW:break;default:iY(n,!1,'"maxAge" argument'),a.push("auth_time")}let o=await nP(e,t,r,a,s);iZ(o.id_token,'"response" body "id_token" property',n0,{body:o});let l=nI(o);if(n!==nW){let e=i9()+i8(t),r=i4(t);if(l.auth_time+n<e-r)throw iK("too much time has elapsed since the last End-User authentication",n8,{claims:l,now:e,tolerance:r,claim:"auth_time"})}if(i===nK){if(void 0!==l.nonce)throw iK('unexpected ID Token "nonce" claim value',n4,{expected:void 0,claims:l,claim:"nonce"})}else if(l.nonce!==i)throw iK('unexpected ID Token "nonce" claim value',n4,{expected:i,claims:l,claim:"nonce"});return o}async function nF(e,t,r,i){let n=await nP(e,t,r,void 0,i),s=nI(n);if(s){if(void 0!==t.default_max_age){iY(t.default_max_age,!1,'"client.default_max_age"');let e=i9()+i8(t),r=i4(t);if(s.auth_time+t.default_max_age<e-r)throw iK("too much time has elapsed since the last End-User authentication",n8,{claims:s,now:e,tolerance:r,claim:"auth_time"})}if(void 0!==s.nonce)throw iK('unexpected ID Token "nonce" claim value',n4,{expected:void 0,claims:s,claim:"nonce"})}return n}let nV="OAUTH_WWW_AUTHENTICATE_CHALLENGE",nX="OAUTH_RESPONSE_BODY_ERROR",nz="OAUTH_UNSUPPORTED_OPERATION",nG="OAUTH_AUTHORIZATION_RESPONSE_ERROR",nY="OAUTH_JWT_USERINFO_EXPECTED",nZ="OAUTH_PARSE_ERROR",n0="OAUTH_INVALID_RESPONSE",n1="OAUTH_INVALID_REQUEST",n2="OAUTH_RESPONSE_IS_NOT_JSON",n5="OAUTH_RESPONSE_IS_NOT_CONFORM",n6="OAUTH_HTTP_REQUEST_FORBIDDEN",n3="OAUTH_REQUEST_PROTOCOL_FORBIDDEN",n8="OAUTH_JWT_TIMESTAMP_CHECK_FAILED",n4="OAUTH_JWT_CLAIM_COMPARISON_FAILED",n9="OAUTH_JSON_ATTRIBUTE_COMPARISON_FAILED",n7="OAUTH_KEY_SELECTION_FAILED",se="OAUTH_MISSING_SERVER_METADATA",st="OAUTH_INVALID_SERVER_METADATA";function sr(e,t){if("string"!=typeof t.header.typ||t.header.typ.toLowerCase().replace(/^application\//,"")!==e)throw iK('unexpected JWT "typ" header parameter value',n0,{header:t.header});return t}function si(e){if(e.bodyUsed)throw i$('"response" body has been used already',iT)}async function sn(e,t){i7(e);let r=nl(e,"jwks_uri",!1,t?.[iO]!==!0),i=iF(t?.headers);return i.set("accept","application/json"),i.append("accept","application/jwk-set+json"),(t?.[iP]||fetch)(r.href,{body:void 0,headers:Object.fromEntries(i.entries()),method:"GET",redirect:"manual",signal:t?.signal?iV(t.signal):void 0})}async function ss(e){if(!iE(e,Response))throw i$('"response" must be an instance of Response',iC);if(200!==e.status)throw iK('"response" is not a conform JSON Web Key Set response (unexpected HTTP status code)',n5,e);si(e);let t=await s_(e,e=>(function(e,...t){if(!t.includes(nE(e)))throw i2(e,...t)})(e,"application/json","application/jwk-set+json"));if(!Array.isArray(t.keys))throw iK('"response" body "keys" property must be an array',n0,{body:t});if(!Array.prototype.every.call(t.keys,iJ))throw iK('"response" body "keys" property members must be JWK formatted objects',n0,{body:t});return t}function sa(e){switch(e){case"PS256":case"ES256":case"RS256":case"PS384":case"ES384":case"RS384":case"PS512":case"ES512":case"RS512":case"Ed25519":case"EdDSA":return!0;default:return!1}}function so(e){let{algorithm:t}=e;if("number"!=typeof t.modulusLength||t.modulusLength<2048)throw new iB(`unsupported ${t.name} modulusLength`,{cause:e})}function sl(e){switch(e.algorithm.name){case"ECDSA":return{name:e.algorithm.name,hash:function(e){let{algorithm:t}=e;switch(t.namedCurve){case"P-256":return"SHA-256";case"P-384":return"SHA-384";case"P-521":return"SHA-512";default:throw new iB("unsupported ECDSA namedCurve",{cause:e})}}(e)};case"RSA-PSS":switch(so(e),e.algorithm.hash.name){case"SHA-256":case"SHA-384":case"SHA-512":return{name:e.algorithm.name,saltLength:parseInt(e.algorithm.hash.name.slice(-3),10)>>3};default:throw new iB("unsupported RSA-PSS hash name",{cause:e})}case"RSASSA-PKCS1-v1_5":return so(e),e.algorithm.name;case"Ed25519":return e.algorithm.name}throw new iB("unsupported CryptoKey algorithm name",{cause:e})}async function sc(e,t,r,i){let n=iN(`${e}.${t}`),s=sl(r);if(!await crypto.subtle.verify(s,r,i,n))throw iK("JWT signature verification failed",n0,{key:r,data:n,signature:i,algorithm:s})}async function su(e,t,r,i,n){let s,a,{0:o,1:l,length:c}=e.split(".");if(5===c)if(void 0!==n)e=await n(e),{0:o,1:l,length:c}=e.split(".");else throw new iB("JWE decryption is not configured",{cause:e});if(3!==c)throw iK("Invalid JWT",n0,e);try{s=JSON.parse(iN(iM(o)))}catch(e){throw iK("failed to parse JWT Header body as base64url encoded JSON",nZ,e)}if(!iJ(s))throw iK("JWT Header must be a top level object",n0,e);if(t(s),void 0!==s.crit)throw new iB('no JWT "crit" header parameter extensions are supported',{cause:{header:s}});try{a=JSON.parse(iN(iM(l)))}catch(e){throw iK("failed to parse JWT Payload body as base64url encoded JSON",nZ,e)}if(!iJ(a))throw iK("JWT Payload must be a top level object",n0,e);let u=i9()+r;if(void 0!==a.exp){if("number"!=typeof a.exp)throw iK('unexpected JWT "exp" (expiration time) claim type',n0,{claims:a});if(a.exp<=u-i)throw iK('unexpected JWT "exp" (expiration time) claim value, expiration is past current timestamp',n8,{claims:a,now:u,tolerance:i,claim:"exp"})}if(void 0!==a.iat&&"number"!=typeof a.iat)throw iK('unexpected JWT "iat" (issued at) claim type',n0,{claims:a});if(void 0!==a.iss&&"string"!=typeof a.iss)throw iK('unexpected JWT "iss" (issuer) claim type',n0,{claims:a});if(void 0!==a.nbf){if("number"!=typeof a.nbf)throw iK('unexpected JWT "nbf" (not before) claim type',n0,{claims:a});if(a.nbf>u+i)throw iK('unexpected JWT "nbf" (not before) claim value',n8,{claims:a,now:u,tolerance:i,claim:"nbf"})}if(void 0!==a.aud&&"string"!=typeof a.aud&&!Array.isArray(a.aud))throw iK('unexpected JWT "aud" (audience) claim type',n0,{claims:a});return{header:s,claims:a,jwt:e}}async function sd(e,t,r){let i;switch(t.alg){case"RS256":case"PS256":case"ES256":i="SHA-256";break;case"RS384":case"PS384":case"ES384":i="SHA-384";break;case"RS512":case"PS512":case"ES512":case"Ed25519":case"EdDSA":i="SHA-512";break;default:throw new iB(`unsupported JWS algorithm for ${r} calculation`,{cause:{alg:t.alg}})}let n=await crypto.subtle.digest(i,iN(e));return iM(n.slice(0,n.byteLength/2))}async function sf(e,t,r,i){return t===await sd(e,r,i)}async function sh(e){if(e.bodyUsed)throw i$("form_post Request instances must contain a readable body",iT,{cause:e});return e.text()}async function sp(e){if("POST"!==e.method)throw i$("form_post responses are expected to use the POST method",iT,{cause:e});if("application/x-www-form-urlencoded"!==nE(e))throw i$("form_post responses are expected to use the application/x-www-form-urlencoded content-type",iT,{cause:e});return sh(e)}function sy(e,t,r,i){if(void 0!==e){if("string"==typeof e?i.alg!==e:!e.includes(i.alg))throw iK('unexpected JWT "alg" header parameter',n0,{header:i,expected:e,reason:"client configuration"});return}if(Array.isArray(t)){if(!t.includes(i.alg))throw iK('unexpected JWT "alg" header parameter',n0,{header:i,expected:t,reason:"authorization server metadata"});return}if(void 0!==r){if("string"==typeof r?i.alg!==r:"function"==typeof r?!r(i.alg):!r.includes(i.alg))throw iK('unexpected JWT "alg" header parameter',n0,{header:i,expected:r,reason:"default value"});return}throw iK('missing client or server configuration to verify used JWT "alg" header parameter',void 0,{client:e,issuer:t,fallback:r})}function sm(e,t){let{0:r,length:i}=e.getAll(t);if(i>1)throw iK(`"${t}" parameter must be provided only once`,n0);return r}let sg=Symbol(),sb=Symbol();function sw(e,t,r,i){var n;if(i7(e),ne(t),r instanceof URL&&(r=r.searchParams),!(r instanceof URLSearchParams))throw i$('"parameters" must be an instance of URLSearchParams, or URL',iC);if(sm(r,"response"))throw iK('"parameters" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()',n0,{parameters:r});let s=sm(r,"iss"),a=sm(r,"state");if(!s&&e.authorization_response_iss_parameter_supported)throw iK('response parameter "iss" (issuer) missing',n0,{parameters:r});if(s&&s!==e.issuer)throw iK('unexpected "iss" (issuer) response parameter value',n0,{expected:e.issuer,parameters:r});switch(i){case void 0:case sb:if(void 0!==a)throw iK('unexpected "state" response parameter encountered',n0,{expected:void 0,parameters:r});break;case sg:break;default:if(iZ(i,'"expectedState" argument'),a!==i)throw iK(void 0===a?'response parameter "state" missing':'unexpected "state" response parameter value',n0,{expected:i,parameters:r})}if(sm(r,"error"))throw new nu("authorization response from the server is an error",{cause:r});let o=sm(r,"id_token"),l=sm(r,"token");if(void 0!==o||void 0!==l)throw new iB("implicit and hybrid flows are not supported");return n=new URLSearchParams(r),nN.add(n),n}async function sx(e,t){let{ext:r,key_ops:i,use:n,...s}=t;return crypto.subtle.importKey("jwk",s,function(e){switch(e){case"PS256":case"PS384":case"PS512":return{name:"RSA-PSS",hash:`SHA-${e.slice(-3)}`};case"RS256":case"RS384":case"RS512":return{name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.slice(-3)}`};case"ES256":case"ES384":return{name:"ECDSA",namedCurve:`P-${e.slice(-3)}`};case"ES512":return{name:"ECDSA",namedCurve:"P-521"};case"Ed25519":case"EdDSA":return"Ed25519";default:throw new iB("unsupported JWS algorithm",{cause:{alg:e}})}}(e),!0,["verify"])}function sv(e){let t=new URL(e);return t.search="",t.hash="",t.href}async function s_(e,t=i1){let r;try{r=await e.json()}catch(r){throw t(e),iK('failed to parse "response" body as JSON',nZ,r)}if(!iJ(r))throw iK('"response" body must be a top level object',n0,{body:r});return r}let sS=Symbol(),sk=Symbol(),sA=Symbol();async function sE(e,t,r){let{cookies:i,logger:n}=r,s=i[e],a=new Date;a.setTime(a.getTime()+9e5),n.debug(`CREATE_${e.toUpperCase()}`,{name:s.name,payload:t,COOKIE_TTL:900,expires:a});let o=await tY({...r.jwt,maxAge:900,token:{value:t},salt:s.name}),l={...s.options,expires:a};return{name:s.name,value:o,options:l}}async function sT(e,t,r){try{let{logger:i,cookies:n,jwt:s}=r;if(i.debug(`PARSE_${e.toUpperCase()}`,{cookie:t}),!t)throw new S(`${e} cookie was missing`);let a=await tZ({...s,token:t,salt:n[e].name});if(a?.value)return a.value;throw Error("Invalid cookie")}catch(t){throw new S(`${e} value could not be parsed`,{cause:t})}}function sC(e,t,r){let{logger:i,cookies:n}=t,s=n[e];i.debug(`CLEAR_${e.toUpperCase()}`,{cookie:s}),r.push({name:s.name,value:"",options:{...n[e].options,maxAge:0}})}function s$(e,t){return async function(r,i,n){let{provider:s,logger:a}=n;if(!s?.checks?.includes(e))return;let o=r?.[n.cookies[t].name];a.debug(`USE_${t.toUpperCase()}`,{value:o});let l=await sT(t,o,n);return sC(t,n,i),l}}let sO={async create(e){let t=i5(),r=await i6(t);return{cookie:await sE("pkceCodeVerifier",t,e),value:r}},use:s$("pkce","pkceCodeVerifier")},sR="encodedState",sI={async create(e,t){let{provider:r}=e;if(!r.checks.includes("state")){if(t)throw new S("State data was provided but the provider is not configured to use state");return}let i={origin:t,random:i5()},n=await tY({secret:e.jwt.secret,token:i,salt:sR,maxAge:900});return{cookie:await sE("state",n,e),value:n}},use:s$("state","state"),async decode(e,t){try{t.logger.debug("DECODE_STATE",{state:e});let r=await tZ({secret:t.jwt.secret,token:e,salt:sR});if(r)return r;throw Error("Invalid state")}catch(e){throw new S("State could not be decoded",{cause:e})}}},sP={async create(e){if(!e.provider.checks.includes("nonce"))return;let t=i5();return{cookie:await sE("nonce",t,e),value:t}},use:s$("nonce","nonce")},sj="encodedWebauthnChallenge",sU={create:async(e,t,r)=>({cookie:await sE("webauthnChallenge",await tY({secret:e.jwt.secret,token:{challenge:t,registerData:r},salt:sj,maxAge:900}),e)}),async use(e,t,r){let i=t?.[e.cookies.webauthnChallenge.name],n=await sT("webauthnChallenge",i,e),s=await tZ({secret:e.jwt.secret,token:n,salt:sj});if(sC("webauthnChallenge",e,r),!s)throw new S("WebAuthn challenge was missing");return s}};function sq(e){return encodeURIComponent(e).replace(/%20/g,"+")}async function sD(e,t,r){let i,n,s,{logger:a,provider:o}=r,{token:l,userinfo:c}=o;if(l?.url&&"authjs.dev"!==l.url.host||c?.url&&"authjs.dev"!==c.url.host)i={issuer:o.issuer??"https://authjs.dev",token_endpoint:l?.url.toString(),userinfo_endpoint:c?.url.toString()};else{let e=new URL(o.issuer),t=await iG(e,{[iO]:!0,[iP]:o[ru]});if(!(i=await i0(e,t)).token_endpoint)throw TypeError("TODO: Authorization server did not provide a token endpoint.");if(!i.userinfo_endpoint)throw TypeError("TODO: Authorization server did not provide a userinfo endpoint.")}let u={client_id:o.clientId,...o.client};switch(u.token_endpoint_auth_method){case void 0:case"client_secret_basic":n=(e,t,r,i)=>{i.set("authorization",function(e,t){let r=sq(e),i=sq(t),n=btoa(`${r}:${i}`);return`Basic ${n}`}(o.clientId,o.clientSecret))};break;case"client_secret_post":var d;iZ(d=o.clientSecret,'"clientSecret"'),n=(e,t,r,i)=>{r.set("client_id",t.client_id),r.set("client_secret",d)};break;case"client_secret_jwt":n=function(e,t){let r;iZ(e,'"clientSecret"');let i=void 0;return async(t,n,s,a)=>{r||=await crypto.subtle.importKey("raw",iN(e),{hash:"SHA-256",name:"HMAC"},!1,["sign"]);let o={alg:"HS256"},l=nt(t,n);i?.(o,l);let c=`${iM(iN(JSON.stringify(o)))}.${iM(iN(JSON.stringify(l)))}`,u=await crypto.subtle.sign(r.algorithm,r,iN(c));s.set("client_id",n.client_id),s.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),s.set("client_assertion",`${c}.${iM(new Uint8Array(u))}`)}}(o.clientSecret);break;case"private_key_jwt":n=function(e,t){var r;let{key:i,kid:n}=(r=e)instanceof CryptoKey?{key:r}:r?.key instanceof CryptoKey?(void 0!==r.kid&&iZ(r.kid,'"kid"'),{key:r.key,kid:r.kid}):{};return iQ(i,'"clientPrivateKey.key"'),async(e,r,s,a)=>{let o={alg:i3(i),kid:n},l=nt(e,r);t?.[ij]?.(o,l),s.set("client_id",r.client_id),s.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),s.set("client_assertion",await nr(o,l,i))}}(o.token.clientPrivateKey,{[ij](e,t){t.aud=[i.issuer,i.token_endpoint]}});break;case"none":n=(e,t,r,i)=>{r.set("client_id",t.client_id)};break;default:throw Error("unsupported client authentication method")}let f=[],h=await sI.use(t,f,r);try{s=sw(i,u,new URLSearchParams(e),o.checks.includes("state")?h:sg)}catch(e){if(e instanceof nu){let t={providerId:o.id,...Object.fromEntries(e.cause.entries())};throw a.debug("OAuthCallbackError",t),new O("OAuth Provider returned an error",t)}throw e}let p=await sO.use(t,f,r),y=o.callbackUrl;!r.isOnRedirectProxy&&o.redirectProxyUrl&&(y=o.redirectProxyUrl);let m=await nM(i,u,n,s,y,p??"decoy",{[iO]:!0,[iP]:(...e)=>(o.checks.includes("pkce")||e[1].body.delete("code_verifier"),(o[ru]??fetch)(...e))});o.token?.conform&&(m=await o.token.conform(m.clone())??m);let g={},b="oidc"===o.type;if(o[rd])switch(o.id){case"microsoft-entra-id":case"azure-ad":{let e=await m.clone().json();if(e.error){let t={providerId:o.id,...e};throw new O(`OAuth Provider returned an error: ${e.error}`,t)}let{tid:t}=function(e){let t,r;if("string"!=typeof e)throw new eS("JWTs must use Compact JWS serialization, JWT must be a string");let{1:i,length:n}=e.split(".");if(5===n)throw new eS("Only JWTs using Compact JWS serialization can be decoded");if(3!==n)throw new eS("Invalid JWT");if(!i)throw new eS("JWTs must contain a payload");try{t=ep(i)}catch{throw new eS("Failed to base64url decode the payload")}try{r=JSON.parse(ec.decode(t))}catch{throw new eS("Failed to parse the decoded payload as JSON")}if(!eO(r))throw new eS("Invalid JWT Claims Set");return r}(e.id_token);if("string"==typeof t){let e=i.issuer?.match(/microsoftonline\.com\/(\w+)\/v2\.0/)?.[1]??"common",r=new URL(i.issuer.replace(e,t)),n=await iG(r,{[iP]:o[ru]});i=await i0(r,n)}}}let w=await nQ(i,u,m,{expectedNonce:await sP.use(t,f,r),requireIdToken:b});if(b){let t=nI(w);if(g=t,o[rd]&&"apple"===o.id)try{g.user=JSON.parse(e?.user)}catch{}if(!1===o.idToken){let e=await nv(i,u,w.access_token,{[iP]:o[ru],[iO]:!0});g=await nT(i,u,t.sub,e)}}else if(c?.request){let e=await c.request({tokens:w,provider:o});e instanceof Object&&(g=e)}else if(c?.url){let e=await nv(i,u,w.access_token,{[iP]:o[ru],[iO]:!0});g=await e.json()}else throw TypeError("No userinfo endpoint configured");return w.expires_in&&(w.expires_at=Math.floor(Date.now()/1e3)+Number(w.expires_in)),{...await sL(g,o,w,a),profile:g,cookies:f}}async function sL(e,t,r,i){try{let i=await t.profile(e,r);return{user:{...i,id:crypto.randomUUID(),email:i.email?.toLowerCase()},account:{...r,provider:t.id,type:t.type,providerAccountId:i.id??crypto.randomUUID()}}}catch(r){i.debug("getProfile error details",e),i.error(new R(r,{provider:t.id}))}}async function sN(e,t,r,i){let n=await sW(e,t,r),{cookie:s}=await sU.create(e,n.challenge,r);return{status:200,cookies:[...i??[],s],body:{action:"register",options:n},headers:{"Content-Type":"application/json"}}}async function sM(e,t,r,i){let n=await sK(e,t,r),{cookie:s}=await sU.create(e,n.challenge);return{status:200,cookies:[...i??[],s],body:{action:"authenticate",options:n},headers:{"Content-Type":"application/json"}}}async function sB(e,t,r){let i,{adapter:n,provider:s}=e,a=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!a||"object"!=typeof a||!("id"in a)||"string"!=typeof a.id)throw new h("Invalid WebAuthn Authentication response");let o=sF(sJ(a.id)),l=await n.getAuthenticator(o);if(!l)throw new h(`WebAuthn authenticator not found in database: ${JSON.stringify({credentialID:o})}`);let{challenge:c}=await sU.use(e,t.cookies,r);try{var u;let r=s.getRelayingParty(e,t);i=await s.simpleWebAuthn.verifyAuthenticationResponse({...s.verifyAuthenticationOptions,expectedChallenge:c,response:a,authenticator:{...u=l,credentialDeviceType:u.credentialDeviceType,transports:sV(u.transports),credentialID:sJ(u.credentialID),credentialPublicKey:sJ(u.credentialPublicKey)},expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new Q(e)}let{verified:d,authenticationInfo:f}=i;if(!d)throw new Q("WebAuthn authentication response could not be verified");try{let{newCounter:e}=f;await n.updateAuthenticatorCounter(l.credentialID,e)}catch(e){throw new y(`Failed to update authenticator counter. This may cause future authentication attempts to fail. ${JSON.stringify({credentialID:o,oldCounter:l.counter,newCounter:f.newCounter})}`,e)}let p=await n.getAccount(l.providerAccountId,s.id);if(!p)throw new h(`WebAuthn account not found in database: ${JSON.stringify({credentialID:o,providerAccountId:l.providerAccountId})}`);let m=await n.getUser(p.userId);if(!m)throw new h(`WebAuthn user not found in database: ${JSON.stringify({credentialID:o,providerAccountId:l.providerAccountId,userID:p.userId})}`);return{account:p,user:m}}async function sH(e,t,r){var i;let n,{provider:s}=e,a=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!a||"object"!=typeof a||!("id"in a)||"string"!=typeof a.id)throw new h("Invalid WebAuthn Registration response");let{challenge:o,registerData:l}=await sU.use(e,t.cookies,r);if(!l)throw new h("Missing user registration data in WebAuthn challenge cookie");try{let r=s.getRelayingParty(e,t);n=await s.simpleWebAuthn.verifyRegistrationResponse({...s.verifyRegistrationOptions,expectedChallenge:o,response:a,expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new Q(e)}if(!n.verified||!n.registrationInfo)throw new Q("WebAuthn registration response could not be verified");let c={providerAccountId:sF(n.registrationInfo.credentialID),provider:e.provider.id,type:s.type},u={providerAccountId:c.providerAccountId,counter:n.registrationInfo.counter,credentialID:sF(n.registrationInfo.credentialID),credentialPublicKey:sF(n.registrationInfo.credentialPublicKey),credentialBackedUp:n.registrationInfo.credentialBackedUp,credentialDeviceType:n.registrationInfo.credentialDeviceType,transports:(i=a.response.transports,i?.join(","))};return{user:l,account:c,authenticator:u}}async function sK(e,t,r){let{provider:i,adapter:n}=e,s=r&&r.id?await n.listAuthenticatorsByUserId(r.id):null,a=i.getRelayingParty(e,t);return await i.simpleWebAuthn.generateAuthenticationOptions({...i.authenticationOptions,rpID:a.id,allowCredentials:s?.map(e=>({id:sJ(e.credentialID),type:"public-key",transports:sV(e.transports)}))})}async function sW(e,t,r){let{provider:i,adapter:n}=e,s=r.id?await n.listAuthenticatorsByUserId(r.id):null,a=ri(32),o=i.getRelayingParty(e,t);return await i.simpleWebAuthn.generateRegistrationOptions({...i.registrationOptions,userID:a,userName:r.email,userDisplayName:r.name??void 0,rpID:o.id,rpName:o.name,excludeCredentials:s?.map(e=>({id:sJ(e.credentialID),type:"public-key",transports:sV(e.transports)}))})}function sQ(e){let{provider:t,adapter:r}=e;if(!r)throw new A("An adapter is required for the WebAuthn provider");if(!t||"webauthn"!==t.type)throw new L("Provider must be WebAuthn");return{...e,provider:t,adapter:r}}function sJ(e){return new Uint8Array(Buffer.from(e,"base64"))}function sF(e){return Buffer.from(e).toString("base64")}function sV(e){return e?e.split(","):void 0}async function sX(e,t,r,i){if(!t.provider)throw new L("Callback route called without provider");let{query:n,body:s,method:a,headers:o}=e,{provider:l,adapter:c,url:u,callbackUrl:d,pages:f,jwt:p,events:y,callbacks:m,session:{strategy:b,maxAge:w},logger:x}=t,_="jwt"===b;try{if("oauth"===l.type||"oidc"===l.type){let a,o=l.authorization?.url.searchParams.get("response_mode")==="form_post"?s:n;if(t.isOnRedirectProxy&&o?.state){let e=await sI.decode(o.state,t);if(e?.origin&&new URL(e.origin).origin!==t.url.origin){let t=`${e.origin}?${new URLSearchParams(o)}`;return x.debug("Proxy redirecting to",t),{redirect:t,cookies:i}}}let h=await sD(o,e.cookies,t);h.cookies.length&&i.push(...h.cookies),x.debug("authorization result",h);let{user:g,account:b,profile:v}=h;if(!g||!b||!v)return{redirect:`${u}/signin`,cookies:i};if(c){let{getUserByAccount:e}=c;a=await e({providerAccountId:b.providerAccountId,provider:l.id})}let S=await sz({user:a??g,account:b,profile:v},t);if(S)return{redirect:S,cookies:i};let{user:k,session:A,isNewUser:E}=await iA(r.value,g,b,t);if(_){let e={name:k.name,email:k.email,picture:k.image,sub:k.id?.toString()},n=await m.jwt({token:e,user:k,account:b,profile:v,isNewUser:E,trigger:E?"signUp":"signIn"});if(null===n)i.push(...r.clean());else{let e=t.cookies.sessionToken.name,s=await p.encode({...p,token:n,salt:e}),a=new Date;a.setTime(a.getTime()+1e3*w);let o=r.chunk(s,{expires:a});i.push(...o)}}else i.push({name:t.cookies.sessionToken.name,value:A.sessionToken,options:{...t.cookies.sessionToken.options,expires:A.expires}});if(await y.signIn?.({user:k,account:b,profile:v,isNewUser:E}),E&&f.newUser)return{redirect:`${f.newUser}${f.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:i};return{redirect:d,cookies:i}}if("email"===l.type){let e=n?.token,s=n?.email;if(!e){let t=TypeError("Missing token. The sign-in URL was manually opened without token or the link was not sent correctly in the email.",{cause:{hasToken:!!e}});throw t.name="Configuration",t}let a=l.secret??t.secret,o=await c.useVerificationToken({identifier:s,token:await rr(`${e}${a}`)}),u=!!o,h=u&&o.expires.valueOf()<Date.now();if(!u||h||s&&o.identifier!==s)throw new M({hasInvite:u,expired:h});let{identifier:g}=o,b=await c.getUserByEmail(g)??{id:crypto.randomUUID(),email:g,emailVerified:null},x={providerAccountId:b.email,userId:b.id,type:"email",provider:l.id},v=await sz({user:b,account:x},t);if(v)return{redirect:v,cookies:i};let{user:S,session:k,isNewUser:A}=await iA(r.value,b,x,t);if(_){let e={name:S.name,email:S.email,picture:S.image,sub:S.id?.toString()},n=await m.jwt({token:e,user:S,account:x,isNewUser:A,trigger:A?"signUp":"signIn"});if(null===n)i.push(...r.clean());else{let e=t.cookies.sessionToken.name,s=await p.encode({...p,token:n,salt:e}),a=new Date;a.setTime(a.getTime()+1e3*w);let o=r.chunk(s,{expires:a});i.push(...o)}}else i.push({name:t.cookies.sessionToken.name,value:k.sessionToken,options:{...t.cookies.sessionToken.options,expires:k.expires}});if(await y.signIn?.({user:S,account:x,isNewUser:A}),A&&f.newUser)return{redirect:`${f.newUser}${f.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:i};return{redirect:d,cookies:i}}if("credentials"===l.type&&"POST"===a){let e=s??{};Object.entries(n??{}).forEach(([e,t])=>u.searchParams.set(e,t));let c=await l.authorize(e,new Request(u,{headers:o,method:a,body:JSON.stringify(s)}));if(c)c.id=c.id?.toString()??crypto.randomUUID();else throw new v;let f={providerAccountId:c.id,type:"credentials",provider:l.id},h=await sz({user:c,account:f,credentials:e},t);if(h)return{redirect:h,cookies:i};let g={name:c.name,email:c.email,picture:c.image,sub:c.id},b=await m.jwt({token:g,user:c,account:f,isNewUser:!1,trigger:"signIn"});if(null===b)i.push(...r.clean());else{let e=t.cookies.sessionToken.name,n=await p.encode({...p,token:b,salt:e}),s=new Date;s.setTime(s.getTime()+1e3*w);let a=r.chunk(n,{expires:s});i.push(...a)}return await y.signIn?.({user:c,account:f}),{redirect:d,cookies:i}}else if("webauthn"===l.type&&"POST"===a){let n,s,a,o=e.body?.action;if("string"!=typeof o||"authenticate"!==o&&"register"!==o)throw new h("Invalid action parameter");let l=sQ(t);switch(o){case"authenticate":{let t=await sB(l,e,i);n=t.user,s=t.account;break}case"register":{let r=await sH(t,e,i);n=r.user,s=r.account,a=r.authenticator}}await sz({user:n,account:s},t);let{user:c,isNewUser:u,session:g,account:b}=await iA(r.value,n,s,t);if(!b)throw new h("Error creating or finding account");if(a&&c.id&&await l.adapter.createAuthenticator({...a,userId:c.id}),_){let e={name:c.name,email:c.email,picture:c.image,sub:c.id?.toString()},n=await m.jwt({token:e,user:c,account:b,isNewUser:u,trigger:u?"signUp":"signIn"});if(null===n)i.push(...r.clean());else{let e=t.cookies.sessionToken.name,s=await p.encode({...p,token:n,salt:e}),a=new Date;a.setTime(a.getTime()+1e3*w);let o=r.chunk(s,{expires:a});i.push(...o)}}else i.push({name:t.cookies.sessionToken.name,value:g.sessionToken,options:{...t.cookies.sessionToken.options,expires:g.expires}});if(await y.signIn?.({user:c,account:b,isNewUser:u}),u&&f.newUser)return{redirect:`${f.newUser}${f.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:i};return{redirect:d,cookies:i}}throw new L(`Callback for provider type (${l.type}) is not supported`)}catch(t){if(t instanceof h)throw t;let e=new g(t,{provider:l.id});throw x.debug("callback route error details",{method:a,query:n,body:s}),e}}async function sz(e,t){let r,{signIn:i,redirect:n}=t.callbacks;try{r=await i(e)}catch(e){if(e instanceof h)throw e;throw new m(e)}if(!r)throw new m("AccessDenied");if("string"==typeof r)return await n({url:r,baseUrl:t.url.origin})}async function sG(e,t,r,i,n){let{adapter:s,jwt:a,events:o,callbacks:l,logger:c,session:{strategy:u,maxAge:d}}=e,f={body:null,headers:{"Content-Type":"application/json",...!i&&{"Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"}},cookies:r},h=t.value;if(!h)return f;if("jwt"===u){try{let r=e.cookies.sessionToken.name,s=await a.decode({...a,token:h,salt:r});if(!s)throw Error("Invalid JWT");let c=await l.jwt({token:s,...i&&{trigger:"update"},session:n}),u=ik(d);if(null!==c){let e={user:{name:c.name,email:c.email,image:c.picture},expires:u.toISOString()},i=await l.session({session:e,token:c});f.body=i;let n=await a.encode({...a,token:c,salt:r}),s=t.chunk(n,{expires:u});f.cookies?.push(...s),await o.session?.({session:i,token:c})}else f.cookies?.push(...t.clean())}catch(e){c.error(new k(e)),f.cookies?.push(...t.clean())}return f}try{let{getSessionAndUser:r,deleteSession:a,updateSession:c}=s,u=await r(h);if(u&&u.session.expires.valueOf()<Date.now()&&(await a(h),u=null),u){let{user:t,session:r}=u,s=e.session.updateAge,a=r.expires.valueOf()-1e3*d+1e3*s,p=ik(d);a<=Date.now()&&await c({sessionToken:h,expires:p});let y=await l.session({session:{...r,user:t},user:t,newSession:n,...i?{trigger:"update"}:{}});f.body=y,f.cookies?.push({name:e.cookies.sessionToken.name,value:h,options:{...e.cookies.sessionToken.options,expires:p}}),await o.session?.({session:y})}else h&&f.cookies?.push(...t.clean())}catch(e){c.error(new I(e))}return f}async function sY(e,t){let r,i,{logger:n,provider:s}=t,a=s.authorization?.url;if(!a||"authjs.dev"===a.host){let e=new URL(s.issuer),t=await iG(e,{[iP]:s[ru],[iO]:!0}),r=await i0(e,t).catch(t=>{if(!(t instanceof TypeError)||"Invalid URL"!==t.message)throw t;throw TypeError(`Discovery request responded with an invalid issuer. expected: ${e}`)});if(!r.authorization_endpoint)throw TypeError("Authorization server did not provide an authorization endpoint.");a=new URL(r.authorization_endpoint)}let o=a.searchParams,l=s.callbackUrl;!t.isOnRedirectProxy&&s.redirectProxyUrl&&(l=s.redirectProxyUrl,i=s.callbackUrl,n.debug("using redirect proxy",{redirect_uri:l,data:i}));let c=Object.assign({response_type:"code",client_id:s.clientId,redirect_uri:l,...s.authorization?.params},Object.fromEntries(s.authorization?.url.searchParams??[]),e);for(let e in c)o.set(e,c[e]);let u=[];s.authorization?.url.searchParams.get("response_mode")==="form_post"&&(t.cookies.state.options.sameSite="none",t.cookies.state.options.secure=!0,t.cookies.nonce.options.sameSite="none",t.cookies.nonce.options.secure=!0);let d=await sI.create(t,i);if(d&&(o.set("state",d.value),u.push(d.cookie)),s.checks?.includes("pkce"))if(r&&!r.code_challenge_methods_supported?.includes("S256"))"oidc"===s.type&&(s.checks=["nonce"]);else{let{value:e,cookie:r}=await sO.create(t);o.set("code_challenge",e),o.set("code_challenge_method","S256"),u.push(r)}let f=await sP.create(t);return f&&(o.set("nonce",f.value),u.push(f.cookie)),"oidc"!==s.type||a.searchParams.has("scope")||a.searchParams.set("scope","openid profile email"),n.debug("authorization url is ready",{url:a,cookies:u,provider:s}),{redirect:a.toString(),cookies:u}}async function sZ(e,t){let r,{body:i}=e,{provider:n,callbacks:s,adapter:a}=t,o=(n.normalizeIdentifier??function(e){if(!e)throw Error("Missing email from request body.");let[t,r]=e.toLowerCase().trim().split("@");return r=r.split(",")[0],`${t}@${r}`})(i?.email),l={id:crypto.randomUUID(),email:o,emailVerified:null},c=await a.getUserByEmail(o)??l,u={providerAccountId:o,userId:c.id,type:"email",provider:n.id};try{r=await s.signIn({user:c,account:u,email:{verificationRequest:!0}})}catch(e){throw new m(e)}if(!r)throw new m("AccessDenied");if("string"==typeof r)return{redirect:await s.redirect({url:r,baseUrl:t.url.origin})};let{callbackUrl:d,theme:f}=t,h=await n.generateVerificationToken?.()??ri(32),p=new Date(Date.now()+(n.maxAge??86400)*1e3),y=n.secret??t.secret,g=new URL(t.basePath,t.url.origin),b=n.sendVerificationRequest({identifier:o,token:h,expires:p,url:`${g}/callback/${n.id}?${new URLSearchParams({callbackUrl:d,token:h,email:o})}`,provider:n,theme:f,request:new Request(e.url,{headers:e.headers,method:e.method,body:"POST"===e.method?JSON.stringify(e.body??{}):void 0})}),w=a.createVerificationToken?.({identifier:o,token:await rr(`${h}${y}`),expires:p});return await Promise.all([b,w]),{redirect:`${g}/verify-request?${new URLSearchParams({provider:n.id,type:n.type})}`}}async function s0(e,t,r){let i=`${r.url.origin}${r.basePath}/signin`;if(!r.provider)return{redirect:i,cookies:t};switch(r.provider.type){case"oauth":case"oidc":{let{redirect:i,cookies:n}=await sY(e.query,r);return n&&t.push(...n),{redirect:i,cookies:t}}case"email":return{...await sZ(e,r),cookies:t};default:return{redirect:i,cookies:t}}}async function s1(e,t,r){let{jwt:i,events:n,callbackUrl:s,logger:a,session:o}=r,l=t.value;if(!l)return{redirect:s,cookies:e};try{if("jwt"===o.strategy){let e=r.cookies.sessionToken.name,t=await i.decode({...i,token:l,salt:e});await n.signOut?.({token:t})}else{let e=await r.adapter?.deleteSession(l);await n.signOut?.({session:e})}}catch(e){a.error(new U(e))}return e.push(...t.clean()),{redirect:s,cookies:e}}async function s2(e,t){let{adapter:r,jwt:i,session:{strategy:n}}=e,s=t.value;if(!s)return null;if("jwt"===n){let t=e.cookies.sessionToken.name,r=await i.decode({...i,token:s,salt:t});if(r&&r.sub)return{id:r.sub,name:r.name,email:r.email,image:r.picture}}else{let e=await r?.getSessionAndUser(s);if(e)return e.user}return null}async function s5(e,t,r,i){let n=sQ(t),{provider:s}=n,{action:a}=e.query??{};if("register"!==a&&"authenticate"!==a&&void 0!==a)return{status:400,body:{error:"Invalid action"},cookies:i,headers:{"Content-Type":"application/json"}};let o=await s2(t,r),l=o?{user:o,exists:!0}:await s.getUserInfo(t,e),c=l?.user;switch(function(e,t,r){let{user:i,exists:n=!1}=r??{};switch(e){case"authenticate":return"authenticate";case"register":if(i&&t===n)return"register";break;case void 0:if(!t)if(!i)return"authenticate";else if(n)return"authenticate";else return"register"}return null}(a,!!o,l)){case"authenticate":return sM(n,e,c,i);case"register":if("string"==typeof c?.email)return sN(n,e,c,i);break;default:return{status:400,body:{error:"Invalid request"},cookies:i,headers:{"Content-Type":"application/json"}}}}async function s6(e,t){let{action:r,providerId:i,error:n,method:s}=e,a=t.skipCSRFCheck===rl,{options:o,cookies:l}=await rg({authOptions:t,action:r,providerId:i,url:e.url,callbackUrl:e.body?.callbackUrl??e.query?.callbackUrl,csrfToken:e.body?.csrfToken,cookies:e.cookies,isPost:"POST"===s,csrfDisabled:a}),c=new f(o.cookies.sessionToken,e.cookies,o.logger);if("GET"===s){let t=iS({...o,query:e.query,cookies:l});switch(r){case"callback":return await sX(e,o,c,l);case"csrf":return t.csrf(a,o,l);case"error":return t.error(n);case"providers":return t.providers(o.providers);case"session":return await sG(o,c,l);case"signin":return t.signin(i,n);case"signout":return t.signout();case"verify-request":return t.verifyRequest();case"webauthn-options":return await s5(e,o,c,l)}}else{let{csrfTokenVerified:t}=o;switch(r){case"callback":return"credentials"===o.provider.type&&rs(r,t),await sX(e,o,c,l);case"session":return rs(r,t),await sG(o,c,l,!0,e.body?.data);case"signin":return rs(r,t),await s0(e,l,o);case"signout":return rs(r,t),await s1(l,c,o)}}throw new q(`Cannot handle action: ${r}`)}function s3(e,t,r,i,n){let s,a=n?.basePath,o=i.AUTH_URL??i.NEXTAUTH_URL;if(o)s=new URL(o),a&&"/"!==a&&"/"!==s.pathname&&(s.pathname!==a&&t3(n).warn("env-url-basepath-mismatch"),s.pathname="/");else{let e=r.get("x-forwarded-host")??r.get("host"),i=r.get("x-forwarded-proto")??t??"https",n=i.endsWith(":")?i:i+":";s=new URL(`${n}//${e}`)}let l=s.toString().replace(/\/$/,"");if(a){let t=a?.replace(/(^\/|\/$)/g,"")??"";return new URL(`${l}/${t}/${e}`)}return new URL(`${l}/${e}`)}async function s8(e,t){let r=t3(t),i=await re(e,t);if(!i)return Response.json("Bad request.",{status:400});let n=function(e,t){let{url:r}=e,i=[];if(!V&&t.debug&&i.push("debug-enabled"),!t.trustHost)return new N(`Host must be trusted. URL was: ${e.url}`);if(!t.secret?.length)return new C("Please define a `secret`");let n=e.query?.callbackUrl;if(n&&!X(n,r.origin))return new x(`Invalid callback URL. Received: ${n}`);let{callbackUrl:s}=d(t.useSecureCookies??"https:"===r.protocol),a=e.cookies?.[t.cookies?.callbackUrl?.name??s.name];if(a&&!X(a,r.origin))return new x(`Invalid callback URL. Received: ${a}`);let o=!1;for(let e of t.providers){let t="function"==typeof e?e():e;if(("oauth"===t.type||"oidc"===t.type)&&!(t.issuer??t.options?.issuer)){let e,{authorization:r,token:i,userinfo:n}=t;if("string"==typeof r||r?.url?"string"==typeof i||i?.url?"string"==typeof n||n?.url||(e="userinfo"):e="token":e="authorization",e)return new _(`Provider "${t.id}" is missing both \`issuer\` and \`${e}\` endpoint config. At least one of them is required`)}if("credentials"===t.type)z=!0;else if("email"===t.type)G=!0;else if("webauthn"===t.type){var l;if(Y=!0,t.simpleWebAuthnBrowserVersion&&(l=t.simpleWebAuthnBrowserVersion,!/^v\d+(?:\.\d+){0,2}$/.test(l)))return new h(`Invalid provider config for "${t.id}": simpleWebAuthnBrowserVersion "${t.simpleWebAuthnBrowserVersion}" must be a valid semver string.`);if(t.enableConditionalUI){if(o)return new K("Multiple webauthn providers have 'enableConditionalUI' set to True. Only one provider can have this option enabled at a time");if(o=!0,!Object.values(t.formFields).some(e=>e.autocomplete&&e.autocomplete.toString().indexOf("webauthn")>-1))return new W(`Provider "${t.id}" has 'enableConditionalUI' set to True, but none of its formFields have 'webauthn' in their autocomplete param`)}}}if(z){let e=t.session?.strategy==="database",r=!t.providers.some(e=>"credentials"!==("function"==typeof e?e():e).type);if(e&&r)return new D("Signing in with credentials only supported if JWT strategy is enabled");if(t.providers.some(e=>{let t="function"==typeof e?e():e;return"credentials"===t.type&&!t.authorize}))return new T("Must define an authorize() handler to use credentials authentication provider")}let{adapter:c,session:u}=t,f=[];if(G||u?.strategy==="database"||!u?.strategy&&c)if(G){if(!c)return new A("Email login requires an adapter");f.push(...Z)}else{if(!c)return new A("Database session requires an adapter");f.push(...ee)}if(Y){if(!t.experimental?.enableWebAuthn)return new F("WebAuthn is an experimental feature. To enable it, set `experimental.enableWebAuthn` to `true` in your config");if(i.push("experimental-webauthn"),!c)return new A("WebAuthn requires an adapter");f.push(...et)}if(c){let e=f.filter(e=>!(e in c));if(e.length)return new E(`Required adapter methods were missing: ${e.join(", ")}`)}return V||(V=!0),i}(i,t);if(Array.isArray(n))n.forEach(r.warn);else if(n){if(r.error(n),!new Set(["signin","signout","error","verify-request"]).has(i.action)||"GET"!==i.method)return Response.json({message:"There was a problem with the server configuration. Check the server logs for more information."},{status:500});let{pages:e,theme:s}=t,a=e?.error&&i.url.searchParams.get("callbackUrl")?.startsWith(e.error);if(!e?.error||a)return a&&r.error(new b(`The error page ${e?.error} should not require authentication`)),rt(iS({theme:s}).error("Configuration"));let o=`${i.url.origin}${e.error}?error=Configuration`;return Response.redirect(o)}let s=e.headers?.has("X-Auth-Return-Redirect"),a=t.raw===rc;try{let e=await s6(i,t);if(a)return e;let r=rt(e),n=r.headers.get("Location");if(!s||!n)return r;return Response.json({url:n},{headers:r.headers})}catch(d){r.error(d);let n=d instanceof h;if(n&&a&&!s)throw d;if("POST"===e.method&&"session"===i.action)return Response.json(null,{status:400});let o=new URLSearchParams({error:d instanceof h&&H.has(d.type)?d.type:"Configuration"});d instanceof v&&o.set("code",d.code);let l=n&&d.kind||"error",c=t.pages?.[l]??`${t.basePath}/${l.toLowerCase()}`,u=`${i.url.origin}${c}?${o}`;if(s)return Response.json({url:u});return Response.redirect(u)}}var s4=r(32190);function s9(e){let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return e;let{origin:r}=new URL(t),{href:i,origin:n}=e.nextUrl;return new s4.NextRequest(i.replace(n,r),e)}function s7(e){try{e.secret??(e.secret=process.env.AUTH_SECRET??process.env.NEXTAUTH_SECRET);let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return;let{pathname:r}=new URL(t);if("/"===r)return;e.basePath||(e.basePath=r)}catch{}finally{e.basePath||(e.basePath="/api/auth"),function(e,t,r=!1){try{let i=e.AUTH_URL;i&&(t.basePath?r||t3(t).warn("env-url-basepath-redundant"):t.basePath=new URL(i).pathname)}catch{}finally{t.basePath??(t.basePath="/auth")}if(!t.secret?.length){t.secret=[];let r=e.AUTH_SECRET;for(let i of(r&&t.secret.push(r),[1,2,3])){let r=e[`AUTH_SECRET_${i}`];r&&t.secret.unshift(r)}}t.redirectProxyUrl??(t.redirectProxyUrl=e.AUTH_REDIRECT_PROXY_URL),t.trustHost??(t.trustHost=!!(e.AUTH_URL??e.AUTH_TRUST_HOST??e.VERCEL??e.CF_PAGES??"production"!==e.NODE_ENV)),t.providers=t.providers.map(t=>{let{id:r}="function"==typeof t?t({}):t,i=r.toUpperCase().replace(/-/g,"_"),n=e[`AUTH_${i}_ID`],s=e[`AUTH_${i}_SECRET`],a=e[`AUTH_${i}_ISSUER`],o=e[`AUTH_${i}_KEY`],l="function"==typeof t?t({clientId:n,clientSecret:s,issuer:a,apiKey:o}):t;return"oauth"===l.type||"oidc"===l.type?(l.clientId??(l.clientId=n),l.clientSecret??(l.clientSecret=s),l.issuer??(l.issuer=a)):"email"===l.type&&(l.apiKey??(l.apiKey=o)),l})}(process.env,e,!0)}}var ae=r(99933),at=r(86280);async function ar(e,t){return s8(new Request(s3("session",e.get("x-forwarded-proto"),e,process.env,t),{headers:{cookie:e.get("cookie")??""}}),{...t,callbacks:{...t.callbacks,async session(...e){let r=await t.callbacks?.session?.(...e)??{...e[0].session,expires:e[0].session.expires?.toISOString?.()??e[0].session.expires};return{user:e[0].user??e[0].token,...r}}}})}function ai(e){return"function"==typeof e}function an(e,t){return"function"==typeof e?async(...r)=>{if(!r.length){let r=await (0,at.b)(),i=await e(void 0);return t?.(i),ar(r,i).then(e=>e.json())}if(r[0]instanceof Request){let i=r[0],n=r[1],s=await e(i);return t?.(s),as([i,n],s)}if(ai(r[0])){let i=r[0];return async(...r)=>{let n=await e(r[0]);return t?.(n),as(r,n,i)}}let i="req"in r[0]?r[0].req:r[0],n="res"in r[0]?r[0].res:r[1],s=await e(i);return t?.(s),ar(new Headers(i.headers),s).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in n?n.headers.append("set-cookie",t):n.appendHeader("set-cookie",t);return t})}:(...t)=>{if(!t.length)return Promise.resolve((0,at.b)()).then(t=>ar(t,e).then(e=>e.json()));if(t[0]instanceof Request)return as([t[0],t[1]],e);if(ai(t[0])){let r=t[0];return async(...t)=>as(t,e,r).then(e=>e)}let r="req"in t[0]?t[0].req:t[0],i="res"in t[0]?t[0].res:t[1];return ar(new Headers(r.headers),e).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in i?i.headers.append("set-cookie",t):i.appendHeader("set-cookie",t);return t})}}async function as(e,t,r){let i=s9(e[0]),n=await ar(i.headers,t),s=await n.json(),a=!0;t.callbacks?.authorized&&(a=await t.callbacks.authorized({request:i,auth:s}));let o=s4.NextResponse.next?.();if(a instanceof Response){o=a;let e=a.headers.get("Location"),{pathname:r}=i.nextUrl;e&&function(e,t,r){let i=t.replace(`${e}/`,""),n=Object.values(r.pages??{});return(aa.has(i)||n.includes(t))&&t===e}(r,new URL(e).pathname,t)&&(a=!0)}else if(r)i.auth=s,o=await r(i,e[1])??s4.NextResponse.next();else if(!a){let e=t.pages?.signIn??`${t.basePath}/signin`;if(i.nextUrl.pathname!==e){let t=i.nextUrl.clone();t.pathname=e,t.searchParams.set("callbackUrl",i.nextUrl.href),o=s4.NextResponse.redirect(t)}}let l=new Response(o?.body,o);for(let e of n.headers.getSetCookie())l.headers.append("set-cookie",e);return l}r(73913);let aa=new Set(["providers","session","csrf","signin","signout","callback","verify-request","error"]);var ao=r(39916);async function al(e,t={},r,i){let n=new Headers(await (0,at.b)()),{redirect:s=!0,redirectTo:a,...o}=t instanceof FormData?Object.fromEntries(t):t,l=a?.toString()??n.get("Referer")??"/",c=s3("signin",n.get("x-forwarded-proto"),n,process.env,i);if(!e)return c.searchParams.append("callbackUrl",l),s&&(0,ao.redirect)(c.toString()),c.toString();let u=`${c}/${e}?${new URLSearchParams(r)}`,d={};for(let t of i.providers){let{options:r,...i}="function"==typeof t?t():t,n=r?.id??i.id;if(n===e){d={id:n,type:r?.type??i.type};break}}if(!d.id){let e=`${c}?${new URLSearchParams({callbackUrl:l})}`;return s&&(0,ao.redirect)(e),e}"credentials"===d.type&&(u=u.replace("signin","callback")),n.set("Content-Type","application/x-www-form-urlencoded");let f=new Request(u,{method:"POST",headers:n,body:new URLSearchParams({...o,callbackUrl:l})}),h=await s8(f,{...i,raw:rc,skipCSRFCheck:rl}),p=await (0,ae.U)();for(let e of h?.cookies??[])p.set(e.name,e.value,e.options);let y=(h instanceof Response?h.headers.get("Location"):h.redirect)??u;return s?(0,ao.redirect)(y):y}async function ac(e,t){let r=new Headers(await (0,at.b)());r.set("Content-Type","application/x-www-form-urlencoded");let i=s3("signout",r.get("x-forwarded-proto"),r,process.env,t),n=new URLSearchParams({callbackUrl:e?.redirectTo??r.get("Referer")??"/"}),s=new Request(i,{method:"POST",headers:r,body:n}),a=await s8(s,{...t,raw:rc,skipCSRFCheck:rl}),o=await (0,ae.U)();for(let e of a?.cookies??[])o.set(e.name,e.value,e.options);return e?.redirect??!0?(0,ao.redirect)(a.redirect):a}async function au(e,t){let r=new Headers(await (0,at.b)());r.set("Content-Type","application/json");let i=new Request(s3("session",r.get("x-forwarded-proto"),r,process.env,t),{method:"POST",headers:r,body:JSON.stringify({data:e})}),n=await s8(i,{...t,raw:rc,skipCSRFCheck:rl}),s=await (0,ae.U)();for(let e of n?.cookies??[])s.set(e.name,e.value,e.options);return n.body}function ad(e){if("function"==typeof e){let t=async t=>{let r=await e(t);return s7(r),s8(s9(t),r)};return{handlers:{GET:t,POST:t},auth:an(e,e=>s7(e)),signIn:async(t,r,i)=>{let n=await e(void 0);return s7(n),al(t,r,i,n)},signOut:async t=>{let r=await e(void 0);return s7(r),ac(t,r)},unstable_update:async t=>{let r=await e(void 0);return s7(r),au(t,r)}}}s7(e);let t=t=>s8(s9(t),e);return{handlers:{GET:t,POST:t},auth:an(e),signIn:(t,r,i)=>al(t,r,i,e),signOut:t=>ac(t,e),unstable_update:t=>au(t,e)}}},31162:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return s}});let i=r(8704),n=r(49026);function s(e){return(0,n.isRedirectError)(e)||(0,i.isHTTPAccessFallbackError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},32705:(e,t,r)=>{r.d(t,{_:()=>rY});var i=r(5730),n=r(45958),s=r(96657),a=r(67083);class o extends s.Xs{constructor(e){super(o.buildEmbeddedCount(e.source,e.filters).queryChunks),this.params=e,this.mapWith(Number),this.session=e.session,this.sql=o.buildCount(e.source,e.filters)}sql;static [i.i]="MySqlCountBuilder";[Symbol.toStringTag]="MySqlCountBuilder";session;static buildEmbeddedCount(e,t){return(0,s.ll)`(select count(*) from ${e}${s.ll.raw(" where ").if(t)}${t})`}static buildCount(e,t){return(0,s.ll)`select count(*) as count from ${e}${s.ll.raw(" where ").if(t)}${t}`}then(e,t){return Promise.resolve(this.session.count(this.sql)).then(e,t)}catch(e){return this.then(void 0,e)}finally(e){return this.then(t=>(e?.(),t),t=>{throw e?.(),t})}}var l=r(89685),c=r(86890),u=r(10007),d=r(40774),f=r(3884),h=r(94634),p=r(24717),y=r(79608),m=r(12772),g=r(38949),b=r(86214);class w{static [i.i]="MySqlForeignKeyBuilder";reference;_onUpdate;_onDelete;constructor(e,t){this.reference=()=>{let{name:t,columns:r,foreignColumns:i}=e();return{name:t,columns:r,foreignTable:i[0].table,foreignColumns:i}},t&&(this._onUpdate=t.onUpdate,this._onDelete=t.onDelete)}onUpdate(e){return this._onUpdate=e,this}onDelete(e){return this._onDelete=e,this}build(e){return new x(e,this)}}class x{constructor(e,t){this.table=e,this.reference=t.reference,this.onUpdate=t._onUpdate,this.onDelete=t._onDelete}static [i.i]="MySqlForeignKey";reference;onUpdate;onDelete;getName(){let{name:e,columns:t,foreignColumns:r}=this.reference(),i=t.map(e=>e.name),n=r.map(e=>e.name),s=[this.table[b.E],...i,r[0].table[b.E],...n];return e??`${s.join("_")}_fk`}}function v(e,t){return`${e[b.E]}_${t.join("_")}_unique`}class _{constructor(e,t){this.name=t,this.columns=e}static [i.i]=null;columns;build(e){return new k(e,this.columns,this.name)}}class S{static [i.i]=null;name;constructor(e){this.name=e}on(...e){return new _(e,this.name)}}class k{constructor(e,t,r){this.table=e,this.columns=t,this.name=r??v(this.table,this.columns.map(e=>e.name))}static [i.i]=null;columns;name;nullsNotDistinct=!1;getName(){return this.name}}class A extends g.Q{static [i.i]="MySqlColumnBuilder";foreignKeyConfigs=[];references(e,t={}){return this.foreignKeyConfigs.push({ref:e,actions:t}),this}unique(e){return this.config.isUnique=!0,this.config.uniqueName=e,this}generatedAlwaysAs(e,t){return this.config.generated={as:e,type:"always",mode:t?.mode??"virtual"},this}buildForeignKeys(e,t){return this.foreignKeyConfigs.map(({ref:r,actions:i})=>((r,i)=>{let n=new w(()=>({columns:[e],foreignColumns:[r()]}));return i.onUpdate&&n.onUpdate(i.onUpdate),i.onDelete&&n.onDelete(i.onDelete),n.build(t)})(r,i))}}class E extends u.V{constructor(e,t){t.uniqueName||(t.uniqueName=v(e,[t.name])),super(e,t),this.table=e}static [i.i]="MySqlColumn"}class T extends A{static [i.i]="MySqlColumnBuilderWithAutoIncrement";constructor(e,t,r){super(e,t,r),this.config.autoIncrement=!1}autoincrement(){return this.config.autoIncrement=!0,this.config.hasDefault=!0,this}}class C extends E{static [i.i]="MySqlColumnWithAutoIncrement";autoIncrement=this.config.autoIncrement}class $ extends T{static [i.i]="MySqlBigInt53Builder";constructor(e,t=!1){super(e,"number","MySqlBigInt53"),this.config.unsigned=t}build(e){return new O(e,this.config)}}class O extends C{static [i.i]="MySqlBigInt53";getSQLType(){return`bigint${this.config.unsigned?" unsigned":""}`}mapFromDriverValue(e){return"number"==typeof e?e:Number(e)}}class R extends T{static [i.i]="MySqlBigInt64Builder";constructor(e,t=!1){super(e,"bigint","MySqlBigInt64"),this.config.unsigned=t}build(e){return new I(e,this.config)}}class I extends C{static [i.i]="MySqlBigInt64";getSQLType(){return`bigint${this.config.unsigned?" unsigned":""}`}mapFromDriverValue(e){return BigInt(e)}}function P(e,t){let{name:r,config:i}=(0,y.Ll)(e,t);return"number"===i.mode?new $(r,i.unsigned):new R(r,i.unsigned)}class j extends A{static [i.i]="MySqlBinaryBuilder";constructor(e,t){super(e,"string","MySqlBinary"),this.config.length=t}build(e){return new U(e,this.config)}}class U extends E{static [i.i]="MySqlBinary";length=this.config.length;mapFromDriverValue(e){if("string"==typeof e)return e;if(Buffer.isBuffer(e))return e.toString();let t=[];for(let r of e)t.push(49===r?"1":"0");return t.join("")}getSQLType(){return void 0===this.length?"binary":`binary(${this.length})`}}function q(e,t={}){let{name:r,config:i}=(0,y.Ll)(e,t);return new j(r,i.length)}class D extends A{static [i.i]="MySqlBooleanBuilder";constructor(e){super(e,"boolean","MySqlBoolean")}build(e){return new L(e,this.config)}}class L extends E{static [i.i]="MySqlBoolean";getSQLType(){return"boolean"}mapFromDriverValue(e){return"boolean"==typeof e?e:1===e}}function N(e){return new D(e??"")}class M extends A{static [i.i]="MySqlCharBuilder";constructor(e,t){super(e,"string","MySqlChar"),this.config.length=t.length,this.config.enum=t.enum}build(e){return new B(e,this.config)}}class B extends E{static [i.i]="MySqlChar";length=this.config.length;enumValues=this.config.enum;getSQLType(){return void 0===this.length?"char":`char(${this.length})`}}function H(e,t={}){let{name:r,config:i}=(0,y.Ll)(e,t);return new M(r,i)}class K extends A{static [i.i]="MySqlCustomColumnBuilder";constructor(e,t,r){super(e,"custom","MySqlCustomColumn"),this.config.fieldConfig=t,this.config.customTypeParams=r}build(e){return new W(e,this.config)}}class W extends E{static [i.i]="MySqlCustomColumn";sqlName;mapTo;mapFrom;constructor(e,t){super(e,t),this.sqlName=t.customTypeParams.dataType(t.fieldConfig),this.mapTo=t.customTypeParams.toDriver,this.mapFrom=t.customTypeParams.fromDriver}getSQLType(){return this.sqlName}mapFromDriverValue(e){return"function"==typeof this.mapFrom?this.mapFrom(e):e}mapToDriverValue(e){return"function"==typeof this.mapTo?this.mapTo(e):e}}function Q(e){return(t,r)=>{let{name:i,config:n}=(0,y.Ll)(t,r);return new K(i,n,e)}}class J extends A{static [i.i]="MySqlDateBuilder";constructor(e){super(e,"date","MySqlDate")}build(e){return new F(e,this.config)}}class F extends E{static [i.i]="MySqlDate";constructor(e,t){super(e,t)}getSQLType(){return"date"}mapFromDriverValue(e){return new Date(e)}}class V extends A{static [i.i]="MySqlDateStringBuilder";constructor(e){super(e,"string","MySqlDateString")}build(e){return new X(e,this.config)}}class X extends E{static [i.i]="MySqlDateString";constructor(e,t){super(e,t)}getSQLType(){return"date"}}function z(e,t){let{name:r,config:i}=(0,y.Ll)(e,t);return i?.mode==="string"?new V(r):new J(r)}class G extends A{static [i.i]="MySqlDateTimeBuilder";constructor(e,t){super(e,"date","MySqlDateTime"),this.config.fsp=t?.fsp}build(e){return new Y(e,this.config)}}class Y extends E{static [i.i]="MySqlDateTime";fsp;constructor(e,t){super(e,t),this.fsp=t.fsp}getSQLType(){let e=void 0===this.fsp?"":`(${this.fsp})`;return`datetime${e}`}mapToDriverValue(e){return e.toISOString().replace("T"," ").replace("Z","")}mapFromDriverValue(e){return new Date(e.replace(" ","T")+"Z")}}class Z extends A{static [i.i]="MySqlDateTimeStringBuilder";constructor(e,t){super(e,"string","MySqlDateTimeString"),this.config.fsp=t?.fsp}build(e){return new ee(e,this.config)}}class ee extends E{static [i.i]="MySqlDateTimeString";fsp;constructor(e,t){super(e,t),this.fsp=t.fsp}getSQLType(){let e=void 0===this.fsp?"":`(${this.fsp})`;return`datetime${e}`}}function et(e,t){let{name:r,config:i}=(0,y.Ll)(e,t);return i?.mode==="string"?new Z(r,i):new G(r,i)}class er extends T{static [i.i]="MySqlDecimalBuilder";constructor(e,t){super(e,"string","MySqlDecimal"),this.config.precision=t?.precision,this.config.scale=t?.scale,this.config.unsigned=t?.unsigned}build(e){return new ei(e,this.config)}}class ei extends C{static [i.i]="MySqlDecimal";precision=this.config.precision;scale=this.config.scale;unsigned=this.config.unsigned;mapFromDriverValue(e){return"string"==typeof e?e:String(e)}getSQLType(){let e="";return void 0!==this.precision&&void 0!==this.scale?e+=`decimal(${this.precision},${this.scale})`:void 0===this.precision?e+="decimal":e+=`decimal(${this.precision})`,e="decimal(10,0)"===e||"decimal(10)"===e?"decimal":e,this.unsigned?`${e} unsigned`:e}}class en extends T{static [i.i]="MySqlDecimalNumberBuilder";constructor(e,t){super(e,"number","MySqlDecimalNumber"),this.config.precision=t?.precision,this.config.scale=t?.scale,this.config.unsigned=t?.unsigned}build(e){return new es(e,this.config)}}class es extends C{static [i.i]="MySqlDecimalNumber";precision=this.config.precision;scale=this.config.scale;unsigned=this.config.unsigned;mapFromDriverValue(e){return"number"==typeof e?e:Number(e)}mapToDriverValue=String;getSQLType(){let e="";return void 0!==this.precision&&void 0!==this.scale?e+=`decimal(${this.precision},${this.scale})`:void 0===this.precision?e+="decimal":e+=`decimal(${this.precision})`,e="decimal(10,0)"===e||"decimal(10)"===e?"decimal":e,this.unsigned?`${e} unsigned`:e}}class ea extends T{static [i.i]="MySqlDecimalBigIntBuilder";constructor(e,t){super(e,"bigint","MySqlDecimalBigInt"),this.config.precision=t?.precision,this.config.scale=t?.scale,this.config.unsigned=t?.unsigned}build(e){return new eo(e,this.config)}}class eo extends C{static [i.i]="MySqlDecimalBigInt";precision=this.config.precision;scale=this.config.scale;unsigned=this.config.unsigned;mapFromDriverValue=BigInt;mapToDriverValue=String;getSQLType(){let e="";return void 0!==this.precision&&void 0!==this.scale?e+=`decimal(${this.precision},${this.scale})`:void 0===this.precision?e+="decimal":e+=`decimal(${this.precision})`,e="decimal(10,0)"===e||"decimal(10)"===e?"decimal":e,this.unsigned?`${e} unsigned`:e}}function el(e,t={}){let{name:r,config:i}=(0,y.Ll)(e,t),n=i?.mode;return"number"===n?new en(r,i):"bigint"===n?new ea(r,i):new er(r,i)}class ec extends T{static [i.i]="MySqlDoubleBuilder";constructor(e,t){super(e,"number","MySqlDouble"),this.config.precision=t?.precision,this.config.scale=t?.scale,this.config.unsigned=t?.unsigned}build(e){return new eu(e,this.config)}}class eu extends C{static [i.i]="MySqlDouble";precision=this.config.precision;scale=this.config.scale;unsigned=this.config.unsigned;getSQLType(){let e="";return void 0!==this.precision&&void 0!==this.scale?e+=`double(${this.precision},${this.scale})`:void 0===this.precision?e+="double":e+=`double(${this.precision})`,this.unsigned?`${e} unsigned`:e}}function ed(e,t){let{name:r,config:i}=(0,y.Ll)(e,t);return new ec(r,i)}class ef extends A{static [i.i]="MySqlEnumColumnBuilder";constructor(e,t){super(e,"string","MySqlEnumColumn"),this.config.enumValues=t}build(e){return new eh(e,this.config)}}class eh extends E{static [i.i]="MySqlEnumColumn";enumValues=this.config.enumValues;getSQLType(){return`enum(${this.enumValues.map(e=>`'${e}'`).join(",")})`}}class ep extends A{static [i.i]="MySqlEnumObjectColumnBuilder";constructor(e,t){super(e,"string","MySqlEnumObjectColumn"),this.config.enumValues=t}build(e){return new ey(e,this.config)}}class ey extends E{static [i.i]="MySqlEnumObjectColumn";enumValues=this.config.enumValues;getSQLType(){return`enum(${this.enumValues.map(e=>`'${e}'`).join(",")})`}}function em(e,t){if("string"==typeof e&&Array.isArray(t)||Array.isArray(e)){let r="string"==typeof e&&e.length>0?e:"",i=("string"==typeof e?t:e)??[];if(0===i.length)throw Error(`You have an empty array for "${r}" enum values`);return new ef(r,i)}if("string"==typeof e&&"object"==typeof t||"object"==typeof e){let r="object"==typeof e?"":e,i="object"==typeof e?Object.values(e):"object"==typeof t?Object.values(t):[];if(0===i.length)throw Error(`You have an empty array for "${r}" enum values`);return new ep(r,i)}}class eg extends T{static [i.i]="MySqlFloatBuilder";constructor(e,t){super(e,"number","MySqlFloat"),this.config.precision=t?.precision,this.config.scale=t?.scale,this.config.unsigned=t?.unsigned}build(e){return new eb(e,this.config)}}class eb extends C{static [i.i]="MySqlFloat";precision=this.config.precision;scale=this.config.scale;unsigned=this.config.unsigned;getSQLType(){let e="";return void 0!==this.precision&&void 0!==this.scale?e+=`float(${this.precision},${this.scale})`:void 0===this.precision?e+="float":e+=`float(${this.precision})`,this.unsigned?`${e} unsigned`:e}}function ew(e,t){let{name:r,config:i}=(0,y.Ll)(e,t);return new eg(r,i)}class ex extends T{static [i.i]="MySqlIntBuilder";constructor(e,t){super(e,"number","MySqlInt"),this.config.unsigned=!!t&&t.unsigned}build(e){return new ev(e,this.config)}}class ev extends C{static [i.i]="MySqlInt";getSQLType(){return`int${this.config.unsigned?" unsigned":""}`}mapFromDriverValue(e){return"string"==typeof e?Number(e):e}}function e_(e,t){let{name:r,config:i}=(0,y.Ll)(e,t);return new ex(r,i)}class eS extends A{static [i.i]="MySqlJsonBuilder";constructor(e){super(e,"json","MySqlJson")}build(e){return new ek(e,this.config)}}class ek extends E{static [i.i]="MySqlJson";getSQLType(){return"json"}mapToDriverValue(e){return JSON.stringify(e)}}function eA(e){return new eS(e??"")}class eE extends T{static [i.i]="MySqlMediumIntBuilder";constructor(e,t){super(e,"number","MySqlMediumInt"),this.config.unsigned=!!t&&t.unsigned}build(e){return new eT(e,this.config)}}class eT extends C{static [i.i]="MySqlMediumInt";getSQLType(){return`mediumint${this.config.unsigned?" unsigned":""}`}mapFromDriverValue(e){return"string"==typeof e?Number(e):e}}function eC(e,t){let{name:r,config:i}=(0,y.Ll)(e,t);return new eE(r,i)}class e$ extends T{static [i.i]="MySqlRealBuilder";constructor(e,t){super(e,"number","MySqlReal"),this.config.precision=t?.precision,this.config.scale=t?.scale}build(e){return new eO(e,this.config)}}class eO extends C{static [i.i]="MySqlReal";precision=this.config.precision;scale=this.config.scale;getSQLType(){return void 0!==this.precision&&void 0!==this.scale?`real(${this.precision}, ${this.scale})`:void 0===this.precision?"real":`real(${this.precision})`}}function eR(e,t={}){let{name:r,config:i}=(0,y.Ll)(e,t);return new e$(r,i)}class eI extends T{static [i.i]="MySqlSerialBuilder";constructor(e){super(e,"number","MySqlSerial"),this.config.hasDefault=!0,this.config.autoIncrement=!0}build(e){return new eP(e,this.config)}}class eP extends C{static [i.i]="MySqlSerial";getSQLType(){return"serial"}mapFromDriverValue(e){return"string"==typeof e?Number(e):e}}function ej(e){return new eI(e??"")}class eU extends T{static [i.i]="MySqlSmallIntBuilder";constructor(e,t){super(e,"number","MySqlSmallInt"),this.config.unsigned=!!t&&t.unsigned}build(e){return new eq(e,this.config)}}class eq extends C{static [i.i]="MySqlSmallInt";getSQLType(){return`smallint${this.config.unsigned?" unsigned":""}`}mapFromDriverValue(e){return"string"==typeof e?Number(e):e}}function eD(e,t){let{name:r,config:i}=(0,y.Ll)(e,t);return new eU(r,i)}class eL extends A{static [i.i]="MySqlTextBuilder";constructor(e,t,r){super(e,"string","MySqlText"),this.config.textType=t,this.config.enumValues=r.enum}build(e){return new eN(e,this.config)}}class eN extends E{static [i.i]="MySqlText";textType=this.config.textType;enumValues=this.config.enumValues;getSQLType(){return this.textType}}function eM(e,t={}){let{name:r,config:i}=(0,y.Ll)(e,t);return new eL(r,"text",i)}function eB(e,t={}){let{name:r,config:i}=(0,y.Ll)(e,t);return new eL(r,"tinytext",i)}function eH(e,t={}){let{name:r,config:i}=(0,y.Ll)(e,t);return new eL(r,"mediumtext",i)}function eK(e,t={}){let{name:r,config:i}=(0,y.Ll)(e,t);return new eL(r,"longtext",i)}class eW extends A{static [i.i]="MySqlTimeBuilder";constructor(e,t){super(e,"string","MySqlTime"),this.config.fsp=t?.fsp}build(e){return new eQ(e,this.config)}}class eQ extends E{static [i.i]="MySqlTime";fsp=this.config.fsp;getSQLType(){let e=void 0===this.fsp?"":`(${this.fsp})`;return`time${e}`}}function eJ(e,t){let{name:r,config:i}=(0,y.Ll)(e,t);return new eW(r,i)}class eF extends A{static [i.i]="MySqlDateColumnBuilder";defaultNow(){return this.default((0,s.ll)`(now())`)}onUpdateNow(){return this.config.hasOnUpdateNow=!0,this.config.hasDefault=!0,this}}class eV extends E{static [i.i]="MySqlDateColumn";hasOnUpdateNow=this.config.hasOnUpdateNow}class eX extends eF{static [i.i]="MySqlTimestampBuilder";constructor(e,t){super(e,"date","MySqlTimestamp"),this.config.fsp=t?.fsp}build(e){return new ez(e,this.config)}}class ez extends eV{static [i.i]="MySqlTimestamp";fsp=this.config.fsp;getSQLType(){let e=void 0===this.fsp?"":`(${this.fsp})`;return`timestamp${e}`}mapFromDriverValue(e){return new Date(e+"+0000")}mapToDriverValue(e){return e.toISOString().slice(0,-1).replace("T"," ")}}class eG extends eF{static [i.i]="MySqlTimestampStringBuilder";constructor(e,t){super(e,"string","MySqlTimestampString"),this.config.fsp=t?.fsp}build(e){return new eY(e,this.config)}}class eY extends eV{static [i.i]="MySqlTimestampString";fsp=this.config.fsp;getSQLType(){let e=void 0===this.fsp?"":`(${this.fsp})`;return`timestamp${e}`}}function eZ(e,t={}){let{name:r,config:i}=(0,y.Ll)(e,t);return i?.mode==="string"?new eG(r,i):new eX(r,i)}class e0 extends T{static [i.i]="MySqlTinyIntBuilder";constructor(e,t){super(e,"number","MySqlTinyInt"),this.config.unsigned=!!t&&t.unsigned}build(e){return new e1(e,this.config)}}class e1 extends C{static [i.i]="MySqlTinyInt";getSQLType(){return`tinyint${this.config.unsigned?" unsigned":""}`}mapFromDriverValue(e){return"string"==typeof e?Number(e):e}}function e2(e,t){let{name:r,config:i}=(0,y.Ll)(e,t);return new e0(r,i)}class e5 extends A{static [i.i]="MySqlVarBinaryBuilder";constructor(e,t){super(e,"string","MySqlVarBinary"),this.config.length=t?.length}build(e){return new e6(e,this.config)}}class e6 extends E{static [i.i]="MySqlVarBinary";length=this.config.length;mapFromDriverValue(e){if("string"==typeof e)return e;if(Buffer.isBuffer(e))return e.toString();let t=[];for(let r of e)t.push(49===r?"1":"0");return t.join("")}getSQLType(){return void 0===this.length?"varbinary":`varbinary(${this.length})`}}function e3(e,t){let{name:r,config:i}=(0,y.Ll)(e,t);return new e5(r,i)}class e8 extends A{static [i.i]="MySqlVarCharBuilder";constructor(e,t){super(e,"string","MySqlVarChar"),this.config.length=t.length,this.config.enum=t.enum}build(e){return new e4(e,this.config)}}class e4 extends E{static [i.i]="MySqlVarChar";length=this.config.length;enumValues=this.config.enum;getSQLType(){return void 0===this.length?"varchar":`varchar(${this.length})`}}function e9(e,t){let{name:r,config:i}=(0,y.Ll)(e,t);return new e8(r,i)}class e7 extends A{static [i.i]="MySqlYearBuilder";constructor(e){super(e,"number","MySqlYear")}build(e){return new te(e,this.config)}}class te extends E{static [i.i]="MySqlYear";getSQLType(){return"year"}}function tt(e){return new e7(e??"")}let tr=Symbol.for("drizzle:MySqlInlineForeignKeys");class ti extends p.XI{static [i.i]="MySqlTable";static Symbol=Object.assign({},p.XI.Symbol,{InlineForeignKeys:tr});[p.XI.Symbol.Columns];[tr]=[];[p.XI.Symbol.ExtraConfigBuilder]=void 0}let tn=(e,t,r)=>(function(e,t,r,i,n=e){let s=new ti(e,i,n),a=Object.fromEntries(Object.entries("function"==typeof t?t({bigint:P,binary:q,boolean:N,char:H,customType:Q,date:z,datetime:et,decimal:el,double:ed,mysqlEnum:em,float:ew,int:e_,json:eA,mediumint:eC,real:eR,serial:ej,smallint:eD,text:eM,time:eJ,timestamp:eZ,tinyint:e2,varbinary:e3,varchar:e9,year:tt,longtext:eK,mediumtext:eH,tinytext:eB}):t).map(([e,t])=>{t.setName(e);let r=t.build(s);return s[tr].push(...t.buildForeignKeys(r,s)),[e,r]})),o=Object.assign(s,a);return o[p.XI.Symbol.Columns]=a,o[p.XI.Symbol.ExtraConfigColumns]=a,r&&(o[ti.Symbol.ExtraConfigBuilder]=r),o})(e,t,r,void 0,e);class ts extends s.Ss{static [i.i]="MySqlViewBase"}class ta{static [i.i]="MySqlDialect";casing;constructor(e){this.casing=new c.Yn(e?.casing)}async migrate(e,t,r){let i=r.migrationsTable??"__drizzle_migrations",n=(0,s.ll)`
			create table if not exists ${s.ll.identifier(i)} (
				id serial primary key,
				hash text not null,
				created_at bigint
			)
		`;await t.execute(n);let a=(await t.all((0,s.ll)`select id, hash, created_at from ${s.ll.identifier(i)} order by created_at desc limit 1`))[0];await t.transaction(async t=>{for(let r of e)if(!a||Number(a.created_at)<r.folderMillis){for(let e of r.sql)await t.execute(s.ll.raw(e));await t.execute((0,s.ll)`insert into ${s.ll.identifier(i)} (\`hash\`, \`created_at\`) values(${r.hash}, ${r.folderMillis})`)}})}escapeName(e){return`\`${e}\``}escapeParam(e){return"?"}escapeString(e){return`'${e.replace(/'/g,"''")}'`}buildWithCTE(e){if(!e?.length)return;let t=[(0,s.ll)`with `];for(let[r,i]of e.entries())t.push((0,s.ll)`${s.ll.identifier(i._.alias)} as (${i._.sql})`),r<e.length-1&&t.push((0,s.ll)`, `);return t.push((0,s.ll)` `),s.ll.join(t)}buildDeleteQuery({table:e,where:t,returning:r,withList:i,limit:n,orderBy:a}){let o=this.buildWithCTE(i),l=r?(0,s.ll)` returning ${this.buildSelection(r,{isSingleTable:!0})}`:void 0,c=t?(0,s.ll)` where ${t}`:void 0,u=this.buildOrderBy(a),d=this.buildLimit(n);return(0,s.ll)`${o}delete from ${e}${c}${u}${d}${l}`}buildUpdateSet(e,t){let r=e[p.XI.Symbol.Columns],i=Object.keys(r).filter(e=>void 0!==t[e]||r[e]?.onUpdateFn!==void 0),n=i.length;return s.ll.join(i.flatMap((e,i)=>{let a=r[e],o=t[e]??s.ll.param(a.onUpdateFn(),a),l=(0,s.ll)`${s.ll.identifier(this.casing.getColumnCasing(a))} = ${o}`;return i<n-1?[l,s.ll.raw(", ")]:[l]}))}buildUpdateQuery({table:e,set:t,where:r,returning:i,withList:n,limit:a,orderBy:o}){let l=this.buildWithCTE(n),c=this.buildUpdateSet(e,t),u=i?(0,s.ll)` returning ${this.buildSelection(i,{isSingleTable:!0})}`:void 0,d=r?(0,s.ll)` where ${r}`:void 0,f=this.buildOrderBy(o),h=this.buildLimit(a);return(0,s.ll)`${l}update ${e} set ${c}${d}${f}${h}${u}`}buildSelection(e,{isSingleTable:t=!1}={}){let r=e.length,n=e.flatMap(({field:e},n)=>{let a=[];if((0,i.is)(e,s.Xs.Aliased)&&e.isSelectionField)a.push(s.ll.identifier(e.fieldAlias));else if((0,i.is)(e,s.Xs.Aliased)||(0,i.is)(e,s.Xs)){let r=(0,i.is)(e,s.Xs.Aliased)?e.sql:e;t?a.push(new s.Xs(r.queryChunks.map(e=>(0,i.is)(e,E)?s.ll.identifier(this.casing.getColumnCasing(e)):e))):a.push(r),(0,i.is)(e,s.Xs.Aliased)&&a.push((0,s.ll)` as ${s.ll.identifier(e.fieldAlias)}`)}else(0,i.is)(e,u.V)&&(t?a.push(s.ll.identifier(this.casing.getColumnCasing(e))):a.push(e));return n<r-1&&a.push((0,s.ll)`, `),a});return s.ll.join(n)}buildLimit(e){return"object"==typeof e||"number"==typeof e&&e>=0?(0,s.ll)` limit ${e}`:void 0}buildOrderBy(e){return e&&e.length>0?(0,s.ll)` order by ${s.ll.join(e,(0,s.ll)`, `)}`:void 0}buildIndex({indexes:e,indexFor:t}){return e&&e.length>0?(0,s.ll)` ${s.ll.raw(t)} INDEX (${s.ll.raw(e.join(", "))})`:void 0}buildSelectQuery({withList:e,fields:t,fieldsFlat:r,where:n,having:o,table:l,joins:c,orderBy:d,groupBy:f,limit:h,offset:g,lockingClause:b,distinct:w,setOperators:x,useIndex:v,forceIndex:_,ignoreIndex:S}){let k,A=r??(0,y.He)(t);for(let e of A){let t;if((0,i.is)(e.field,u.V)&&(0,p.Io)(e.field.table)!==((0,i.is)(l,a.n)?l._.alias:(0,i.is)(l,ts)?l[m.n].name:(0,i.is)(l,s.Xs)?void 0:(0,p.Io)(l))&&(t=e.field.table,!c?.some(({alias:e})=>e===(t[p.XI.Symbol.IsAlias]?(0,p.Io)(t):t[p.XI.Symbol.BaseName])))){let t=(0,p.Io)(e.field.table);throw Error(`Your "${e.path.join("->")}" field references a column "${t}"."${e.field.name}", but the table "${t}" is not part of the query! Did you forget to join it?`)}}let E=!c||0===c.length,T=this.buildWithCTE(e),C=w?(0,s.ll)` distinct`:void 0,$=this.buildSelection(A,{isSingleTable:E}),O=(0,i.is)(l,p.XI)&&l[p.XI.Symbol.IsAlias]?(0,s.ll)`${(0,s.ll)`${s.ll.identifier(l[p.XI.Symbol.Schema]??"")}.`.if(l[p.XI.Symbol.Schema])}${s.ll.identifier(l[p.XI.Symbol.OriginalName])} ${s.ll.identifier(l[p.XI.Symbol.Name])}`:l,R=[];if(c)for(let[e,t]of c.entries()){0===e&&R.push((0,s.ll)` `);let r=t.table,n=t.lateral?(0,s.ll)` lateral`:void 0,a=t.on?(0,s.ll)` on ${t.on}`:void 0;if((0,i.is)(r,ti)){let e=r[ti.Symbol.Name],i=r[ti.Symbol.Schema],o=r[ti.Symbol.OriginalName],l=e===o?void 0:t.alias,c=this.buildIndex({indexes:t.useIndex,indexFor:"USE"}),u=this.buildIndex({indexes:t.forceIndex,indexFor:"FORCE"}),d=this.buildIndex({indexes:t.ignoreIndex,indexFor:"IGNORE"});R.push((0,s.ll)`${s.ll.raw(t.joinType)} join${n} ${i?(0,s.ll)`${s.ll.identifier(i)}.`:void 0}${s.ll.identifier(o)}${c}${u}${d}${l&&(0,s.ll)` ${s.ll.identifier(l)}`}${a}`)}else if((0,i.is)(r,s.Ss)){let e=r[m.n].name,i=r[m.n].schema,o=r[m.n].originalName,l=e===o?void 0:t.alias;R.push((0,s.ll)`${s.ll.raw(t.joinType)} join${n} ${i?(0,s.ll)`${s.ll.identifier(i)}.`:void 0}${s.ll.identifier(o)}${l&&(0,s.ll)` ${s.ll.identifier(l)}`}${a}`)}else R.push((0,s.ll)`${s.ll.raw(t.joinType)} join${n} ${r}${a}`);e<c.length-1&&R.push((0,s.ll)` `)}let I=s.ll.join(R),P=n?(0,s.ll)` where ${n}`:void 0,j=o?(0,s.ll)` having ${o}`:void 0,U=this.buildOrderBy(d),q=f&&f.length>0?(0,s.ll)` group by ${s.ll.join(f,(0,s.ll)`, `)}`:void 0,D=this.buildLimit(h),L=g?(0,s.ll)` offset ${g}`:void 0,N=this.buildIndex({indexes:v,indexFor:"USE"}),M=this.buildIndex({indexes:_,indexFor:"FORCE"}),B=this.buildIndex({indexes:S,indexFor:"IGNORE"});if(b){let{config:e,strength:t}=b;k=(0,s.ll)` for ${s.ll.raw(t)}`,e.noWait?k.append((0,s.ll)` nowait`):e.skipLocked&&k.append((0,s.ll)` skip locked`)}let H=(0,s.ll)`${T}select${C} ${$} from ${O}${N}${M}${B}${I}${P}${q}${j}${U}${D}${L}${k}`;return x.length>0?this.buildSetOperations(H,x):H}buildSetOperations(e,t){let[r,...i]=t;if(!r)throw Error("Cannot pass undefined values to any set operator");return 0===i.length?this.buildSetOperationQuery({leftSelect:e,setOperator:r}):this.buildSetOperations(this.buildSetOperationQuery({leftSelect:e,setOperator:r}),i)}buildSetOperationQuery({leftSelect:e,setOperator:{type:t,isAll:r,rightSelect:n,limit:a,orderBy:o,offset:l}}){let c,u=(0,s.ll)`(${e.getSQL()}) `,d=(0,s.ll)`(${n.getSQL()})`;if(o&&o.length>0){let e=[];for(let t of o)if((0,i.is)(t,E))e.push(s.ll.identifier(this.casing.getColumnCasing(t)));else if((0,i.is)(t,s.Xs)){for(let e=0;e<t.queryChunks.length;e++){let r=t.queryChunks[e];(0,i.is)(r,E)&&(t.queryChunks[e]=s.ll.identifier(this.casing.getColumnCasing(r)))}e.push((0,s.ll)`${t}`)}else e.push((0,s.ll)`${t}`);c=(0,s.ll)` order by ${s.ll.join(e,(0,s.ll)`, `)} `}let f="object"==typeof a||"number"==typeof a&&a>=0?(0,s.ll)` limit ${a}`:void 0,h=s.ll.raw(`${t} ${r?"all ":""}`),p=l?(0,s.ll)` offset ${l}`:void 0;return(0,s.ll)`${u}${h}${d}${c}${f}${p}`}buildInsertQuery({table:e,values:t,ignore:r,onConflict:n,select:a}){let o=[],l=Object.entries(e[p.XI.Symbol.Columns]).filter(([e,t])=>!t.shouldDisableInsert()),c=l.map(([,e])=>s.ll.identifier(this.casing.getColumnCasing(e))),u=[];if(a)(0,i.is)(t,s.Xs)?o.push(t):o.push(t.getSQL());else for(let[e,r]of(o.push(s.ll.raw("values ")),t.entries())){let n={},a=[];for(let[e,t]of l){let o=r[e];if(void 0===o||(0,i.is)(o,s.Iw)&&void 0===o.value)if(void 0!==t.defaultFn){let r=t.defaultFn();n[e]=r;let o=(0,i.is)(r,s.Xs)?r:s.ll.param(r,t);a.push(o)}else if(t.default||void 0===t.onUpdateFn)a.push((0,s.ll)`default`);else{let e=t.onUpdateFn(),r=(0,i.is)(e,s.Xs)?e:s.ll.param(e,t);a.push(r)}else t.defaultFn&&(0,i.is)(o,s.Iw)&&(n[e]=o.value),a.push(o)}u.push(n),o.push(a),e<t.length-1&&o.push((0,s.ll)`, `)}let d=s.ll.join(o),f=r?(0,s.ll)` ignore`:void 0,h=n?(0,s.ll)` on duplicate key ${n}`:void 0;return{sql:(0,s.ll)`insert${f} into ${e} ${c} ${d}${h}`,generatedIds:u}}sqlToQuery(e,t){return e.toQuery({casing:this.casing,escapeName:this.escapeName,escapeParam:this.escapeParam,escapeString:this.escapeString,invokeSource:t})}buildRelationalQuery({fullSchema:e,schema:t,tableNamesMap:r,table:n,tableConfig:o,queryConfig:c,tableAlias:y,nestedQueryRelation:m,joinOn:g}){let b,w,x,v,_,S=[],k=[];if(!0===c)S=Object.entries(o.columns).map(([e,t])=>({dbKey:t.name,tsKey:e,field:(0,l.ug)(t,y),relationTableTsKey:void 0,isJson:!1,selection:[]}));else{let n=Object.fromEntries(Object.entries(o.columns).map(([e,t])=>[e,(0,l.ug)(t,y)]));if(c.where){let e="function"==typeof c.where?c.where(n,(0,f.mm)()):c.where;v=e&&(0,l.yY)(e,y)}let d=[],m=[];if(c.columns){let e=!1;for(let[t,r]of Object.entries(c.columns))void 0!==r&&t in o.columns&&(e||!0!==r||(e=!0),m.push(t));m.length>0&&(m=e?m.filter(e=>c.columns?.[e]===!0):Object.keys(o.columns).filter(e=>!m.includes(e)))}else m=Object.keys(o.columns);for(let e of m){let t=o.columns[e];d.push({tsKey:e,value:t})}let g=[];if(c.with&&(g=Object.entries(c.with).filter(e=>!!e[1]).map(([e,t])=>({tsKey:e,queryConfig:t,relation:o.relations[e]}))),c.extras)for(let[e,t]of Object.entries("function"==typeof c.extras?c.extras(n,{sql:s.ll}):c.extras))d.push({tsKey:e,value:(0,l.Hs)(t,y)});for(let{tsKey:e,value:t}of d)S.push({dbKey:(0,i.is)(t,s.Xs.Aliased)?t.fieldAlias:o.columns[e].name,tsKey:e,field:(0,i.is)(t,u.V)?(0,l.ug)(t,y):t,relationTableTsKey:void 0,isJson:!1,selection:[]});let _="function"==typeof c.orderBy?c.orderBy(n,(0,f.rl)()):c.orderBy??[];for(let{tsKey:n,queryConfig:o,relation:d}of(Array.isArray(_)||(_=[_]),x=_.map(e=>(0,i.is)(e,u.V)?(0,l.ug)(e,y):(0,l.yY)(e,y)),b=c.limit,w=c.offset,g)){let c=(0,f.W0)(t,r,d),u=r[(0,p.Lf)(d.referencedTable)],m=`${y}_${n}`,g=(0,h.Uo)(...c.fields.map((e,t)=>(0,h.eq)((0,l.ug)(c.references[t],m),(0,l.ug)(e,y)))),b=this.buildRelationalQuery({fullSchema:e,schema:t,tableNamesMap:r,table:e[u],tableConfig:t[u],queryConfig:(0,i.is)(d,f.pD)?!0===o?{limit:1}:{...o,limit:1}:o,tableAlias:m,joinOn:g,nestedQueryRelation:d}),w=(0,s.ll)`${s.ll.identifier(m)}.${s.ll.identifier("data")}`.as(n);k.push({on:(0,s.ll)`true`,table:new a.n(b.sql,{},m),alias:m,joinType:"left",lateral:!0}),S.push({dbKey:n,tsKey:n,field:w,relationTableTsKey:u,isJson:!0,selection:b.selection})}}if(0===S.length)throw new d.n({message:`No fields selected for table "${o.tsName}" ("${y}")`});if(v=(0,h.Uo)(g,v),m){let e=(0,s.ll)`json_array(${s.ll.join(S.map(({field:e,tsKey:t,isJson:r})=>r?(0,s.ll)`${s.ll.identifier(`${y}_${t}`)}.${s.ll.identifier("data")}`:(0,i.is)(e,s.Xs.Aliased)?e.sql:e),(0,s.ll)`, `)})`;(0,i.is)(m,f.iv)&&(e=(0,s.ll)`coalesce(json_arrayagg(${e}), json_array())`);let t=[{dbKey:"data",tsKey:"data",field:e.as("data"),isJson:!0,relationTableTsKey:o.tsName,selection:S}];void 0!==b||void 0!==w||(x?.length??0)>0?(_=this.buildSelectQuery({table:(0,l.oG)(n,y),fields:{},fieldsFlat:[{path:[],field:s.ll.raw("*")},...(x?.length??0)>0?[{path:[],field:(0,s.ll)`row_number() over (order by ${s.ll.join(x,(0,s.ll)`, `)})`}]:[]],where:v,limit:b,offset:w,setOperators:[]}),v=void 0,b=void 0,w=void 0,x=void 0):_=(0,l.oG)(n,y),_=this.buildSelectQuery({table:(0,i.is)(_,ti)?_:new a.n(_,{},y),fields:{},fieldsFlat:t.map(({field:e})=>({path:[],field:(0,i.is)(e,u.V)?(0,l.ug)(e,y):e})),joins:k,where:v,limit:b,offset:w,orderBy:x,setOperators:[]})}else _=this.buildSelectQuery({table:(0,l.oG)(n,y),fields:{},fieldsFlat:S.map(({field:e})=>({path:[],field:(0,i.is)(e,u.V)?(0,l.ug)(e,y):e})),joins:k,where:v,limit:b,offset:w,orderBy:x,setOperators:[]});return{tableTsKey:o.tsName,sql:_,selection:S}}buildRelationalQueryWithoutLateralSubqueries({fullSchema:e,schema:t,tableNamesMap:r,table:n,tableConfig:o,queryConfig:c,tableAlias:y,nestedQueryRelation:m,joinOn:g}){let b,w=[],x,v,_=[],S;if(!0===c)w=Object.entries(o.columns).map(([e,t])=>({dbKey:t.name,tsKey:e,field:(0,l.ug)(t,y),relationTableTsKey:void 0,isJson:!1,selection:[]}));else{let n=Object.fromEntries(Object.entries(o.columns).map(([e,t])=>[e,(0,l.ug)(t,y)]));if(c.where){let e="function"==typeof c.where?c.where(n,(0,f.mm)()):c.where;S=e&&(0,l.yY)(e,y)}let a=[],d=[];if(c.columns){let e=!1;for(let[t,r]of Object.entries(c.columns))void 0!==r&&t in o.columns&&(e||!0!==r||(e=!0),d.push(t));d.length>0&&(d=e?d.filter(e=>c.columns?.[e]===!0):Object.keys(o.columns).filter(e=>!d.includes(e)))}else d=Object.keys(o.columns);for(let e of d){let t=o.columns[e];a.push({tsKey:e,value:t})}let m=[];if(c.with&&(m=Object.entries(c.with).filter(e=>!!e[1]).map(([e,t])=>({tsKey:e,queryConfig:t,relation:o.relations[e]}))),c.extras)for(let[e,t]of Object.entries("function"==typeof c.extras?c.extras(n,{sql:s.ll}):c.extras))a.push({tsKey:e,value:(0,l.Hs)(t,y)});for(let{tsKey:e,value:t}of a)w.push({dbKey:(0,i.is)(t,s.Xs.Aliased)?t.fieldAlias:o.columns[e].name,tsKey:e,field:(0,i.is)(t,u.V)?(0,l.ug)(t,y):t,relationTableTsKey:void 0,isJson:!1,selection:[]});let g="function"==typeof c.orderBy?c.orderBy(n,(0,f.rl)()):c.orderBy??[];for(let{tsKey:n,queryConfig:a,relation:o}of(Array.isArray(g)||(g=[g]),_=g.map(e=>(0,i.is)(e,u.V)?(0,l.ug)(e,y):(0,l.yY)(e,y)),x=c.limit,v=c.offset,m)){let c=(0,f.W0)(t,r,o),u=r[(0,p.Lf)(o.referencedTable)],d=`${y}_${n}`,m=(0,h.Uo)(...c.fields.map((e,t)=>(0,h.eq)((0,l.ug)(c.references[t],d),(0,l.ug)(e,y)))),g=this.buildRelationalQueryWithoutLateralSubqueries({fullSchema:e,schema:t,tableNamesMap:r,table:e[u],tableConfig:t[u],queryConfig:(0,i.is)(o,f.pD)?!0===a?{limit:1}:{...a,limit:1}:a,tableAlias:d,joinOn:m,nestedQueryRelation:o}),b=(0,s.ll)`(${g.sql})`;(0,i.is)(o,f.iv)&&(b=(0,s.ll)`coalesce(${b}, json_array())`);let x=b.as(n);w.push({dbKey:n,tsKey:n,field:x,relationTableTsKey:u,isJson:!0,selection:g.selection})}}if(0===w.length)throw new d.n({message:`No fields selected for table "${o.tsName}" ("${y}"). You need to have at least one item in "columns", "with" or "extras". If you need to select all columns, omit the "columns" key or set it to undefined.`});if(S=(0,h.Uo)(g,S),m){let e=(0,s.ll)`json_array(${s.ll.join(w.map(({field:e})=>(0,i.is)(e,E)?s.ll.identifier(this.casing.getColumnCasing(e)):(0,i.is)(e,s.Xs.Aliased)?e.sql:e),(0,s.ll)`, `)})`;(0,i.is)(m,f.iv)&&(e=(0,s.ll)`json_arrayagg(${e})`);let t=[{dbKey:"data",tsKey:"data",field:e,isJson:!0,relationTableTsKey:o.tsName,selection:w}];void 0!==x||void 0!==v||_.length>0?(b=this.buildSelectQuery({table:(0,l.oG)(n,y),fields:{},fieldsFlat:[{path:[],field:s.ll.raw("*")},..._.length>0?[{path:[],field:(0,s.ll)`row_number() over (order by ${s.ll.join(_,(0,s.ll)`, `)})`}]:[]],where:S,limit:x,offset:v,setOperators:[]}),S=void 0,x=void 0,v=void 0,_=void 0):b=(0,l.oG)(n,y),b=this.buildSelectQuery({table:(0,i.is)(b,ti)?b:new a.n(b,{},y),fields:{},fieldsFlat:t.map(({field:e})=>({path:[],field:(0,i.is)(e,u.V)?(0,l.ug)(e,y):e})),where:S,limit:x,offset:v,orderBy:_,setOperators:[]})}else b=this.buildSelectQuery({table:(0,l.oG)(n,y),fields:{},fieldsFlat:w.map(({field:e})=>({path:[],field:(0,i.is)(e,u.V)?(0,l.ug)(e,y):e})),where:S,limit:x,offset:v,orderBy:_,setOperators:[]});return{tableTsKey:o.tsName,sql:b,selection:w}}}var to=r(41879),tl=r(49197);function tc(e){return(0,i.is)(e,ti)?[`${e[p.XI.Symbol.BaseName]}`]:(0,i.is)(e,a.n)?e._.usedTables??[]:(0,i.is)(e,s.Xs)?e.usedTables??[]:[]}function tu(e){return e.map(e=>"object"==typeof e?e.config.name:e)}function td(e){return Array.isArray(e)?e:[e]}class tf{static [i.i]="MySqlSelectBuilder";fields;session;dialect;withList=[];distinct;constructor(e){this.fields=e.fields,this.session=e.session,this.dialect=e.dialect,e.withList&&(this.withList=e.withList),this.distinct=e.distinct}from(e,t){let r,n=!!this.fields;r=this.fields?this.fields:(0,i.is)(e,a.n)?Object.fromEntries(Object.keys(e._.selectedFields).map(t=>[t,e[t]])):(0,i.is)(e,ts)?e[m.n].selectedFields:(0,i.is)(e,s.Xs)?{}:(0,y.YD)(e);let o=[],l=[],c=[];return(0,i.is)(e,ti)&&t&&"string"!=typeof t&&(t.useIndex&&(o=tu(td(t.useIndex))),t.forceIndex&&(l=tu(td(t.forceIndex))),t.ignoreIndex&&(c=tu(td(t.ignoreIndex)))),new tp({table:e,fields:r,isPartialSelect:n,session:this.session,dialect:this.dialect,withList:this.withList,distinct:this.distinct,useIndex:o,forceIndex:l,ignoreIndex:c})}}class th extends to.O{static [i.i]="MySqlSelectQueryBuilder";_;config;joinsNotNullableMap;tableName;isPartialSelect;session;dialect;cacheConfig=void 0;usedTables=new Set;constructor({table:e,fields:t,isPartialSelect:r,session:i,dialect:n,withList:s,distinct:a,useIndex:o,forceIndex:l,ignoreIndex:c}){for(let u of(super(),this.config={withList:s,table:e,fields:{...t},distinct:a,setOperators:[],useIndex:o,forceIndex:l,ignoreIndex:c},this.isPartialSelect=r,this.session=i,this.dialect=n,this._={selectedFields:t,config:this.config},this.tableName=(0,y.zN)(e),this.joinsNotNullableMap="string"==typeof this.tableName?{[this.tableName]:!0}:{},tc(e)))this.usedTables.add(u)}getUsedTables(){return[...this.usedTables]}createJoin(e,t){return(r,o,l)=>{let c="cross"===e,u=c?void 0:o,d=c?o:l,f=this.tableName,h=(0,y.zN)(r);for(let e of tc(r))this.usedTables.add(e);if("string"==typeof h&&this.config.joins?.some(e=>e.alias===h))throw Error(`Alias "${h}" is already used in this query`);if(!this.isPartialSelect&&(1===Object.keys(this.joinsNotNullableMap).length&&"string"==typeof f&&(this.config.fields={[f]:this.config.fields}),"string"==typeof h&&!(0,i.is)(r,s.Xs))){let e=(0,i.is)(r,a.n)?r._.selectedFields:(0,i.is)(r,s.Ss)?r[m.n].selectedFields:r[p.XI.Symbol.Columns];this.config.fields[h]=e}"function"==typeof u&&(u=u(new Proxy(this.config.fields,new n.b({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.joins||(this.config.joins=[]);let g=[],b=[],w=[];if((0,i.is)(r,ti)&&d&&"string"!=typeof d&&(d.useIndex&&(g=tu(td(d.useIndex))),d.forceIndex&&(b=tu(td(d.forceIndex))),d.ignoreIndex&&(w=tu(td(d.ignoreIndex)))),this.config.joins.push({on:u,table:r,joinType:e,alias:h,useIndex:g,forceIndex:b,ignoreIndex:w,lateral:t}),"string"==typeof h)switch(e){case"left":this.joinsNotNullableMap[h]=!1;break;case"right":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[h]=!0;break;case"cross":case"inner":this.joinsNotNullableMap[h]=!0}return this}}leftJoin=this.createJoin("left",!1);leftJoinLateral=this.createJoin("left",!0);rightJoin=this.createJoin("right",!1);innerJoin=this.createJoin("inner",!1);innerJoinLateral=this.createJoin("inner",!0);crossJoin=this.createJoin("cross",!1);crossJoinLateral=this.createJoin("cross",!0);createSetOperator(e,t){return r=>{let i="function"==typeof r?r(tm()):r;if(!(0,y.DV)(this.getSelectedFields(),i.getSelectedFields()))throw Error("Set operator error (union / intersect / except): selected fields are not the same or are in a different order");return this.config.setOperators.push({type:e,isAll:t,rightSelect:i}),this}}union=this.createSetOperator("union",!1);unionAll=this.createSetOperator("union",!0);intersect=this.createSetOperator("intersect",!1);intersectAll=this.createSetOperator("intersect",!0);except=this.createSetOperator("except",!1);exceptAll=this.createSetOperator("except",!0);addSetOperators(e){return this.config.setOperators.push(...e),this}where(e){return"function"==typeof e&&(e=e(new Proxy(this.config.fields,new n.b({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.where=e,this}having(e){return"function"==typeof e&&(e=e(new Proxy(this.config.fields,new n.b({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.having=e,this}groupBy(...e){if("function"==typeof e[0]){let t=e[0](new Proxy(this.config.fields,new n.b({sqlAliasedBehavior:"alias",sqlBehavior:"sql"})));this.config.groupBy=Array.isArray(t)?t:[t]}else this.config.groupBy=e;return this}orderBy(...e){if("function"==typeof e[0]){let t=e[0](new Proxy(this.config.fields,new n.b({sqlAliasedBehavior:"alias",sqlBehavior:"sql"}))),r=Array.isArray(t)?t:[t];this.config.setOperators.length>0?this.config.setOperators.at(-1).orderBy=r:this.config.orderBy=r}else this.config.setOperators.length>0?this.config.setOperators.at(-1).orderBy=e:this.config.orderBy=e;return this}limit(e){return this.config.setOperators.length>0?this.config.setOperators.at(-1).limit=e:this.config.limit=e,this}offset(e){return this.config.setOperators.length>0?this.config.setOperators.at(-1).offset=e:this.config.offset=e,this}for(e,t={}){return this.config.lockingClause={strength:e,config:t},this}getSQL(){return this.dialect.buildSelectQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}as(e){let t=[];if(t.push(...tc(this.config.table)),this.config.joins)for(let e of this.config.joins)t.push(...tc(e.table));return new Proxy(new a.n(this.getSQL(),this.config.fields,e,!1,[...new Set(t)]),new n.b({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"}))}getSelectedFields(){return new Proxy(this.config.fields,new n.b({alias:this.tableName,sqlAliasedBehavior:"alias",sqlBehavior:"error"}))}$dynamic(){return this}$withCache(e){return this.cacheConfig=void 0===e?{config:{},enable:!0,autoInvalidate:!0}:!1===e?{enable:!1}:{enable:!0,autoInvalidate:!0,...e},this}}class tp extends th{static [i.i]="MySqlSelect";prepare(){if(!this.session)throw Error("Cannot execute a query on a query builder. Please use a database instance instead.");let e=(0,y.He)(this.config.fields),t=this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),e,void 0,void 0,void 0,{type:"select",tables:[...this.usedTables]},this.cacheConfig);return t.joinsNotNullableMap=this.joinsNotNullableMap,t}execute=e=>this.prepare().execute(e);createIterator=()=>{let e=this;return async function*(t){yield*e.prepare().iterator(t)}};iterator=this.createIterator()}function ty(e,t){return(r,i,...n)=>{let s=[i,...n].map(r=>({type:e,isAll:t,rightSelect:r}));for(let e of s)if(!(0,y.DV)(r.getSelectedFields(),e.rightSelect.getSelectedFields()))throw Error("Set operator error (union / intersect / except): selected fields are not the same or are in a different order");return r.addSetOperators(s)}}(0,y.XJ)(tp,[tl.k]);let tm=()=>({union:tg,unionAll:tb,intersect:tw,intersectAll:tx,except:tv,exceptAll:t_}),tg=ty("union",!1),tb=ty("union",!0),tw=ty("intersect",!1),tx=ty("intersect",!0),tv=ty("except",!1),t_=ty("except",!0);class tS{static [i.i]="MySqlQueryBuilder";dialect;dialectConfig;constructor(e){this.dialect=(0,i.is)(e,ta)?e:void 0,this.dialectConfig=(0,i.is)(e,ta)?void 0:e}$with=(e,t)=>{let r=this;return{as:i=>("function"==typeof i&&(i=i(r)),new Proxy(new a.J(i.getSQL(),t??("getSelectedFields"in i?i.getSelectedFields()??{}:{}),e,!0),new n.b({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"})))}};with(...e){let t=this;return{select:function(r){return new tf({fields:r??void 0,session:void 0,dialect:t.getDialect(),withList:e})},selectDistinct:function(r){return new tf({fields:r??void 0,session:void 0,dialect:t.getDialect(),withList:e,distinct:!0})}}}select(e){return new tf({fields:e??void 0,session:void 0,dialect:this.getDialect()})}selectDistinct(e){return new tf({fields:e??void 0,session:void 0,dialect:this.getDialect(),distinct:!0})}getDialect(){return this.dialect||(this.dialect=new ta(this.dialectConfig)),this.dialect}}class tk{constructor(e,t,r,i){this.table=e,this.session=t,this.dialect=r,this.withList=i}static [i.i]="MySqlUpdateBuilder";set(e){return new tA(this.table,(0,y.q)(this.table,e),this.session,this.dialect,this.withList)}}class tA extends tl.k{constructor(e,t,r,i,n){super(),this.session=r,this.dialect=i,this.config={set:t,table:e,withList:n}}static [i.i]="MySqlUpdate";config;cacheConfig;where(e){return this.config.where=e,this}orderBy(...e){if("function"==typeof e[0]){let t=e[0](new Proxy(this.config.table[p.XI.Symbol.Columns],new n.b({sqlAliasedBehavior:"alias",sqlBehavior:"sql"}))),r=Array.isArray(t)?t:[t];this.config.orderBy=r}else this.config.orderBy=e;return this}limit(e){return this.config.limit=e,this}getSQL(){return this.dialect.buildUpdateQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}prepare(){return this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),void 0,void 0,void 0,this.config.returning,{type:"insert",tables:tc(this.config.table)},this.cacheConfig)}execute=e=>this.prepare().execute(e);createIterator=()=>{let e=this;return async function*(t){yield*e.prepare().iterator(t)}};iterator=this.createIterator();$dynamic(){return this}}class tE extends tl.k{constructor(e,t,r,i){super(),this.table=e,this.session=t,this.dialect=r,this.config={table:e,withList:i}}static [i.i]="MySqlDelete";config;where(e){return this.config.where=e,this}orderBy(...e){if("function"==typeof e[0]){let t=e[0](new Proxy(this.config.table[p.XI.Symbol.Columns],new n.b({sqlAliasedBehavior:"alias",sqlBehavior:"sql"}))),r=Array.isArray(t)?t:[t];this.config.orderBy=r}else this.config.orderBy=e;return this}limit(e){return this.config.limit=e,this}getSQL(){return this.dialect.buildDeleteQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}prepare(){return this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()),this.config.returning,void 0,void 0,void 0,{type:"delete",tables:tc(this.config.table)})}execute=e=>this.prepare().execute(e);createIterator=()=>{let e=this;return async function*(t){yield*e.prepare().iterator(t)}};iterator=this.createIterator();$dynamic(){return this}}class tT{constructor(e,t,r){this.table=e,this.session=t,this.dialect=r}static [i.i]="MySqlInsertBuilder";shouldIgnore=!1;ignore(){return this.shouldIgnore=!0,this}values(e){if(0===(e=Array.isArray(e)?e:[e]).length)throw Error("values() must be called with at least one value");let t=e.map(e=>{let t={},r=this.table[p.XI.Symbol.Columns];for(let n of Object.keys(e)){let a=e[n];t[n]=(0,i.is)(a,s.Xs)?a:new s.Iw(a,r[n])}return t});return new tC(this.table,t,this.shouldIgnore,this.session,this.dialect)}select(e){let t="function"==typeof e?e(new tS):e;if(!(0,i.is)(t,s.Xs)&&!(0,y.DV)(this.table[p.e],t._.selectedFields))throw Error("Insert select error: selected fields are not the same or are in a different order compared to the table definition");return new tC(this.table,t,this.shouldIgnore,this.session,this.dialect,!0)}}class tC extends tl.k{constructor(e,t,r,i,n,s){super(),this.session=i,this.dialect=n,this.config={table:e,values:t,select:s,ignore:r}}static [i.i]="MySqlInsert";config;cacheConfig;onDuplicateKeyUpdate(e){let t=this.dialect.buildUpdateSet(this.config.table,(0,y.q)(this.config.table,e.set));return this.config.onConflict=(0,s.ll)`update ${t}`,this}$returningId(){let e=[];for(let[t,r]of Object.entries(this.config.table[p.XI.Symbol.Columns]))r.primary&&e.push({field:r,path:[t]});return this.config.returning=e,this}getSQL(){return this.dialect.buildInsertQuery(this.config).sql}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}prepare(){let{sql:e,generatedIds:t}=this.dialect.buildInsertQuery(this.config);return this.session.prepareQuery(this.dialect.sqlToQuery(e),void 0,void 0,t,this.config.returning,{type:"insert",tables:tc(this.config.table)},this.cacheConfig)}execute=e=>this.prepare().execute(e);createIterator=()=>{let e=this;return async function*(t){yield*e.prepare().iterator(t)}};iterator=this.createIterator();$dynamic(){return this}}class t${constructor(e,t,r,i,n,s,a,o){this.fullSchema=e,this.schema=t,this.tableNamesMap=r,this.table=i,this.tableConfig=n,this.dialect=s,this.session=a,this.mode=o}static [i.i]="MySqlRelationalQueryBuilder";findMany(e){return new tO(this.fullSchema,this.schema,this.tableNamesMap,this.table,this.tableConfig,this.dialect,this.session,e||{},"many",this.mode)}findFirst(e){return new tO(this.fullSchema,this.schema,this.tableNamesMap,this.table,this.tableConfig,this.dialect,this.session,e?{...e,limit:1}:{limit:1},"first",this.mode)}}class tO extends tl.k{constructor(e,t,r,i,n,s,a,o,l,c){super(),this.fullSchema=e,this.schema=t,this.tableNamesMap=r,this.table=i,this.tableConfig=n,this.dialect=s,this.session=a,this.config=o,this.queryMode=l,this.mode=c}static [i.i]="MySqlRelationalQuery";prepare(){let{query:e,builtQuery:t}=this._toSQL();return this.session.prepareQuery(t,void 0,t=>{let r=t.map(t=>(0,f.I$)(this.schema,this.tableConfig,t,e.selection));return"first"===this.queryMode?r[0]:r})}_getQuery(){return"planetscale"===this.mode?this.dialect.buildRelationalQueryWithoutLateralSubqueries({fullSchema:this.fullSchema,schema:this.schema,tableNamesMap:this.tableNamesMap,table:this.table,tableConfig:this.tableConfig,queryConfig:this.config,tableAlias:this.tableConfig.tsName}):this.dialect.buildRelationalQuery({fullSchema:this.fullSchema,schema:this.schema,tableNamesMap:this.tableNamesMap,table:this.table,tableConfig:this.tableConfig,queryConfig:this.config,tableAlias:this.tableConfig.tsName})}_toSQL(){let e=this._getQuery();return{builtQuery:this.dialect.sqlToQuery(e.sql),query:e}}getSQL(){return this._getQuery().sql}toSQL(){return this._toSQL().builtQuery}execute(){return this.prepare().execute()}}class tR{constructor(e,t,r,i){if(this.dialect=e,this.session=t,this.mode=i,this._=r?{schema:r.schema,fullSchema:r.fullSchema,tableNamesMap:r.tableNamesMap}:{schema:void 0,fullSchema:{},tableNamesMap:{}},this.query={},this._.schema)for(let[i,n]of Object.entries(this._.schema))this.query[i]=new t$(r.fullSchema,this._.schema,this._.tableNamesMap,r.fullSchema[i],n,e,t,this.mode);this.$cache={invalidate:async e=>{}}}static [i.i]="MySqlDatabase";query;$with=(e,t)=>{let r=this;return{as:i=>("function"==typeof i&&(i=i(new tS(r.dialect))),new Proxy(new a.J(i.getSQL(),t??("getSelectedFields"in i?i.getSelectedFields()??{}:{}),e,!0),new n.b({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"})))}};$count(e,t){return new o({source:e,filters:t,session:this.session})}$cache;with(...e){let t=this;return{select:function(r){return new tf({fields:r??void 0,session:t.session,dialect:t.dialect,withList:e})},selectDistinct:function(r){return new tf({fields:r??void 0,session:t.session,dialect:t.dialect,withList:e,distinct:!0})},update:function(r){return new tk(r,t.session,t.dialect,e)},delete:function(r){return new tE(r,t.session,t.dialect,e)}}}select(e){return new tf({fields:e??void 0,session:this.session,dialect:this.dialect})}selectDistinct(e){return new tf({fields:e??void 0,session:this.session,dialect:this.dialect,distinct:!0})}update(e){return new tk(e,this.session,this.dialect)}insert(e){return new tT(e,this.session,this.dialect)}delete(e){return new tE(e,this.session,this.dialect)}execute(e){return this.session.execute("string"==typeof e?s.ll.raw(e):e.getSQL())}transaction(e,t){return this.session.transaction(e,t)}}var tI=r(58779);class tP{static [i.i]="SQLiteForeignKeyBuilder";reference;_onUpdate;_onDelete;constructor(e,t){this.reference=()=>{let{name:t,columns:r,foreignColumns:i}=e();return{name:t,columns:r,foreignTable:i[0].table,foreignColumns:i}},t&&(this._onUpdate=t.onUpdate,this._onDelete=t.onDelete)}onUpdate(e){return this._onUpdate=e,this}onDelete(e){return this._onDelete=e,this}build(e){return new tj(e,this)}}class tj{constructor(e,t){this.table=e,this.reference=t.reference,this.onUpdate=t._onUpdate,this.onDelete=t._onDelete}static [i.i]="SQLiteForeignKey";reference;onUpdate;onDelete;getName(){let{name:e,columns:t,foreignColumns:r}=this.reference(),i=t.map(e=>e.name),n=r.map(e=>e.name),s=[this.table[b.E],...i,r[0].table[b.E],...n];return e??`${s.join("_")}_fk`}}function tU(e,t){return`${e[b.E]}_${t.join("_")}_unique`}class tq{constructor(e,t){this.name=t,this.columns=e}static [i.i]=null;columns;build(e){return new tL(e,this.columns,this.name)}}class tD{static [i.i]=null;name;constructor(e){this.name=e}on(...e){return new tq(e,this.name)}}class tL{constructor(e,t,r){this.table=e,this.columns=t,this.name=r??tU(this.table,this.columns.map(e=>e.name))}static [i.i]=null;columns;name;getName(){return this.name}}class tN extends g.Q{static [i.i]="SQLiteColumnBuilder";foreignKeyConfigs=[];references(e,t={}){return this.foreignKeyConfigs.push({ref:e,actions:t}),this}unique(e){return this.config.isUnique=!0,this.config.uniqueName=e,this}generatedAlwaysAs(e,t){return this.config.generated={as:e,type:"always",mode:t?.mode??"virtual"},this}buildForeignKeys(e,t){return this.foreignKeyConfigs.map(({ref:r,actions:i})=>((r,i)=>{let n=new tP(()=>({columns:[e],foreignColumns:[r()]}));return i.onUpdate&&n.onUpdate(i.onUpdate),i.onDelete&&n.onDelete(i.onDelete),n.build(t)})(r,i))}}class tM extends u.V{constructor(e,t){t.uniqueName||(t.uniqueName=tU(e,[t.name])),super(e,t),this.table=e}static [i.i]="SQLiteColumn"}class tB extends tN{static [i.i]="SQLiteBigIntBuilder";constructor(e){super(e,"bigint","SQLiteBigInt")}build(e){return new tH(e,this.config)}}class tH extends tM{static [i.i]="SQLiteBigInt";getSQLType(){return"blob"}mapFromDriverValue(e){return Buffer.isBuffer(e)?BigInt(e.toString()):e instanceof ArrayBuffer?BigInt(new TextDecoder().decode(e)):BigInt(String.fromCodePoint(...e))}mapToDriverValue(e){return Buffer.from(e.toString())}}class tK extends tN{static [i.i]="SQLiteBlobJsonBuilder";constructor(e){super(e,"json","SQLiteBlobJson")}build(e){return new tW(e,this.config)}}class tW extends tM{static [i.i]="SQLiteBlobJson";getSQLType(){return"blob"}mapFromDriverValue(e){return Buffer.isBuffer(e)?JSON.parse(e.toString()):e instanceof ArrayBuffer?JSON.parse(new TextDecoder().decode(e)):JSON.parse(String.fromCodePoint(...e))}mapToDriverValue(e){return Buffer.from(JSON.stringify(e))}}class tQ extends tN{static [i.i]="SQLiteBlobBufferBuilder";constructor(e){super(e,"buffer","SQLiteBlobBuffer")}build(e){return new tJ(e,this.config)}}class tJ extends tM{static [i.i]="SQLiteBlobBuffer";mapFromDriverValue(e){return Buffer.isBuffer(e)?e:Buffer.from(e)}getSQLType(){return"blob"}}function tF(e,t){let{name:r,config:i}=(0,y.Ll)(e,t);return i?.mode==="json"?new tK(r):i?.mode==="bigint"?new tB(r):new tQ(r)}class tV extends tN{static [i.i]="SQLiteCustomColumnBuilder";constructor(e,t,r){super(e,"custom","SQLiteCustomColumn"),this.config.fieldConfig=t,this.config.customTypeParams=r}build(e){return new tX(e,this.config)}}class tX extends tM{static [i.i]="SQLiteCustomColumn";sqlName;mapTo;mapFrom;constructor(e,t){super(e,t),this.sqlName=t.customTypeParams.dataType(t.fieldConfig),this.mapTo=t.customTypeParams.toDriver,this.mapFrom=t.customTypeParams.fromDriver}getSQLType(){return this.sqlName}mapFromDriverValue(e){return"function"==typeof this.mapFrom?this.mapFrom(e):e}mapToDriverValue(e){return"function"==typeof this.mapTo?this.mapTo(e):e}}function tz(e){return(t,r)=>{let{name:i,config:n}=(0,y.Ll)(t,r);return new tV(i,n,e)}}class tG extends tN{static [i.i]="SQLiteBaseIntegerBuilder";constructor(e,t,r){super(e,t,r),this.config.autoIncrement=!1}primaryKey(e){return e?.autoIncrement&&(this.config.autoIncrement=!0),this.config.hasDefault=!0,super.primaryKey()}}class tY extends tM{static [i.i]="SQLiteBaseInteger";autoIncrement=this.config.autoIncrement;getSQLType(){return"integer"}}class tZ extends tG{static [i.i]="SQLiteIntegerBuilder";constructor(e){super(e,"number","SQLiteInteger")}build(e){return new t0(e,this.config)}}class t0 extends tY{static [i.i]="SQLiteInteger"}class t1 extends tG{static [i.i]="SQLiteTimestampBuilder";constructor(e,t){super(e,"date","SQLiteTimestamp"),this.config.mode=t}defaultNow(){return this.default((0,s.ll)`(cast((julianday('now') - 2440587.5)*86400000 as integer))`)}build(e){return new t2(e,this.config)}}class t2 extends tY{static [i.i]="SQLiteTimestamp";mode=this.config.mode;mapFromDriverValue(e){return new Date("timestamp"===this.config.mode?1e3*e:e)}mapToDriverValue(e){let t=e.getTime();return"timestamp"===this.config.mode?Math.floor(t/1e3):t}}class t5 extends tG{static [i.i]="SQLiteBooleanBuilder";constructor(e,t){super(e,"boolean","SQLiteBoolean"),this.config.mode=t}build(e){return new t6(e,this.config)}}class t6 extends tY{static [i.i]="SQLiteBoolean";mode=this.config.mode;mapFromDriverValue(e){return 1===Number(e)}mapToDriverValue(e){return+!!e}}function t3(e,t){let{name:r,config:i}=(0,y.Ll)(e,t);return i?.mode==="timestamp"||i?.mode==="timestamp_ms"?new t1(r,i.mode):i?.mode==="boolean"?new t5(r,i.mode):new tZ(r)}class t8 extends tN{static [i.i]="SQLiteNumericBuilder";constructor(e){super(e,"string","SQLiteNumeric")}build(e){return new t4(e,this.config)}}class t4 extends tM{static [i.i]="SQLiteNumeric";mapFromDriverValue(e){return"string"==typeof e?e:String(e)}getSQLType(){return"numeric"}}class t9 extends tN{static [i.i]="SQLiteNumericNumberBuilder";constructor(e){super(e,"number","SQLiteNumericNumber")}build(e){return new t7(e,this.config)}}class t7 extends tM{static [i.i]="SQLiteNumericNumber";mapFromDriverValue(e){return"number"==typeof e?e:Number(e)}mapToDriverValue=String;getSQLType(){return"numeric"}}class re extends tN{static [i.i]="SQLiteNumericBigIntBuilder";constructor(e){super(e,"bigint","SQLiteNumericBigInt")}build(e){return new rt(e,this.config)}}class rt extends tM{static [i.i]="SQLiteNumericBigInt";mapFromDriverValue=BigInt;mapToDriverValue=String;getSQLType(){return"numeric"}}function rr(e,t){let{name:r,config:i}=(0,y.Ll)(e,t),n=i?.mode;return"number"===n?new t9(r):"bigint"===n?new re(r):new t8(r)}class ri extends tN{static [i.i]="SQLiteRealBuilder";constructor(e){super(e,"number","SQLiteReal")}build(e){return new rn(e,this.config)}}class rn extends tM{static [i.i]="SQLiteReal";getSQLType(){return"real"}}function rs(e){return new ri(e??"")}class ra extends tN{static [i.i]="SQLiteTextBuilder";constructor(e,t){super(e,"string","SQLiteText"),this.config.enumValues=t.enum,this.config.length=t.length}build(e){return new ro(e,this.config)}}class ro extends tM{static [i.i]="SQLiteText";enumValues=this.config.enumValues;length=this.config.length;constructor(e,t){super(e,t)}getSQLType(){return`text${this.config.length?`(${this.config.length})`:""}`}}class rl extends tN{static [i.i]="SQLiteTextJsonBuilder";constructor(e){super(e,"json","SQLiteTextJson")}build(e){return new rc(e,this.config)}}class rc extends tM{static [i.i]="SQLiteTextJson";getSQLType(){return"text"}mapFromDriverValue(e){return JSON.parse(e)}mapToDriverValue(e){return JSON.stringify(e)}}function ru(e,t={}){let{name:r,config:i}=(0,y.Ll)(e,t);return"json"===i.mode?new rl(r):new ra(r,i)}let rd=Symbol.for("drizzle:SQLiteInlineForeignKeys");class rf extends p.XI{static [i.i]="SQLiteTable";static Symbol=Object.assign({},p.XI.Symbol,{InlineForeignKeys:rd});[p.XI.Symbol.Columns];[rd]=[];[p.XI.Symbol.ExtraConfigBuilder]=void 0}let rh=(e,t,r)=>(function(e,t,r,i,n=e){let s=new rf(e,void 0,n),a=Object.fromEntries(Object.entries("function"==typeof t?t({blob:tF,customType:tz,integer:t3,numeric:rr,real:rs,text:ru}):t).map(([e,t])=>{t.setName(e);let r=t.build(s);return s[rd].push(...t.buildForeignKeys(r,s)),[e,r]})),o=Object.assign(s,a);return o[p.XI.Symbol.Columns]=a,o[p.XI.Symbol.ExtraConfigColumns]=a,r&&(o[rf.Symbol.ExtraConfigBuilder]=r),o})(e,t,r);class rp extends s.Ss{static [i.i]="SQLiteViewBase"}class ry{static [i.i]="SQLiteDialect";casing;constructor(e){this.casing=new c.Yn(e?.casing)}escapeName(e){return`"${e}"`}escapeParam(e){return"?"}escapeString(e){return`'${e.replace(/'/g,"''")}'`}buildWithCTE(e){if(!e?.length)return;let t=[(0,s.ll)`with `];for(let[r,i]of e.entries())t.push((0,s.ll)`${s.ll.identifier(i._.alias)} as (${i._.sql})`),r<e.length-1&&t.push((0,s.ll)`, `);return t.push((0,s.ll)` `),s.ll.join(t)}buildDeleteQuery({table:e,where:t,returning:r,withList:i,limit:n,orderBy:a}){let o=this.buildWithCTE(i),l=r?(0,s.ll)` returning ${this.buildSelection(r,{isSingleTable:!0})}`:void 0,c=t?(0,s.ll)` where ${t}`:void 0,u=this.buildOrderBy(a),d=this.buildLimit(n);return(0,s.ll)`${o}delete from ${e}${c}${l}${u}${d}`}buildUpdateSet(e,t){let r=e[p.XI.Symbol.Columns],i=Object.keys(r).filter(e=>void 0!==t[e]||r[e]?.onUpdateFn!==void 0),n=i.length;return s.ll.join(i.flatMap((e,i)=>{let a=r[e],o=t[e]??s.ll.param(a.onUpdateFn(),a),l=(0,s.ll)`${s.ll.identifier(this.casing.getColumnCasing(a))} = ${o}`;return i<n-1?[l,s.ll.raw(", ")]:[l]}))}buildUpdateQuery({table:e,set:t,where:r,returning:i,withList:n,joins:a,from:o,limit:l,orderBy:c}){let u=this.buildWithCTE(n),d=this.buildUpdateSet(e,t),f=o&&s.ll.join([s.ll.raw(" from "),this.buildFromTable(o)]),h=this.buildJoins(a),p=i?(0,s.ll)` returning ${this.buildSelection(i,{isSingleTable:!0})}`:void 0,y=r?(0,s.ll)` where ${r}`:void 0,m=this.buildOrderBy(c),g=this.buildLimit(l);return(0,s.ll)`${u}update ${e} set ${d}${f}${h}${y}${p}${m}${g}`}buildSelection(e,{isSingleTable:t=!1}={}){let r=e.length,n=e.flatMap(({field:e},n)=>{let a=[];if((0,i.is)(e,s.Xs.Aliased)&&e.isSelectionField)a.push(s.ll.identifier(e.fieldAlias));else if((0,i.is)(e,s.Xs.Aliased)||(0,i.is)(e,s.Xs)){let r=(0,i.is)(e,s.Xs.Aliased)?e.sql:e;t?a.push(new s.Xs(r.queryChunks.map(e=>(0,i.is)(e,u.V)?s.ll.identifier(this.casing.getColumnCasing(e)):e))):a.push(r),(0,i.is)(e,s.Xs.Aliased)&&a.push((0,s.ll)` as ${s.ll.identifier(e.fieldAlias)}`)}else if((0,i.is)(e,u.V)){let r=e.table[p.XI.Symbol.Name];"SQLiteNumericBigInt"===e.columnType?t?a.push((0,s.ll)`cast(${s.ll.identifier(this.casing.getColumnCasing(e))} as text)`):a.push((0,s.ll)`cast(${s.ll.identifier(r)}.${s.ll.identifier(this.casing.getColumnCasing(e))} as text)`):t?a.push(s.ll.identifier(this.casing.getColumnCasing(e))):a.push((0,s.ll)`${s.ll.identifier(r)}.${s.ll.identifier(this.casing.getColumnCasing(e))}`)}return n<r-1&&a.push((0,s.ll)`, `),a});return s.ll.join(n)}buildJoins(e){if(!e||0===e.length)return;let t=[];if(e)for(let[r,n]of e.entries()){0===r&&t.push((0,s.ll)` `);let a=n.table,o=n.on?(0,s.ll)` on ${n.on}`:void 0;if((0,i.is)(a,rf)){let e=a[rf.Symbol.Name],r=a[rf.Symbol.Schema],i=a[rf.Symbol.OriginalName],l=e===i?void 0:n.alias;t.push((0,s.ll)`${s.ll.raw(n.joinType)} join ${r?(0,s.ll)`${s.ll.identifier(r)}.`:void 0}${s.ll.identifier(i)}${l&&(0,s.ll)` ${s.ll.identifier(l)}`}${o}`)}else t.push((0,s.ll)`${s.ll.raw(n.joinType)} join ${a}${o}`);r<e.length-1&&t.push((0,s.ll)` `)}return s.ll.join(t)}buildLimit(e){return"object"==typeof e||"number"==typeof e&&e>=0?(0,s.ll)` limit ${e}`:void 0}buildOrderBy(e){let t=[];if(e)for(let[r,i]of e.entries())t.push(i),r<e.length-1&&t.push((0,s.ll)`, `);return t.length>0?(0,s.ll)` order by ${s.ll.join(t)}`:void 0}buildFromTable(e){return(0,i.is)(e,p.XI)&&e[p.XI.Symbol.IsAlias]?(0,s.ll)`${(0,s.ll)`${s.ll.identifier(e[p.XI.Symbol.Schema]??"")}.`.if(e[p.XI.Symbol.Schema])}${s.ll.identifier(e[p.XI.Symbol.OriginalName])} ${s.ll.identifier(e[p.XI.Symbol.Name])}`:e}buildSelectQuery({withList:e,fields:t,fieldsFlat:r,where:n,having:o,table:l,joins:c,orderBy:d,groupBy:f,limit:h,offset:g,distinct:b,setOperators:w}){let x=r??(0,y.He)(t);for(let e of x){let t;if((0,i.is)(e.field,u.V)&&(0,p.Io)(e.field.table)!==((0,i.is)(l,a.n)?l._.alias:(0,i.is)(l,rp)?l[m.n].name:(0,i.is)(l,s.Xs)?void 0:(0,p.Io)(l))&&(t=e.field.table,!c?.some(({alias:e})=>e===(t[p.XI.Symbol.IsAlias]?(0,p.Io)(t):t[p.XI.Symbol.BaseName])))){let t=(0,p.Io)(e.field.table);throw Error(`Your "${e.path.join("->")}" field references a column "${t}"."${e.field.name}", but the table "${t}" is not part of the query! Did you forget to join it?`)}}let v=!c||0===c.length,_=this.buildWithCTE(e),S=b?(0,s.ll)` distinct`:void 0,k=this.buildSelection(x,{isSingleTable:v}),A=this.buildFromTable(l),E=this.buildJoins(c),T=n?(0,s.ll)` where ${n}`:void 0,C=o?(0,s.ll)` having ${o}`:void 0,$=[];if(f)for(let[e,t]of f.entries())$.push(t),e<f.length-1&&$.push((0,s.ll)`, `);let O=$.length>0?(0,s.ll)` group by ${s.ll.join($)}`:void 0,R=this.buildOrderBy(d),I=this.buildLimit(h),P=g?(0,s.ll)` offset ${g}`:void 0,j=(0,s.ll)`${_}select${S} ${k} from ${A}${E}${T}${O}${C}${R}${I}${P}`;return w.length>0?this.buildSetOperations(j,w):j}buildSetOperations(e,t){let[r,...i]=t;if(!r)throw Error("Cannot pass undefined values to any set operator");return 0===i.length?this.buildSetOperationQuery({leftSelect:e,setOperator:r}):this.buildSetOperations(this.buildSetOperationQuery({leftSelect:e,setOperator:r}),i)}buildSetOperationQuery({leftSelect:e,setOperator:{type:t,isAll:r,rightSelect:n,limit:a,orderBy:o,offset:l}}){let c,u=(0,s.ll)`${e.getSQL()} `,d=(0,s.ll)`${n.getSQL()}`;if(o&&o.length>0){let e=[];for(let t of o)if((0,i.is)(t,tM))e.push(s.ll.identifier(t.name));else if((0,i.is)(t,s.Xs)){for(let e=0;e<t.queryChunks.length;e++){let r=t.queryChunks[e];(0,i.is)(r,tM)&&(t.queryChunks[e]=s.ll.identifier(this.casing.getColumnCasing(r)))}e.push((0,s.ll)`${t}`)}else e.push((0,s.ll)`${t}`);c=(0,s.ll)` order by ${s.ll.join(e,(0,s.ll)`, `)}`}let f="object"==typeof a||"number"==typeof a&&a>=0?(0,s.ll)` limit ${a}`:void 0,h=s.ll.raw(`${t} ${r?"all ":""}`),p=l?(0,s.ll)` offset ${l}`:void 0;return(0,s.ll)`${u}${h}${d}${c}${f}${p}`}buildInsertQuery({table:e,values:t,onConflict:r,returning:n,withList:a,select:o}){let l=[],c=Object.entries(e[p.XI.Symbol.Columns]).filter(([e,t])=>!t.shouldDisableInsert()),u=c.map(([,e])=>s.ll.identifier(this.casing.getColumnCasing(e)));if(o)(0,i.is)(t,s.Xs)?l.push(t):l.push(t.getSQL());else for(let[e,r]of(l.push(s.ll.raw("values ")),t.entries())){let n=[];for(let[e,t]of c){let a=r[e];if(void 0===a||(0,i.is)(a,s.Iw)&&void 0===a.value){let e;if(null!==t.default&&void 0!==t.default)e=(0,i.is)(t.default,s.Xs)?t.default:s.ll.param(t.default,t);else if(void 0!==t.defaultFn){let r=t.defaultFn();e=(0,i.is)(r,s.Xs)?r:s.ll.param(r,t)}else if(t.default||void 0===t.onUpdateFn)e=(0,s.ll)`null`;else{let r=t.onUpdateFn();e=(0,i.is)(r,s.Xs)?r:s.ll.param(r,t)}n.push(e)}else n.push(a)}l.push(n),e<t.length-1&&l.push((0,s.ll)`, `)}let d=this.buildWithCTE(a),f=s.ll.join(l),h=n?(0,s.ll)` returning ${this.buildSelection(n,{isSingleTable:!0})}`:void 0,y=r?.length?s.ll.join(r):void 0;return(0,s.ll)`${d}insert into ${e} ${u} ${f}${y}${h}`}sqlToQuery(e,t){return e.toQuery({casing:this.casing,escapeName:this.escapeName,escapeParam:this.escapeParam,escapeString:this.escapeString,invokeSource:t})}buildRelationalQuery({fullSchema:e,schema:t,tableNamesMap:r,table:n,tableConfig:o,queryConfig:c,tableAlias:y,nestedQueryRelation:m,joinOn:g}){let b,w=[],x,v,_=[],S,k=[];if(!0===c)w=Object.entries(o.columns).map(([e,t])=>({dbKey:t.name,tsKey:e,field:(0,l.ug)(t,y),relationTableTsKey:void 0,isJson:!1,selection:[]}));else{let n=Object.fromEntries(Object.entries(o.columns).map(([e,t])=>[e,(0,l.ug)(t,y)]));if(c.where){let e="function"==typeof c.where?c.where(n,(0,f.mm)()):c.where;S=e&&(0,l.yY)(e,y)}let a=[],d=[];if(c.columns){let e=!1;for(let[t,r]of Object.entries(c.columns))void 0!==r&&t in o.columns&&(e||!0!==r||(e=!0),d.push(t));d.length>0&&(d=e?d.filter(e=>c.columns?.[e]===!0):Object.keys(o.columns).filter(e=>!d.includes(e)))}else d=Object.keys(o.columns);for(let e of d){let t=o.columns[e];a.push({tsKey:e,value:t})}let m=[];if(c.with&&(m=Object.entries(c.with).filter(e=>!!e[1]).map(([e,t])=>({tsKey:e,queryConfig:t,relation:o.relations[e]}))),c.extras)for(let[e,t]of Object.entries("function"==typeof c.extras?c.extras(n,{sql:s.ll}):c.extras))a.push({tsKey:e,value:(0,l.Hs)(t,y)});for(let{tsKey:e,value:t}of a)w.push({dbKey:(0,i.is)(t,s.Xs.Aliased)?t.fieldAlias:o.columns[e].name,tsKey:e,field:(0,i.is)(t,u.V)?(0,l.ug)(t,y):t,relationTableTsKey:void 0,isJson:!1,selection:[]});let g="function"==typeof c.orderBy?c.orderBy(n,(0,f.rl)()):c.orderBy??[];for(let{tsKey:n,queryConfig:a,relation:o}of(Array.isArray(g)||(g=[g]),_=g.map(e=>(0,i.is)(e,u.V)?(0,l.ug)(e,y):(0,l.yY)(e,y)),x=c.limit,v=c.offset,m)){let c=(0,f.W0)(t,r,o),u=r[(0,p.Lf)(o.referencedTable)],d=`${y}_${n}`,m=(0,h.Uo)(...c.fields.map((e,t)=>(0,h.eq)((0,l.ug)(c.references[t],d),(0,l.ug)(e,y)))),g=this.buildRelationalQuery({fullSchema:e,schema:t,tableNamesMap:r,table:e[u],tableConfig:t[u],queryConfig:(0,i.is)(o,f.pD)?!0===a?{limit:1}:{...a,limit:1}:a,tableAlias:d,joinOn:m,nestedQueryRelation:o}),b=(0,s.ll)`(${g.sql})`.as(n);w.push({dbKey:n,tsKey:n,field:b,relationTableTsKey:u,isJson:!0,selection:g.selection})}}if(0===w.length)throw new d.n({message:`No fields selected for table "${o.tsName}" ("${y}"). You need to have at least one item in "columns", "with" or "extras". If you need to select all columns, omit the "columns" key or set it to undefined.`});if(S=(0,h.Uo)(g,S),m){let e=(0,s.ll)`json_array(${s.ll.join(w.map(({field:e})=>(0,i.is)(e,tM)?s.ll.identifier(this.casing.getColumnCasing(e)):(0,i.is)(e,s.Xs.Aliased)?e.sql:e),(0,s.ll)`, `)})`;(0,i.is)(m,f.iv)&&(e=(0,s.ll)`coalesce(json_group_array(${e}), json_array())`);let t=[{dbKey:"data",tsKey:"data",field:e.as("data"),isJson:!0,relationTableTsKey:o.tsName,selection:w}];void 0!==x||void 0!==v||_.length>0?(b=this.buildSelectQuery({table:(0,l.oG)(n,y),fields:{},fieldsFlat:[{path:[],field:s.ll.raw("*")}],where:S,limit:x,offset:v,orderBy:_,setOperators:[]}),S=void 0,x=void 0,v=void 0,_=void 0):b=(0,l.oG)(n,y),b=this.buildSelectQuery({table:(0,i.is)(b,rf)?b:new a.n(b,{},y),fields:{},fieldsFlat:t.map(({field:e})=>({path:[],field:(0,i.is)(e,u.V)?(0,l.ug)(e,y):e})),joins:k,where:S,limit:x,offset:v,orderBy:_,setOperators:[]})}else b=this.buildSelectQuery({table:(0,l.oG)(n,y),fields:{},fieldsFlat:w.map(({field:e})=>({path:[],field:(0,i.is)(e,u.V)?(0,l.ug)(e,y):e})),joins:k,where:S,limit:x,offset:v,orderBy:_,setOperators:[]});return{tableTsKey:o.tsName,sql:b,selection:w}}}class rm extends ry{static [i.i]="SQLiteSyncDialect";migrate(e,t,r){let i=void 0===r||"string"==typeof r?"__drizzle_migrations":r.migrationsTable??"__drizzle_migrations",n=(0,s.ll)`
			CREATE TABLE IF NOT EXISTS ${s.ll.identifier(i)} (
				id SERIAL PRIMARY KEY,
				hash text NOT NULL,
				created_at numeric
			)
		`;t.run(n);let a=t.values((0,s.ll)`SELECT id, hash, created_at FROM ${s.ll.identifier(i)} ORDER BY created_at DESC LIMIT 1`)[0]??void 0;t.run((0,s.ll)`BEGIN`);try{for(let r of e)if(!a||Number(a[2])<r.folderMillis){for(let e of r.sql)t.run(s.ll.raw(e));t.run((0,s.ll)`INSERT INTO ${s.ll.identifier(i)} ("hash", "created_at") VALUES(${r.hash}, ${r.folderMillis})`)}t.run((0,s.ll)`COMMIT`)}catch(e){throw t.run((0,s.ll)`ROLLBACK`),e}}}class rg extends null{static [i.i]=null;async migrate(e,t,r){let i=void 0===r||"string"==typeof r?"__drizzle_migrations":r.migrationsTable??"__drizzle_migrations",n=sql`
			CREATE TABLE IF NOT EXISTS ${sql.identifier(i)} (
				id SERIAL PRIMARY KEY,
				hash text NOT NULL,
				created_at numeric
			)
		`;await t.run(n);let s=(await t.values(sql`SELECT id, hash, created_at FROM ${sql.identifier(i)} ORDER BY created_at DESC LIMIT 1`))[0]??void 0;await t.transaction(async t=>{for(let r of e)if(!s||Number(s[2])<r.folderMillis){for(let e of r.sql)await t.run(sql.raw(e));await t.run(sql`INSERT INTO ${sql.identifier(i)} ("hash", "created_at") VALUES(${r.hash}, ${r.folderMillis})`)}})}}function rb(e){return(0,i.is)(e,rf)?[`${e[p.XI.Symbol.BaseName]}`]:(0,i.is)(e,a.n)?e._.usedTables??[]:(0,i.is)(e,s.Xs)?e.usedTables??[]:[]}class rw{static [i.i]="SQLiteSelectBuilder";fields;session;dialect;withList;distinct;constructor(e){this.fields=e.fields,this.session=e.session,this.dialect=e.dialect,this.withList=e.withList,this.distinct=e.distinct}from(e){let t,r=!!this.fields;return t=this.fields?this.fields:(0,i.is)(e,a.n)?Object.fromEntries(Object.keys(e._.selectedFields).map(t=>[t,e[t]])):(0,i.is)(e,rp)?e[m.n].selectedFields:(0,i.is)(e,s.Xs)?{}:(0,y.YD)(e),new rv({table:e,fields:t,isPartialSelect:r,session:this.session,dialect:this.dialect,withList:this.withList,distinct:this.distinct})}}class rx extends to.O{static [i.i]="SQLiteSelectQueryBuilder";_;config;joinsNotNullableMap;tableName;isPartialSelect;session;dialect;cacheConfig=void 0;usedTables=new Set;constructor({table:e,fields:t,isPartialSelect:r,session:i,dialect:n,withList:s,distinct:a}){for(let o of(super(),this.config={withList:s,table:e,fields:{...t},distinct:a,setOperators:[]},this.isPartialSelect=r,this.session=i,this.dialect=n,this._={selectedFields:t,config:this.config},this.tableName=(0,y.zN)(e),this.joinsNotNullableMap="string"==typeof this.tableName?{[this.tableName]:!0}:{},rb(e)))this.usedTables.add(o)}getUsedTables(){return[...this.usedTables]}createJoin(e){return(t,r)=>{let o=this.tableName,l=(0,y.zN)(t);for(let e of rb(t))this.usedTables.add(e);if("string"==typeof l&&this.config.joins?.some(e=>e.alias===l))throw Error(`Alias "${l}" is already used in this query`);if(!this.isPartialSelect&&(1===Object.keys(this.joinsNotNullableMap).length&&"string"==typeof o&&(this.config.fields={[o]:this.config.fields}),"string"==typeof l&&!(0,i.is)(t,s.Xs))){let e=(0,i.is)(t,a.n)?t._.selectedFields:(0,i.is)(t,s.Ss)?t[m.n].selectedFields:t[p.XI.Symbol.Columns];this.config.fields[l]=e}if("function"==typeof r&&(r=r(new Proxy(this.config.fields,new n.b({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.joins||(this.config.joins=[]),this.config.joins.push({on:r,table:t,joinType:e,alias:l}),"string"==typeof l)switch(e){case"left":this.joinsNotNullableMap[l]=!1;break;case"right":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[l]=!0;break;case"cross":case"inner":this.joinsNotNullableMap[l]=!0;break;case"full":this.joinsNotNullableMap=Object.fromEntries(Object.entries(this.joinsNotNullableMap).map(([e])=>[e,!1])),this.joinsNotNullableMap[l]=!1}return this}}leftJoin=this.createJoin("left");rightJoin=this.createJoin("right");innerJoin=this.createJoin("inner");fullJoin=this.createJoin("full");crossJoin=this.createJoin("cross");createSetOperator(e,t){return r=>{let i="function"==typeof r?r(rS()):r;if(!(0,y.DV)(this.getSelectedFields(),i.getSelectedFields()))throw Error("Set operator error (union / intersect / except): selected fields are not the same or are in a different order");return this.config.setOperators.push({type:e,isAll:t,rightSelect:i}),this}}union=this.createSetOperator("union",!1);unionAll=this.createSetOperator("union",!0);intersect=this.createSetOperator("intersect",!1);except=this.createSetOperator("except",!1);addSetOperators(e){return this.config.setOperators.push(...e),this}where(e){return"function"==typeof e&&(e=e(new Proxy(this.config.fields,new n.b({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.where=e,this}having(e){return"function"==typeof e&&(e=e(new Proxy(this.config.fields,new n.b({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))),this.config.having=e,this}groupBy(...e){if("function"==typeof e[0]){let t=e[0](new Proxy(this.config.fields,new n.b({sqlAliasedBehavior:"alias",sqlBehavior:"sql"})));this.config.groupBy=Array.isArray(t)?t:[t]}else this.config.groupBy=e;return this}orderBy(...e){if("function"==typeof e[0]){let t=e[0](new Proxy(this.config.fields,new n.b({sqlAliasedBehavior:"alias",sqlBehavior:"sql"}))),r=Array.isArray(t)?t:[t];this.config.setOperators.length>0?this.config.setOperators.at(-1).orderBy=r:this.config.orderBy=r}else this.config.setOperators.length>0?this.config.setOperators.at(-1).orderBy=e:this.config.orderBy=e;return this}limit(e){return this.config.setOperators.length>0?this.config.setOperators.at(-1).limit=e:this.config.limit=e,this}offset(e){return this.config.setOperators.length>0?this.config.setOperators.at(-1).offset=e:this.config.offset=e,this}getSQL(){return this.dialect.buildSelectQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}as(e){let t=[];if(t.push(...rb(this.config.table)),this.config.joins)for(let e of this.config.joins)t.push(...rb(e.table));return new Proxy(new a.n(this.getSQL(),this.config.fields,e,!1,[...new Set(t)]),new n.b({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"}))}getSelectedFields(){return new Proxy(this.config.fields,new n.b({alias:this.tableName,sqlAliasedBehavior:"alias",sqlBehavior:"error"}))}$dynamic(){return this}}class rv extends rx{static [i.i]="SQLiteSelect";_prepare(e=!0){if(!this.session)throw Error("Cannot execute a query on a query builder. Please use a database instance instead.");let t=(0,y.He)(this.config.fields),r=this.session[e?"prepareOneTimeQuery":"prepareQuery"](this.dialect.sqlToQuery(this.getSQL()),t,"all",!0,void 0,{type:"select",tables:[...this.usedTables]},this.cacheConfig);return r.joinsNotNullableMap=this.joinsNotNullableMap,r}$withCache(e){return this.cacheConfig=void 0===e?{config:{},enable:!0,autoInvalidate:!0}:!1===e?{enable:!1}:{enable:!0,autoInvalidate:!0,...e},this}prepare(){return this._prepare(!1)}run=e=>this._prepare().run(e);all=e=>this._prepare().all(e);get=e=>this._prepare().get(e);values=e=>this._prepare().values(e);async execute(){return this.all()}}function r_(e,t){return(r,i,...n)=>{let s=[i,...n].map(r=>({type:e,isAll:t,rightSelect:r}));for(let e of s)if(!(0,y.DV)(r.getSelectedFields(),e.rightSelect.getSelectedFields()))throw Error("Set operator error (union / intersect / except): selected fields are not the same or are in a different order");return r.addSetOperators(s)}}(0,y.XJ)(rv,[tl.k]);let rS=()=>({union:rk,unionAll:rA,intersect:rE,except:rT}),rk=r_("union",!1),rA=r_("union",!0),rE=r_("intersect",!1),rT=r_("except",!1);class rC{static [i.i]="SQLiteQueryBuilder";dialect;dialectConfig;constructor(e){this.dialect=(0,i.is)(e,ry)?e:void 0,this.dialectConfig=(0,i.is)(e,ry)?void 0:e}$with=(e,t)=>{let r=this;return{as:i=>("function"==typeof i&&(i=i(r)),new Proxy(new a.J(i.getSQL(),t??("getSelectedFields"in i?i.getSelectedFields()??{}:{}),e,!0),new n.b({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"})))}};with(...e){let t=this;return{select:function(r){return new rw({fields:r??void 0,session:void 0,dialect:t.getDialect(),withList:e})},selectDistinct:function(r){return new rw({fields:r??void 0,session:void 0,dialect:t.getDialect(),withList:e,distinct:!0})}}}select(e){return new rw({fields:e??void 0,session:void 0,dialect:this.getDialect()})}selectDistinct(e){return new rw({fields:e??void 0,session:void 0,dialect:this.getDialect(),distinct:!0})}getDialect(){return this.dialect||(this.dialect=new rm(this.dialectConfig)),this.dialect}}class r${constructor(e,t,r,i){this.table=e,this.session=t,this.dialect=r,this.withList=i}static [i.i]="SQLiteUpdateBuilder";set(e){return new rO(this.table,(0,y.q)(this.table,e),this.session,this.dialect,this.withList)}}class rO extends tl.k{constructor(e,t,r,i,n){super(),this.session=r,this.dialect=i,this.config={set:t,table:e,withList:n,joins:[]}}static [i.i]="SQLiteUpdate";config;from(e){return this.config.from=e,this}createJoin(e){return(t,r)=>{let s=(0,y.zN)(t);if("string"==typeof s&&this.config.joins.some(e=>e.alias===s))throw Error(`Alias "${s}" is already used in this query`);if("function"==typeof r){let e=this.config.from?(0,i.is)(t,rf)?t[p.XI.Symbol.Columns]:(0,i.is)(t,a.n)?t._.selectedFields:(0,i.is)(t,rp)?t[m.n].selectedFields:void 0:void 0;r=r(new Proxy(this.config.table[p.XI.Symbol.Columns],new n.b({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})),e&&new Proxy(e,new n.b({sqlAliasedBehavior:"sql",sqlBehavior:"sql"})))}return this.config.joins.push({on:r,table:t,joinType:e,alias:s}),this}}leftJoin=this.createJoin("left");rightJoin=this.createJoin("right");innerJoin=this.createJoin("inner");fullJoin=this.createJoin("full");where(e){return this.config.where=e,this}orderBy(...e){if("function"==typeof e[0]){let t=e[0](new Proxy(this.config.table[p.XI.Symbol.Columns],new n.b({sqlAliasedBehavior:"alias",sqlBehavior:"sql"}))),r=Array.isArray(t)?t:[t];this.config.orderBy=r}else this.config.orderBy=e;return this}limit(e){return this.config.limit=e,this}returning(e=this.config.table[rf.Symbol.Columns]){return this.config.returning=(0,y.He)(e),this}getSQL(){return this.dialect.buildUpdateQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e=!0){return this.session[e?"prepareOneTimeQuery":"prepareQuery"](this.dialect.sqlToQuery(this.getSQL()),this.config.returning,this.config.returning?"all":"run",!0,void 0,{type:"insert",tables:rb(this.config.table)})}prepare(){return this._prepare(!1)}run=e=>this._prepare().run(e);all=e=>this._prepare().all(e);get=e=>this._prepare().get(e);values=e=>this._prepare().values(e);async execute(){return this.config.returning?this.all():this.run()}$dynamic(){return this}}class rR{constructor(e,t,r,i){this.table=e,this.session=t,this.dialect=r,this.withList=i}static [i.i]="SQLiteInsertBuilder";values(e){if(0===(e=Array.isArray(e)?e:[e]).length)throw Error("values() must be called with at least one value");let t=e.map(e=>{let t={},r=this.table[p.XI.Symbol.Columns];for(let n of Object.keys(e)){let a=e[n];t[n]=(0,i.is)(a,s.Xs)?a:new s.Iw(a,r[n])}return t});return new rI(this.table,t,this.session,this.dialect,this.withList)}select(e){let t="function"==typeof e?e(new rC):e;if(!(0,i.is)(t,s.Xs)&&!(0,y.DV)(this.table[p.e],t._.selectedFields))throw Error("Insert select error: selected fields are not the same or are in a different order compared to the table definition");return new rI(this.table,t,this.session,this.dialect,this.withList,!0)}}class rI extends tl.k{constructor(e,t,r,i,n,s){super(),this.session=r,this.dialect=i,this.config={table:e,values:t,withList:n,select:s}}static [i.i]="SQLiteInsert";config;returning(e=this.config.table[rf.Symbol.Columns]){return this.config.returning=(0,y.He)(e),this}onConflictDoNothing(e={}){if(this.config.onConflict||(this.config.onConflict=[]),void 0===e.target)this.config.onConflict.push((0,s.ll)` on conflict do nothing`);else{let t=Array.isArray(e.target)?(0,s.ll)`${e.target}`:(0,s.ll)`${[e.target]}`,r=e.where?(0,s.ll)` where ${e.where}`:(0,s.ll)``;this.config.onConflict.push((0,s.ll)` on conflict ${t} do nothing${r}`)}return this}onConflictDoUpdate(e){if(e.where&&(e.targetWhere||e.setWhere))throw Error('You cannot use both "where" and "targetWhere"/"setWhere" at the same time - "where" is deprecated, use "targetWhere" or "setWhere" instead.');this.config.onConflict||(this.config.onConflict=[]);let t=e.where?(0,s.ll)` where ${e.where}`:void 0,r=e.targetWhere?(0,s.ll)` where ${e.targetWhere}`:void 0,i=e.setWhere?(0,s.ll)` where ${e.setWhere}`:void 0,n=Array.isArray(e.target)?(0,s.ll)`${e.target}`:(0,s.ll)`${[e.target]}`,a=this.dialect.buildUpdateSet(this.config.table,(0,y.q)(this.config.table,e.set));return this.config.onConflict.push((0,s.ll)` on conflict ${n}${r} do update set ${a}${t}${i}`),this}getSQL(){return this.dialect.buildInsertQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e=!0){return this.session[e?"prepareOneTimeQuery":"prepareQuery"](this.dialect.sqlToQuery(this.getSQL()),this.config.returning,this.config.returning?"all":"run",!0,void 0,{type:"insert",tables:rb(this.config.table)})}prepare(){return this._prepare(!1)}run=e=>this._prepare().run(e);all=e=>this._prepare().all(e);get=e=>this._prepare().get(e);values=e=>this._prepare().values(e);async execute(){return this.config.returning?this.all():this.run()}$dynamic(){return this}}class rP extends tl.k{constructor(e,t,r,i){super(),this.table=e,this.session=t,this.dialect=r,this.config={table:e,withList:i}}static [i.i]="SQLiteDelete";config;where(e){return this.config.where=e,this}orderBy(...e){if("function"==typeof e[0]){let t=e[0](new Proxy(this.config.table[p.XI.Symbol.Columns],new n.b({sqlAliasedBehavior:"alias",sqlBehavior:"sql"}))),r=Array.isArray(t)?t:[t];this.config.orderBy=r}else this.config.orderBy=e;return this}limit(e){return this.config.limit=e,this}returning(e=this.table[rf.Symbol.Columns]){return this.config.returning=(0,y.He)(e),this}getSQL(){return this.dialect.buildDeleteQuery(this.config)}toSQL(){let{typings:e,...t}=this.dialect.sqlToQuery(this.getSQL());return t}_prepare(e=!0){return this.session[e?"prepareOneTimeQuery":"prepareQuery"](this.dialect.sqlToQuery(this.getSQL()),this.config.returning,this.config.returning?"all":"run",!0,void 0,{type:"delete",tables:rb(this.config.table)})}prepare(){return this._prepare(!1)}run=e=>this._prepare().run(e);all=e=>this._prepare().all(e);get=e=>this._prepare().get(e);values=e=>this._prepare().values(e);async execute(e){return this._prepare().execute(e)}$dynamic(){return this}}class rj extends s.Xs{constructor(e){super(rj.buildEmbeddedCount(e.source,e.filters).queryChunks),this.params=e,this.session=e.session,this.sql=rj.buildCount(e.source,e.filters)}sql;static [i.i]="SQLiteCountBuilderAsync";[Symbol.toStringTag]="SQLiteCountBuilderAsync";session;static buildEmbeddedCount(e,t){return(0,s.ll)`(select count(*) from ${e}${s.ll.raw(" where ").if(t)}${t})`}static buildCount(e,t){return(0,s.ll)`select count(*) from ${e}${s.ll.raw(" where ").if(t)}${t}`}then(e,t){return Promise.resolve(this.session.count(this.sql)).then(e,t)}catch(e){return this.then(void 0,e)}finally(e){return this.then(t=>(e?.(),t),t=>{throw e?.(),t})}}class rU{constructor(e,t,r,i,n,s,a,o){this.mode=e,this.fullSchema=t,this.schema=r,this.tableNamesMap=i,this.table=n,this.tableConfig=s,this.dialect=a,this.session=o}static [i.i]="SQLiteAsyncRelationalQueryBuilder";findMany(e){return"sync"===this.mode?new rD(this.fullSchema,this.schema,this.tableNamesMap,this.table,this.tableConfig,this.dialect,this.session,e||{},"many"):new rq(this.fullSchema,this.schema,this.tableNamesMap,this.table,this.tableConfig,this.dialect,this.session,e||{},"many")}findFirst(e){return"sync"===this.mode?new rD(this.fullSchema,this.schema,this.tableNamesMap,this.table,this.tableConfig,this.dialect,this.session,e?{...e,limit:1}:{limit:1},"first"):new rq(this.fullSchema,this.schema,this.tableNamesMap,this.table,this.tableConfig,this.dialect,this.session,e?{...e,limit:1}:{limit:1},"first")}}class rq extends tl.k{constructor(e,t,r,i,n,s,a,o,l){super(),this.fullSchema=e,this.schema=t,this.tableNamesMap=r,this.table=i,this.tableConfig=n,this.dialect=s,this.session=a,this.config=o,this.mode=l}static [i.i]="SQLiteAsyncRelationalQuery";mode;getSQL(){return this.dialect.buildRelationalQuery({fullSchema:this.fullSchema,schema:this.schema,tableNamesMap:this.tableNamesMap,table:this.table,tableConfig:this.tableConfig,queryConfig:this.config,tableAlias:this.tableConfig.tsName}).sql}_prepare(e=!1){let{query:t,builtQuery:r}=this._toSQL();return this.session[e?"prepareOneTimeQuery":"prepareQuery"](r,void 0,"first"===this.mode?"get":"all",!0,(e,r)=>{let i=e.map(e=>(0,f.I$)(this.schema,this.tableConfig,e,t.selection,r));return"first"===this.mode?i[0]:i})}prepare(){return this._prepare(!1)}_toSQL(){let e=this.dialect.buildRelationalQuery({fullSchema:this.fullSchema,schema:this.schema,tableNamesMap:this.tableNamesMap,table:this.table,tableConfig:this.tableConfig,queryConfig:this.config,tableAlias:this.tableConfig.tsName}),t=this.dialect.sqlToQuery(e.sql);return{query:e,builtQuery:t}}toSQL(){return this._toSQL().builtQuery}executeRaw(){return"first"===this.mode?this._prepare(!1).get():this._prepare(!1).all()}async execute(){return this.executeRaw()}}class rD extends rq{static [i.i]="SQLiteSyncRelationalQuery";sync(){return this.executeRaw()}}class rL extends tl.k{constructor(e,t,r,i,n){super(),this.execute=e,this.getSQL=t,this.dialect=i,this.mapBatchResult=n,this.config={action:r}}static [i.i]="SQLiteRaw";config;getQuery(){return{...this.dialect.sqlToQuery(this.getSQL()),method:this.config.action}}mapResult(e,t){return t?this.mapBatchResult(e):e}_prepare(){return this}isResponseInArrayMode(){return!1}}class rN{constructor(e,t,r,i){this.resultKind=e,this.dialect=t,this.session=r,this._=i?{schema:i.schema,fullSchema:i.fullSchema,tableNamesMap:i.tableNamesMap}:{schema:void 0,fullSchema:{},tableNamesMap:{}},this.query={};let n=this.query;if(this._.schema)for(let[s,a]of Object.entries(this._.schema))n[s]=new rU(e,i.fullSchema,this._.schema,this._.tableNamesMap,i.fullSchema[s],a,t,r);this.$cache={invalidate:async e=>{}}}static [i.i]="BaseSQLiteDatabase";query;$with=(e,t)=>{let r=this;return{as:i=>("function"==typeof i&&(i=i(new rC(r.dialect))),new Proxy(new a.J(i.getSQL(),t??("getSelectedFields"in i?i.getSelectedFields()??{}:{}),e,!0),new n.b({alias:e,sqlAliasedBehavior:"alias",sqlBehavior:"error"})))}};$count(e,t){return new rj({source:e,filters:t,session:this.session})}with(...e){let t=this;return{select:function(r){return new rw({fields:r??void 0,session:t.session,dialect:t.dialect,withList:e})},selectDistinct:function(r){return new rw({fields:r??void 0,session:t.session,dialect:t.dialect,withList:e,distinct:!0})},update:function(r){return new r$(r,t.session,t.dialect,e)},insert:function(r){return new rR(r,t.session,t.dialect,e)},delete:function(r){return new rP(r,t.session,t.dialect,e)}}}select(e){return new rw({fields:e??void 0,session:this.session,dialect:this.dialect})}selectDistinct(e){return new rw({fields:e??void 0,session:this.session,dialect:this.dialect,distinct:!0})}update(e){return new r$(e,this.session,this.dialect)}$cache;insert(e){return new rR(e,this.session,this.dialect)}delete(e){return new rP(e,this.session,this.dialect)}run(e){let t="string"==typeof e?s.ll.raw(e):e.getSQL();return"async"===this.resultKind?new rL(async()=>this.session.run(t),()=>t,"run",this.dialect,this.session.extractRawRunValueFromBatchResult.bind(this.session)):this.session.run(t)}all(e){let t="string"==typeof e?s.ll.raw(e):e.getSQL();return"async"===this.resultKind?new rL(async()=>this.session.all(t),()=>t,"all",this.dialect,this.session.extractRawAllValueFromBatchResult.bind(this.session)):this.session.all(t)}get(e){let t="string"==typeof e?s.ll.raw(e):e.getSQL();return"async"===this.resultKind?new rL(async()=>this.session.get(t),()=>t,"get",this.dialect,this.session.extractRawGetValueFromBatchResult.bind(this.session)):this.session.get(t)}values(e){let t="string"==typeof e?s.ll.raw(e):e.getSQL();return"async"===this.resultKind?new rL(async()=>this.session.values(t),()=>t,"values",this.dialect,this.session.extractRawValuesValueFromBatchResult.bind(this.session)):this.session.values(t)}transaction(e,t){return this.session.transaction(e,t)}}function rM(...e){return e[0].columns?new rB(e[0].columns,e[0].name):new rB(e)}class rB{static [i.i]="MySqlPrimaryKeyBuilder";columns;name;constructor(e,t){this.columns=e,this.name=t}build(e){return new rH(e,this.columns,this.name)}}class rH{constructor(e,t,r){this.table=e,this.columns=t,this.name=r}static [i.i]="MySqlPrimaryKey";columns;name;getName(){return this.name??`${this.table[ti.Symbol.Name]}_${this.columns.map(e=>e.name).join("_")}_pk`}}var rK=r(92768),rW=r(29334),rQ=r(9253),rJ=r(63431),rF=r(58152),rV=r(54693);function rX(...e){return e[0].columns?new rz(e[0].columns,e[0].name):new rz(e)}class rz{static [i.i]="SQLitePrimaryKeyBuilder";columns;name;constructor(e,t){this.columns=e,this.name=t}build(e){return new rG(e,this.columns,this.name)}}class rG{constructor(e,t,r){this.table=e,this.columns=t,this.name=r}static [i.i]="SQLitePrimaryKey";columns;name;getName(){return this.name??`${this.table[rf.Symbol.Name]}_${this.columns.map(e=>e.name).join("_")}_pk`}}function rY(e,t){if((0,i.is)(e,tR)){let{usersTable:r,accountsTable:i,sessionsTable:n,verificationTokensTable:s,authenticatorsTable:a}=function(e={}){let t=e.usersTable??tn("user",{id:e9("id",{length:255}).primaryKey().$defaultFn(()=>crypto.randomUUID()),name:e9("name",{length:255}),email:e9("email",{length:255}).unique(),emailVerified:eZ("emailVerified",{mode:"date",fsp:3}),image:e9("image",{length:255})}),r=e.accountsTable??tn("account",{userId:e9("userId",{length:255}).notNull().references(()=>t.id,{onDelete:"cascade"}),type:e9("type",{length:255}).$type().notNull(),provider:e9("provider",{length:255}).notNull(),providerAccountId:e9("providerAccountId",{length:255}).notNull(),refresh_token:e9("refresh_token",{length:255}),access_token:e9("access_token",{length:255}),expires_at:e_("expires_at"),token_type:e9("token_type",{length:255}),scope:e9("scope",{length:255}),id_token:e9("id_token",{length:2048}),session_state:e9("session_state",{length:255})},e=>({compositePk:rM({columns:[e.provider,e.providerAccountId]})})),i=e.sessionsTable??tn("session",{sessionToken:e9("sessionToken",{length:255}).primaryKey(),userId:e9("userId",{length:255}).notNull().references(()=>t.id,{onDelete:"cascade"}),expires:eZ("expires",{mode:"date"}).notNull()}),n=e.verificationTokensTable??tn("verificationToken",{identifier:e9("identifier",{length:255}).notNull(),token:e9("token",{length:255}).notNull(),expires:eZ("expires",{mode:"date"}).notNull()},e=>({compositePk:rM({columns:[e.identifier,e.token]})})),s=e.authenticatorsTable??tn("authenticator",{credentialID:e9("credentialID",{length:255}).notNull().unique(),userId:e9("userId",{length:255}).notNull().references(()=>t.id,{onDelete:"cascade"}),providerAccountId:e9("providerAccountId",{length:255}).notNull(),credentialPublicKey:e9("credentialPublicKey",{length:255}).notNull(),counter:e_("counter").notNull(),credentialDeviceType:e9("credentialDeviceType",{length:255}).notNull(),credentialBackedUp:N("credentialBackedUp").notNull(),transports:e9("transports",{length:255})},e=>({compositePk:rM({columns:[e.userId,e.credentialID]})}));return{usersTable:t,accountsTable:r,sessionsTable:i,verificationTokensTable:n,authenticatorsTable:s}}(t);return{async createUser(t){let{id:i,...n}=t,s=(0,y.YD)(r).id.defaultFn,[a]=await e.insert(r).values(s?n:{...n,id:i}).$returningId();return e.select().from(r).where((0,h.eq)(r.id,a?a.id:i)).then(e=>e[0])},getUser:async t=>e.select().from(r).where((0,h.eq)(r.id,t)).then(e=>e.length>0?e[0]:null),getUserByEmail:async t=>e.select().from(r).where((0,h.eq)(r.email,t)).then(e=>e.length>0?e[0]:null),createSession:async t=>(await e.insert(n).values(t),e.select().from(n).where((0,h.eq)(n.sessionToken,t.sessionToken)).then(e=>e[0])),getSessionAndUser:async t=>e.select({session:n,user:r}).from(n).where((0,h.eq)(n.sessionToken,t)).innerJoin(r,(0,h.eq)(r.id,n.userId)).then(e=>e.length>0?e[0]:null),async updateUser(t){if(!t.id)throw Error("No user id.");await e.update(r).set(t).where((0,h.eq)(r.id,t.id));let[i]=await e.select().from(r).where((0,h.eq)(r.id,t.id));if(!i)throw Error("No user found.");return i},updateSession:async t=>(await e.update(n).set(t).where((0,h.eq)(n.sessionToken,t.sessionToken)),e.select().from(n).where((0,h.eq)(n.sessionToken,t.sessionToken)).then(e=>e[0])),async linkAccount(t){await e.insert(i).values(t)},async getUserByAccount(t){let n=await e.select({account:i,user:r}).from(i).innerJoin(r,(0,h.eq)(i.userId,r.id)).where((0,h.Uo)((0,h.eq)(i.provider,t.provider),(0,h.eq)(i.providerAccountId,t.providerAccountId))).then(e=>e[0]);return n?.user??null},async deleteSession(t){await e.delete(n).where((0,h.eq)(n.sessionToken,t))},createVerificationToken:async t=>(await e.insert(s).values(t),e.select().from(s).where((0,h.eq)(s.identifier,t.identifier)).then(e=>e[0])),async useVerificationToken(t){let r=await e.select().from(s).where((0,h.Uo)((0,h.eq)(s.identifier,t.identifier),(0,h.eq)(s.token,t.token))).then(e=>e.length>0?e[0]:null);return r&&await e.delete(s).where((0,h.Uo)((0,h.eq)(s.identifier,t.identifier),(0,h.eq)(s.token,t.token))),r},async deleteUser(t){await e.delete(r).where((0,h.eq)(r.id,t))},async unlinkAccount(t){await e.delete(i).where((0,h.Uo)((0,h.eq)(i.provider,t.provider),(0,h.eq)(i.providerAccountId,t.providerAccountId)))},getAccount:async(t,r)=>e.select().from(i).where((0,h.Uo)((0,h.eq)(i.provider,r),(0,h.eq)(i.providerAccountId,t))).then(e=>e[0]??null),createAuthenticator:async t=>(await e.insert(a).values(t),await e.select().from(a).where((0,h.eq)(a.credentialID,t.credentialID)).then(e=>e[0]??null)),getAuthenticator:async t=>await e.select().from(a).where((0,h.eq)(a.credentialID,t)).then(e=>e[0]??null),listAuthenticatorsByUserId:async t=>await e.select().from(a).where((0,h.eq)(a.userId,t)).then(e=>e),async updateAuthenticatorCounter(t,r){await e.update(a).set({counter:r}).where((0,h.eq)(a.credentialID,t));let i=await e.select().from(a).where((0,h.eq)(a.credentialID,t)).then(e=>e[0]);if(!i)throw Error("Authenticator not found.");return i}}}if((0,i.is)(e,tI.p)){let{usersTable:r,accountsTable:i,sessionsTable:n,verificationTokensTable:s,authenticatorsTable:a}=function(e={}){let t=e.usersTable??(0,rK.cJ)("user",{id:(0,rW.Qq)("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),name:(0,rW.Qq)("name"),email:(0,rW.Qq)("email").unique(),emailVerified:(0,rQ.vE)("emailVerified",{mode:"date"}),image:(0,rW.Qq)("image")}),r=e.accountsTable??(0,rK.cJ)("account",{userId:(0,rW.Qq)("userId").notNull().references(()=>t.id,{onDelete:"cascade"}),type:(0,rW.Qq)("type").$type().notNull(),provider:(0,rW.Qq)("provider").notNull(),providerAccountId:(0,rW.Qq)("providerAccountId").notNull(),refresh_token:(0,rW.Qq)("refresh_token"),access_token:(0,rW.Qq)("access_token"),expires_at:(0,rJ.nd)("expires_at"),token_type:(0,rW.Qq)("token_type"),scope:(0,rW.Qq)("scope"),id_token:(0,rW.Qq)("id_token"),session_state:(0,rW.Qq)("session_state")},e=>({compositePk:(0,rF.ie)({columns:[e.provider,e.providerAccountId]})})),i=e.sessionsTable??(0,rK.cJ)("session",{sessionToken:(0,rW.Qq)("sessionToken").primaryKey(),userId:(0,rW.Qq)("userId").notNull().references(()=>t.id,{onDelete:"cascade"}),expires:(0,rQ.vE)("expires",{mode:"date"}).notNull()}),n=e.verificationTokensTable??(0,rK.cJ)("verificationToken",{identifier:(0,rW.Qq)("identifier").notNull(),token:(0,rW.Qq)("token").notNull(),expires:(0,rQ.vE)("expires",{mode:"date"}).notNull()},e=>({compositePk:(0,rF.ie)({columns:[e.identifier,e.token]})})),s=e.authenticatorsTable??(0,rK.cJ)("authenticator",{credentialID:(0,rW.Qq)("credentialID").notNull().unique(),userId:(0,rW.Qq)("userId").notNull().references(()=>t.id,{onDelete:"cascade"}),providerAccountId:(0,rW.Qq)("providerAccountId").notNull(),credentialPublicKey:(0,rW.Qq)("credentialPublicKey").notNull(),counter:(0,rJ.nd)("counter").notNull(),credentialDeviceType:(0,rW.Qq)("credentialDeviceType").notNull(),credentialBackedUp:(0,rV.zM)("credentialBackedUp").notNull(),transports:(0,rW.Qq)("transports")},e=>({compositePK:(0,rF.ie)({columns:[e.userId,e.credentialID]})}));return{usersTable:t,accountsTable:r,sessionsTable:i,verificationTokensTable:n,authenticatorsTable:s}}(t);return{async createUser(t){let{id:i,...n}=t,s=(0,y.YD)(r).id.hasDefault;return e.insert(r).values(s?n:{...n,id:i}).returning().then(e=>e[0])},getUser:async t=>e.select().from(r).where((0,h.eq)(r.id,t)).then(e=>e.length>0?e[0]:null),getUserByEmail:async t=>e.select().from(r).where((0,h.eq)(r.email,t)).then(e=>e.length>0?e[0]:null),createSession:async t=>e.insert(n).values(t).returning().then(e=>e[0]),getSessionAndUser:async t=>e.select({session:n,user:r}).from(n).where((0,h.eq)(n.sessionToken,t)).innerJoin(r,(0,h.eq)(r.id,n.userId)).then(e=>e.length>0?e[0]:null),async updateUser(t){if(!t.id)throw Error("No user id.");let[i]=await e.update(r).set(t).where((0,h.eq)(r.id,t.id)).returning();if(!i)throw Error("No user found.");return i},updateSession:async t=>e.update(n).set(t).where((0,h.eq)(n.sessionToken,t.sessionToken)).returning().then(e=>e[0]),async linkAccount(t){await e.insert(i).values(t)},async getUserByAccount(t){let n=await e.select({account:i,user:r}).from(i).innerJoin(r,(0,h.eq)(i.userId,r.id)).where((0,h.Uo)((0,h.eq)(i.provider,t.provider),(0,h.eq)(i.providerAccountId,t.providerAccountId))).then(e=>e[0]);return n?.user??null},async deleteSession(t){await e.delete(n).where((0,h.eq)(n.sessionToken,t))},createVerificationToken:async t=>e.insert(s).values(t).returning().then(e=>e[0]),useVerificationToken:async t=>e.delete(s).where((0,h.Uo)((0,h.eq)(s.identifier,t.identifier),(0,h.eq)(s.token,t.token))).returning().then(e=>e.length>0?e[0]:null),async deleteUser(t){await e.delete(r).where((0,h.eq)(r.id,t))},async unlinkAccount(t){await e.delete(i).where((0,h.Uo)((0,h.eq)(i.provider,t.provider),(0,h.eq)(i.providerAccountId,t.providerAccountId)))},getAccount:async(t,r)=>e.select().from(i).where((0,h.Uo)((0,h.eq)(i.provider,r),(0,h.eq)(i.providerAccountId,t))).then(e=>e[0]??null),createAuthenticator:async t=>e.insert(a).values(t).returning().then(e=>e[0]??null),getAuthenticator:async t=>e.select().from(a).where((0,h.eq)(a.credentialID,t)).then(e=>e[0]??null),listAuthenticatorsByUserId:async t=>e.select().from(a).where((0,h.eq)(a.userId,t)).then(e=>e),async updateAuthenticatorCounter(t,r){let i=await e.update(a).set({counter:r}).where((0,h.eq)(a.credentialID,t)).returning().then(e=>e[0]);if(!i)throw Error("Authenticator not found.");return i}}}if((0,i.is)(e,rN)){let{usersTable:r,accountsTable:i,sessionsTable:n,verificationTokensTable:s,authenticatorsTable:a}=function(e={}){let t=e.usersTable??rh("user",{id:ru("id").primaryKey().$defaultFn(()=>crypto.randomUUID()),name:ru("name"),email:ru("email").unique(),emailVerified:t3("emailVerified",{mode:"timestamp_ms"}),image:ru("image")}),r=e.accountsTable??rh("account",{userId:ru("userId").notNull().references(()=>t.id,{onDelete:"cascade"}),type:ru("type").$type().notNull(),provider:ru("provider").notNull(),providerAccountId:ru("providerAccountId").notNull(),refresh_token:ru("refresh_token"),access_token:ru("access_token"),expires_at:t3("expires_at"),token_type:ru("token_type"),scope:ru("scope"),id_token:ru("id_token"),session_state:ru("session_state")},e=>({compositePk:rX({columns:[e.provider,e.providerAccountId]})})),i=e.sessionsTable??rh("session",{sessionToken:ru("sessionToken").primaryKey(),userId:ru("userId").notNull().references(()=>t.id,{onDelete:"cascade"}),expires:t3("expires",{mode:"timestamp_ms"}).notNull()}),n=e.verificationTokensTable??rh("verificationToken",{identifier:ru("identifier").notNull(),token:ru("token").notNull(),expires:t3("expires",{mode:"timestamp_ms"}).notNull()},e=>({compositePk:rX({columns:[e.identifier,e.token]})})),s=e.authenticatorsTable??rh("authenticator",{credentialID:ru("credentialID").notNull().unique(),userId:ru("userId").notNull().references(()=>t.id,{onDelete:"cascade"}),providerAccountId:ru("providerAccountId").notNull(),credentialPublicKey:ru("credentialPublicKey").notNull(),counter:t3("counter").notNull(),credentialDeviceType:ru("credentialDeviceType").notNull(),credentialBackedUp:t3("credentialBackedUp",{mode:"boolean"}).notNull(),transports:ru("transports")},e=>({compositePK:rX({columns:[e.userId,e.credentialID]})}));return{usersTable:t,accountsTable:r,sessionsTable:i,verificationTokensTable:n,authenticatorsTable:s}}(t);return{async createUser(t){let{id:i,...n}=t,s=(0,y.YD)(r).id.hasDefault;return e.insert(r).values(s?n:{...n,id:i}).returning().get()},getUser:async t=>await e.select().from(r).where((0,h.eq)(r.id,t)).get()??null,getUserByEmail:async t=>await e.select().from(r).where((0,h.eq)(r.email,t)).get()??null,createSession:async t=>e.insert(n).values(t).returning().get(),getSessionAndUser:async t=>await e.select({session:n,user:r}).from(n).where((0,h.eq)(n.sessionToken,t)).innerJoin(r,(0,h.eq)(r.id,n.userId)).get()??null,async updateUser(t){if(!t.id)throw Error("No user id.");let i=await e.update(r).set(t).where((0,h.eq)(r.id,t.id)).returning().get();if(!i)throw Error("User not found.");return i},updateSession:async t=>await e.update(n).set(t).where((0,h.eq)(n.sessionToken,t.sessionToken)).returning().get()??null,async linkAccount(t){await e.insert(i).values(t).run()},async getUserByAccount(t){let n=await e.select({account:i,user:r}).from(i).innerJoin(r,(0,h.eq)(i.userId,r.id)).where((0,h.Uo)((0,h.eq)(i.provider,t.provider),(0,h.eq)(i.providerAccountId,t.providerAccountId))).get();return n?.user??null},async deleteSession(t){await e.delete(n).where((0,h.eq)(n.sessionToken,t)).run()},createVerificationToken:async t=>e.insert(s).values(t).returning().get(),useVerificationToken:async t=>await e.delete(s).where((0,h.Uo)((0,h.eq)(s.identifier,t.identifier),(0,h.eq)(s.token,t.token))).returning().get()??null,async deleteUser(t){await e.delete(r).where((0,h.eq)(r.id,t)).run()},async unlinkAccount(t){await e.delete(i).where((0,h.Uo)((0,h.eq)(i.provider,t.provider),(0,h.eq)(i.providerAccountId,t.providerAccountId))).run()},getAccount:async(t,r)=>e.select().from(i).where((0,h.Uo)((0,h.eq)(i.provider,r),(0,h.eq)(i.providerAccountId,t))).then(e=>e[0]??null),createAuthenticator:async t=>e.insert(a).values(t).returning().then(e=>e[0]??null),getAuthenticator:async t=>e.select().from(a).where((0,h.eq)(a.credentialID,t)).then(e=>e[0]??null),listAuthenticatorsByUserId:async t=>e.select().from(a).where((0,h.eq)(a.userId,t)).then(e=>e),async updateAuthenticatorCounter(t,r){let i=await e.update(a).set({counter:r}).where((0,h.eq)(a.credentialID,t)).returning().then(e=>e[0]);if(!i)throw Error("Authenticator not found.");return i}}}throw Error(`Unsupported database type (${typeof e}) in Auth.js Drizzle adapter.`)}},39916:(e,t,r)=>{var i=r(97576);r.o(i,"redirect")&&r.d(t,{redirect:function(){return i.redirect}})},48976:(e,t,r)=>{function i(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return i}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},49026:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{REDIRECT_ERROR_CODE:function(){return n},RedirectType:function(){return s},isRedirectError:function(){return a}});let i=r(52836),n="NEXT_REDIRECT";var s=function(e){return e.push="push",e.replace="replace",e}({});function a(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,s]=t,a=t.slice(2,-2).join(";"),o=Number(t.at(-2));return r===n&&("replace"===s||"push"===s)&&"string"==typeof a&&!isNaN(o)&&o in i.RedirectStatusCode}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51846:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return i},isBailoutToCSRError:function(){return n}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class i extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function n(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},52637:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isPostpone",{enumerable:!0,get:function(){return i}});let r=Symbol.for("react.postpone");function i(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}},52836:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}});var r=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},62765:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return n}});let i=""+r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function n(){let e=Object.defineProperty(Error(i),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=i,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70899:(e,t,r)=>{function i(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return i}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71042:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,a.isNextRouterError)(t)||(0,s.isBailoutToCSRError)(t)||(0,l.isDynamicServerError)(t)||(0,o.isDynamicPostpone)(t)||(0,n.isPostpone)(t)||(0,i.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let i=r(68388),n=r(52637),s=r(51846),a=r(31162),o=r(84971),l=r(98479);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},73913:(e,t,r)=>{let i=r(63033),n=r(29294),s=r(84971),a=r(76926),o=r(80023),l=r(98479);function c(){let e=n.workAsyncStorage.getStore(),t=i.workUnitAsyncStorage.getStore();switch((!e||!t)&&(0,i.throwForMissingRequestStore)("draftMode"),t.type){case"request":return u(t.draftMode,e);case"cache":case"unstable-cache":let r=(0,i.getDraftModeProviderForCacheScope)(e,t);if(r)return u(r,e);case"prerender":case"prerender-ppr":case"prerender-legacy":return f(null);default:return t}}function u(e,t){let r,i=d.get(c);return i||(r=f(e),d.set(e,r),r)}let d=new WeakMap;function f(e){let t=new h(e),r=Promise.resolve(t);return Object.defineProperty(r,"isEnabled",{get:()=>t.isEnabled,set(e){Object.defineProperty(r,"isEnabled",{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0}),r.enable=t.enable.bind(t),r.disable=t.disable.bind(t),r}class h{constructor(e){this._provider=e}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){y("draftMode().enable()"),null!==this._provider&&this._provider.enable()}disable(){y("draftMode().disable()"),null!==this._provider&&this._provider.disable()}}let p=(0,a.createDedupedByCallsiteServerErrorLoggerDev)(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})});function y(e){let t=n.workAsyncStorage.getStore(),r=i.workUnitAsyncStorage.getStore();if(t){if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:!1,configurable:!0});else if("after"===r.phase)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new o.StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(r){if("prerender"===r.type){let i=Object.defineProperty(Error(`Route ${t.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:!1,configurable:!0});(0,s.abortAndThrowOnSynchronousRequestDataAccess)(t.route,e,i,r)}else if("prerender-ppr"===r.type)(0,s.postponeWithTracking)(t.route,e,r.dynamicTracking);else if("prerender-legacy"===r.type){r.revalidate=0;let i=Object.defineProperty(new l.DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw t.dynamicUsageDescription=e,t.dynamicUsageStack=i.stack,i}}}}},76926:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return l}});let i=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=n(t);if(r&&r.has(e))return r.get(e);var i={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var o=s?Object.getOwnPropertyDescriptor(e,a):null;o&&(o.get||o.set)?Object.defineProperty(i,a,o):i[a]=e[a]}return i.default=e,r&&r.set(e,i),i}(r(61120));function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}let s={current:null},a="function"==typeof i.cache?i.cache:e=>e,o=console.warn;function l(e){return function(...t){o(e(...t))}}a(e=>{try{o(s.current)}finally{s.current=null}})},85663:(e,t,r)=>{r.d(t,{Ay:()=>E});var i=r(55511),n=null;function s(e,t){if("number"!=typeof(e=e||g))throw Error("Illegal arguments: "+typeof e+", "+typeof t);e<4?e=4:e>31&&(e=31);var r=[];return r.push("$2b$"),e<10&&r.push("0"),r.push(e.toString()),r.push("$"),r.push(p(function(e){try{return crypto.getRandomValues(new Uint8Array(e))}catch{}try{return i.randomBytes(e)}catch{}if(!n)throw Error("Neither WebCryptoAPI nor a crypto module is available. Use bcrypt.setRandomFallback to set an alternative");return n(e)}(m),m)),r.join("")}function a(e,t,r){if("function"==typeof t&&(r=t,t=void 0),"function"==typeof e&&(r=e,e=void 0),void 0===e)e=g;else if("number"!=typeof e)throw Error("illegal arguments: "+typeof e);function i(t){u(function(){try{t(null,s(e))}catch(e){t(e)}})}if(!r)return new Promise(function(e,t){i(function(r,i){if(r)return void t(r);e(i)})});if("function"!=typeof r)throw Error("Illegal callback: "+typeof r);i(r)}function o(e,t){if(void 0===t&&(t=g),"number"==typeof t&&(t=s(t)),"string"!=typeof e||"string"!=typeof t)throw Error("Illegal arguments: "+typeof e+", "+typeof t);return A(e,t)}function l(e,t,r,i){function n(r){"string"==typeof e&&"number"==typeof t?a(t,function(t,n){A(e,n,r,i)}):"string"==typeof e&&"string"==typeof t?A(e,t,r,i):u(r.bind(this,Error("Illegal arguments: "+typeof e+", "+typeof t)))}if(!r)return new Promise(function(e,t){n(function(r,i){if(r)return void t(r);e(i)})});if("function"!=typeof r)throw Error("Illegal callback: "+typeof r);n(r)}function c(e,t){for(var r=e.length^t.length,i=0;i<e.length;++i)r|=e.charCodeAt(i)^t.charCodeAt(i);return 0===r}var u="undefined"!=typeof process&&process&&"function"==typeof process.nextTick?"function"==typeof setImmediate?setImmediate:process.nextTick:setTimeout;function d(e){for(var t=0,r=0,i=0;i<e.length;++i)(r=e.charCodeAt(i))<128?t+=1:r<2048?t+=2:(64512&r)==55296&&(64512&e.charCodeAt(i+1))==56320?(++i,t+=4):t+=3;return t}var f="./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),h=[-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,1,54,55,56,57,58,59,60,61,62,63,-1,-1,-1,-1,-1,-1,-1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,-1,-1,-1,-1,-1,-1,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,-1,-1,-1,-1,-1];function p(e,t){var r,i,n=0,s=[];if(t<=0||t>e.length)throw Error("Illegal len: "+t);for(;n<t;){if(r=255&e[n++],s.push(f[r>>2&63]),r=(3&r)<<4,n>=t||(r|=(i=255&e[n++])>>4&15,s.push(f[63&r]),r=(15&i)<<2,n>=t)){s.push(f[63&r]);break}r|=(i=255&e[n++])>>6&3,s.push(f[63&r]),s.push(f[63&i])}return s.join("")}function y(e,t){var r,i,n,s,a,o=0,l=e.length,c=0,u=[];if(t<=0)throw Error("Illegal len: "+t);for(;o<l-1&&c<t&&(r=(a=e.charCodeAt(o++))<h.length?h[a]:-1,i=(a=e.charCodeAt(o++))<h.length?h[a]:-1,-1!=r&&-1!=i)&&(s=r<<2>>>0|(48&i)>>4,u.push(String.fromCharCode(s)),!(++c>=t||o>=l||-1==(n=(a=e.charCodeAt(o++))<h.length?h[a]:-1)||(s=(15&i)<<4>>>0|(60&n)>>2,u.push(String.fromCharCode(s)),++c>=t||o>=l)));){;s=(3&n)<<6>>>0|((a=e.charCodeAt(o++))<h.length?h[a]:-1),u.push(String.fromCharCode(s)),++c}var d=[];for(o=0;o<c;o++)d.push(u[o].charCodeAt(0));return d}var m=16,g=10,b=[0x243f6a88,0x85a308d3,0x13198a2e,0x3707344,0xa4093822,0x299f31d0,0x82efa98,0xec4e6c89,0x452821e6,0x38d01377,0xbe5466cf,0x34e90c6c,0xc0ac29b7,0xc97c50dd,0x3f84d5b5,0xb5470917,0x9216d5d9,0x8979fb1b],w=[0xd1310ba6,0x98dfb5ac,0x2ffd72db,0xd01adfb7,0xb8e1afed,0x6a267e96,0xba7c9045,0xf12c7f99,0x24a19947,0xb3916cf7,0x801f2e2,0x858efc16,0x636920d8,0x71574e69,0xa458fea3,0xf4933d7e,0xd95748f,0x728eb658,0x718bcd58,0x82154aee,0x7b54a41d,0xc25a59b5,0x9c30d539,0x2af26013,0xc5d1b023,0x286085f0,0xca417918,0xb8db38ef,0x8e79dcb0,0x603a180e,0x6c9e0e8b,0xb01e8a3e,0xd71577c1,0xbd314b27,0x78af2fda,0x55605c60,0xe65525f3,0xaa55ab94,0x57489862,0x63e81440,0x55ca396a,0x2aab10b6,0xb4cc5c34,0x1141e8ce,0xa15486af,0x7c72e993,0xb3ee1411,0x636fbc2a,0x2ba9c55d,0x741831f6,0xce5c3e16,0x9b87931e,0xafd6ba33,0x6c24cf5c,0x7a325381,0x28958677,0x3b8f4898,0x6b4bb9af,0xc4bfe81b,0x66282193,0x61d809cc,0xfb21a991,0x487cac60,0x5dec8032,0xef845d5d,0xe98575b1,0xdc262302,0xeb651b88,0x23893e81,0xd396acc5,0xf6d6ff3,0x83f44239,0x2e0b4482,0xa4842004,0x69c8f04a,0x9e1f9b5e,0x21c66842,0xf6e96c9a,0x670c9c61,0xabd388f0,0x6a51a0d2,0xd8542f68,0x960fa728,0xab5133a3,0x6eef0b6c,0x137a3be4,0xba3bf050,0x7efb2a98,0xa1f1651d,0x39af0176,0x66ca593e,0x82430e88,0x8cee8619,0x456f9fb4,0x7d84a5c3,0x3b8b5ebe,0xe06f75d8,0x85c12073,0x401a449f,0x56c16aa6,0x4ed3aa62,0x363f7706,0x1bfedf72,0x429b023d,0x37d0d724,0xd00a1248,0xdb0fead3,0x49f1c09b,0x75372c9,0x80991b7b,0x25d479d8,0xf6e8def7,0xe3fe501a,0xb6794c3b,0x976ce0bd,0x4c006ba,0xc1a94fb6,0x409f60c4,0x5e5c9ec2,0x196a2463,0x68fb6faf,0x3e6c53b5,0x1339b2eb,0x3b52ec6f,0x6dfc511f,0x9b30952c,0xcc814544,0xaf5ebd09,0xbee3d004,0xde334afd,0x660f2807,0x192e4bb3,0xc0cba857,0x45c8740f,0xd20b5f39,0xb9d3fbdb,0x5579c0bd,0x1a60320a,0xd6a100c6,0x402c7279,0x679f25fe,0xfb1fa3cc,0x8ea5e9f8,0xdb3222f8,0x3c7516df,0xfd616b15,0x2f501ec8,0xad0552ab,0x323db5fa,0xfd238760,0x53317b48,0x3e00df82,0x9e5c57bb,0xca6f8ca0,0x1a87562e,0xdf1769db,0xd542a8f6,0x287effc3,0xac6732c6,0x8c4f5573,0x695b27b0,0xbbca58c8,0xe1ffa35d,0xb8f011a0,0x10fa3d98,0xfd2183b8,0x4afcb56c,0x2dd1d35b,0x9a53e479,0xb6f84565,0xd28e49bc,0x4bfb9790,0xe1ddf2da,0xa4cb7e33,0x62fb1341,0xcee4c6e8,0xef20cada,0x36774c01,0xd07e9efe,0x2bf11fb4,0x95dbda4d,0xae909198,0xeaad8e71,0x6b93d5a0,0xd08ed1d0,0xafc725e0,0x8e3c5b2f,0x8e7594b7,0x8ff6e2fb,0xf2122b64,0x8888b812,0x900df01c,0x4fad5ea0,0x688fc31c,0xd1cff191,0xb3a8c1ad,0x2f2f2218,0xbe0e1777,0xea752dfe,0x8b021fa1,0xe5a0cc0f,0xb56f74e8,0x18acf3d6,0xce89e299,0xb4a84fe0,0xfd13e0b7,0x7cc43b81,0xd2ada8d9,0x165fa266,0x80957705,0x93cc7314,0x211a1477,0xe6ad2065,0x77b5fa86,0xc75442f5,0xfb9d35cf,0xebcdaf0c,0x7b3e89a0,0xd6411bd3,0xae1e7e49,2428461,0x2071b35e,0x226800bb,0x57b8e0af,0x2464369b,0xf009b91e,0x5563911d,0x59dfa6aa,0x78c14389,0xd95a537f,0x207d5ba2,0x2e5b9c5,0x83260376,0x6295cfa9,0x11c81968,0x4e734a41,0xb3472dca,0x7b14a94a,0x1b510052,0x9a532915,0xd60f573f,0xbc9bc6e4,0x2b60a476,0x81e67400,0x8ba6fb5,0x571be91f,0xf296ec6b,0x2a0dd915,0xb6636521,0xe7b9f9b6,0xff34052e,0xc5855664,0x53b02d5d,0xa99f8fa1,0x8ba4799,0x6e85076a,0x4b7a70e9,0xb5b32944,0xdb75092e,0xc4192623,290971e4,0x49a7df7d,0x9cee60b8,0x8fedb266,0xecaa8c71,0x699a17ff,0x5664526c,0xc2b19ee1,0x193602a5,0x75094c29,0xa0591340,0xe4183a3e,0x3f54989a,0x5b429d65,0x6b8fe4d6,0x99f73fd6,0xa1d29c07,0xefe830f5,0x4d2d38e6,0xf0255dc1,0x4cdd2086,0x8470eb26,0x6382e9c6,0x21ecc5e,0x9686b3f,0x3ebaefc9,0x3c971814,0x6b6a70a1,0x687f3584,0x52a0e286,0xb79c5305,0xaa500737,0x3e07841c,0x7fdeae5c,0x8e7d44ec,0x5716f2b8,0xb03ada37,0xf0500c0d,0xf01c1f04,0x200b3ff,0xae0cf51a,0x3cb574b2,0x25837a58,0xdc0921bd,0xd19113f9,0x7ca92ff6,0x94324773,0x22f54701,0x3ae5e581,0x37c2dadc,0xc8b57634,0x9af3dda7,0xa9446146,0xfd0030e,0xecc8c73e,0xa4751e41,0xe238cd99,0x3bea0e2f,0x3280bba1,0x183eb331,0x4e548b38,0x4f6db908,0x6f420d03,0xf60a04bf,0x2cb81290,0x24977c79,0x5679b072,0xbcaf89af,0xde9a771f,0xd9930810,0xb38bae12,0xdccf3f2e,0x5512721f,0x2e6b7124,0x501adde6,0x9f84cd87,0x7a584718,0x7408da17,0xbc9f9abc,0xe94b7d8c,0xec7aec3a,0xdb851dfa,0x63094366,0xc464c3d2,0xef1c1847,0x3215d908,0xdd433b37,0x24c2ba16,0x12a14d43,0x2a65c451,0x50940002,0x133ae4dd,0x71dff89e,0x10314e55,0x81ac77d6,0x5f11199b,0x43556f1,0xd7a3c76b,0x3c11183b,0x5924a509,0xf28fe6ed,0x97f1fbfa,0x9ebabf2c,0x1e153c6e,0x86e34570,0xeae96fb1,0x860e5e0a,0x5a3e2ab3,0x771fe71c,0x4e3d06fa,0x2965dcb9,0x99e71d0f,0x803e89d6,0x5266c825,0x2e4cc978,0x9c10b36a,0xc6150eba,0x94e2ea78,0xa5fc3c53,0x1e0a2df4,0xf2f74ea7,0x361d2b3d,0x1939260f,0x19c27960,0x5223a708,0xf71312b6,0xebadfe6e,0xeac31f66,0xe3bc4595,0xa67bc883,0xb17f37d1,0x18cff28,0xc332ddef,0xbe6c5aa5,0x65582185,0x68ab9802,0xeecea50f,0xdb2f953b,0x2aef7dad,0x5b6e2f84,0x1521b628,0x29076170,0xecdd4775,0x619f1510,0x13cca830,0xeb61bd96,0x334fe1e,0xaa0363cf,0xb5735c90,0x4c70a239,0xd59e9e0b,0xcbaade14,0xeecc86bc,0x60622ca7,0x9cab5cab,0xb2f3846e,0x648b1eaf,0x19bdf0ca,0xa02369b9,0x655abb50,0x40685a32,0x3c2ab4b3,0x319ee9d5,0xc021b8f7,0x9b540b19,0x875fa099,0x95f7997e,0x623d7da8,0xf837889a,0x97e32d77,0x11ed935f,0x16681281,0xe358829,0xc7e61fd6,0x96dedfa1,0x7858ba99,0x57f584a5,0x1b227263,0x9b83c3ff,0x1ac24696,0xcdb30aeb,0x532e3054,0x8fd948e4,0x6dbc3128,0x58ebf2ef,0x34c6ffea,0xfe28ed61,0xee7c3c73,0x5d4a14d9,0xe864b7e3,0x42105d14,0x203e13e0,0x45eee2b6,0xa3aaabea,0xdb6c4f15,0xfacb4fd0,0xc742f442,0xef6abbb5,0x654f3b1d,0x41cd2105,0xd81e799e,0x86854dc7,0xe44b476a,0x3d816250,0xcf62a1f2,0x5b8d2646,0xfc8883a0,0xc1c7b6a3,0x7f1524c3,0x69cb7492,0x47848a0b,0x5692b285,0x95bbf00,0xad19489d,0x1462b174,0x23820e00,0x58428d2a,0xc55f5ea,0x1dadf43e,0x233f7061,0x3372f092,0x8d937e41,0xd65fecf1,0x6c223bdb,0x7cde3759,0xcbee7460,0x4085f2a7,0xce77326e,0xa6078084,0x19f8509e,0xe8efd855,0x61d99735,0xa969a7aa,0xc50c06c2,0x5a04abfc,0x800bcadc,0x9e447a2e,0xc3453484,0xfdd56705,0xe1e9ec9,0xdb73dbd3,0x105588cd,0x675fda79,0xe3674340,0xc5c43465,0x713e38d8,0x3d28f89e,0xf16dff20,0x153e21e7,0x8fb03d4a,0xe6e39f2b,0xdb83adf7,0xe93d5a68,0x948140f7,0xf64c261c,0x94692934,0x411520f7,0x7602d4f7,0xbcf46b2e,0xd4a20068,0xd4082471,0x3320f46a,0x43b7d4b7,0x500061af,0x1e39f62e,0x97244546,0x14214f74,0xbf8b8840,0x4d95fc1d,0x96b591af,0x70f4ddd3,0x66a02f45,0xbfbc09ec,0x3bd9785,0x7fac6dd0,0x31cb8504,0x96eb27b3,0x55fd3941,0xda2547e6,0xabca0a9a,0x28507825,0x530429f4,0xa2c86da,0xe9b66dfb,0x68dc1462,0xd7486900,0x680ec0a4,0x27a18dee,0x4f3ffea2,0xe887ad8c,0xb58ce006,0x7af4d6b6,0xaace1e7c,0xd3375fec,0xce78a399,0x406b2a42,0x20fe9e35,0xd9f385b9,0xee39d7ab,0x3b124e8b,0x1dc9faf7,0x4b6d1856,0x26a36631,0xeae397b2,0x3a6efa74,0xdd5b4332,0x6841e7f7,0xca7820fb,0xfb0af54e,0xd8feb397,0x454056ac,0xba489527,0x55533a3a,0x20838d87,0xfe6ba9b7,0xd096954b,0x55a867bc,0xa1159a58,0xcca92963,0x99e1db33,0xa62a4a56,0x3f3125f9,0x5ef47e1c,0x9029317c,0xfdf8e802,0x4272f70,0x80bb155c,0x5282ce3,0x95c11548,0xe4c66d22,0x48c1133f,0xc70f86dc,0x7f9c9ee,0x41041f0f,0x404779a4,0x5d886e17,0x325f51eb,0xd59bc0d1,0xf2bcc18f,0x41113564,0x257b7834,0x602a9c60,0xdff8e8a3,0x1f636c1b,0xe12b4c2,0x2e1329e,0xaf664fd1,0xcad18115,0x6b2395e0,0x333e92e1,0x3b240b62,0xeebeb922,0x85b2a20e,0xe6ba0d99,0xde720c8c,0x2da2f728,0xd0127845,0x95b794fd,0x647d0862,0xe7ccf5f0,0x5449a36f,0x877d48fa,0xc39dfd27,0xf33e8d1e,0xa476341,0x992eff74,0x3a6f6eab,0xf4f8fd37,0xa812dc60,0xa1ebddf8,0x991be14c,0xdb6e6b0d,0xc67b5510,0x6d672c37,0x2765d43b,0xdcd0e804,0xf1290dc7,0xcc00ffa3,0xb5390f92,0x690fed0b,0x667b9ffb,0xcedb7d9c,0xa091cf0b,0xd9155ea3,0xbb132f88,0x515bad24,0x7b9479bf,0x763bd6eb,0x37392eb3,0xcc115979,0x8026e297,0xf42e312d,0x6842ada7,0xc66a2b3b,0x12754ccc,0x782ef11c,0x6a124237,0xb79251e7,0x6a1bbe6,0x4bfb6350,0x1a6b1018,0x11caedfa,0x3d25bdd8,0xe2e1c3c9,0x44421659,0xa121386,0xd90cec6e,0xd5abea2a,0x64af674e,0xda86a85f,0xbebfe988,0x64e4c3fe,0x9dbc8057,0xf0f7c086,0x60787bf8,0x6003604d,0xd1fd8346,0xf6381fb0,0x7745ae04,0xd736fccc,0x83426b33,0xf01eab71,0xb0804187,0x3c005e5f,0x77a057be,0xbde8ae24,0x55464299,0xbf582e61,0x4e58f48f,0xf2ddfda2,0xf474ef38,0x8789bdc2,0x5366f9c3,0xc8b38e74,0xb475f255,0x46fcd9b9,0x7aeb2661,0x8b1ddf84,0x846a0e79,0x915f95e2,0x466e598e,0x20b45770,0x8cd55591,0xc902de4c,0xb90bace1,0xbb8205d0,0x11a86248,0x7574a99e,0xb77f19b6,0xe0a9dc09,0x662d09a1,0xc4324633,0xe85a1f02,0x9f0be8c,0x4a99a025,0x1d6efe10,0x1ab93d1d,0xba5a4df,0xa186f20f,0x2868f169,0xdcb7da83,0x573906fe,0xa1e2ce9b,0x4fcd7f52,0x50115e01,0xa70683fa,0xa002b5c4,0xde6d027,0x9af88c27,0x773f8641,0xc3604c06,0x61a806b5,0xf0177a28,0xc0f586e0,6314154,0x30dc7d62,0x11e69ed7,0x2338ea63,0x53c2dd94,0xc2c21634,0xbbcbee56,0x90bcb6de,0xebfc7da1,0xce591d76,0x6f05e409,0x4b7c0188,0x39720a3d,0x7c927c24,0x86e3725f,0x724d9db9,0x1ac15bb4,0xd39eb8fc,0xed545578,0x8fca5b5,0xd83d7cd3,0x4dad0fc4,0x1e50ef5e,0xb161e6f8,0xa28514d9,0x6c51133c,0x6fd5c7e7,0x56e14ec4,0x362abfce,0xddc6c837,0xd79a3234,0x92638212,0x670efa8e,0x406000e0,0x3a39ce37,0xd3faf5cf,0xabc27737,0x5ac52d1b,0x5cb0679e,0x4fa33742,0xd3822740,0x99bc9bbe,0xd5118e9d,0xbf0f7315,0xd62d1c7e,0xc700c47b,0xb78c1b6b,0x21a19045,0xb26eb1be,0x6a366eb4,0x5748ab2f,0xbc946e79,0xc6a376d2,0x6549c2c8,0x530ff8ee,0x468dde7d,0xd5730a1d,0x4cd04dc6,0x2939bbdb,0xa9ba4650,0xac9526e8,0xbe5ee304,0xa1fad5f0,0x6a2d519a,0x63ef8ce2,0x9a86ee22,0xc089c2b8,0x43242ef6,0xa51e03aa,0x9cf2d0a4,0x83c061ba,0x9be96a4d,0x8fe51550,0xba645bd6,0x2826a2f9,0xa73a3ae1,0x4ba99586,0xef5562e9,0xc72fefd3,0xf752f7da,0x3f046f69,0x77fa0a59,0x80e4a915,0x87b08601,0x9b09e6ad,0x3b3ee593,0xe990fd5a,0x9e34d797,0x2cf0b7d9,0x22b8b51,0x96d5ac3a,0x17da67d,0xd1cf3ed6,0x7c7d2d28,0x1f9f25cf,0xadf2b89b,0x5ad6b472,0x5a88f54c,0xe029ac71,0xe019a5e6,0x47b0acfd,0xed93fa9b,0xe8d3c48d,0x283b57cc,0xf8d56629,0x79132e28,0x785f0191,0xed756055,0xf7960e44,0xe3d35e8c,0x15056dd4,0x88f46dba,0x3a16125,0x564f0bd,0xc3eb9e15,0x3c9057a2,0x97271aec,0xa93a072a,0x1b3f6d9b,0x1e6321f5,0xf59c66fb,0x26dcf319,0x7533d928,0xb155fdf5,0x3563482,0x8aba3cbb,0x28517711,0xc20ad9f8,0xabcc5167,0xccad925f,0x4de81751,0x3830dc8e,0x379d5862,0x9320f991,0xea7a90c2,0xfb3e7bce,0x5121ce64,0x774fbe32,0xa8b6e37e,0xc3293d46,0x48de5369,0x6413e680,0xa2ae0810,0xdd6db224,0x69852dfd,0x9072166,0xb39a460a,0x6445c0dd,0x586cdecf,0x1c20c8ae,0x5bbef7dd,0x1b588d40,0xccd2017f,0x6bb4e3bb,0xdda26a7e,0x3a59ff45,0x3e350a44,0xbcb4cdd5,0x72eacea8,0xfa6484bb,0x8d6612ae,0xbf3c6f47,0xd29be463,0x542f5d9e,0xaec2771b,0xf64e6370,0x740e0d8d,0xe75b1357,0xf8721671,0xaf537d5d,0x4040cb08,0x4eb4e2cc,0x34d2466a,0x115af84,3786409e3,0x95983a1d,0x6b89fb4,0xce6ea048,0x6f3f3b82,0x3520ab82,0x11a1d4b,0x277227f8,0x611560b1,0xe7933fdc,0xbb3a792b,0x344525bd,0xa08839e1,0x51ce794b,0x2f32c9b7,0xa01fbac9,0xe01cc87e,0xbcc7d1f6,0xcf0111c3,0xa1e8aac7,0x1a908749,0xd44fbd9a,0xd0dadecb,0xd50ada38,0x339c32a,0xc6913667,0x8df9317c,0xe0b12b4f,0xf79e59b7,0x43f5bb3a,0xf2d519ff,0x27d9459c,0xbf97222c,0x15e6fc2a,0xf91fc71,0x9b941525,0xfae59361,0xceb69ceb,0xc2a86459,0x12baa8d1,0xb6c1075e,0xe3056a0c,0x10d25065,0xcb03a442,0xe0ec6e0e,0x1698db3b,0x4c98a0be,0x3278e964,0x9f1f9532,0xe0d392df,0xd3a0342b,0x8971f21e,0x1b0a7441,0x4ba3348c,0xc5be7120,0xc37632d8,0xdf359f8d,0x9b992f2e,0xe60b6f47,0xfe3f11d,0xe54cda54,0x1edad891,0xce6279cf,0xcd3e7e6f,0x1618b166,0xfd2c1d05,0x848fd2c5,0xf6fb2299,0xf523f357,0xa6327623,0x93a83531,0x56cccd02,0xacf08162,0x5a75ebb5,0x6e163697,0x88d273cc,0xde966292,0x81b949d0,0x4c50901b,0x71c65614,0xe6c6c7bd,0x327a140a,0x45e1d006,0xc3f27b9a,0xc9aa53fd,0x62a80f00,0xbb25bfe2,0x35bdd2f6,0x71126905,0xb2040222,0xb6cbcf7c,0xcd769c2b,0x53113ec0,0x1640e3d3,0x38abbd60,0x2547adf0,0xba38209c,0xf746ce76,0x77afa1c5,0x20756060,0x85cbfe4e,0x8ae88dd8,0x7aaaf9b0,0x4cf9aa7e,0x1948c25c,0x2fb8a8c,0x1c36ae4,0xd6ebe1f9,0x90d4f869,0xa65cdea0,0x3f09252d,0xc208e69f,0xb74e6132,0xce77e25b,0x578fdfe3,0x3ac372e6],x=[0x4f727068,0x65616e42,0x65686f6c,0x64657253,0x63727944,0x6f756274];function v(e,t,r,i){var n,s=e[t],a=e[t+1];return s^=r[0],a^=(i[s>>>24]+i[256|s>>16&255]^i[512|s>>8&255])+i[768|255&s]^r[1],s^=(i[a>>>24]+i[256|a>>16&255]^i[512|a>>8&255])+i[768|255&a]^r[2],a^=(i[s>>>24]+i[256|s>>16&255]^i[512|s>>8&255])+i[768|255&s]^r[3],s^=(i[a>>>24]+i[256|a>>16&255]^i[512|a>>8&255])+i[768|255&a]^r[4],a^=(i[s>>>24]+i[256|s>>16&255]^i[512|s>>8&255])+i[768|255&s]^r[5],s^=(i[a>>>24]+i[256|a>>16&255]^i[512|a>>8&255])+i[768|255&a]^r[6],a^=(i[s>>>24]+i[256|s>>16&255]^i[512|s>>8&255])+i[768|255&s]^r[7],s^=(i[a>>>24]+i[256|a>>16&255]^i[512|a>>8&255])+i[768|255&a]^r[8],a^=(i[s>>>24]+i[256|s>>16&255]^i[512|s>>8&255])+i[768|255&s]^r[9],s^=(i[a>>>24]+i[256|a>>16&255]^i[512|a>>8&255])+i[768|255&a]^r[10],a^=(i[s>>>24]+i[256|s>>16&255]^i[512|s>>8&255])+i[768|255&s]^r[11],s^=(i[a>>>24]+i[256|a>>16&255]^i[512|a>>8&255])+i[768|255&a]^r[12],a^=(i[s>>>24]+i[256|s>>16&255]^i[512|s>>8&255])+i[768|255&s]^r[13],s^=(i[a>>>24]+i[256|a>>16&255]^i[512|a>>8&255])+i[768|255&a]^r[14],a^=(i[s>>>24]+i[256|s>>16&255]^i[512|s>>8&255])+i[768|255&s]^r[15],s^=(i[a>>>24]+i[256|a>>16&255]^i[512|a>>8&255])+i[768|255&a]^r[16],e[t]=a^r[17],e[t+1]=s,e}function _(e,t){for(var r=0,i=0;r<4;++r)i=i<<8|255&e[t],t=(t+1)%e.length;return{key:i,offp:t}}function S(e,t,r){for(var i,n=0,s=[0,0],a=t.length,o=r.length,l=0;l<a;l++)n=(i=_(e,n)).offp,t[l]=t[l]^i.key;for(l=0;l<a;l+=2)s=v(s,0,t,r),t[l]=s[0],t[l+1]=s[1];for(l=0;l<o;l+=2)s=v(s,0,t,r),r[l]=s[0],r[l+1]=s[1]}function k(e,t,r,i,n){var s,a,o=x.slice(),l=o.length;if(r<4||r>31){if(a=Error("Illegal number of rounds (4-31): "+r),i)return void u(i.bind(this,a));throw a}if(t.length!==m){if(a=Error("Illegal salt length: "+t.length+" != "+m),i)return void u(i.bind(this,a));throw a}r=1<<r>>>0;var c,d,f,h=0;function p(){if(n&&n(h/r),h<r)for(var s=Date.now();h<r&&(h+=1,S(e,c,d),S(t,c,d),!(Date.now()-s>100)););else{for(h=0;h<64;h++)for(f=0;f<l>>1;f++)v(o,f<<1,c,d);var a=[];for(h=0;h<l;h++)a.push((o[h]>>24&255)>>>0),a.push((o[h]>>16&255)>>>0),a.push((o[h]>>8&255)>>>0),a.push((255&o[h])>>>0);return i?void i(null,a):a}i&&u(p)}if("function"==typeof Int32Array?(c=new Int32Array(b),d=new Int32Array(w)):(c=b.slice(),d=w.slice()),!function(e,t,r,i){for(var n,s=0,a=[0,0],o=r.length,l=i.length,c=0;c<o;c++)s=(n=_(t,s)).offp,r[c]=r[c]^n.key;for(c=0,s=0;c<o;c+=2)s=(n=_(e,s)).offp,a[0]^=n.key,s=(n=_(e,s)).offp,a[1]^=n.key,a=v(a,0,r,i),r[c]=a[0],r[c+1]=a[1];for(c=0;c<l;c+=2)s=(n=_(e,s)).offp,a[0]^=n.key,s=(n=_(e,s)).offp,a[1]^=n.key,a=v(a,0,r,i),i[c]=a[0],i[c+1]=a[1]}(t,e,c,d),void 0!==i)p();else for(;;)if(void 0!==(s=p()))return s||[]}function A(e,t,r,i){if("string"!=typeof e||"string"!=typeof t){if(n=Error("Invalid string / salt: Not a string"),r)return void u(r.bind(this,n));throw n}if("$"!==t.charAt(0)||"2"!==t.charAt(1)){if(n=Error("Invalid salt version: "+t.substring(0,2)),r)return void u(r.bind(this,n));throw n}if("$"===t.charAt(2))s="\0",a=3;else{if("a"!==(s=t.charAt(2))&&"b"!==s&&"y"!==s||"$"!==t.charAt(3)){if(n=Error("Invalid salt revision: "+t.substring(2,4)),r)return void u(r.bind(this,n));throw n}a=4}if(t.charAt(a+2)>"$"){if(n=Error("Missing salt rounds"),r)return void u(r.bind(this,n));throw n}var n,s,a,o=10*parseInt(t.substring(a,a+1),10)+parseInt(t.substring(a+1,a+2),10),l=t.substring(a+3,a+25),c=function(e){for(var t,r,i=0,n=Array(d(e)),s=0,a=e.length;s<a;++s)(t=e.charCodeAt(s))<128?n[i++]=t:(t<2048?n[i++]=t>>6|192:((64512&t)==55296&&(64512&(r=e.charCodeAt(s+1)))==56320?(t=65536+((1023&t)<<10)+(1023&r),++s,n[i++]=t>>18|240,n[i++]=t>>12&63|128):n[i++]=t>>12|224,n[i++]=t>>6&63|128),n[i++]=63&t|128);return n}(e+=s>="a"?"\0":""),f=y(l,m);function h(e){var t=[];return t.push("$2"),s>="a"&&t.push(s),t.push("$"),o<10&&t.push("0"),t.push(o.toString()),t.push("$"),t.push(p(f,f.length)),t.push(p(e,4*x.length-1)),t.join("")}if(void 0===r)return h(k(c,f,o));k(c,f,o,function(e,t){e?r(e,null):r(null,h(t))},i)}let E={setRandomFallback:function(e){n=e},genSaltSync:s,genSalt:a,hashSync:o,hash:l,compareSync:function(e,t){if("string"!=typeof e||"string"!=typeof t)throw Error("Illegal arguments: "+typeof e+", "+typeof t);return 60===t.length&&c(o(e,t.substring(0,t.length-31)),t)},compare:function(e,t,r,i){function n(r){return"string"!=typeof e||"string"!=typeof t?void u(r.bind(this,Error("Illegal arguments: "+typeof e+", "+typeof t))):60!==t.length?void u(r.bind(this,null,!1)):void l(e,t.substring(0,29),function(e,i){e?r(e):r(null,c(i,t))},i)}if(!r)return new Promise(function(e,t){n(function(r,i){if(r)return void t(r);e(i)})});if("function"!=typeof r)throw Error("Illegal callback: "+typeof r);n(r)},getRounds:function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);return parseInt(e.split("$")[2],10)},getSalt:function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);if(60!==e.length)throw Error("Illegal hash length: "+e.length+" != 60");return e.substring(0,29)},truncates:function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);return d(e)>72},encodeBase64:function(e,t){return p(e,t)},decodeBase64:function(e,t){return y(e,t)}}},86280:(e,t,r)=>{Object.defineProperty(t,"b",{enumerable:!0,get:function(){return d}});let i=r(92584),n=r(29294),s=r(63033),a=r(84971),o=r(80023),l=r(68388),c=r(76926),u=(r(44523),r(8719));function d(){let e=n.workAsyncStorage.getStore(),t=s.workUnitAsyncStorage.getStore();if(e){if(t&&"after"===t.phase&&!(0,u.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(e.forceStatic)return h(i.HeadersAdapter.seal(new Headers({})));if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});else if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new o.StaticGenBailoutError(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(t)if("prerender"===t.type){var r=e.route,c=t;let i=f.get(c);if(i)return i;let n=(0,l.makeHangingPromise)(c.renderSignal,"`headers()`");return f.set(c,n),Object.defineProperties(n,{append:{value:function(){let e=`\`headers().append(${p(arguments[0])}, ...)\``,t=m(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},delete:{value:function(){let e=`\`headers().delete(${p(arguments[0])})\``,t=m(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},get:{value:function(){let e=`\`headers().get(${p(arguments[0])})\``,t=m(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},has:{value:function(){let e=`\`headers().has(${p(arguments[0])})\``,t=m(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},set:{value:function(){let e=`\`headers().set(${p(arguments[0])}, ...)\``,t=m(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},getSetCookie:{value:function(){let e="`headers().getSetCookie()`",t=m(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},forEach:{value:function(){let e="`headers().forEach(...)`",t=m(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},keys:{value:function(){let e="`headers().keys()`",t=m(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},values:{value:function(){let e="`headers().values()`",t=m(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},entries:{value:function(){let e="`headers().entries()`",t=m(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},[Symbol.iterator]:{value:function(){let e="`headers()[Symbol.iterator]()`",t=m(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}}}),n}else"prerender-ppr"===t.type?(0,a.postponeWithTracking)(e.route,"headers",t.dynamicTracking):"prerender-legacy"===t.type&&(0,a.throwToInterruptStaticGeneration)("headers",e,t);(0,a.trackDynamicDataInDynamicRender)(e,t)}return h((0,s.getExpectedRequestStore)("headers").headers)}let f=new WeakMap;function h(e){let t=f.get(e);if(t)return t;let r=Promise.resolve(e);return f.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}function p(e){return"string"==typeof e?`'${e}'`:"..."}let y=(0,c.createDedupedByCallsiteServerErrorLoggerDev)(m);function m(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})}},86897:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return a},getRedirectStatusCodeFromError:function(){return d},getRedirectTypeFromError:function(){return u},getURLFromRedirectError:function(){return c},permanentRedirect:function(){return l},redirect:function(){return o}});let i=r(52836),n=r(49026),s=r(19121).actionAsyncStorage;function a(e,t,r){void 0===r&&(r=i.RedirectStatusCode.TemporaryRedirect);let s=Object.defineProperty(Error(n.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return s.digest=n.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",s}function o(e,t){var r;throw null!=t||(t=(null==s||null==(r=s.getStore())?void 0:r.isAction)?n.RedirectType.push:n.RedirectType.replace),a(e,t,i.RedirectStatusCode.TemporaryRedirect)}function l(e,t){throw void 0===t&&(t=n.RedirectType.replace),a(e,t,i.RedirectStatusCode.PermanentRedirect)}function c(e){return(0,n.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function u(e){if(!(0,n.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function d(e){if(!(0,n.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},92584:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return s},ReadonlyHeadersError:function(){return n}});let i=r(43763);class n extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new n}}class s extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,n){if("symbol"==typeof r)return i.ReflectAdapter.get(t,r,n);let s=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===s);if(void 0!==a)return i.ReflectAdapter.get(t,a,n)},set(t,r,n,s){if("symbol"==typeof r)return i.ReflectAdapter.set(t,r,n,s);let a=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===a);return i.ReflectAdapter.set(t,o??r,n,s)},has(t,r){if("symbol"==typeof r)return i.ReflectAdapter.has(t,r);let n=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0!==s&&i.ReflectAdapter.has(t,s)},deleteProperty(t,r){if("symbol"==typeof r)return i.ReflectAdapter.deleteProperty(t,r);let n=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0===s||i.ReflectAdapter.deleteProperty(t,s)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return n.callable;default:return i.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new s(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,i]of this.entries())e.call(t,i,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},94069:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return f},ReadonlyRequestCookiesError:function(){return o},RequestCookiesAdapter:function(){return l},appendMutableCookies:function(){return d},areCookiesMutableInCurrentPhase:function(){return p},getModifiedCookieValues:function(){return u},responseCookiesToRequestCookies:function(){return m},wrapWithMutableAccessCheck:function(){return h}});let i=r(23158),n=r(43763),s=r(29294),a=r(63033);class o extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new o}}class l{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return o.callable;default:return n.ReflectAdapter.get(e,t,r)}}})}}let c=Symbol.for("next.mutated.cookies");function u(e){let t=e[c];return t&&Array.isArray(t)&&0!==t.length?t:[]}function d(e,t){let r=u(t);if(0===r.length)return!1;let n=new i.ResponseCookies(e),s=n.getAll();for(let e of r)n.set(e);for(let e of s)n.set(e);return!0}class f{static wrap(e,t){let r=new i.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let a=[],o=new Set,l=()=>{let e=s.workAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),a=r.getAll().filter(e=>o.has(e.name)),t){let e=[];for(let t of a){let r=new i.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}},u=new Proxy(r,{get(e,t,r){switch(t){case c:return a;case"delete":return function(...t){o.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),u}finally{l()}};case"set":return function(...t){o.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),u}finally{l()}};default:return n.ReflectAdapter.get(e,t,r)}}});return u}}function h(e){let t=new Proxy(e,{get(e,r,i){switch(r){case"delete":return function(...r){return y("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return y("cookies().set"),e.set(...r),t};default:return n.ReflectAdapter.get(e,r,i)}}});return t}function p(e){return"action"===e.phase}function y(e){if(!p((0,a.getExpectedRequestStore)(e)))throw new o}function m(e){let t=new i.RequestCookies(new Headers);for(let r of e.getAll())t.set(r);return t}},97576:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return u},RedirectType:function(){return n.RedirectType},forbidden:function(){return a.forbidden},notFound:function(){return s.notFound},permanentRedirect:function(){return i.permanentRedirect},redirect:function(){return i.redirect},unauthorized:function(){return o.unauthorized},unstable_rethrow:function(){return l.unstable_rethrow}});let i=r(86897),n=r(49026),s=r(62765),a=r(48976),o=r(70899),l=r(163);class c extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class u extends URLSearchParams{append(){throw new c}delete(){throw new c}set(){throw new c}sort(){throw new c}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},99933:(e,t,r)=>{Object.defineProperty(t,"U",{enumerable:!0,get:function(){return f}});let i=r(94069),n=r(23158),s=r(29294),a=r(63033),o=r(84971),l=r(80023),c=r(68388),u=r(76926),d=(r(44523),r(8719));function f(){let e="cookies",t=s.workAsyncStorage.getStore(),r=a.workUnitAsyncStorage.getStore();if(t){if(r&&"after"===r.phase&&!(0,d.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "cookies" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E88",enumerable:!1,configurable:!0});if(t.forceStatic)return p(i.RequestCookiesAdapter.seal(new n.RequestCookies(new Headers({}))));if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E398",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E157",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new l.StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`cookies\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E549",enumerable:!1,configurable:!0});if(r)if("prerender"===r.type){var u=t.route,f=r;let e=h.get(f);if(e)return e;let i=(0,c.makeHangingPromise)(f.renderSignal,"`cookies()`");return h.set(f,i),Object.defineProperties(i,{[Symbol.iterator]:{value:function(){let e="`cookies()[Symbol.iterator]()`",t=g(u,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},size:{get(){let e="`cookies().size`",t=g(u,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},get:{value:function(){let e;e=0==arguments.length?"`cookies().get()`":`\`cookies().get(${y(arguments[0])})\``;let t=g(u,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},getAll:{value:function(){let e;e=0==arguments.length?"`cookies().getAll()`":`\`cookies().getAll(${y(arguments[0])})\``;let t=g(u,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},has:{value:function(){let e;e=0==arguments.length?"`cookies().has()`":`\`cookies().has(${y(arguments[0])})\``;let t=g(u,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},set:{value:function(){let e;if(0==arguments.length)e="`cookies().set()`";else{let t=arguments[0];e=t?`\`cookies().set(${y(t)}, ...)\``:"`cookies().set(...)`"}let t=g(u,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},delete:{value:function(){let e;e=0==arguments.length?"`cookies().delete()`":1==arguments.length?`\`cookies().delete(${y(arguments[0])})\``:`\`cookies().delete(${y(arguments[0])}, ...)\``;let t=g(u,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},clear:{value:function(){let e="`cookies().clear()`",t=g(u,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}},toString:{value:function(){let e="`cookies().toString()`",t=g(u,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(u,e,t,f)}}}),i}else"prerender-ppr"===r.type?(0,o.postponeWithTracking)(t.route,e,r.dynamicTracking):"prerender-legacy"===r.type&&(0,o.throwToInterruptStaticGeneration)(e,t,r);(0,o.trackDynamicDataInDynamicRender)(t,r)}let m=(0,a.getExpectedRequestStore)(e);return p((0,i.areCookiesMutableInCurrentPhase)(m)?m.userspaceMutableCookies:m.cookies)}let h=new WeakMap;function p(e){let t=h.get(e);if(t)return t;let r=Promise.resolve(e);return h.set(e,r),Object.defineProperties(r,{[Symbol.iterator]:{value:e[Symbol.iterator]?e[Symbol.iterator].bind(e):b.bind(e)},size:{get:()=>e.size},get:{value:e.get.bind(e)},getAll:{value:e.getAll.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},delete:{value:e.delete.bind(e)},clear:{value:"function"==typeof e.clear?e.clear.bind(e):w.bind(e,r)},toString:{value:e.toString.bind(e)}}),r}function y(e){return"object"==typeof e&&null!==e&&"string"==typeof e.name?`'${e.name}'`:"string"==typeof e?`'${e}'`:"..."}let m=(0,u.createDedupedByCallsiteServerErrorLoggerDev)(g);function g(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})}function b(){return this.getAll().map(e=>[e.name,e]).values()}function w(e){for(let e of this.getAll())this.delete(e.name);return e}}};