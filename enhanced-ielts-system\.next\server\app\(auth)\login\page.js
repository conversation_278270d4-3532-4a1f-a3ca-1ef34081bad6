(()=>{var e={};e.id=72,e.ids=[72],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7766:(e,t,r)=>{"use strict";r.d(t,{cn:()=>o});var n=r(49384),s=r(82348);function o(...e){return(0,s.QP)((0,n.$)(e))}},8704:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTTPAccessErrorStatus:function(){return r},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return s},getAccessFallbackErrorTypeByStatus:function(){return a},getAccessFallbackHTTPStatus:function(){return i},isHTTPAccessFallbackError:function(){return o}});let r={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},n=new Set(Object.values(r)),s="NEXT_HTTP_ERROR_FALLBACK";function o(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===s&&n.has(Number(r))}function i(e){return Number(e.digest.split(";")[1])}function a(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8973:(e,t,r)=>{Promise.resolve().then(r.bind(r,41912))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>i});var n=r(60687),s=r(43210),o=r(7766);let i=s.forwardRef(({className:e,variant:t="default",size:r="default",...s},i)=>(0,n.jsx)("button",{className:(0,o.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{"bg-blue-600 text-white hover:bg-blue-700":"default"===t,"bg-red-600 text-white hover:bg-red-700":"destructive"===t,"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50":"outline"===t,"bg-gray-100 text-gray-900 hover:bg-gray-200":"secondary"===t,"hover:bg-gray-100 hover:text-gray-900":"ghost"===t,"text-blue-600 underline-offset-4 hover:underline":"link"===t},{"h-10 px-4 py-2":"default"===r,"h-9 rounded-md px-3":"sm"===r,"h-11 rounded-md px-8":"lg"===r,"h-10 w-10":"icon"===r},e),ref:i,...s}));i.displayName="Button"},31162:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return o}});let n=r(8704),s=r(49026);function o(e){return(0,s.isRedirectError)(e)||(0,n.isHTTPAccessFallbackError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},32941:(e,t,r)=>{Promise.resolve().then(r.bind(r,42330))},33873:e=>{"use strict";e.exports=require("path")},37882:()=>{},41912:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\TLD System\\\\enhanced-ielts-system\\\\src\\\\app\\\\(auth)\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\(auth)\\login\\page.tsx","default")},42330:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var n=r(60687),s=r(43210),o=r(99208),i=r(16189),a=r(29523),l=r(89667);function u(){let[e,t]=(0,s.useState)(""),[r,u]=(0,s.useState)(""),[d,c]=(0,s.useState)(!1),[f,p]=(0,s.useState)(""),m=(0,i.useRouter)(),b=(0,i.useSearchParams)().get("callbackUrl")||"/admin",h=async t=>{t.preventDefault(),c(!0),p("");try{let t=await (0,o.Jv)("credentials",{email:e,password:r,redirect:!1});t?.error?p("Invalid email or password"):t?.ok&&(m.push(b),m.refresh())}catch(e){console.error("Login error:",e),p("An error occurred during login")}finally{c(!1)}};return(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,n.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Enhanced IELTS System"}),(0,n.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Sign in to your account"})]}),(0,n.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:h,children:[(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)(l.p,{type:"email",placeholder:"Email address",value:e,onChange:e=>t(e.target.value),required:!0,autoComplete:"email"}),(0,n.jsx)(l.p,{type:"password",placeholder:"Password",value:r,onChange:e=>u(e.target.value),required:!0,autoComplete:"current-password"})]}),f&&(0,n.jsx)("div",{className:"text-red-600 text-sm text-center",children:f}),(0,n.jsx)(a.$,{type:"submit",disabled:d,className:"w-full",children:d?"Signing in...":"Sign In"})]})]})})}function d(){return(0,n.jsx)(s.Suspense,{fallback:(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}),children:(0,n.jsx)(u,{})})}},49026:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{REDIRECT_ERROR_CODE:function(){return s},RedirectType:function(){return o},isRedirectError:function(){return i}});let n=r(52836),s="NEXT_REDIRECT";var o=function(e){return e.push="push",e.replace="replace",e}({});function i(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,o]=t,i=t.slice(2,-2).join(";"),a=Number(t.at(-2));return r===s&&("replace"===o||"push"===o)&&"string"==typeof i&&!isNaN(a)&&a in n.RedirectStatusCode}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51846:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return n},isBailoutToCSRError:function(){return s}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class n extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function s(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},52637:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isPostpone",{enumerable:!0,get:function(){return n}});let r=Symbol.for("react.postpone");function n(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}},52836:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}});var r=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67973:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var n=r(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},76926:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return l}});let n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=s(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var a=o?Object.getOwnPropertyDescriptor(e,i):null;a&&(a.get||a.set)?Object.defineProperty(n,i,a):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}(r(61120));function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}let o={current:null},i="function"==typeof n.cache?n.cache:e=>e,a=console.warn;function l(e){return function(...t){a(e(...t))}}i(e=>{try{a(o.current)}finally{o.current=null}})},78245:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},79551:e=>{"use strict";e.exports=require("url")},85915:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>f,tree:()=>u});var n=r(65239),s=r(48088),o=r(88170),i=r.n(o),a=r(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let u={children:["",{children:["(auth)",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,41912)),"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\(auth)\\login\\page.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\(auth)\\login\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(auth)/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var n=r(60687),s=r(43210),o=r(7766);let i=s.forwardRef(({className:e,type:t,error:r,...s},i)=>(0,n.jsxs)("div",{className:"w-full",children:[(0,n.jsx)("input",{type:t,className:(0,o.cn)("flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:cursor-not-allowed disabled:opacity-50",r&&"border-red-500 focus:ring-red-500 focus:border-red-500",e),ref:i,...s}),r&&(0,n.jsx)("p",{className:"mt-1 text-sm text-red-500",children:r})]}));i.displayName="Input"},91026:()=>{},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,metadata:()=>l});var n=r(37413),s=r(22376),o=r.n(s),i=r(68726),a=r.n(i);r(61135);let l={title:"Create Next App",description:"Generated by create next app"};function u({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{className:`${o().variable} ${a().variable} antialiased`,children:e})})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4447,145,1658,9069,892],()=>r(85915));module.exports=n})();