exports.id=7864,exports.ids=[7864],exports.modules={5336:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},7766:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var a=r(49384),n=r(82348);function i(...e){return(0,n.QP)((0,a.$)(e))}},8704:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTTPAccessErrorStatus:function(){return r},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return n},getAccessFallbackErrorTypeByStatus:function(){return d},getAccessFallbackHTTPStatus:function(){return s},isHTTPAccessFallbackError:function(){return i}});let r={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},a=new Set(Object.values(r)),n="NEXT_HTTP_ERROR_FALLBACK";function i(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===n&&a.has(Number(r))}function s(e){return Number(e.digest.split(";")[1])}function d(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},17442:()=>{},31162:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return i}});let a=r(8704),n=r(49026);function i(e){return(0,n.isRedirectError)(e)||(0,a.isHTTPAccessFallbackError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},32767:(e,t,r)=>{"use strict";r.r(t),r.d(t,{accessPermissions:()=>x,accessPermissionsRelations:()=>A,aiFeedback:()=>P,aiFeedbackRelations:()=>T,candidates:()=>y,candidatesRelations:()=>h,certificateLifecycle:()=>I,certificateLifecycleRelations:()=>O,organizations:()=>_,organizationsRelations:()=>q,paymentTransactions:()=>v,paymentTransactionsRelations:()=>k,promotionalRules:()=>N,promotionalRulesRelations:()=>w,testRegistrations:()=>m,testRegistrationsRelations:()=>E,testResults:()=>b,testResultsRelations:()=>Q,users:()=>g,usersRelations:()=>R});var a=r(92768),n=r(29334),i=r(34359),s=r(9253),d=r(89697),o=r(54693),l=r(63431),u=r(91036),c=r(72170),f=r(52175),p=r(3884);let _=(0,a.cJ)("organizations",{id:(0,n.Qq)("id").primaryKey().$defaultFn(()=>(0,f.sX)()),name:(0,n.Qq)("name").notNull(),slug:(0,n.Qq)("slug").unique().notNull(),settings:(0,i.Pq)("settings").$type().default({}),features:(0,i.Pq)("features").$type().default([]),billingPlan:(0,n.Qq)("billing_plan",{enum:["basic","premium","enterprise"]}).default("basic"),status:(0,n.Qq)("status",{enum:["active","suspended","disabled"]}).default("active"),createdAt:(0,s.vE)("created_at").defaultNow().notNull(),updatedAt:(0,s.vE)("updated_at").defaultNow().notNull()},e=>({slugIdx:(0,d.Pe)("org_slug_idx").on(e.slug),statusIdx:(0,d.Pe)("org_status_idx").on(e.status)})),g=(0,a.cJ)("users",{id:(0,n.Qq)("id").primaryKey().$defaultFn(()=>(0,f.sX)()),organizationId:(0,n.Qq)("organization_id").references(()=>_.id,{onDelete:"cascade"}),email:(0,n.Qq)("email").unique().notNull(),password:(0,n.Qq)("password").notNull(),name:(0,n.Qq)("name").notNull(),role:(0,n.Qq)("role",{enum:["admin","checker"]}).notNull(),masterAdmin:(0,o.zM)("master_admin").default(!1),status:(0,n.Qq)("status",{enum:["active","inactive","suspended"]}).default("active"),lastLoginAt:(0,s.vE)("last_login_at"),createdAt:(0,s.vE)("created_at").defaultNow().notNull(),updatedAt:(0,s.vE)("updated_at").defaultNow().notNull()},e=>({emailIdx:(0,d.Pe)("user_email_idx").on(e.email),orgIdx:(0,d.Pe)("user_org_idx").on(e.organizationId),roleIdx:(0,d.Pe)("user_role_idx").on(e.role)})),y=(0,a.cJ)("candidates",{id:(0,n.Qq)("id").primaryKey().$defaultFn(()=>(0,f.sX)()),organizationId:(0,n.Qq)("organization_id").references(()=>_.id,{onDelete:"cascade"}).notNull(),fullName:(0,n.Qq)("full_name").notNull(),email:(0,n.Qq)("email"),phoneNumber:(0,n.Qq)("phone_number"),dateOfBirth:(0,s.vE)("date_of_birth"),nationality:(0,n.Qq)("nationality"),passportNumber:(0,n.Qq)("passport_number").notNull(),photoData:(0,n.Qq)("photo_data"),studentStatus:(0,o.zM)("student_status").default(!1),totalTests:(0,l.nd)("total_tests").default(0),createdAt:(0,s.vE)("created_at").defaultNow().notNull(),updatedAt:(0,s.vE)("updated_at").defaultNow().notNull()},e=>({passportIdx:(0,d.Pe)("candidate_passport_idx").on(e.passportNumber),orgIdx:(0,d.Pe)("candidate_org_idx").on(e.organizationId),nameIdx:(0,d.Pe)("candidate_name_idx").on(e.fullName),uniquePassport:(0,u.Am)("unique_passport_per_org").on(e.organizationId,e.passportNumber)})),m=(0,a.cJ)("test_registrations",{id:(0,n.Qq)("id").primaryKey().$defaultFn(()=>(0,f.sX)()),candidateId:(0,n.Qq)("candidate_id").references(()=>y.id,{onDelete:"cascade"}).notNull(),candidateNumber:(0,n.Qq)("candidate_number").notNull(),testDate:(0,s.vE)("test_date").notNull(),testCenter:(0,n.Qq)("test_center").notNull(),status:(0,n.Qq)("status",{enum:["registered","completed","cancelled"]}).default("registered"),createdAt:(0,s.vE)("created_at").defaultNow().notNull(),updatedAt:(0,s.vE)("updated_at").defaultNow().notNull()},e=>({candidateIdx:(0,d.Pe)("test_reg_candidate_idx").on(e.candidateId),dateIdx:(0,d.Pe)("test_reg_date_idx").on(e.testDate),statusIdx:(0,d.Pe)("test_reg_status_idx").on(e.status)})),b=(0,a.cJ)("test_results",{id:(0,n.Qq)("id").primaryKey().$defaultFn(()=>(0,f.sX)()),testRegistrationId:(0,n.Qq)("test_registration_id").references(()=>m.id,{onDelete:"cascade"}).notNull(),listeningScore:(0,l.nd)("listening_score"),listeningBandScore:(0,c._)("listening_band_score",{precision:2,scale:1}),readingScore:(0,l.nd)("reading_score"),readingBandScore:(0,c._)("reading_band_score",{precision:2,scale:1}),writingTask1Score:(0,c._)("writing_task1_score",{precision:2,scale:1}),writingTask2Score:(0,c._)("writing_task2_score",{precision:2,scale:1}),writingBandScore:(0,c._)("writing_band_score",{precision:2,scale:1}),speakingFluencyScore:(0,c._)("speaking_fluency_score",{precision:2,scale:1}),speakingLexicalScore:(0,c._)("speaking_lexical_score",{precision:2,scale:1}),speakingGrammarScore:(0,c._)("speaking_grammar_score",{precision:2,scale:1}),speakingPronunciationScore:(0,c._)("speaking_pronunciation_score",{precision:2,scale:1}),speakingBandScore:(0,c._)("speaking_band_score",{precision:2,scale:1}),overallBandScore:(0,c._)("overall_band_score",{precision:2,scale:1}),status:(0,n.Qq)("status",{enum:["draft","completed","verified"]}).default("draft"),enteredBy:(0,n.Qq)("entered_by").references(()=>g.id),verifiedBy:(0,n.Qq)("verified_by").references(()=>g.id),createdAt:(0,s.vE)("created_at").defaultNow().notNull(),updatedAt:(0,s.vE)("updated_at").defaultNow().notNull()},e=>({testRegIdx:(0,d.Pe)("test_result_reg_idx").on(e.testRegistrationId),statusIdx:(0,d.Pe)("test_result_status_idx").on(e.status),overallScoreIdx:(0,d.Pe)("test_result_overall_idx").on(e.overallBandScore)})),v=(0,a.cJ)("payment_transactions",{id:(0,n.Qq)("id").primaryKey().$defaultFn(()=>(0,f.sX)()),candidateId:(0,n.Qq)("candidate_id").references(()=>y.id,{onDelete:"cascade"}).notNull(),organizationId:(0,n.Qq)("organization_id").references(()=>_.id,{onDelete:"cascade"}).notNull(),amount:(0,c._)("amount",{precision:10,scale:2}).notNull(),currency:(0,n.Qq)("currency").default("UZS").notNull(),gateway:(0,n.Qq)("gateway",{enum:["click","payme","manual"]}).notNull(),gatewayTransactionId:(0,n.Qq)("gateway_transaction_id"),status:(0,n.Qq)("status",{enum:["pending","completed","failed","cancelled","refunded"]}).default("pending"),featureType:(0,n.Qq)("feature_type",{enum:["feedback","certificate","progress"]}).notNull(),resultId:(0,n.Qq)("result_id").references(()=>b.id),metadata:(0,i.Pq)("metadata").$type().default({}),createdAt:(0,s.vE)("created_at").defaultNow().notNull(),completedAt:(0,s.vE)("completed_at")},e=>({candidateIdx:(0,d.Pe)("payment_candidate_idx").on(e.candidateId),statusIdx:(0,d.Pe)("payment_status_idx").on(e.status),gatewayIdx:(0,d.Pe)("payment_gateway_idx").on(e.gateway),featureIdx:(0,d.Pe)("payment_feature_idx").on(e.featureType)})),x=(0,a.cJ)("access_permissions",{id:(0,n.Qq)("id").primaryKey().$defaultFn(()=>(0,f.sX)()),candidateId:(0,n.Qq)("candidate_id").references(()=>y.id,{onDelete:"cascade"}).notNull(),resultId:(0,n.Qq)("result_id").references(()=>b.id,{onDelete:"cascade"}),featureType:(0,n.Qq)("feature_type",{enum:["feedback","certificate","progress"]}).notNull(),accessType:(0,n.Qq)("access_type",{enum:["paid","promotional","manual"]}).notNull(),grantedBy:(0,n.Qq)("granted_by").references(()=>g.id),grantedAt:(0,s.vE)("granted_at").defaultNow().notNull(),expiresAt:(0,s.vE)("expires_at"),metadata:(0,i.Pq)("metadata").$type().default({})},e=>({candidateIdx:(0,d.Pe)("access_candidate_idx").on(e.candidateId),resultIdx:(0,d.Pe)("access_result_idx").on(e.resultId),featureIdx:(0,d.Pe)("access_feature_idx").on(e.featureType),expiryIdx:(0,d.Pe)("access_expiry_idx").on(e.expiresAt)})),N=(0,a.cJ)("promotional_rules",{id:(0,n.Qq)("id").primaryKey().$defaultFn(()=>(0,f.sX)()),organizationId:(0,n.Qq)("organization_id").references(()=>_.id,{onDelete:"cascade"}).notNull(),name:(0,n.Qq)("name").notNull(),type:(0,n.Qq)("type",{enum:["student_discount","loyalty_reward","time_based","custom"]}).notNull(),featureType:(0,n.Qq)("feature_type",{enum:["feedback","certificate","progress","all"]}).notNull(),criteria:(0,i.Pq)("criteria").$type().notNull(),benefits:(0,i.Pq)("benefits").$type().notNull(),status:(0,n.Qq)("status",{enum:["active","inactive","expired"]}).default("active"),validFrom:(0,s.vE)("valid_from").notNull(),validUntil:(0,s.vE)("valid_until"),usageLimit:(0,l.nd)("usage_limit"),usageCount:(0,l.nd)("usage_count").default(0),createdAt:(0,s.vE)("created_at").defaultNow().notNull(),updatedAt:(0,s.vE)("updated_at").defaultNow().notNull()},e=>({orgIdx:(0,d.Pe)("promo_org_idx").on(e.organizationId),statusIdx:(0,d.Pe)("promo_status_idx").on(e.status),typeIdx:(0,d.Pe)("promo_type_idx").on(e.type),validityIdx:(0,d.Pe)("promo_validity_idx").on(e.validFrom,e.validUntil)})),P=(0,a.cJ)("ai_feedback",{id:(0,n.Qq)("id").primaryKey().$defaultFn(()=>(0,f.sX)()),testResultId:(0,n.Qq)("test_result_id").references(()=>b.id,{onDelete:"cascade"}).notNull().unique(),listeningFeedback:(0,n.Qq)("listening_feedback"),readingFeedback:(0,n.Qq)("reading_feedback"),writingFeedback:(0,n.Qq)("writing_feedback"),speakingFeedback:(0,n.Qq)("speaking_feedback"),overallFeedback:(0,n.Qq)("overall_feedback"),studyRecommendations:(0,n.Qq)("study_recommendations"),strengths:(0,i.Pq)("strengths").$type().default([]),weaknesses:(0,i.Pq)("weaknesses").$type().default([]),studyPlan:(0,i.Pq)("study_plan").$type().default({}),generatedAt:(0,s.vE)("generated_at").defaultNow().notNull()},e=>({resultIdx:(0,d.Pe)("ai_feedback_result_idx").on(e.testResultId)})),I=(0,a.cJ)("certificate_lifecycle",{id:(0,n.Qq)("id").primaryKey().$defaultFn(()=>(0,f.sX)()),resultId:(0,n.Qq)("result_id").references(()=>b.id,{onDelete:"cascade"}).notNull().unique(),serialNumber:(0,n.Qq)("serial_number").unique().notNull(),generatedAt:(0,s.vE)("generated_at").defaultNow().notNull(),expiresAt:(0,s.vE)("expires_at").notNull(),status:(0,n.Qq)("status",{enum:["active","expired","deleted"]}).default("active"),deletionScheduledAt:(0,s.vE)("deletion_scheduled_at"),metadata:(0,i.Pq)("metadata").$type().default({}),createdAt:(0,s.vE)("created_at").defaultNow().notNull(),updatedAt:(0,s.vE)("updated_at").defaultNow().notNull()},e=>({resultIdx:(0,d.Pe)("cert_result_idx").on(e.resultId),serialIdx:(0,d.Pe)("cert_serial_idx").on(e.serialNumber),statusIdx:(0,d.Pe)("cert_status_idx").on(e.status),expiryIdx:(0,d.Pe)("cert_expiry_idx").on(e.expiresAt)})),q=(0,p.K1)(_,({many:e})=>({users:e(g),candidates:e(y),paymentTransactions:e(v),promotionalRules:e(N)})),R=(0,p.K1)(g,({one:e,many:t})=>({organization:e(_,{fields:[g.organizationId],references:[_.id]}),enteredResults:t(b,{relationName:"enteredBy"}),verifiedResults:t(b,{relationName:"verifiedBy"}),grantedPermissions:t(x)})),h=(0,p.K1)(y,({one:e,many:t})=>({organization:e(_,{fields:[y.organizationId],references:[_.id]}),testRegistrations:t(m),paymentTransactions:t(v),accessPermissions:t(x)})),E=(0,p.K1)(m,({one:e,many:t})=>({candidate:e(y,{fields:[m.candidateId],references:[y.id]}),testResults:t(b)})),Q=(0,p.K1)(b,({one:e})=>({testRegistration:e(m,{fields:[b.testRegistrationId],references:[m.id]}),enteredByUser:e(g,{fields:[b.enteredBy],references:[g.id],relationName:"enteredBy"}),verifiedByUser:e(g,{fields:[b.verifiedBy],references:[g.id],relationName:"verifiedBy"}),aiFeedback:e(P,{fields:[b.id],references:[P.testResultId]}),certificate:e(I,{fields:[b.id],references:[I.resultId]})})),k=(0,p.K1)(v,({one:e})=>({candidate:e(y,{fields:[v.candidateId],references:[y.id]}),organization:e(_,{fields:[v.organizationId],references:[_.id]}),result:e(b,{fields:[v.resultId],references:[b.id]})})),A=(0,p.K1)(x,({one:e})=>({candidate:e(y,{fields:[x.candidateId],references:[y.id]}),result:e(b,{fields:[x.resultId],references:[b.id]}),grantedByUser:e(g,{fields:[x.grantedBy],references:[g.id]})})),w=(0,p.K1)(N,({one:e})=>({organization:e(_,{fields:[N.organizationId],references:[_.id]})})),T=(0,p.K1)(P,({one:e})=>({testResult:e(b,{fields:[P.testResultId],references:[b.id]})})),O=(0,p.K1)(I,({one:e})=>({result:e(b,{fields:[I.resultId],references:[b.id]})}))},37882:()=>{},40228:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},49026:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{REDIRECT_ERROR_CODE:function(){return n},RedirectType:function(){return i},isRedirectError:function(){return s}});let a=r(52836),n="NEXT_REDIRECT";var i=function(e){return e.push="push",e.replace="replace",e}({});function s(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,i]=t,s=t.slice(2,-2).join(";"),d=Number(t.at(-2));return r===n&&("replace"===i||"push"===i)&&"string"==typeof s&&!isNaN(d)&&d in a.RedirectStatusCode}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51846:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return a},isBailoutToCSRError:function(){return n}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class a extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function n(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},52637:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isPostpone",{enumerable:!0,get:function(){return a}});let r=Symbol.for("react.postpone");function a(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}},52836:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}});var r=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},58448:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>s});var a=r(37413),n=r(95609),i=r.n(n);r(61135);let s={title:"IELTS Results Portal - Search Your Test Results",description:"Search and view your IELTS test results, progress analytics, AI feedback, and official certificates.",keywords:"IELTS, test results, English proficiency, band score, certificate"};function d({children:e}){return(0,a.jsx)("html",{lang:"en",children:(0,a.jsx)("body",{className:i().className,children:(0,a.jsx)("main",{children:e})})})}},61135:()=>{},67973:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var a=r(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71682:(e,t,r)=>{"use strict";r.d(t,{db:()=>s});var a=r(30686),n=r(43971),i=r(32767);let s=function(){if(!process.env.DATABASE_URL)throw Error("DATABASE_URL environment variable is required");let e=process.env.DATABASE_URL,t=(0,n.A)(e,{prepare:!1});return(0,a.f)(t,{schema:i})}()},76926:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return o}});let a=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=n(t);if(r&&r.has(e))return r.get(e);var a={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&Object.prototype.hasOwnProperty.call(e,s)){var d=i?Object.getOwnPropertyDescriptor(e,s):null;d&&(d.get||d.set)?Object.defineProperty(a,s,d):a[s]=e[s]}return a.default=e,r&&r.set(e,a),a}(r(61120));function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}let i={current:null},s="function"==typeof a.cache?a.cache:e=>e,d=console.warn;function o(e){return function(...t){d(e(...t))}}s(e=>{try{d(i.current)}finally{i.current=null}})},78245:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},86561:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},91026:()=>{},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>o});var a=r(37413),n=r(22376),i=r.n(n),s=r(68726),d=r.n(s);r(61135);let o={title:"Create Next App",description:"Generated by create next app"};function l({children:e}){return(0,a.jsx)("html",{lang:"en",children:(0,a.jsx)("body",{className:`${i().variable} ${d().variable} antialiased`,children:e})})}},95609:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c"}},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var a=r(60687);r(43210);var n=r(7766);function i({className:e,variant:t="default",...r}){return(0,a.jsx)("div",{className:(0,n.cn)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{"border-transparent bg-primary text-primary-foreground hover:bg-primary/80":"default"===t,"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80":"secondary"===t,"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80":"destructive"===t,"text-foreground":"outline"===t,"border-transparent bg-blue-100 text-blue-800":"blue"===t,"border-transparent bg-green-100 text-green-800":"green"===t,"border-transparent bg-yellow-100 text-yellow-800":"yellow"===t,"border-transparent bg-red-100 text-red-800":"red"===t,"border-transparent bg-purple-100 text-purple-800":"purple"===t,"border-transparent bg-orange-100 text-orange-800":"orange"===t,"border-transparent bg-gray-100 text-gray-800":"gray"===t},e),...r})}},99514:()=>{}};