{"/_not-found/page": "/_not-found", "/api/ai/feedback/route": "/api/ai/feedback", "/api/candidates/route": "/api/candidates", "/api/certificates/generate/route": "/api/certificates/generate", "/api/certificates/download/[id]/route": "/api/certificates/download/[id]", "/api/master/organizations/route": "/api/master/organizations", "/api/certificates/route": "/api/certificates", "/api/payments/initiate/route": "/api/payments/initiate", "/api/payments/manual/[id]/approve/route": "/api/payments/manual/[id]/approve", "/api/payments/verify/route": "/api/payments/verify", "/api/payments/manual/route": "/api/payments/manual", "/api/payments/webhook/click/route": "/api/payments/webhook/click", "/api/payments/webhook/payme/route": "/api/payments/webhook/payme", "/api/public/validate-search/route": "/api/public/validate-search", "/api/test-registrations/route": "/api/test-registrations", "/api/test-results/[id]/route": "/api/test-results/[id]", "/api/test-results/route": "/api/test-results", "/favicon.ico/route": "/favicon.ico", "/api/auth/[...nextauth]/route": "/api/auth/[...next<PERSON>h]", "/(auth)/login/page": "/login", "/page": "/", "/admin/candidates/page": "/admin/candidates", "/admin/results/page": "/admin/results", "/admin/payments/page": "/admin/payments", "/admin/dashboard/page": "/admin/dashboard", "/checker/dashboard/page": "/checker/dashboard", "/master/dashboard/page": "/master/dashboard", "/master/organizations/page": "/master/organizations", "/(public)/verify/[serial]/page": "/verify/[serial]", "/(public)/search/page": "/search"}