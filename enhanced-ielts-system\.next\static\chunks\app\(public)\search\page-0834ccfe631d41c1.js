(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9368],{257:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6874,23)),Promise.resolve().then(t.bind(t,1904)),Promise.resolve().then(t.bind(t,465))},285:(e,s,t)=>{"use strict";t.d(s,{$:()=>i});var a=t(5155),r=t(2115),l=t(6486);let i=r.forwardRef((e,s)=>{let{className:t,variant:r="default",size:i="default",...n}=e;return(0,a.jsx)("button",{className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{"bg-blue-600 text-white hover:bg-blue-700":"default"===r,"bg-red-600 text-white hover:bg-red-700":"destructive"===r,"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50":"outline"===r,"bg-gray-100 text-gray-900 hover:bg-gray-200":"secondary"===r,"hover:bg-gray-100 hover:text-gray-900":"ghost"===r,"text-blue-600 underline-offset-4 hover:underline":"link"===r},{"h-10 px-4 py-2":"default"===i,"h-9 rounded-md px-3":"sm"===i,"h-11 rounded-md px-8":"lg"===i,"h-10 w-10":"icon"===i},t),ref:s,...n})});i.displayName="Button"},465:(e,s,t)=>{"use strict";t.d(s,{PublicSearchForm:()=>g});var a=t(5155),r=t(2115),l=t(5695),i=t(2177),n=t(221),c=t(1153),d=t(285),o=t(2523),m=t(5339),x=t(1154),h=t(7924);let u=c.z.object({passport:c.z.string().min(5,"Passport/Birth certificate number must be at least 5 characters"),dateOfBirth:c.z.string().min(1,"Date of birth is required")});function g(){var e,s;let[t,c]=(0,r.useState)(!1),[g,j]=(0,r.useState)(null),f=(0,l.useRouter)(),{register:b,handleSubmit:N,formState:{errors:p}}=(0,i.mN)({resolver:(0,n.u)(u)}),v=async e=>{c(!0),j(null);try{let s=await fetch("/api/public/validate-search",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok){let e=await s.json();throw Error(e.message||"Search validation failed")}let t=new URLSearchParams({passport:e.passport.trim(),dob:e.dateOfBirth});f.push("/search?".concat(t.toString()))}catch(e){console.error("Search error:",e),j(e instanceof Error?e.message:"Search failed. Please try again.")}finally{c(!1)}};return(0,a.jsxs)("form",{onSubmit:N(v),className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Passport or Birth Certificate Number"}),(0,a.jsx)(o.p,{...b("passport"),placeholder:"Enter your passport or birth certificate number",className:"text-lg",error:null==(e=p.passport)?void 0:e.message,disabled:t}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Enter the exact number used during test registration"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Date of Birth"}),(0,a.jsx)(o.p,{...b("dateOfBirth"),type:"date",className:"text-lg",error:null==(s=p.dateOfBirth)?void 0:s.message,disabled:t}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Select your date of birth as registered"})]}),g&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(m.A,{className:"h-5 w-5 text-red-600 mr-3 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Search Error"}),(0,a.jsx)("p",{className:"text-sm text-red-700 mt-1",children:g})]})]})}),(0,a.jsx)(d.$,{type:"submit",disabled:t,className:"w-full text-lg py-3",children:t?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.A,{className:"h-5 w-5 mr-2 animate-spin"}),"Searching..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(h.A,{className:"h-5 w-5 mr-2"}),"Search My Results"]})}),(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"bg-blue-100 p-1 rounded-full mr-3 mt-0.5",children:(0,a.jsx)(m.A,{className:"h-4 w-4 text-blue-600"})}),(0,a.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,a.jsx)("h4",{className:"font-medium mb-1",children:"Privacy & Security"}),(0,a.jsx)("p",{children:"Your personal information is protected and only used to retrieve your test results. We do not store or share your search data."})]})]})}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Search Tips:"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,a.jsx)("li",{children:"• Use the exact passport/birth certificate number from your registration"}),(0,a.jsx)("li",{children:"• Check for any spaces or special characters in your document number"}),(0,a.jsx)("li",{children:"• Ensure your date of birth matches your registration details"}),(0,a.jsx)("li",{children:"• Results are available immediately after test completion"})]})]})]})}},1904:(e,s,t)=>{"use strict";t.d(s,{PublicResultsInterface:()=>eu});var a=t(5155),r=t(2115),l=t(6486),i=t(7870);let n=r.createContext(void 0);function c(){let e=r.useContext(n);if(!e)throw Error("Tabs components must be used within a Tabs provider");return e}function d(e){let{value:s,onValueChange:t,children:r,className:i}=e;return(0,a.jsx)(n.Provider,{value:{value:s,onValueChange:t},children:(0,a.jsx)("div",{className:(0,l.cn)("w-full",i),children:r})})}function o(e){let{children:s,className:t}=e;return(0,a.jsx)("div",{className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-gray-100 p-1 text-gray-500",t),children:s})}function m(e){let{value:s,children:t,className:r,disabled:i}=e,{value:n,onValueChange:d}=c(),o=n===s;return(0,a.jsx)("button",{type:"button",disabled:i,onClick:()=>!i&&d(s),className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-white transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",o?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900",r),children:t})}function x(e){let{value:s,children:t,className:r}=e,{value:i}=c();return i!==s?null:(0,a.jsx)("div",{className:(0,l.cn)("mt-2 ring-offset-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2",r),children:t})}var h=t(6126),u=t(9074),g=t(646),j=t(4186),f=t(7434),b=t(4516),N=t(1284),p=t(3109);function v(e){let{candidate:s,testData:t}=e,r=t.filter(e=>e.result&&"completed"===e.result.status),l=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),n=e=>{let s={registered:{color:"blue",label:"Registered",icon:u.A},completed:{color:"green",label:"Completed",icon:g.A},cancelled:{color:"red",label:"Cancelled",icon:j.A}}[e]||{color:"gray",label:e,icon:j.A},t=s.icon;return(0,a.jsxs)(h.E,{variant:s.color,className:"flex items-center",children:[(0,a.jsx)(t,{className:"h-3 w-3 mr-1"}),s.label]})},c=(e,s,t,r)=>{let l=s?parseFloat(s):0,n=(0,i.Fd)(l);return(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:e}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:s||"N/A"}),null!==t&&r&&(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[t,"/",r]})]})]}),s&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(h.E,{variant:n.color,className:"text-xs",children:n.level}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:(0,i.EH)(l)})]})]})};return 0===r.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"bg-blue-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center",children:(0,a.jsx)(f.A,{className:"h-8 w-8 text-blue-600"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No Completed Tests"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"You don't have any completed test results yet."}),t.length>0&&(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md mx-auto",children:[(0,a.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"Upcoming Tests"}),(0,a.jsx)("div",{className:"space-y-2",children:t.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{children:l(e.registration.testDate)}),n(e.registration.status)]},e.registration.id))})]})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Test Results"}),(0,a.jsxs)(h.E,{variant:"blue",children:[r.length," Completed Test",r.length>1?"s":""]})]}),r.map((e,s)=>{var t,r,d,o,m,x,h,g,j,f,v,y,w,S;return(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["IELTS Test #",e.registration.candidateNumber]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 mt-2 text-sm text-gray-600",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-1"}),l(e.registration.testDate)]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 mr-1"}),e.registration.testCenter]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-1",children:(null==(t=e.result)?void 0:t.overallBandScore)||"N/A"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Overall Band Score"}),n(e.registration.status)]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[c("Listening",null==(r=e.result)?void 0:r.listeningBandScore,null==(d=e.result)?void 0:d.listeningScore,40),c("Reading",null==(o=e.result)?void 0:o.readingBandScore,null==(m=e.result)?void 0:m.readingScore,40),c("Writing",null==(x=e.result)?void 0:x.writingBandScore),c("Speaking",null==(h=e.result)?void 0:h.speakingBandScore)]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsxs)("h4",{className:"font-medium text-gray-900 mb-3 flex items-center",children:[(0,a.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"Detailed Breakdown"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"font-medium text-gray-800 mb-2",children:"Writing Tasks"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Task 1:"}),(0,a.jsx)("span",{className:"font-medium",children:(null==(g=e.result)?void 0:g.writingTask1Score)||"N/A"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Task 2:"}),(0,a.jsx)("span",{className:"font-medium",children:(null==(j=e.result)?void 0:j.writingTask2Score)||"N/A"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"font-medium text-gray-800 mb-2",children:"Speaking Criteria"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Fluency & Coherence:"}),(0,a.jsx)("span",{className:"font-medium",children:(null==(f=e.result)?void 0:f.speakingFluencyScore)||"N/A"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Lexical Resource:"}),(0,a.jsx)("span",{className:"font-medium",children:(null==(v=e.result)?void 0:v.speakingLexicalScore)||"N/A"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Grammar:"}),(0,a.jsx)("span",{className:"font-medium",children:(null==(y=e.result)?void 0:y.speakingGrammarScore)||"N/A"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Pronunciation:"}),(0,a.jsx)("span",{className:"font-medium",children:(null==(w=e.result)?void 0:w.speakingPronunciationScore)||"N/A"})]})]})]})]})]}),(null==(S=e.result)?void 0:S.overallBandScore)&&(0,a.jsx)("div",{className:"mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(p.A,{className:"h-5 w-5 text-blue-600 mr-3 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"font-medium text-blue-900 mb-1",children:"Performance Summary"}),(0,a.jsx)("p",{className:"text-sm text-blue-800",children:(0,i.EH)(parseFloat(e.result.overallBandScore))})]})]})})]},e.registration.id)}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"IELTS Band Score Guide"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-800",children:"Band 9-8"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Expert to Very Good User"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-800",children:"Band 7-6"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Good to Competent User"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-800",children:"Band 5-4"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Modest to Limited User"})]})]})]})]})}var y=t(3352),w=t(285),S=t(2177),A=t(221),k=t(1153),C=t(2523),T=t(3227),P=t(1586),F=t(4357),B=t(5339),E=t(1154),D=t(6848);let I=k.z.object({paymentMethod:k.z.enum(["bank_transfer","cash"]),referenceNumber:k.z.string().min(1,"Reference number is required"),paymentDate:k.z.string().min(1,"Payment date is required"),notes:k.z.string().optional()});function L(e){var s,t;let{isOpen:l,onClose:i,candidateId:n,featureType:c,resultId:d,amount:o,currency:m,onSubmit:x}=e,[h,u]=(0,r.useState)(!1),[j,b]=(0,r.useState)(!1),{register:N,handleSubmit:p,reset:v,watch:k,formState:{errors:L}}=(0,S.mN)({resolver:(0,A.u)(I)}),U=k("paymentMethod"),R={bankName:"National Bank of Uzbekistan",accountNumber:"20208000600000000001",accountName:"TLD System LLC",swift:"NBFAUZ2X",purpose:"Payment for ".concat(c," feature - ").concat(n)},O=async e=>{u(!0);try{let s=await fetch("/api/payments/manual",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({candidateId:n,featureType:c,resultId:d,amount:o,currency:m,...e})});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to submit payment")}v(),x()}catch(e){console.error("Manual payment submission error:",e),alert("Failed to submit payment. Please try again.")}finally{u(!1)}};return(0,a.jsx)(y.a,{isOpen:l,onClose:i,size:"large",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[(0,a.jsx)(T.A,{className:"h-6 w-6 text-blue-600 mr-2"}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Manual Payment"})]}),(0,a.jsxs)("form",{onSubmit:p(O),className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-blue-50 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-blue-900",children:"Payment Amount"}),(0,a.jsx)("div",{className:"text-sm text-blue-700",children:"One-time payment"})]}),(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-900",children:(0,D.ej)(o,m)})]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Payment Method"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("label",{className:"relative",children:[(0,a.jsx)("input",{type:"radio",value:"bank_transfer",...N("paymentMethod"),className:"sr-only"}),(0,a.jsxs)("div",{className:"\n                  border-2 rounded-lg p-4 cursor-pointer transition-all\n                  ".concat("bank_transfer"===U?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300","\n                "),children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(P.A,{className:"h-5 w-5 text-gray-600 mr-2"}),(0,a.jsx)("span",{className:"font-medium",children:"Bank Transfer"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Transfer to our bank account"})]})]}),(0,a.jsxs)("label",{className:"relative",children:[(0,a.jsx)("input",{type:"radio",value:"cash",...N("paymentMethod"),className:"sr-only"}),(0,a.jsxs)("div",{className:"\n                  border-2 rounded-lg p-4 cursor-pointer transition-all\n                  ".concat("cash"===U?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300","\n                "),children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(T.A,{className:"h-5 w-5 text-gray-600 mr-2"}),(0,a.jsx)("span",{className:"font-medium",children:"Cash Payment"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Pay at our office"})]})]})]}),L.paymentMethod&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:L.paymentMethod.message})]}),"bank_transfer"===U&&(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900",children:"Bank Transfer Details"}),(0,a.jsx)(w.$,{type:"button",variant:"outline",size:"sm",onClick:()=>{let e="\nBank: ".concat(R.bankName,"\nAccount: ").concat(R.accountNumber,"\nAccount Name: ").concat(R.accountName,"\nSWIFT: ").concat(R.swift,"\nAmount: ").concat((0,D.ej)(o,m),"\nPurpose: ").concat(R.purpose,"\n    ").trim();navigator.clipboard.writeText(e),b(!0),setTimeout(()=>b(!1),2e3)},children:j?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.A,{className:"h-4 w-4 mr-1"}),"Copied"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(F.A,{className:"h-4 w-4 mr-1"}),"Copy Details"]})})]}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Bank Name:"}),(0,a.jsx)("span",{className:"font-medium",children:R.bankName})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Account Number:"}),(0,a.jsx)("span",{className:"font-medium font-mono",children:R.accountNumber})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Account Name:"}),(0,a.jsx)("span",{className:"font-medium",children:R.accountName})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"SWIFT Code:"}),(0,a.jsx)("span",{className:"font-medium font-mono",children:R.swift})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Amount:"}),(0,a.jsx)("span",{className:"font-bold text-blue-600",children:(0,D.ej)(o,m)})]}),(0,a.jsxs)("div",{className:"pt-2 border-t",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Purpose:"}),(0,a.jsx)("div",{className:"font-medium text-sm mt-1 p-2 bg-white rounded border",children:R.purpose})]})]})]}),"cash"===U&&(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900 mb-3",children:"Office Address"}),(0,a.jsxs)("div",{className:"text-sm text-gray-700",children:[(0,a.jsx)("div",{className:"font-medium",children:"TLD System Office"}),(0,a.jsx)("div",{children:"123 Amir Temur Street"}),(0,a.jsx)("div",{children:"Tashkent, Uzbekistan 100000"}),(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)("span",{className:"font-medium",children:"Office Hours:"})," Mon-Fri 9:00-18:00"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Phone:"})," +998 71 123 4567"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Reference Number"}),(0,a.jsx)(C.p,{placeholder:"Transaction/Receipt number",...N("referenceNumber"),error:null==(s=L.referenceNumber)?void 0:s.message})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Payment Date"}),(0,a.jsx)(C.p,{type:"date",...N("paymentDate"),error:null==(t=L.paymentDate)?void 0:t.message})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Additional Notes (Optional)"}),(0,a.jsx)("textarea",{...N("notes"),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Any additional information about your payment..."})]}),(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(B.A,{className:"h-5 w-5 text-yellow-600 mr-3 mt-0.5"}),(0,a.jsxs)("div",{className:"text-sm text-yellow-800",children:[(0,a.jsx)("div",{className:"font-medium mb-1",children:"Important Notice"}),(0,a.jsx)("div",{children:"Manual payments require admin approval and may take 1-3 business days to process. You will receive an email confirmation once your payment is verified and access is granted."})]})]})}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,a.jsx)(w.$,{type:"button",variant:"outline",onClick:i,disabled:h,children:"Cancel"}),(0,a.jsx)(w.$,{type:"submit",disabled:h,children:h?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(E.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Submitting..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Submit Payment"]})})]})]})]})})}var U=t(6767),R=t(3786);function O(e){let{candidateId:s,featureType:t,resultId:l,amount:i,currency:n,onPaymentComplete:c,onPaymentError:d,onProcessingChange:o}=e,[m,x]=(0,r.useState)(null),[u,f]=(0,r.useState)(!1),[b,N]=(0,r.useState)(!1),p=[{id:"click",name:"Click",icon:(0,a.jsx)(P.A,{className:"h-6 w-6"}),description:"Pay with Click - Fast and secure",processingTime:"Instant",badge:"Recommended",badgeColor:"green"},{id:"payme",name:"Payme",icon:(0,a.jsx)(U.A,{className:"h-6 w-6"}),description:"Pay with Payme - Convenient mobile payments",processingTime:"Instant",badge:"Popular",badgeColor:"blue"},{id:"manual",name:"Manual Payment",icon:(0,a.jsx)(T.A,{className:"h-6 w-6"}),description:"Bank transfer or cash payment with admin approval",processingTime:"1-3 business days",badge:"Manual Approval",badgeColor:"orange"}],v=async e=>{if("manual"===e)return void f(!0);x(e),N(!0),o(!0);try{let a=await fetch("/api/payments/initiate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({candidateId:s,featureType:t,gateway:e,resultId:l,returnUrl:"".concat(window.location.origin,"/payment/return")})}),r=await a.json();if(!a.ok)throw Error(r.error||"Payment initiation failed");if(r.paymentUrl)window.location.href=r.paymentUrl;else throw Error("No payment URL received")}catch(e){console.error("Payment initiation error:",e),d(e instanceof Error?e.message:"Payment failed"),N(!1),o(!1),x(null)}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Select Payment Method"}),p.map(e=>(0,a.jsxs)("div",{className:"\n              relative border rounded-lg p-4 cursor-pointer transition-all\n              ".concat(m===e.id?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300 hover:bg-gray-50","\n              ").concat(b&&m!==e.id?"opacity-50 pointer-events-none":"","\n            "),onClick:()=>!b&&v(e.id),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"\n                  p-2 rounded-lg\n                  ".concat(m===e.id?"bg-blue-100 text-blue-600":"bg-gray-100 text-gray-600","\n                "),children:e.icon}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:e.name}),(0,a.jsx)(h.E,{variant:e.badgeColor,children:e.badge})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center mt-1",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 text-gray-400 mr-1"}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:e.processingTime})]})]})]}),(0,a.jsx)("div",{className:"flex items-center",children:b&&m===e.id?(0,a.jsx)(E.A,{className:"h-5 w-5 text-blue-600 animate-spin"}):m===e.id?(0,a.jsx)(g.A,{className:"h-5 w-5 text-blue-600"}):(0,a.jsx)(R.A,{className:"h-5 w-5 text-gray-400"})})]}),"click"===e.id&&(0,a.jsx)("div",{className:"mt-3 pt-3 border-t border-gray-200",children:(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"• Supports all major cards and Click wallet • Instant confirmation • Secure 3D authentication"})}),"payme"===e.id&&(0,a.jsx)("div",{className:"mt-3 pt-3 border-t border-gray-200",children:(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"• Mobile-first payment experience • QR code and SMS payments • Instant confirmation"})}),"manual"===e.id&&(0,a.jsx)("div",{className:"mt-3 pt-3 border-t border-gray-200",children:(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"• Bank transfer or cash payment • Requires admin approval • Upload payment proof"})})]},e.id)),(0,a.jsx)("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 text-green-500 mt-0.5"}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"Secure Payment"}),(0,a.jsx)("div",{children:"All payments are processed securely with 256-bit SSL encryption. Your payment information is never stored on our servers."})]})]})}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 text-center",children:["By proceeding with payment, you agree to our"," ",(0,a.jsx)("a",{href:"/terms",className:"text-blue-600 hover:underline",children:"Terms of Service"})," ","and"," ",(0,a.jsx)("a",{href:"/privacy",className:"text-blue-600 hover:underline",children:"Privacy Policy"})]})]}),(0,a.jsx)(L,{isOpen:u,onClose:()=>f(!1),candidateId:s,featureType:t,resultId:l,amount:i,currency:n,onSubmit:()=>{f(!1),c()}})]})}var M=t(8564),G=t(5525),z=t(2919),$=t(4416);let W={feedback:{amount:5e4,currency:"UZS",description:"AI-powered personalized feedback and study recommendations"},certificate:{amount:3e4,currency:"UZS",description:"Official IELTS certificate with verification"},progress:{amount:25e3,currency:"UZS",description:"Detailed progress tracking and analytics"}},V={feedback:"Get detailed AI-powered feedback on your IELTS performance with personalized study recommendations",certificate:"Download your official IELTS certificate with verification QR code",progress:"Access detailed progress tracking, performance analytics, and historical comparisons"};function _(e){let{isOpen:s,onClose:t,featureType:l,candidateId:i,resultId:n,onPaymentSuccess:c,children:d}=e,[o,m]=(0,r.useState)(!1),[x,u]=(0,r.useState)(!1),f=W[l],b=V[l],N=()=>{switch(l){case"feedback":return"AI-Powered Feedback";case"certificate":return"Official Certificate";case"progress":return"Progress Analytics";default:return"Premium Feature"}};return(0,a.jsxs)(a.Fragment,{children:[d&&(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"filter blur-sm pointer-events-none",children:d}),(0,a.jsx)("div",{className:"absolute inset-0 bg-white/80 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center p-6",children:[(0,a.jsx)(z.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Premium Feature Locked"}),(0,a.jsxs)("p",{className:"text-gray-600 mb-4",children:["Unlock ",N()," to access this content"]}),(0,a.jsxs)(w.$,{onClick:()=>m(!0),children:[(0,a.jsx)(P.A,{className:"h-4 w-4 mr-2"}),"Unlock Now - ",(0,D.ej)(f.amount,f.currency)]})]})})]}),(0,a.jsx)(y.a,{isOpen:s||o,onClose:t,size:"large",children:(0,a.jsx)("div",{className:"p-6",children:o?(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Choose Payment Method"}),(0,a.jsx)(w.$,{variant:"ghost",size:"sm",onClick:()=>m(!1),children:(0,a.jsx)($.A,{className:"h-5 w-5"})})]}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:N()}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"One-time payment"})]}),(0,a.jsx)("div",{className:"text-xl font-bold text-gray-900",children:(0,D.ej)(f.amount,f.currency)})]})}),(0,a.jsx)(O,{candidateId:i,featureType:l,resultId:n,amount:f.amount,currency:f.currency,onPaymentComplete:()=>{u(!1),m(!1),t(),null==c||c()},onPaymentError:e=>{u(!1),console.error("Payment error:",e)},onProcessingChange:u}),x&&(0,a.jsx)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(j.A,{className:"h-5 w-5 text-blue-600 mr-3 animate-spin"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-blue-900",children:"Processing Payment..."}),(0,a.jsx)("div",{className:"text-sm text-blue-700",children:"Please wait while we process your payment"})]})]})})]}):(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-6",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:"flex items-center justify-center mb-4",children:(()=>{switch(l){case"feedback":return(0,a.jsx)(M.A,{className:"h-8 w-8 text-yellow-500"});case"certificate":return(0,a.jsx)(G.A,{className:"h-8 w-8 text-blue-500"});case"progress":return(0,a.jsx)(g.A,{className:"h-8 w-8 text-green-500"});default:return(0,a.jsx)(z.A,{className:"h-8 w-8 text-gray-500"})}})()}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:N()}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:b})]}),(0,a.jsx)(w.$,{variant:"ghost",size:"sm",onClick:t,className:"text-gray-400 hover:text-gray-600",children:(0,a.jsx)($.A,{className:"h-5 w-5"})})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6 mb-6",children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-gray-900 mb-2",children:(0,D.ej)(f.amount,f.currency)}),(0,a.jsx)(h.E,{variant:"blue",className:"mb-4",children:"One-time payment"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:f.description})]}),(0,a.jsxs)("div",{className:"text-left mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"What you'll get:"}),(0,a.jsx)("div",{className:"space-y-3",children:(()=>{switch(l){case"feedback":return["Detailed performance analysis","Personalized study recommendations","Weakness identification","Improvement strategies","AI-powered insights"];case"certificate":return["Official IELTS certificate","Verification QR code","Digital download","Shareable format","Lifetime validity"];case"progress":return["Detailed progress tracking","Performance comparisons","Historical analytics","Trend analysis","Goal setting tools"];default:return[]}})().map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 text-green-500 mr-3 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-gray-700",children:e})]},s))})]}),(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,a.jsx)(G.A,{className:"h-4 w-4 mr-2"}),(0,a.jsx)("span",{children:"Secure payment processing with 256-bit SSL encryption"})]})}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)(w.$,{variant:"outline",onClick:t,className:"flex-1",children:"Maybe Later"}),(0,a.jsxs)(w.$,{onClick:()=>{m(!0)},className:"flex-1",disabled:x,children:[(0,a.jsx)(P.A,{className:"h-4 w-4 mr-2"}),"Continue to Payment"]})]})]})})})]})}function Z(e){let{candidateId:s,featureType:t,resultId:a}=e,[l,i]=(0,r.useState)({hasAccess:!1,isLoading:!0,error:null}),n=async()=>{i(e=>({...e,isLoading:!0,error:null}));try{let e=new URLSearchParams({candidateId:s,featureType:t});a&&e.append("resultId",a);let r=await fetch("/api/payments/verify?".concat(e.toString()));if(!r.ok)throw Error("Failed to check access");let l=await r.json();i({hasAccess:l.hasAccess,isLoading:!1,error:null,accessType:l.accessType,expiresAt:l.expiresAt?new Date(l.expiresAt):void 0,daysRemaining:l.daysRemaining})}catch(e){i({hasAccess:!1,isLoading:!1,error:e instanceof Error?e.message:"Unknown error"})}};return(0,r.useEffect)(()=>{n()},[s,t,a]),{...l,refreshAccess:()=>{n()}}}var Q=t(7712),q=t(8500),H=t(2713),Y=t(6785),J=t(9037),K=t(9881),X=t(8832);function ee(e){var s,t;let{candidate:l,testData:i}=e,[n,c]=(0,r.useState)(!1),{hasAccess:d,isLoading:o}=Z({candidateId:l.id,featureType:"progress"}),m=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),x=(e,s)=>{let t=e-s;return .5>Math.abs(t)?{trend:"stable",icon:Q.A,color:"text-gray-500"}:t>0?{trend:"up",icon:p.A,color:"text-green-500"}:{trend:"down",icon:q.A,color:"text-red-500"}},g=()=>{if(i.length<2)return null;let e=i[0],s=i[1],t=parseFloat(e.result.overallBandScore||"0"),a=parseFloat(s.result.overallBandScore||"0");return{overall:{current:t,previous:a,trend:x(t,a)},listening:{current:parseFloat(e.result.listeningBandScore||"0"),previous:parseFloat(s.result.listeningBandScore||"0")},reading:{current:parseFloat(e.result.readingBandScore||"0"),previous:parseFloat(s.result.readingBandScore||"0")},writing:{current:parseFloat(e.result.writingBandScore||"0"),previous:parseFloat(s.result.writingBandScore||"0")},speaking:{current:parseFloat(e.result.speakingBandScore||"0"),previous:parseFloat(s.result.speakingBandScore||"0")}}},j=()=>{if(0===i.length)return(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(H.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No Progress Data"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Take more tests to see your progress over time."})]});if(1===i.length){let e=i[0];return(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(Y.A,{className:"h-12 w-12 text-blue-600 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"First Test Completed!"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Take another test to see your progress and improvement trends."}),(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md mx-auto",children:[(0,a.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"Your Current Score"}),(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:e.result.overallBandScore}),(0,a.jsxs)("div",{className:"text-sm text-blue-700",children:["Test Date: ",m(e.registration.testDate)]})]})]})})}let e=g();return e?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Overall Progress"}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-blue-600",children:e.overall.current}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Current Band Score"})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(e.overall.trend.icon,{className:"h-5 w-5 ".concat(e.overall.trend.color)}),(0,a.jsx)("span",{className:"font-medium ".concat(e.overall.trend.color),children:Math.abs(e.overall.current-e.overall.previous).toFixed(1)})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["vs Previous: ",e.overall.previous]})]})]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[{name:"Listening",data:e.listening},{name:"Reading",data:e.reading},{name:"Writing",data:e.writing},{name:"Speaking",data:e.speaking}].map(e=>{let s=x(e.data.current,e.data.previous);return(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:e.name}),(0,a.jsx)(s.icon,{className:"h-4 w-4 ".concat(s.color)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"text-xl font-bold text-gray-900",children:e.data.current}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["was ",e.data.previous]})]})]},e.name)})}),(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Test History"}),(0,a.jsx)("div",{className:"space-y-3",children:i.slice(0,3).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"bg-blue-100 p-2 rounded-full",children:(0,a.jsx)(u.A,{className:"h-4 w-4 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:m(e.registration.testDate)}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.registration.testCenter})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-gray-900",children:e.result.overallBandScore}),0===s&&(0,a.jsx)(h.E,{variant:"blue",className:"text-xs",children:"Latest"})]})]},e.result.id))})]})]}):null};return o?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):d?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Progress Analytics"}),(0,a.jsx)(h.E,{variant:"green",children:"Premium Feature"})]}),j(),i.length>=2&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-900 mb-4 flex items-center",children:[(0,a.jsx)(Y.A,{className:"h-5 w-5 mr-2"}),"Improvement Recommendations"]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(K.A,{className:"h-5 w-5 text-green-600 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-green-900",children:"Strengths"}),(0,a.jsx)("div",{className:"text-sm text-green-800",children:"Your listening skills show consistent improvement. Keep practicing with varied accents."})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(X.A,{className:"h-5 w-5 text-orange-600 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-orange-900",children:"Areas for Improvement"}),(0,a.jsx)("div",{className:"text-sm text-orange-800",children:"Focus on writing task 2 structure and coherence to boost your writing score."})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-blue-900 mb-4",children:"Goal Tracking"}),(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-blue-900",children:"Target Band Score: 7.0"}),(0,a.jsxs)("span",{className:"text-sm text-blue-700",children:["Current: ",(null==(s=i[0])?void 0:s.result.overallBandScore)||"N/A"]})]}),(0,a.jsx)("div",{className:"w-full bg-blue-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"".concat(Math.min(100,parseFloat((null==(t=i[0])?void 0:t.result.overallBandScore)||"0")/7*100),"%")}})})]})})]})]})]}):(0,a.jsx)(_,{isOpen:n,onClose:()=>c(!1),featureType:"progress",candidateId:l.id,onPaymentSuccess:()=>window.location.reload(),children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"filter blur-sm pointer-events-none",children:j()}),(0,a.jsx)("div",{className:"absolute inset-0 bg-white/80 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center p-6",children:[(0,a.jsx)(z.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Premium Progress Analytics"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Unlock detailed progress tracking, performance analytics, and historical comparisons"}),(0,a.jsxs)(w.$,{onClick:()=>c(!0),children:[(0,a.jsx)(J.A,{className:"h-4 w-4 mr-2"}),"Unlock Progress Analytics - 25,000 UZS"]})]})})]})})}var es=t(1497),et=t(463),ea=t(9376),er=t(5040);function el(e){var s,t;let{candidate:l,testData:i}=e,[n,c]=(0,r.useState)((null==(s=i[0])?void 0:s.result.id)||""),[d,o]=(0,r.useState)(!1),[m,x]=(0,r.useState)(null),[u,j]=(0,r.useState)(!1),[f,b]=(0,r.useState)(null),{hasAccess:N,isLoading:v}=Z({candidateId:l.id,featureType:"feedback",resultId:n});(0,r.useEffect)(()=>{N&&n&&y()},[N,n]);let y=async()=>{try{j(!0),b(null);let e=await fetch("/api/ai/feedback?testResultId=".concat(n));if(404===e.status)return void await S();if(!e.ok)throw Error("Failed to fetch feedback");let s=await e.json();x(s),s.listeningFeedback&&""!==s.overallFeedback||setTimeout(y,3e3)}catch(e){b(e instanceof Error?e.message:"Failed to load feedback")}finally{j(!1)}},S=async()=>{try{let e=await fetch("/api/ai/feedback",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({testResultId:n})});if(!e.ok)throw Error("Failed to generate feedback");let s=await e.json();x(s),setTimeout(y,3e3)}catch(e){b(e instanceof Error?e.message:"Failed to generate feedback")}},A=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});if(v)return(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})});if(!N)return(0,a.jsx)(_,{isOpen:d,onClose:()=>o(!1),featureType:"feedback",candidateId:l.id,resultId:n,onPaymentSuccess:()=>window.location.reload(),children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"filter blur-sm pointer-events-none",children:(()=>{if(0===i.length)return(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(es.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No Test Results"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Complete a test to receive AI-powered feedback and recommendations."})]});let e=i.find(e=>e.result.id===n)||i[0];return(0,a.jsxs)("div",{className:"space-y-6",children:[i.length>1&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Test for Feedback"}),(0,a.jsx)("select",{value:n,onChange:e=>c(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:i.map(e=>(0,a.jsxs)("option",{value:e.result.id,children:[A(e.registration.testDate)," - Band ",e.result.overallBandScore]},e.result.id))})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Performance Overview"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:e.result.listeningBandScore||"N/A"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Listening"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:e.result.readingBandScore||"N/A"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Reading"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:e.result.writingBandScore||"N/A"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Writing"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:e.result.speakingBandScore||"N/A"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Speaking"})]})]})]}),(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-blue-900 mb-4 flex items-center",children:[(0,a.jsx)(et.A,{className:"h-5 w-5 mr-2"}),"General Study Tips"]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 text-green-600 mt-0.5"}),(0,a.jsx)("div",{className:"text-sm text-blue-800",children:"Practice regularly with authentic IELTS materials"})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 text-green-600 mt-0.5"}),(0,a.jsx)("div",{className:"text-sm text-blue-800",children:"Focus on time management during practice sessions"})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 text-green-600 mt-0.5"}),(0,a.jsx)("div",{className:"text-sm text-blue-800",children:"Expand your vocabulary through reading diverse topics"})]})]})]})]})})()}),(0,a.jsx)("div",{className:"absolute inset-0 bg-white/80 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center p-6",children:[(0,a.jsx)(z.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"AI-Powered Feedback"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Get detailed AI analysis, personalized study recommendations, and improvement strategies"}),(0,a.jsxs)(w.$,{onClick:()=>o(!0),children:[(0,a.jsx)(M.A,{className:"h-4 w-4 mr-2"}),"Unlock AI Feedback - 50,000 UZS"]})]})})]})});let k=i.find(e=>e.result.id===n)||i[0];return u?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(E.A,{className:"h-8 w-8 animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:(null==m?void 0:m.overallFeedback)===""?"AI is analyzing your performance...":"Loading feedback..."})]})}):f?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(B.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-red-600 mb-4",children:f}),(0,a.jsx)(w.$,{onClick:y,variant:"outline",children:"Try Again"})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"AI-Powered Feedback"}),(0,a.jsx)(h.E,{variant:"green",children:"Premium Feature"})]}),i.length>1&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Test for Detailed Feedback"}),(0,a.jsx)("select",{value:n,onChange:e=>c(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:i.map(e=>(0,a.jsxs)("option",{value:e.result.id,children:[A(e.registration.testDate)," - Band ",e.result.overallBandScore]},e.result.id))})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-purple-900 mb-4 flex items-center",children:[(0,a.jsx)(ea.A,{className:"h-5 w-5 mr-2"}),"AI Performance Analysis"]}),(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-purple-900 mb-2",children:"Overall Assessment"}),(0,a.jsx)("p",{className:"text-sm text-purple-800",children:(null==m?void 0:m.overallFeedback)||"Based on your band score of ".concat(k.result.overallBandScore,", you demonstrate ").concat(parseFloat(k.result.overallBandScore||"0")>=7?" good":" developing"," English proficiency.")})]})})]}),m&&(m.strengths.length>0||m.weaknesses.length>0)&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[m.strengths.length>0&&(0,a.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-900 mb-4 flex items-center",children:[(0,a.jsx)(p.A,{className:"h-5 w-5 mr-2"}),"Strengths"]}),(0,a.jsx)("div",{className:"space-y-2",children:m.strengths.map((e,s)=>(0,a.jsx)(h.E,{variant:"secondary",className:"bg-green-100 text-green-800 mr-2 mb-2",children:e},s))})]}),m.weaknesses.length>0&&(0,a.jsxs)("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-orange-900 mb-4 flex items-center",children:[(0,a.jsx)(Y.A,{className:"h-5 w-5 mr-2"}),"Areas for Improvement"]}),(0,a.jsx)("div",{className:"space-y-2",children:m.weaknesses.map((e,s)=>(0,a.jsx)(h.E,{variant:"secondary",className:"bg-orange-100 text-orange-800 mr-2 mb-2",children:e},s))})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,a.jsxs)("h4",{className:"font-medium text-gray-900 mb-3 flex items-center",children:[(0,a.jsx)(Y.A,{className:"h-4 w-4 mr-2 text-blue-600"}),"Listening Analysis"]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Band Score"}),(0,a.jsx)("span",{className:"font-bold text-blue-600",children:k.result.listeningBandScore})]}),(0,a.jsx)("div",{className:"bg-blue-50 p-3 rounded",children:(0,a.jsx)("p",{className:"text-xs text-blue-800",children:(null==m?void 0:m.listeningFeedback)||"Good comprehension of main ideas and supporting details. Practice with different accents and faster speech patterns."})})]})]}),(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,a.jsxs)("h4",{className:"font-medium text-gray-900 mb-3 flex items-center",children:[(0,a.jsx)(er.A,{className:"h-4 w-4 mr-2 text-green-600"}),"Reading Analysis"]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Band Score"}),(0,a.jsx)("span",{className:"font-bold text-green-600",children:k.result.readingBandScore})]}),(0,a.jsx)("div",{className:"bg-green-50 p-3 rounded",children:(0,a.jsx)("p",{className:"text-xs text-green-800",children:(null==m?void 0:m.readingFeedback)||"Effective skimming and scanning techniques. Work on inference and understanding implicit meaning."})})]})]}),(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,a.jsxs)("h4",{className:"font-medium text-gray-900 mb-3 flex items-center",children:[(0,a.jsx)(es.A,{className:"h-4 w-4 mr-2 text-purple-600"}),"Writing Analysis"]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Band Score"}),(0,a.jsx)("span",{className:"font-bold text-purple-600",children:k.result.writingBandScore})]}),(0,a.jsx)("div",{className:"bg-purple-50 p-3 rounded",children:(0,a.jsx)("p",{className:"text-xs text-purple-800",children:(null==m?void 0:m.writingFeedback)||"Clear task response and good organization. Enhance lexical resource and grammatical range."})})]})]}),(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,a.jsxs)("h4",{className:"font-medium text-gray-900 mb-3 flex items-center",children:[(0,a.jsx)(M.A,{className:"h-4 w-4 mr-2 text-yellow-600"}),"Speaking Analysis"]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Band Score"}),(0,a.jsx)("span",{className:"font-bold text-yellow-600",children:k.result.speakingBandScore})]}),(0,a.jsx)("div",{className:"bg-yellow-50 p-3 rounded",children:(0,a.jsx)("p",{className:"text-xs text-yellow-800",children:(null==m?void 0:m.speakingFeedback)||"Good fluency and natural conversation flow. Focus on pronunciation clarity and intonation."})})]})]})]}),m&&(m.studyRecommendations||m.studyPlan)&&(0,a.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-900 mb-4 flex items-center",children:[(0,a.jsx)(er.A,{className:"h-5 w-5 mr-2"}),"Study Recommendations"]}),m.studyRecommendations&&(0,a.jsx)("div",{className:"bg-white rounded-lg p-4 mb-4",children:(0,a.jsx)("p",{className:"text-sm text-green-800",children:m.studyRecommendations})}),(null==(t=m.studyPlan)?void 0:t.plan)&&(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-green-900 mb-2",children:"Personalized Study Plan"}),(0,a.jsx)("div",{className:"whitespace-pre-line text-sm text-green-800",children:m.studyPlan.plan})]})]}),!m&&(0,a.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-900 mb-4 flex items-center",children:[(0,a.jsx)(p.A,{className:"h-5 w-5 mr-2"}),"General Study Plan"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-green-900 mb-2",children:"Week 1-2: Focus on Writing"}),(0,a.jsxs)("ul",{className:"text-sm text-green-800 space-y-1",children:[(0,a.jsx)("li",{children:"• Practice Task 2 essay structure daily"}),(0,a.jsx)("li",{children:"• Learn 10 new academic vocabulary words per day"}),(0,a.jsx)("li",{children:"• Complete 3 practice essays with time limits"})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-green-900 mb-2",children:"Week 3-4: Speaking Enhancement"}),(0,a.jsxs)("ul",{className:"text-sm text-green-800 space-y-1",children:[(0,a.jsx)("li",{children:"• Record yourself speaking for 2 minutes daily"}),(0,a.jsx)("li",{children:"• Practice pronunciation with tongue twisters"}),(0,a.jsx)("li",{children:"• Engage in conversation practice with native speakers"})]})]})]})]})]})}var ei=t(7939),en=t(1788),ec=t(6516);function ed(e){var s;let{candidate:t,testData:l}=e,[i,n]=(0,r.useState)((null==(s=l[0])?void 0:s.result.id)||""),[c,d]=(0,r.useState)(!1),[o,m]=(0,r.useState)(null),[x,u]=(0,r.useState)(!1),[j,b]=(0,r.useState)(null),[N,p]=(0,r.useState)(!1),{hasAccess:v,isLoading:y}=Z({candidateId:t.id,featureType:"certificate",resultId:i});(0,r.useEffect)(()=>{v&&i&&S()},[v,i]);let S=async()=>{try{u(!0),b(null);let e=await fetch("/api/certificates?resultId=".concat(i));if(404===e.status)return void m(null);if(!e.ok)throw Error("Failed to fetch certificate");let s=await e.json();m(s)}catch(e){b(e instanceof Error?e.message:"Failed to load certificate")}finally{u(!1)}},A=async()=>{try{p(!0),b(null);let e=await fetch("/api/certificates/generate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({resultId:i})});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to generate certificate")}let s=await e.json();m({id:s.certificateId,serialNumber:s.serialNumber,generatedAt:new Date().toISOString(),expiresAt:new Date(Date.now()+15552e6).toISOString(),status:"active",downloadUrl:s.downloadUrl})}catch(e){b(e instanceof Error?e.message:"Failed to generate certificate")}finally{p(!1)}},k=async()=>{if(o)try{let e=await fetch(o.downloadUrl);if(!e.ok)throw Error("Download failed");let s=await e.blob(),t=window.URL.createObjectURL(s),a=document.createElement("a");a.href=t,a.download="IELTS_Certificate_".concat(o.serialNumber,".pdf"),document.body.appendChild(a),a.click(),window.URL.revokeObjectURL(t),document.body.removeChild(a)}catch(e){b("Failed to download certificate")}},C=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});if(y)return(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})});if(!v)return(0,a.jsx)(_,{isOpen:c,onClose:()=>d(!1),featureType:"certificate",candidateId:t.id,resultId:i,onPaymentSuccess:()=>window.location.reload(),children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"filter blur-sm pointer-events-none",children:(()=>{if(0===l.length)return(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(J.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No Certificates Available"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Complete a test to generate your official IELTS certificate."})]});let e=l.find(e=>e.result.id===i)||l[0];return(0,a.jsxs)("div",{className:"space-y-6",children:[l.length>1&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Test for Certificate"}),(0,a.jsx)("select",{value:i,onChange:e=>n(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:l.map(e=>(0,a.jsxs)("option",{value:e.result.id,children:[C(e.registration.testDate)," - Band ",e.result.overallBandScore]},e.result.id))})]}),(0,a.jsx)("div",{className:"bg-gradient-to-br from-blue-50 to-indigo-100 border-2 border-blue-200 rounded-lg p-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"bg-blue-600 p-3 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center",children:(0,a.jsx)(J.A,{className:"h-8 w-8 text-white"})}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:"IELTS Test Certificate"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Official certification of your English proficiency"}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 mb-6 shadow-sm",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900 mb-4",children:t.fullName}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Test Date:"}),(0,a.jsx)("div",{className:"font-medium",children:C(e.registration.testDate)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Test Center:"}),(0,a.jsx)("div",{className:"font-medium",children:e.registration.testCenter})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Candidate Number:"}),(0,a.jsx)("div",{className:"font-medium",children:e.registration.candidateNumber})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Overall Band Score:"}),(0,a.jsx)("div",{className:"font-bold text-blue-600 text-lg",children:e.result.overallBandScore})]})]})]}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"This is a preview. Unlock the full certificate with verification features."})]})}),(0,a.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-yellow-900 mb-4",children:"Certificate Features"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(G.A,{className:"h-5 w-5 text-yellow-600 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-yellow-900",children:"Official Verification"}),(0,a.jsx)("div",{className:"text-sm text-yellow-800",children:"Digitally signed and verifiable"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(ei.A,{className:"h-5 w-5 text-yellow-600 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-yellow-900",children:"QR Code Verification"}),(0,a.jsx)("div",{className:"text-sm text-yellow-800",children:"Instant verification via QR code"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(en.A,{className:"h-5 w-5 text-yellow-600 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-yellow-900",children:"PDF Download"}),(0,a.jsx)("div",{className:"text-sm text-yellow-800",children:"High-quality PDF format"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(ec.A,{className:"h-5 w-5 text-yellow-600 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-yellow-900",children:"Shareable Link"}),(0,a.jsx)("div",{className:"text-sm text-yellow-800",children:"Share with institutions"})]})]})]})]})]})})()}),(0,a.jsx)("div",{className:"absolute inset-0 bg-white/80 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center p-6",children:[(0,a.jsx)(z.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Official IELTS Certificate"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Download your official certificate with verification QR code and digital signature"}),(0,a.jsxs)(w.$,{onClick:()=>d(!0),children:[(0,a.jsx)(J.A,{className:"h-4 w-4 mr-2"}),"Get Certificate - 30,000 UZS"]})]})})]})});let T=l.find(e=>e.result.id===i)||l[0];return x?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(E.A,{className:"h-8 w-8 animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading certificate information..."})]})}):j?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(B.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-red-600 mb-4",children:j}),(0,a.jsx)(w.$,{onClick:S,variant:"outline",children:"Try Again"})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Official Certificate"}),(0,a.jsx)(h.E,{variant:"green",children:"Premium Feature"})]}),l.length>1&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Test for Certificate"}),(0,a.jsx)("select",{value:i,onChange:e=>n(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:l.map(e=>(0,a.jsxs)("option",{value:e.result.id,children:[C(e.registration.testDate)," - Band ",e.result.overallBandScore]},e.result.id))})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-indigo-100 border-2 border-blue-200 rounded-lg p-8",children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)("div",{className:"bg-blue-600 p-3 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center",children:(0,a.jsx)(J.A,{className:"h-8 w-8 text-white"})}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Official IELTS Certificate"}),(0,a.jsx)(h.E,{variant:"green",className:"mb-4",children:"Verified & Authentic"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-8 shadow-lg border-2 border-gray-200",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-blue-900 mb-2",children:"IELTS"}),(0,a.jsx)("h3",{className:"text-xl text-gray-700",children:"International English Language Testing System"}),(0,a.jsx)("div",{className:"w-24 h-1 bg-blue-600 mx-auto mt-4"})]}),(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("p",{className:"text-lg text-gray-700 mb-4",children:"This is to certify that"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:t.fullName}),(0,a.jsx)("p",{className:"text-lg text-gray-700 mb-2",children:"has achieved an overall band score of"}),(0,a.jsx)("div",{className:"text-4xl font-bold text-blue-600 mb-4",children:T.result.overallBandScore}),(0,a.jsx)("p",{className:"text-lg text-gray-700",children:"in the IELTS test"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 gap-4 mb-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-gray-900",children:T.result.listeningBandScore}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Listening"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-gray-900",children:T.result.readingBandScore}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Reading"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-gray-900",children:T.result.writingBandScore}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Writing"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-gray-900",children:T.result.speakingBandScore}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Speaking"})]})]}),(0,a.jsx)("div",{className:"border-t border-gray-200 pt-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Test Date:"}),(0,a.jsx)("div",{className:"font-medium",children:C(T.registration.testDate)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Test Center:"}),(0,a.jsx)("div",{className:"font-medium",children:T.registration.testCenter})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Candidate Number:"}),(0,a.jsx)("div",{className:"font-medium",children:T.registration.candidateNumber})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Certificate ID:"}),(0,a.jsxs)("div",{className:"font-medium",children:["CERT-",T.result.id.slice(-8).toUpperCase()]})]})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-between mt-6 pt-6 border-t border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(ei.A,{className:"h-8 w-8 text-gray-600"}),(0,a.jsxs)("div",{className:"text-xs text-gray-600",children:[(0,a.jsx)("div",{children:"Scan to verify"}),(0,a.jsx)("div",{children:"authenticity"})]})]}),(0,a.jsxs)("div",{className:"text-right text-xs text-gray-600",children:[(0,a.jsxs)("div",{children:["Issued: ",C(T.result.createdAt)]}),(0,a.jsx)("div",{children:"Valid for 2 years"})]})]})]})]}),o?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 border",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Certificate Information"}),(0,a.jsx)(h.E,{variant:"active"===o.status?"default":"destructive",className:"active"===o.status?"bg-green-100 text-green-800":"",children:o.status.toUpperCase()})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-500",children:"Serial Number"}),(0,a.jsx)("p",{className:"font-mono text-lg",children:o.serialNumber})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-500",children:"Issue Date"}),(0,a.jsx)("p",{className:"text-lg",children:new Date(o.generatedAt).toLocaleDateString()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-500",children:"Expiry Date"}),(0,a.jsx)("p",{className:"text-lg",children:new Date(o.expiresAt).toLocaleDateString()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-500",children:"Validity"}),(0,a.jsx)("p",{className:"text-lg ".concat(new Date>new Date(o.expiresAt)?"text-orange-600":"text-green-600"),children:new Date>new Date(o.expiresAt)?"Expired":"Valid"})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,a.jsxs)(w.$,{onClick:k,className:"flex-1 sm:flex-none",disabled:"deleted"===o.status,children:[(0,a.jsx)(en.A,{className:"h-4 w-4 mr-2"}),"Download PDF"]}),(0,a.jsxs)(w.$,{variant:"outline",className:"flex-1 sm:flex-none",onClick:()=>window.open("/verify/".concat(o.serialNumber),"_blank"),children:[(0,a.jsx)(R.A,{className:"h-4 w-4 mr-2"}),"Verify Online"]}),(0,a.jsxs)(w.$,{variant:"outline",className:"flex-1 sm:flex-none",onClick:()=>{let e="".concat(window.location.origin,"/verify/").concat(o.serialNumber);navigator.clipboard.writeText(e)},children:[(0,a.jsx)(ec.A,{className:"h-4 w-4 mr-2"}),"Copy Link"]})]})]}):(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(f.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No Certificate Generated"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Generate an official IELTS certificate for your test results."}),(0,a.jsx)(w.$,{onClick:A,disabled:N,className:"flex items-center gap-2",children:N?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(E.A,{className:"h-4 w-4 animate-spin"}),"Generating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.A,{className:"h-4 w-4"}),"Generate Certificate"]})})]}),o&&(0,a.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-900 mb-4 flex items-center",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 mr-2"}),"Certificate Verification"]}),(0,a.jsxs)("div",{className:"space-y-3 text-sm text-green-800",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Verification URL:"}),(0,a.jsxs)("span",{className:"ml-2 font-mono bg-white px-2 py-1 rounded",children:[window.location.origin,"/verify/",o.serialNumber]})]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Serial Number:"})," ",o.serialNumber]}),(0,a.jsx)("p",{children:"This certificate is digitally signed and can be verified by institutions worldwide. The QR code provides instant verification of authenticity."})]})]}),(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-blue-900 mb-4",children:"Certificate Information"}),(0,a.jsxs)("div",{className:"space-y-3 text-sm text-blue-800",children:[(0,a.jsx)("p",{children:"• This certificate is an official document verifying your IELTS test results."}),(0,a.jsx)("p",{children:"• The certificate is valid for 6 months from the test date."}),(0,a.jsx)("p",{children:"• You can verify the authenticity of this certificate using the QR code or serial number."}),(0,a.jsx)("p",{children:"• The certificate includes a secure verification system to prevent fraud."}),(0,a.jsx)("p",{children:"• After expiration, the certificate will be automatically deleted from our system."})]})]})]})}var eo=t(1007),em=t(7550),ex=t(6874),eh=t.n(ex);function eu(e){var s,t,l,i,n;let{candidate:c,organization:h,testData:g}=e,[j,N]=(0,r.useState)("results"),y=g.filter(e=>e.result&&"completed"===e.result.status),w=y[0],S=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});return(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg mb-6",children:[(0,a.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"bg-blue-100 p-3 rounded-full",children:(0,a.jsx)(eo.A,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:c.fullName}),(0,a.jsx)("p",{className:"text-gray-600",children:"IELTS Test Results"})]})]}),(0,a.jsx)(eh(),{href:"/search",children:(0,a.jsxs)("button",{className:"inline-flex items-center px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:[(0,a.jsx)(em.A,{className:"h-4 w-4 mr-2"}),"New Search"]})})]}),(0,a.jsxs)("div",{className:"mt-6 grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(f.A,{className:"h-4 w-4 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Document Number"}),(0,a.jsx)("div",{className:"font-medium",children:c.passportNumber})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Date of Birth"}),(0,a.jsx)("div",{className:"font-medium",children:S(c.dateOfBirth)})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Test Center"}),(0,a.jsx)("div",{className:"font-medium",children:(null==h?void 0:h.name)||"Unknown"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Total Tests"}),(0,a.jsx)("div",{className:"font-medium",children:c.totalTests})]})]})]})]}),w&&(0,a.jsxs)("div",{className:"p-6 bg-gradient-to-r from-blue-50 to-indigo-50",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Latest Test Result"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:(null==(s=w.result)?void 0:s.overallBandScore)||"N/A"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Overall"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-xl font-semibold text-gray-900",children:(null==(t=w.result)?void 0:t.listeningBandScore)||"N/A"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Listening"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-xl font-semibold text-gray-900",children:(null==(l=w.result)?void 0:l.readingBandScore)||"N/A"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Reading"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-xl font-semibold text-gray-900",children:(null==(i=w.result)?void 0:i.writingBandScore)||"N/A"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Writing"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-xl font-semibold text-gray-900",children:(null==(n=w.result)?void 0:n.speakingBandScore)||"N/A"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Speaking"})]})]}),(0,a.jsxs)("div",{className:"mt-3 text-sm text-gray-600",children:["Test Date: ",S(w.registration.testDate)," • Test Center: ",w.registration.testCenter]})]})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-lg",children:(0,a.jsxs)(d,{value:j,onValueChange:N,children:[(0,a.jsxs)(o,{className:"grid w-full grid-cols-4 bg-gray-50 p-1 rounded-t-lg",children:[(0,a.jsxs)(m,{value:"results",className:"flex items-center space-x-2",children:[(0,a.jsx)(f.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Results"})]}),(0,a.jsxs)(m,{value:"progress",className:"flex items-center space-x-2",children:[(0,a.jsx)(p.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Progress"})]}),(0,a.jsxs)(m,{value:"feedback",className:"flex items-center space-x-2",children:[(0,a.jsx)(es.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Feedback"})]}),(0,a.jsxs)(m,{value:"certificate",className:"flex items-center space-x-2",children:[(0,a.jsx)(J.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Certificate"})]})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)(x,{value:"results",className:"mt-0",children:(0,a.jsx)(v,{candidate:c,testData:g})}),(0,a.jsx)(x,{value:"progress",className:"mt-0",children:(0,a.jsx)(ee,{candidate:c,testData:y})}),(0,a.jsx)(x,{value:"feedback",className:"mt-0",children:(0,a.jsx)(el,{candidate:c,testData:y})}),(0,a.jsx)(x,{value:"certificate",className:"mt-0",children:(0,a.jsx)(ed,{candidate:c,testData:y})})]})]})}),(0,a.jsx)("div",{className:"mt-8 text-center text-gray-600",children:(0,a.jsx)("p",{className:"text-sm",children:"Results are updated in real-time. For questions about your results, contact your test center directly."})})]})}},2523:(e,s,t)=>{"use strict";t.d(s,{p:()=>i});var a=t(5155),r=t(2115),l=t(6486);let i=r.forwardRef((e,s)=>{let{className:t,type:r,error:i,...n}=e;return(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)("input",{type:r,className:(0,l.cn)("flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:cursor-not-allowed disabled:opacity-50",i&&"border-red-500 focus:ring-red-500 focus:border-red-500",t),ref:s,...n}),i&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-500",children:i})]})});i.displayName="Input"},3352:(e,s,t)=>{"use strict";t.d(s,{a:()=>d});var a=t(5155),r=t(2115),l=t(5939),i=t(280),n=t(4416),c=t(6486);function d(e){let{isOpen:s,onClose:t,title:d,children:o,size:m="md"}=e;return(0,a.jsx)(l.e,{appear:!0,show:s,as:r.Fragment,children:(0,a.jsxs)(i.lG,{as:"div",className:"relative z-50",onClose:t,children:[(0,a.jsx)(l.e.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,a.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,a.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,a.jsx)(l.e.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,a.jsxs)(i.lG.Panel,{className:(0,c.cn)("w-full transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl"}[m]),children:[d&&(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)(i.lG.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:d}),(0,a.jsx)("button",{type:"button",className:"rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500",onClick:t,children:(0,a.jsx)(n.A,{className:"h-5 w-5"})})]}),o]})})})})]})})}},6126:(e,s,t)=>{"use strict";t.d(s,{E:()=>l});var a=t(5155);t(2115);var r=t(6486);function l(e){let{className:s,variant:t="default",...l}=e;return(0,a.jsx)("div",{className:(0,r.cn)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{"border-transparent bg-primary text-primary-foreground hover:bg-primary/80":"default"===t,"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80":"secondary"===t,"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80":"destructive"===t,"text-foreground":"outline"===t,"border-transparent bg-blue-100 text-blue-800":"blue"===t,"border-transparent bg-green-100 text-green-800":"green"===t,"border-transparent bg-yellow-100 text-yellow-800":"yellow"===t,"border-transparent bg-red-100 text-red-800":"red"===t,"border-transparent bg-purple-100 text-purple-800":"purple"===t,"border-transparent bg-orange-100 text-orange-800":"orange"===t,"border-transparent bg-gray-100 text-gray-800":"gray"===t},s),...l})}},6486:(e,s,t)=>{"use strict";t.d(s,{cn:()=>l});var a=t(2596),r=t(9688);function l(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.QP)((0,a.$)(s))}},6848:(e,s,t)=>{"use strict";function a(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"UZS";return"UZS"===s?new Intl.NumberFormat("uz-UZ",{style:"currency",currency:"UZS",minimumFractionDigits:0}).format(e):new Intl.NumberFormat("en-US",{style:"currency",currency:s}).format(e)}t.d(s,{ej:()=>a})},7870:(e,s,t)=>{"use strict";t.d(s,{AI:()=>i,EH:()=>c,Fd:()=>d,dN:()=>n});let a={39:9,38:9,37:8.5,36:8.5,35:8,34:8,33:7.5,32:7.5,31:7,30:7,29:6.5,28:6.5,27:6,26:6,25:5.5,24:5.5,23:5,22:5,21:4.5,20:4.5,19:4,18:4,17:3.5,16:3.5,15:3,14:3,13:2.5,12:2.5,11:2,10:2,9:1.5,8:1.5,7:1,6:1,5:.5,4:.5,3:0,2:0,1:0,0:0},r={39:9,38:9,37:8.5,36:8.5,35:8,34:8,33:7.5,32:7.5,31:7,30:7,29:6.5,28:6.5,27:6,26:6,25:5.5,24:5.5,23:5,22:5,21:4.5,20:4.5,19:4,18:4,17:3.5,16:3.5,15:3,14:3,13:2.5,12:2.5,11:2,10:2,9:1.5,8:1.5,7:1,6:1,5:.5,4:.5,3:0,2:0,1:0,0:0},l={40:9,39:8.5,38:8.5,37:8,36:8,35:7.5,34:7.5,33:7,32:7,31:6.5,30:6.5,29:6,28:6,27:5.5,26:5.5,25:5,24:5,23:4.5,22:4.5,21:4,20:4,19:3.5,18:3.5,17:3,16:3,15:2.5,14:2.5,13:2,12:2,11:1.5,10:1.5,9:1,8:1,7:.5,6:.5,5:0,4:0,3:0,2:0,1:0,0:0};function i(e,s){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"academic",i=Math.max(0,Math.min(40,Math.floor(s)));return"listening"===e?a[i]||0:"reading"===e&&("academic"===t?r:l)[i]||0}function n(e,s,t,a){return Math.round((e+s+t+a)/4*2)/2}function c(e){return({9:"Expert User",8.5:"Very Good User",8:"Very Good User",7.5:"Good User",7:"Good User",6.5:"Competent User",6:"Competent User",5.5:"Modest User",5:"Modest User",4.5:"Limited User",4:"Limited User",3.5:"Extremely Limited User",3:"Extremely Limited User",2.5:"Intermittent User",2:"Intermittent User",1.5:"Non User",1:"Non User",.5:"Did not attempt the test",0:"Did not attempt the test"})[e]||"Invalid Score"}function d(e){return e>=8.5?{level:"Excellent",color:"green",description:"Very high proficiency level"}:e>=7?{level:"Good",color:"blue",description:"Good proficiency level"}:e>=6?{level:"Competent",color:"yellow",description:"Competent proficiency level"}:e>=5?{level:"Modest",color:"orange",description:"Modest proficiency level"}:{level:"Limited",color:"red",description:"Limited proficiency level"}}}},e=>{var s=s=>e(e.s=s);e.O(0,[4277,6874,4343,6049,8441,1684,7358],()=>s(257)),_N_E=e.O()}]);