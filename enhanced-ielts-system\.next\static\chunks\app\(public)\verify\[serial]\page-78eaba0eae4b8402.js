(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[578],{646:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},847:(e,t,r)=>{Promise.resolve().then(r.bind(r,3961))},1007:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1243:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},3961:(e,t,r)=>{"use strict";r.d(t,{CertificateVerificationDisplay:()=>x});var a=r(5155),s=r(6126),l=r(646),i=r(1243),n=r(4861),d=r(1007),c=r(9037),o=r(9074);function x(e){let{certificate:t,isValid:r,isExpired:x}=e;return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex justify-center mb-4",children:r?(0,a.jsx)(l.A,{className:"h-8 w-8 text-green-600"}):x?(0,a.jsx)(i.A,{className:"h-8 w-8 text-orange-600"}):(0,a.jsx)(n.A,{className:"h-8 w-8 text-red-600"})}),(0,a.jsx)("h1",{className:"text-3xl font-bold ".concat(r?"text-green-600":x?"text-orange-600":"text-red-600"),children:r?"Valid Certificate":x?"Expired Certificate":"Invalid Certificate"}),(0,a.jsxs)("p",{className:"text-gray-600 mt-2",children:["Certificate Serial Number: ",t.serialNumber]})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,a.jsx)(d.A,{className:"h-5 w-5 text-blue-600"}),(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Candidate Information"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Full Name"}),(0,a.jsx)("p",{className:"text-lg font-semibold",children:t.metadata.candidateName})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Test Date"}),(0,a.jsx)("p",{className:"text-lg",children:t.metadata.testDate})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Test Center"}),(0,a.jsx)("p",{className:"text-lg",children:t.metadata.organizationName})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,a.jsx)(c.A,{className:"h-5 w-5 text-green-600"}),(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Test Results"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Certificate Type"}),(0,a.jsx)("p",{className:"text-lg",children:t.metadata.certificateType})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Overall Band Score"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-2xl font-bold text-green-600",children:t.metadata.overallBandScore}),(0,a.jsxs)(s.E,{variant:"secondary",className:"bg-green-100 text-green-800",children:["Band ",t.metadata.overallBandScore]})]})]})]})]})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,a.jsx)(o.A,{className:"h-5 w-5 text-blue-600"}),(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Certificate Status"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Issue Date"}),(0,a.jsx)("p",{className:"text-lg",children:t.generatedAt.toLocaleDateString()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Expiry Date"}),(0,a.jsx)("p",{className:"text-lg",children:t.expiresAt.toLocaleDateString()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Status"}),(0,a.jsx)(s.E,{variant:r?"default":"destructive",className:r?"bg-green-100 text-green-800":"",children:t.status.toUpperCase()})]})]}),x&&(0,a.jsx)("div",{className:"mt-4 p-4 bg-orange-50 border border-orange-200 rounded-lg",children:(0,a.jsxs)("p",{className:"text-orange-800",children:[(0,a.jsx)("strong",{children:"Note:"})," This certificate has expired. IELTS certificates are valid for 6 months from the test date."]})}),!r&&!x&&(0,a.jsx)("div",{className:"mt-4 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,a.jsxs)("p",{className:"text-red-800",children:[(0,a.jsx)("strong",{children:"Warning:"})," This certificate is not valid. It may have been revoked or deleted."]})})]}),(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-blue-800 mb-2",children:"Verification Information"}),(0,a.jsxs)("p",{className:"text-blue-700 text-sm",children:["This certificate has been verified against our secure database. The verification was performed on ",new Date().toLocaleDateString()," at ",new Date().toLocaleTimeString(),"."]})]})]})}},4861:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},6126:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var a=r(5155);r(2115);var s=r(6486);function l(e){let{className:t,variant:r="default",...l}=e;return(0,a.jsx)("div",{className:(0,s.cn)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{"border-transparent bg-primary text-primary-foreground hover:bg-primary/80":"default"===r,"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80":"secondary"===r,"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80":"destructive"===r,"text-foreground":"outline"===r,"border-transparent bg-blue-100 text-blue-800":"blue"===r,"border-transparent bg-green-100 text-green-800":"green"===r,"border-transparent bg-yellow-100 text-yellow-800":"yellow"===r,"border-transparent bg-red-100 text-red-800":"red"===r,"border-transparent bg-purple-100 text-purple-800":"purple"===r,"border-transparent bg-orange-100 text-orange-800":"orange"===r,"border-transparent bg-gray-100 text-gray-800":"gray"===r},t),...l})}},6486:(e,t,r)=>{"use strict";r.d(t,{cn:()=>l});var a=r(2596),s=r(9688);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}},9037:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},9074:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>x});var a=r(2115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),i=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},d=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:s=24,strokeWidth:l=2,absoluteStrokeWidth:i,className:o="",children:x,iconNode:m,...h}=e;return(0,a.createElement)("svg",{ref:t,...c,width:s,height:s,stroke:r,strokeWidth:i?24*Number(l)/Number(s):l,className:n("lucide",o),...!x&&!d(h)&&{"aria-hidden":"true"},...h},[...m.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(x)?x:[x]])}),x=(e,t)=>{let r=(0,a.forwardRef)((r,l)=>{let{className:d,...c}=r;return(0,a.createElement)(o,{ref:l,iconNode:t,className:n("lucide-".concat(s(i(e))),"lucide-".concat(e),d),...c})});return r.displayName=i(e),r}}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,8441,1684,7358],()=>t(847)),_N_E=e.O()}]);