(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1334],{285:(e,r,a)=>{"use strict";a.d(r,{$:()=>n});var s=a(5155),t=a(2115),i=a(6486);let n=t.forwardRef((e,r)=>{let{className:a,variant:t="default",size:n="default",...l}=e;return(0,s.jsx)("button",{className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{"bg-blue-600 text-white hover:bg-blue-700":"default"===t,"bg-red-600 text-white hover:bg-red-700":"destructive"===t,"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50":"outline"===t,"bg-gray-100 text-gray-900 hover:bg-gray-200":"secondary"===t,"hover:bg-gray-100 hover:text-gray-900":"ghost"===t,"text-blue-600 underline-offset-4 hover:underline":"link"===t},{"h-10 px-4 py-2":"default"===n,"h-9 rounded-md px-3":"sm"===n,"h-11 rounded-md px-8":"lg"===n,"h-10 w-10":"icon"===n},a),ref:r,...l})});n.displayName="Button"},2523:(e,r,a)=>{"use strict";a.d(r,{p:()=>n});var s=a(5155),t=a(2115),i=a(6486);let n=t.forwardRef((e,r)=>{let{className:a,type:t,error:n,...l}=e;return(0,s.jsxs)("div",{className:"w-full",children:[(0,s.jsx)("input",{type:t,className:(0,i.cn)("flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:cursor-not-allowed disabled:opacity-50",n&&"border-red-500 focus:ring-red-500 focus:border-red-500",a),ref:r,...l}),n&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-500",children:n})]})});n.displayName="Input"},3352:(e,r,a)=>{"use strict";a.d(r,{a:()=>d});var s=a(5155),t=a(2115),i=a(5939),n=a(280),l=a(4416),o=a(6486);function d(e){let{isOpen:r,onClose:a,title:d,children:c,size:m="md"}=e;return(0,s.jsx)(i.e,{appear:!0,show:r,as:t.Fragment,children:(0,s.jsxs)(n.lG,{as:"div",className:"relative z-50",onClose:a,children:[(0,s.jsx)(i.e.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,s.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,s.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,s.jsx)(i.e.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,s.jsxs)(n.lG.Panel,{className:(0,o.cn)("w-full transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl"}[m]),children:[d&&(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)(n.lG.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:d}),(0,s.jsx)("button",{type:"button",className:"rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500",onClick:a,children:(0,s.jsx)(l.A,{className:"h-5 w-5"})})]}),c]})})})})]})})}},3777:(e,r,a)=>{"use strict";a.d(r,{CreateOrganizationModal:()=>g});var s=a(5155),t=a(2115),i=a(5695),n=a(2177),l=a(221),o=a(1153),d=a(285),c=a(2523),m=a(3352);let u=o.z.object({name:o.z.string().min(2,"Organization name must be at least 2 characters"),slug:o.z.string().min(2,"Slug must be at least 2 characters").regex(/^[a-z0-9-]+$/,"Slug can only contain lowercase letters, numbers, and hyphens"),adminEmail:o.z.string().email("Invalid email address"),adminPassword:o.z.string().min(8,"Password must be at least 8 characters"),adminName:o.z.string().min(2,"Admin name must be at least 2 characters"),billingPlan:o.z.enum(["basic","premium","enterprise"]),features:o.z.array(o.z.string()).default([])});function g(e){var r,a,o,g,h;let{children:x}=e,[f,b]=(0,t.useState)(!1),[p,y]=(0,t.useState)(!1),v=(0,i.useRouter)(),j=(0,n.mN)({resolver:(0,l.u)(u),defaultValues:{billingPlan:"basic",features:[]}}),w=async e=>{y(!0);try{let r=await fetch("/api/master/organizations",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!r.ok){let e=await r.json();throw Error(e.message||"Failed to create organization")}await r.json(),b(!1),j.reset(),v.refresh()}catch(e){console.error("Error creating organization:",e)}finally{y(!1)}},N=e=>{let r=e.target.value.toLowerCase().replace(/[^a-z0-9\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim();j.setValue("slug",r)};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{onClick:()=>b(!0),children:x}),(0,s.jsx)(m.a,{isOpen:f,onClose:()=>b(!1),title:"Create New Organization",children:(0,s.jsxs)("form",{onSubmit:j.handleSubmit(w),className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsx)(c.p,{...j.register("name"),placeholder:"Organization Name",onChange:e=>{j.register("name").onChange(e),N(e)},error:null==(r=j.formState.errors.name)?void 0:r.message}),(0,s.jsx)(c.p,{...j.register("slug"),placeholder:"organization-slug",error:null==(a=j.formState.errors.slug)?void 0:a.message})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"Admin User Details"}),(0,s.jsx)(c.p,{...j.register("adminName"),placeholder:"Admin Full Name",error:null==(o=j.formState.errors.adminName)?void 0:o.message}),(0,s.jsx)(c.p,{...j.register("adminEmail"),type:"email",placeholder:"Admin Email",error:null==(g=j.formState.errors.adminEmail)?void 0:g.message}),(0,s.jsx)(c.p,{...j.register("adminPassword"),type:"password",placeholder:"Admin Password",error:null==(h=j.formState.errors.adminPassword)?void 0:h.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Billing Plan"}),(0,s.jsxs)("select",{...j.register("billingPlan"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"basic",children:"Basic"}),(0,s.jsx)("option",{value:"premium",children:"Premium"}),(0,s.jsx)("option",{value:"enterprise",children:"Enterprise"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Features"}),(0,s.jsx)("div",{className:"grid grid-cols-2 gap-2",children:[{id:"ai_feedback",label:"AI Feedback"},{id:"certificates",label:"Certificates"},{id:"progress_tracking",label:"Progress Tracking"},{id:"promotions",label:"Promotions"},{id:"analytics",label:"Advanced Analytics"}].map(e=>(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"checkbox",value:e.id,...j.register("features"),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,s.jsx)("span",{className:"text-sm text-gray-700",children:e.label})]},e.id))})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,s.jsx)(d.$,{type:"button",variant:"outline",onClick:()=>b(!1),disabled:p,children:"Cancel"}),(0,s.jsx)(d.$,{type:"submit",disabled:p,children:p?"Creating...":"Create Organization"})]})]})})]})}},5695:(e,r,a)=>{"use strict";var s=a(8999);a.o(s,"usePathname")&&a.d(r,{usePathname:function(){return s.usePathname}}),a.o(s,"useRouter")&&a.d(r,{useRouter:function(){return s.useRouter}}),a.o(s,"useSearchParams")&&a.d(r,{useSearchParams:function(){return s.useSearchParams}})},6486:(e,r,a)=>{"use strict";a.d(r,{cn:()=>i});var s=a(2596),t=a(9688);function i(){for(var e=arguments.length,r=Array(e),a=0;a<e;a++)r[a]=arguments[a];return(0,t.QP)((0,s.$)(r))}},7030:(e,r,a)=>{Promise.resolve().then(a.t.bind(a,6874,23)),Promise.resolve().then(a.bind(a,3777))}},e=>{var r=r=>e(e.s=r);e.O(0,[4277,6874,4343,8441,1684,7358],()=>r(7030)),_N_E=e.O()}]);