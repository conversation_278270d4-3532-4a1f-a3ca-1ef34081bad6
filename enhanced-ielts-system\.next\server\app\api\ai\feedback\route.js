(()=>{var e={};e.id=4462,e.ids=[4462],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48127:(e,t,s)=>{"use strict";let r,n,a;s.r(t),s.d(t,{patchFetch:()=>t3,routeModule:()=>t2,serverHooks:()=>t6,workAsyncStorage:()=>t4,workUnitAsyncStorage:()=>t5});var i,o,l,c,u,h,d,p,f,g,m,y,b,w,_,k,v,S,x,R,M,P,$,T,A,E,j,q,I,F,O,N,L,U,W,B,C,D,X,H,J,K,V,z,G,Q,Y,Z,ee,et={};s.r(et),s.d(et,{GET:()=>t1,POST:()=>tZ});var es=s(96559),er=s(48088),en=s(37719),ea=s(32190),ei=s(26326),eo=s(71682),el=s(32767);function ec(e,t,s,r,n){if("m"===r)throw TypeError("Private method is not writable");if("a"===r&&!n)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===r?n.call(e,s):n?n.value=s:t.set(e,s),s}function eu(e,t,s,r){if("a"===s&&!r)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!r:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===s?r:"a"===s?r.call(e):r?r.value:t.get(e)}let eh=function(){let{crypto:e}=globalThis;if(e?.randomUUID)return eh=e.randomUUID.bind(e),e.randomUUID();let t=new Uint8Array(1),s=e?()=>e.getRandomValues(t)[0]:()=>255*Math.random()&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,e=>(e^s()&15>>e/4).toString(16))};function ed(e){return"object"==typeof e&&null!==e&&("name"in e&&"AbortError"===e.name||"message"in e&&String(e.message).includes("FetchRequestCanceledException"))}let ep=e=>{if(e instanceof Error)return e;if("object"==typeof e&&null!==e){try{if("[object Error]"===Object.prototype.toString.call(e)){let t=Error(e.message,e.cause?{cause:e.cause}:{});return e.stack&&(t.stack=e.stack),e.cause&&!t.cause&&(t.cause=e.cause),e.name&&(t.name=e.name),t}}catch{}try{return Error(JSON.stringify(e))}catch{}}return Error(e)};class ef extends Error{}class eg extends ef{constructor(e,t,s,r){super(`${eg.makeMessage(e,t,s)}`),this.status=e,this.headers=r,this.requestID=r?.get("request-id"),this.error=t}static makeMessage(e,t,s){let r=t?.message?"string"==typeof t.message?t.message:JSON.stringify(t.message):t?JSON.stringify(t):s;return e&&r?`${e} ${r}`:e?`${e} status code (no body)`:r||"(no status code or body)"}static generate(e,t,s,r){return e&&r?400===e?new ew(e,t,s,r):401===e?new e_(e,t,s,r):403===e?new ek(e,t,s,r):404===e?new ev(e,t,s,r):409===e?new eS(e,t,s,r):422===e?new ex(e,t,s,r):429===e?new eR(e,t,s,r):e>=500?new eM(e,t,s,r):new eg(e,t,s,r):new ey({message:s,cause:ep(t)})}}class em extends eg{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0)}}class ey extends eg{constructor({message:e,cause:t}){super(void 0,void 0,e||"Connection error.",void 0),t&&(this.cause=t)}}class eb extends ey{constructor({message:e}={}){super({message:e??"Request timed out."})}}class ew extends eg{}class e_ extends eg{}class ek extends eg{}class ev extends eg{}class eS extends eg{}class ex extends eg{}class eR extends eg{}class eM extends eg{}let eP=/^[a-z][a-z0-9+.-]*:/i,e$=e=>eP.test(e);function eT(e){return"object"!=typeof e?{}:e??{}}let eA=(e,t)=>{if("number"!=typeof t||!Number.isInteger(t))throw new ef(`${e} must be an integer`);if(t<0)throw new ef(`${e} must be a positive integer`);return t},eE=e=>{try{return JSON.parse(e)}catch(e){return}},ej=e=>new Promise(t=>setTimeout(t,e)),eq={off:0,error:200,warn:300,info:400,debug:500},eI=(e,t,s)=>{if(e){if(Object.prototype.hasOwnProperty.call(eq,e))return e;eU(s).warn(`${t} was set to ${JSON.stringify(e)}, expected one of ${JSON.stringify(Object.keys(eq))}`)}};function eF(){}function eO(e,t,s){return!t||eq[e]>eq[s]?eF:t[e].bind(t)}let eN={error:eF,warn:eF,info:eF,debug:eF},eL=new WeakMap;function eU(e){let t=e.logger,s=e.logLevel??"off";if(!t)return eN;let r=eL.get(t);if(r&&r[0]===s)return r[1];let n={error:eO("error",t,s),warn:eO("warn",t,s),info:eO("info",t,s),debug:eO("debug",t,s)};return eL.set(t,[s,n]),n}let eW=e=>(e.options&&(e.options={...e.options},delete e.options.headers),e.headers&&(e.headers=Object.fromEntries((e.headers instanceof Headers?[...e.headers]:Object.entries(e.headers)).map(([e,t])=>[e,"x-api-key"===e.toLowerCase()||"authorization"===e.toLowerCase()||"cookie"===e.toLowerCase()||"set-cookie"===e.toLowerCase()?"***":t]))),"retryOfRequestLogID"in e&&(e.retryOfRequestLogID&&(e.retryOf=e.retryOfRequestLogID),delete e.retryOfRequestLogID),e),eB="0.52.0",eC=()=>"undefined"!=typeof window&&void 0!==window.document&&"undefined"!=typeof navigator,eD=()=>{let e="undefined"!=typeof Deno&&null!=Deno.build?"deno":"undefined"!=typeof EdgeRuntime?"edge":"[object process]"===Object.prototype.toString.call(void 0!==globalThis.process?globalThis.process:0)?"node":"unknown";if("deno"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":eB,"X-Stainless-OS":eH(Deno.build.os),"X-Stainless-Arch":eX(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":"string"==typeof Deno.version?Deno.version:Deno.version?.deno??"unknown"};if("undefined"!=typeof EdgeRuntime)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":eB,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if("node"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":eB,"X-Stainless-OS":eH(globalThis.process.platform),"X-Stainless-Arch":eX(globalThis.process.arch),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":globalThis.process.version};let t=function(){if("undefined"==typeof navigator||!navigator)return null;for(let{key:e,pattern:t}of[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}]){let s=t.exec(navigator.userAgent);if(s){let t=s[1]||0,r=s[2]||0,n=s[3]||0;return{browser:e,version:`${t}.${r}.${n}`}}}return null}();return t?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":eB,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${t.browser}`,"X-Stainless-Runtime-Version":t.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":eB,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}},eX=e=>"x32"===e?"x32":"x86_64"===e||"x64"===e?"x64":"arm"===e?"arm":"aarch64"===e||"arm64"===e?"arm64":e?`other:${e}`:"unknown",eH=e=>(e=e.toLowerCase()).includes("ios")?"iOS":"android"===e?"Android":"darwin"===e?"MacOS":"win32"===e?"Windows":"freebsd"===e?"FreeBSD":"openbsd"===e?"OpenBSD":"linux"===e?"Linux":e?`Other:${e}`:"Unknown",eJ=()=>r??(r=eD());function eK(...e){let t=globalThis.ReadableStream;if(void 0===t)throw Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new t(...e)}function eV(e){let t=Symbol.asyncIterator in e?e[Symbol.asyncIterator]():e[Symbol.iterator]();return eK({start(){},async pull(e){let{done:s,value:r}=await t.next();s?e.close():e.enqueue(r)},async cancel(){await t.return?.()}})}function ez(e){if(e[Symbol.asyncIterator])return e;let t=e.getReader();return{async next(){try{let e=await t.read();return e?.done&&t.releaseLock(),e}catch(e){throw t.releaseLock(),e}},async return(){let e=t.cancel();return t.releaseLock(),await e,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function eG(e){if(null===e||"object"!=typeof e)return;if(e[Symbol.asyncIterator])return void await e[Symbol.asyncIterator]().return?.();let t=e.getReader(),s=t.cancel();t.releaseLock(),await s}let eQ=({headers:e,body:t})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(t)});function eY(e){let t;return(n??(n=(t=new globalThis.TextEncoder).encode.bind(t)))(e)}function eZ(e){let t;return(a??(a=(t=new globalThis.TextDecoder).decode.bind(t)))(e)}class e0{constructor(){i.set(this,void 0),o.set(this,void 0),ec(this,i,new Uint8Array,"f"),ec(this,o,null,"f")}decode(e){let t;if(null==e)return[];let s=e instanceof ArrayBuffer?new Uint8Array(e):"string"==typeof e?eY(e):e;ec(this,i,function(e){let t=0;for(let s of e)t+=s.length;let s=new Uint8Array(t),r=0;for(let t of e)s.set(t,r),r+=t.length;return s}([eu(this,i,"f"),s]),"f");let r=[];for(;null!=(t=function(e,t){for(let s=t??0;s<e.length;s++){if(10===e[s])return{preceding:s,index:s+1,carriage:!1};if(13===e[s])return{preceding:s,index:s+1,carriage:!0}}return null}(eu(this,i,"f"),eu(this,o,"f")));){if(t.carriage&&null==eu(this,o,"f")){ec(this,o,t.index,"f");continue}if(null!=eu(this,o,"f")&&(t.index!==eu(this,o,"f")+1||t.carriage)){r.push(eZ(eu(this,i,"f").subarray(0,eu(this,o,"f")-1))),ec(this,i,eu(this,i,"f").subarray(eu(this,o,"f")),"f"),ec(this,o,null,"f");continue}let e=null!==eu(this,o,"f")?t.preceding-1:t.preceding,s=eZ(eu(this,i,"f").subarray(0,e));r.push(s),ec(this,i,eu(this,i,"f").subarray(t.index),"f"),ec(this,o,null,"f")}return r}flush(){return eu(this,i,"f").length?this.decode("\n"):[]}}i=new WeakMap,o=new WeakMap,e0.NEWLINE_CHARS=new Set(["\n","\r"]),e0.NEWLINE_REGEXP=/\r\n|[\n\r]/g;class e1{constructor(e,t){this.iterator=e,this.controller=t}static fromSSEResponse(e,t){let s=!1;async function*r(){if(s)throw new ef("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let r=!1;try{for await(let s of e2(e,t)){if("completion"===s.event)try{yield JSON.parse(s.data)}catch(e){throw console.error("Could not parse message into JSON:",s.data),console.error("From chunk:",s.raw),e}if("message_start"===s.event||"message_delta"===s.event||"message_stop"===s.event||"content_block_start"===s.event||"content_block_delta"===s.event||"content_block_stop"===s.event)try{yield JSON.parse(s.data)}catch(e){throw console.error("Could not parse message into JSON:",s.data),console.error("From chunk:",s.raw),e}if("ping"!==s.event&&"error"===s.event)throw new eg(void 0,eE(s.data)??s.data,void 0,e.headers)}r=!0}catch(e){if(ed(e))return;throw e}finally{r||t.abort()}}return new e1(r,t)}static fromReadableStream(e,t){let s=!1;async function*r(){let t=new e0;for await(let s of ez(e))for(let e of t.decode(s))yield e;for(let e of t.flush())yield e}return new e1(async function*(){if(s)throw new ef("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let e=!1;try{for await(let t of r())!e&&t&&(yield JSON.parse(t));e=!0}catch(e){if(ed(e))return;throw e}finally{e||t.abort()}},t)}[Symbol.asyncIterator](){return this.iterator()}tee(){let e=[],t=[],s=this.iterator(),r=r=>({next:()=>{if(0===r.length){let r=s.next();e.push(r),t.push(r)}return r.shift()}});return[new e1(()=>r(e),this.controller),new e1(()=>r(t),this.controller)]}toReadableStream(){let e,t=this;return eK({async start(){e=t[Symbol.asyncIterator]()},async pull(t){try{let{value:s,done:r}=await e.next();if(r)return t.close();let n=eY(JSON.stringify(s)+"\n");t.enqueue(n)}catch(e){t.error(e)}},async cancel(){await e.return?.()}})}}async function*e2(e,t){if(!e.body){if(t.abort(),void 0!==globalThis.navigator&&"ReactNative"===globalThis.navigator.product)throw new ef("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api");throw new ef("Attempted to iterate over a response with no body")}let s=new e5,r=new e0;for await(let t of e4(ez(e.body)))for(let e of r.decode(t)){let t=s.decode(e);t&&(yield t)}for(let e of r.flush()){let t=s.decode(e);t&&(yield t)}}async function*e4(e){let t=new Uint8Array;for await(let s of e){let e;if(null==s)continue;let r=s instanceof ArrayBuffer?new Uint8Array(s):"string"==typeof s?eY(s):s,n=new Uint8Array(t.length+r.length);for(n.set(t),n.set(r,t.length),t=n;-1!==(e=function(e){for(let t=0;t<e.length-1;t++){if(10===e[t]&&10===e[t+1]||13===e[t]&&13===e[t+1])return t+2;if(13===e[t]&&10===e[t+1]&&t+3<e.length&&13===e[t+2]&&10===e[t+3])return t+4}return -1}(t));)yield t.slice(0,e),t=t.slice(e)}t.length>0&&(yield t)}class e5{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;let e={event:this.event,data:this.data.join("\n"),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],e}if(this.chunks.push(e),e.startsWith(":"))return null;let[t,s,r]=function(e,t){let s=e.indexOf(":");return -1!==s?[e.substring(0,s),t,e.substring(s+t.length)]:[e,"",""]}(e,":");return r.startsWith(" ")&&(r=r.substring(1)),"event"===t?this.event=r:"data"===t&&this.data.push(r),null}}async function e6(e,t){let{response:s,requestLogID:r,retryOfRequestLogID:n,startTime:a}=t,i=await (async()=>{if(t.options.stream)return(eU(e).debug("response",s.status,s.url,s.headers,s.body),t.options.__streamClass)?t.options.__streamClass.fromSSEResponse(s,t.controller):e1.fromSSEResponse(s,t.controller);if(204===s.status)return null;if(t.options.__binaryResponse)return s;let r=s.headers.get("content-type"),n=r?.split(";")[0]?.trim();return n?.includes("application/json")||n?.endsWith("+json")?e3(await s.json(),s):await s.text()})();return eU(e).debug(`[${r}] response parsed`,eW({retryOfRequestLogID:n,url:s.url,status:s.status,body:i,durationMs:Date.now()-a})),i}function e3(e,t){return!e||"object"!=typeof e||Array.isArray(e)?e:Object.defineProperty(e,"_request_id",{value:t.headers.get("request-id"),enumerable:!1})}class e9 extends Promise{constructor(e,t,s=e6){super(e=>{e(null)}),this.responsePromise=t,this.parseResponse=s,l.set(this,void 0),ec(this,l,e,"f")}_thenUnwrap(e){return new e9(eu(this,l,"f"),this.responsePromise,async(t,s)=>e3(e(await this.parseResponse(t,s),s),s.response))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){let[e,t]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:t,request_id:t.headers.get("request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(e=>this.parseResponse(eu(this,l,"f"),e))),this.parsedPromise}then(e,t){return this.parse().then(e,t)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}}l=new WeakMap;class e8{constructor(e,t,s,r){c.set(this,void 0),ec(this,c,e,"f"),this.options=r,this.response=t,this.body=s}hasNextPage(){return!!this.getPaginatedItems().length&&null!=this.nextPageRequestOptions()}async getNextPage(){let e=this.nextPageRequestOptions();if(!e)throw new ef("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await eu(this,c,"f").requestAPIList(this.constructor,e)}async *iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async *[(c=new WeakMap,Symbol.asyncIterator)](){for await(let e of this.iterPages())for(let t of e.getPaginatedItems())yield t}}class e7 extends e9{constructor(e,t,s){super(e,t,async(e,t)=>new s(e,t.response,await e6(e,t),t.options))}async *[Symbol.asyncIterator](){for await(let e of(await this))yield e}}class te extends e8{constructor(e,t,s,r){super(e,t,s,r),this.data=s.data||[],this.has_more=s.has_more||!1,this.first_id=s.first_id||null,this.last_id=s.last_id||null}getPaginatedItems(){return this.data??[]}hasNextPage(){return!1!==this.has_more&&super.hasNextPage()}nextPageRequestOptions(){if(this.options.query?.before_id){let e=this.first_id;return e?{...this.options,query:{...eT(this.options.query),before_id:e}}:null}let e=this.last_id;return e?{...this.options,query:{...eT(this.options.query),after_id:e}}:null}}let tt=()=>{if("undefined"==typeof File){let{process:e}=globalThis;throw Error("`File` is not defined as a global, which is required for file uploads."+("string"==typeof e?.versions?.node&&20>parseInt(e.versions.node.split("."))?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function ts(e,t,s){return tt(),new File(e,t??"unknown_file",s)}function tr(e){return("object"==typeof e&&null!==e&&("name"in e&&e.name&&String(e.name)||"url"in e&&e.url&&String(e.url)||"filename"in e&&e.filename&&String(e.filename)||"path"in e&&e.path&&String(e.path))||"").split(/[\\/]/).pop()||void 0}let tn=e=>null!=e&&"object"==typeof e&&"function"==typeof e[Symbol.asyncIterator],ta=async(e,t)=>({...e,body:await to(e.body,t)}),ti=new WeakMap,to=async(e,t)=>{if(!await function(e){let t="function"==typeof e?e:e.fetch,s=ti.get(t);if(s)return s;let r=(async()=>{try{let e="Response"in t?t.Response:(await t("data:,")).constructor,s=new FormData;if(s.toString()===await new e(s).text())return!1;return!0}catch{return!0}})();return ti.set(t,r),r}(t))throw TypeError("The provided fetch function does not support file uploads with the current global FormData class.");let s=new FormData;return await Promise.all(Object.entries(e||{}).map(([e,t])=>th(s,e,t))),s},tl=e=>e instanceof Blob&&"name"in e,tc=e=>"object"==typeof e&&null!==e&&(e instanceof Response||tn(e)||tl(e)),tu=e=>{if(tc(e))return!0;if(Array.isArray(e))return e.some(tu);if(e&&"object"==typeof e){for(let t in e)if(tu(e[t]))return!0}return!1},th=async(e,t,s)=>{if(void 0!==s){if(null==s)throw TypeError(`Received null for "${t}"; to pass null in FormData, you must use the string 'null'`);if("string"==typeof s||"number"==typeof s||"boolean"==typeof s)e.append(t,String(s));else if(s instanceof Response){let r={},n=s.headers.get("Content-Type");n&&(r={type:n}),e.append(t,ts([await s.blob()],tr(s),r))}else if(tn(s))e.append(t,ts([await new Response(eV(s)).blob()],tr(s)));else if(tl(s))e.append(t,ts([s],tr(s),{type:s.type}));else if(Array.isArray(s))await Promise.all(s.map(s=>th(e,t+"[]",s)));else if("object"==typeof s)await Promise.all(Object.entries(s).map(([s,r])=>th(e,`${t}[${s}]`,r)));else throw TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${s} instead`)}},td=e=>null!=e&&"object"==typeof e&&"number"==typeof e.size&&"string"==typeof e.type&&"function"==typeof e.text&&"function"==typeof e.slice&&"function"==typeof e.arrayBuffer,tp=e=>null!=e&&"object"==typeof e&&"string"==typeof e.name&&"number"==typeof e.lastModified&&td(e),tf=e=>null!=e&&"object"==typeof e&&"string"==typeof e.url&&"function"==typeof e.blob;async function tg(e,t,s){if(tt(),e=await e,t||(t=tr(e)),tp(e))return e instanceof File&&null==t&&null==s?e:ts([await e.arrayBuffer()],t??e.name,{type:e.type,lastModified:e.lastModified,...s});if(tf(e)){let r=await e.blob();return t||(t=new URL(e.url).pathname.split(/[\\/]/).pop()),ts(await tm(r),t,s)}let r=await tm(e);if(!s?.type){let e=r.find(e=>"object"==typeof e&&"type"in e&&e.type);"string"==typeof e&&(s={...s,type:e})}return ts(r,t,s)}async function tm(e){let t=[];if("string"==typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer)t.push(e);else if(td(e))t.push(e instanceof Blob?e:await e.arrayBuffer());else if(tn(e))for await(let s of e)t.push(...await tm(s));else{let t=e?.constructor?.name;throw Error(`Unexpected data type: ${typeof e}${t?`; constructor: ${t}`:""}${function(e){if("object"!=typeof e||null===e)return"";let t=Object.getOwnPropertyNames(e);return`; props: [${t.map(e=>`"${e}"`).join(", ")}]`}(e)}`)}return t}class ty{constructor(e){this._client=e}}let tb=Symbol.for("brand.privateNullableHeaders"),tw=Array.isArray,t_=e=>{let t=new Headers,s=new Set;for(let r of e){let e=new Set;for(let[n,a]of function*(e){let t;if(!e)return;if(tb in e){let{values:t,nulls:s}=e;for(let e of(yield*t.entries(),s))yield[e,null];return}let s=!1;for(let r of(e instanceof Headers?t=e.entries():tw(e)?t=e:(s=!0,t=Object.entries(e??{})),t)){let e=r[0];if("string"!=typeof e)throw TypeError("expected header name to be a string");let t=tw(r[1])?r[1]:[r[1]],n=!1;for(let r of t)void 0!==r&&(s&&!n&&(n=!0,yield[e,null]),yield[e,r])}}(r)){let r=n.toLowerCase();e.has(r)||(t.delete(n),e.add(r)),null===a?(t.delete(n),s.add(r)):(t.append(n,a),s.delete(r))}}return{[tb]:!0,values:t,nulls:s}};function tk(e){return e.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}let tv=((e=tk)=>function(t,...s){let r;if(1===t.length)return t[0];let n=!1,a=t.reduce((t,r,a)=>(/[?#]/.test(r)&&(n=!0),t+r+(a===s.length?"":(n?encodeURIComponent:e)(String(s[a])))),""),i=a.split(/[?#]/,1)[0],o=[],l=/(?<=^|\/)(?:\.|%2e){1,2}(?=\/|$)/gi;for(;null!==(r=l.exec(i));)o.push({start:r.index,length:r[0].length});if(o.length>0){let e=0,t=o.reduce((t,s)=>{let r=" ".repeat(s.start-e),n="^".repeat(s.length);return e=s.start+s.length,t+r+n},"");throw new ef(`Path parameters result in path with invalid segments:
${a}
${t}`)}return a})(tk);class tS extends ty{list(e={},t){let{betas:s,...r}=e??{};return this._client.getAPIList("/v1/files",te,{query:r,...t,headers:t_([{"anthropic-beta":[...s??[],"files-api-2025-04-14"].toString()},t?.headers])})}delete(e,t={},s){let{betas:r}=t??{};return this._client.delete(tv`/v1/files/${e}`,{...s,headers:t_([{"anthropic-beta":[...r??[],"files-api-2025-04-14"].toString()},s?.headers])})}download(e,t={},s){let{betas:r}=t??{};return this._client.get(tv`/v1/files/${e}/content`,{...s,headers:t_([{"anthropic-beta":[...r??[],"files-api-2025-04-14"].toString(),Accept:"application/binary"},s?.headers]),__binaryResponse:!0})}retrieveMetadata(e,t={},s){let{betas:r}=t??{};return this._client.get(tv`/v1/files/${e}`,{...s,headers:t_([{"anthropic-beta":[...r??[],"files-api-2025-04-14"].toString()},s?.headers])})}upload(e,t){let{betas:s,...r}=e;return this._client.post("/v1/files",ta({body:r,...t,headers:t_([{"anthropic-beta":[...s??[],"files-api-2025-04-14"].toString()},t?.headers])},this._client))}}class tx extends ty{retrieve(e,t={},s){let{betas:r}=t??{};return this._client.get(tv`/v1/models/${e}?beta=true`,{...s,headers:t_([{...r?.toString()!=null?{"anthropic-beta":r?.toString()}:void 0},s?.headers])})}list(e={},t){let{betas:s,...r}=e??{};return this._client.getAPIList("/v1/models?beta=true",te,{query:r,...t,headers:t_([{...s?.toString()!=null?{"anthropic-beta":s?.toString()}:void 0},t?.headers])})}}class tR{constructor(e,t){this.iterator=e,this.controller=t}async *decoder(){let e=new e0;for await(let t of this.iterator)for(let s of e.decode(t))yield JSON.parse(s);for(let t of e.flush())yield JSON.parse(t)}[Symbol.asyncIterator](){return this.decoder()}static fromResponse(e,t){if(!e.body){if(t.abort(),void 0!==globalThis.navigator&&"ReactNative"===globalThis.navigator.product)throw new ef("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api");throw new ef("Attempted to iterate over a response with no body")}return new tR(ez(e.body),t)}}class tM extends ty{create(e,t){let{betas:s,...r}=e;return this._client.post("/v1/messages/batches?beta=true",{body:r,...t,headers:t_([{"anthropic-beta":[...s??[],"message-batches-2024-09-24"].toString()},t?.headers])})}retrieve(e,t={},s){let{betas:r}=t??{};return this._client.get(tv`/v1/messages/batches/${e}?beta=true`,{...s,headers:t_([{"anthropic-beta":[...r??[],"message-batches-2024-09-24"].toString()},s?.headers])})}list(e={},t){let{betas:s,...r}=e??{};return this._client.getAPIList("/v1/messages/batches?beta=true",te,{query:r,...t,headers:t_([{"anthropic-beta":[...s??[],"message-batches-2024-09-24"].toString()},t?.headers])})}delete(e,t={},s){let{betas:r}=t??{};return this._client.delete(tv`/v1/messages/batches/${e}?beta=true`,{...s,headers:t_([{"anthropic-beta":[...r??[],"message-batches-2024-09-24"].toString()},s?.headers])})}cancel(e,t={},s){let{betas:r}=t??{};return this._client.post(tv`/v1/messages/batches/${e}/cancel?beta=true`,{...s,headers:t_([{"anthropic-beta":[...r??[],"message-batches-2024-09-24"].toString()},s?.headers])})}async results(e,t={},s){let r=await this.retrieve(e);if(!r.results_url)throw new ef(`No batch \`results_url\`; Has it finished processing? ${r.processing_status} - ${r.id}`);let{betas:n}=t??{};return this._client.get(r.results_url,{...s,headers:t_([{"anthropic-beta":[...n??[],"message-batches-2024-09-24"].toString(),Accept:"application/binary"},s?.headers]),stream:!0,__binaryResponse:!0})._thenUnwrap((e,t)=>tR.fromResponse(t.response,t.controller))}}let tP=e=>{let t=0,s=[];for(;t<e.length;){let r=e[t];if("\\"===r){t++;continue}if("{"===r){s.push({type:"brace",value:"{"}),t++;continue}if("}"===r){s.push({type:"brace",value:"}"}),t++;continue}if("["===r){s.push({type:"paren",value:"["}),t++;continue}if("]"===r){s.push({type:"paren",value:"]"}),t++;continue}if(":"===r){s.push({type:"separator",value:":"}),t++;continue}if(","===r){s.push({type:"delimiter",value:","}),t++;continue}if('"'===r){let n="",a=!1;for(r=e[++t];'"'!==r;){if(t===e.length){a=!0;break}if("\\"===r){if(++t===e.length){a=!0;break}n+=r+e[t],r=e[++t]}else n+=r,r=e[++t]}r=e[++t],a||s.push({type:"string",value:n});continue}let n=/\s/;if(r&&n.test(r)){t++;continue}let a=/[0-9]/;if(r&&a.test(r)||"-"===r||"."===r){let n="";for("-"===r&&(n+=r,r=e[++t]);r&&a.test(r)||"."===r;)n+=r,r=e[++t];s.push({type:"number",value:n});continue}let i=/[a-z]/i;if(r&&i.test(r)){let n="";for(;r&&i.test(r)&&t!==e.length;)n+=r,r=e[++t];"true"==n||"false"==n||"null"===n?s.push({type:"name",value:n}):t++;continue}t++}return s},t$=e=>{if(0===e.length)return e;let t=e[e.length-1];switch(t.type){case"separator":return t$(e=e.slice(0,e.length-1));case"number":let s=t.value[t.value.length-1];if("."===s||"-"===s)return t$(e=e.slice(0,e.length-1));case"string":let r=e[e.length-2];if(r?.type==="delimiter"||r?.type==="brace"&&"{"===r.value)return t$(e=e.slice(0,e.length-1));break;case"delimiter":return t$(e=e.slice(0,e.length-1))}return e},tT=e=>{let t=[];return e.map(e=>{"brace"===e.type&&("{"===e.value?t.push("}"):t.splice(t.lastIndexOf("}"),1)),"paren"===e.type&&("["===e.value?t.push("]"):t.splice(t.lastIndexOf("]"),1))}),t.length>0&&t.reverse().map(t=>{"}"===t?e.push({type:"brace",value:"}"}):"]"===t&&e.push({type:"paren",value:"]"})}),e},tA=e=>{let t="";return e.map(e=>{"string"===e.type?t+='"'+e.value+'"':t+=e.value}),t},tE=e=>JSON.parse(tA(tT(t$(tP(e))))),tj="__json_buf";class tq{constructor(){u.add(this),this.messages=[],this.receivedMessages=[],h.set(this,void 0),this.controller=new AbortController,d.set(this,void 0),p.set(this,()=>{}),f.set(this,()=>{}),g.set(this,void 0),m.set(this,()=>{}),y.set(this,()=>{}),b.set(this,{}),w.set(this,!1),_.set(this,!1),k.set(this,!1),v.set(this,!1),S.set(this,void 0),x.set(this,void 0),P.set(this,e=>{if(ec(this,_,!0,"f"),ed(e)&&(e=new em),e instanceof em)return ec(this,k,!0,"f"),this._emit("abort",e);if(e instanceof ef)return this._emit("error",e);if(e instanceof Error){let t=new ef(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new ef(String(e)))}),ec(this,d,new Promise((e,t)=>{ec(this,p,e,"f"),ec(this,f,t,"f")}),"f"),ec(this,g,new Promise((e,t)=>{ec(this,m,e,"f"),ec(this,y,t,"f")}),"f"),eu(this,d,"f").catch(()=>{}),eu(this,g,"f").catch(()=>{})}get response(){return eu(this,S,"f")}get request_id(){return eu(this,x,"f")}async withResponse(){let e=await eu(this,d,"f");if(!e)throw Error("Could not resolve a `Response` object");return{data:this,response:e,request_id:e.headers.get("request-id")}}static fromReadableStream(e){let t=new tq;return t._run(()=>t._fromReadableStream(e)),t}static createMessage(e,t,s){let r=new tq;for(let e of t.messages)r._addMessageParam(e);return r._run(()=>r._createMessage(e,{...t,stream:!0},{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),r}_run(e){e().then(()=>{this._emitFinal(),this._emit("end")},eu(this,P,"f"))}_addMessageParam(e){this.messages.push(e)}_addMessage(e,t=!0){this.receivedMessages.push(e),t&&this._emit("message",e)}async _createMessage(e,t,s){let r=s?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),eu(this,u,"m",$).call(this);let{response:n,data:a}=await e.create({...t,stream:!0},{...s,signal:this.controller.signal}).withResponse();for await(let e of(this._connected(n),a))eu(this,u,"m",T).call(this,e);if(a.controller.signal?.aborted)throw new em;eu(this,u,"m",A).call(this)}_connected(e){this.ended||(ec(this,S,e,"f"),ec(this,x,e?.headers.get("request-id"),"f"),eu(this,p,"f").call(this,e),this._emit("connect"))}get ended(){return eu(this,w,"f")}get errored(){return eu(this,_,"f")}get aborted(){return eu(this,k,"f")}abort(){this.controller.abort()}on(e,t){return(eu(this,b,"f")[e]||(eu(this,b,"f")[e]=[])).push({listener:t}),this}off(e,t){let s=eu(this,b,"f")[e];if(!s)return this;let r=s.findIndex(e=>e.listener===t);return r>=0&&s.splice(r,1),this}once(e,t){return(eu(this,b,"f")[e]||(eu(this,b,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,s)=>{ec(this,v,!0,"f"),"error"!==e&&this.once("error",s),this.once(e,t)})}async done(){ec(this,v,!0,"f"),await eu(this,g,"f")}get currentMessage(){return eu(this,h,"f")}async finalMessage(){return await this.done(),eu(this,u,"m",R).call(this)}async finalText(){return await this.done(),eu(this,u,"m",M).call(this)}_emit(e,...t){if(eu(this,w,"f"))return;"end"===e&&(ec(this,w,!0,"f"),eu(this,m,"f").call(this));let s=eu(this,b,"f")[e];if(s&&(eu(this,b,"f")[e]=s.filter(e=>!e.once),s.forEach(({listener:e})=>e(...t))),"abort"===e){let e=t[0];eu(this,v,"f")||s?.length||Promise.reject(e),eu(this,f,"f").call(this,e),eu(this,y,"f").call(this,e),this._emit("end");return}if("error"===e){let e=t[0];eu(this,v,"f")||s?.length||Promise.reject(e),eu(this,f,"f").call(this,e),eu(this,y,"f").call(this,e),this._emit("end")}}_emitFinal(){this.receivedMessages.at(-1)&&this._emit("finalMessage",eu(this,u,"m",R).call(this))}async _fromReadableStream(e,t){let s=t?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),eu(this,u,"m",$).call(this),this._connected(null);let r=e1.fromReadableStream(e,this.controller);for await(let e of r)eu(this,u,"m",T).call(this,e);if(r.controller.signal?.aborted)throw new em;eu(this,u,"m",A).call(this)}[(h=new WeakMap,d=new WeakMap,p=new WeakMap,f=new WeakMap,g=new WeakMap,m=new WeakMap,y=new WeakMap,b=new WeakMap,w=new WeakMap,_=new WeakMap,k=new WeakMap,v=new WeakMap,S=new WeakMap,x=new WeakMap,P=new WeakMap,u=new WeakSet,R=function(){if(0===this.receivedMessages.length)throw new ef("stream ended without producing a Message with role=assistant");return this.receivedMessages.at(-1)},M=function(){if(0===this.receivedMessages.length)throw new ef("stream ended without producing a Message with role=assistant");let e=this.receivedMessages.at(-1).content.filter(e=>"text"===e.type).map(e=>e.text);if(0===e.length)throw new ef("stream ended without producing a content block with type=text");return e.join(" ")},$=function(){this.ended||ec(this,h,void 0,"f")},T=function(e){if(this.ended)return;let t=eu(this,u,"m",E).call(this,e);switch(this._emit("streamEvent",e,t),e.type){case"content_block_delta":{let s=t.content.at(-1);switch(e.delta.type){case"text_delta":"text"===s.type&&this._emit("text",e.delta.text,s.text||"");break;case"citations_delta":"text"===s.type&&this._emit("citation",e.delta.citation,s.citations??[]);break;case"input_json_delta":("tool_use"===s.type||"mcp_tool_use"===s.type)&&s.input&&this._emit("inputJson",e.delta.partial_json,s.input);break;case"thinking_delta":"thinking"===s.type&&this._emit("thinking",e.delta.thinking,s.thinking);break;case"signature_delta":"thinking"===s.type&&this._emit("signature",s.signature);break;default:e.delta}break}case"message_stop":this._addMessageParam(t),this._addMessage(t,!0);break;case"content_block_stop":this._emit("contentBlock",t.content.at(-1));break;case"message_start":ec(this,h,t,"f")}},A=function(){if(this.ended)throw new ef("stream has ended, this shouldn't happen");let e=eu(this,h,"f");if(!e)throw new ef("request ended without sending any chunks");return ec(this,h,void 0,"f"),e},E=function(e){let t=eu(this,h,"f");if("message_start"===e.type){if(t)throw new ef(`Unexpected event order, got ${e.type} before receiving "message_stop"`);return e.message}if(!t)throw new ef(`Unexpected event order, got ${e.type} before "message_start"`);switch(e.type){case"message_stop":case"content_block_stop":return t;case"message_delta":return t.container=e.delta.container,t.stop_reason=e.delta.stop_reason,t.stop_sequence=e.delta.stop_sequence,t.usage.output_tokens=e.usage.output_tokens,null!=e.usage.input_tokens&&(t.usage.input_tokens=e.usage.input_tokens),null!=e.usage.cache_creation_input_tokens&&(t.usage.cache_creation_input_tokens=e.usage.cache_creation_input_tokens),null!=e.usage.cache_read_input_tokens&&(t.usage.cache_read_input_tokens=e.usage.cache_read_input_tokens),null!=e.usage.server_tool_use&&(t.usage.server_tool_use=e.usage.server_tool_use),t;case"content_block_start":return t.content.push(e.content_block),t;case"content_block_delta":{let s=t.content.at(e.index);switch(e.delta.type){case"text_delta":s?.type==="text"&&(s.text+=e.delta.text);break;case"citations_delta":s?.type==="text"&&(s.citations??(s.citations=[]),s.citations.push(e.delta.citation));break;case"input_json_delta":if(s?.type==="tool_use"||s?.type==="mcp_tool_use"){let t=s[tj]||"";Object.defineProperty(s,tj,{value:t+=e.delta.partial_json,enumerable:!1,writable:!0}),t&&(s.input=tE(t))}break;case"thinking_delta":s?.type==="thinking"&&(s.thinking+=e.delta.thinking);break;case"signature_delta":s?.type==="thinking"&&(s.signature=e.delta.signature);break;default:e.delta}return t}}},Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("streamEvent",s=>{let r=t.shift();r?r.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),this.on("error",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new e1(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}let tI={"claude-opus-4-20250514":8192,"claude-opus-4-0":8192,"claude-4-opus-20250514":8192,"anthropic.claude-opus-4-20250514-v1:0":8192,"claude-opus-4@20250514":8192},tF={"claude-1.3":"November 6th, 2024","claude-1.3-100k":"November 6th, 2024","claude-instant-1.1":"November 6th, 2024","claude-instant-1.1-100k":"November 6th, 2024","claude-instant-1.2":"November 6th, 2024","claude-3-sonnet-20240229":"July 21st, 2025","claude-2.1":"July 21st, 2025","claude-2.0":"July 21st, 2025"};class tO extends ty{constructor(){super(...arguments),this.batches=new tM(this._client)}create(e,t){let{betas:s,...r}=e;r.model in tF&&console.warn(`The model '${r.model}' is deprecated and will reach end-of-life on ${tF[r.model]}
Please migrate to a newer model. Visit https://docs.anthropic.com/en/docs/resources/model-deprecations for more information.`);let n=this._client._options.timeout;if(!r.stream&&null==n){let e=tI[r.model]??void 0;n=this._client.calculateNonstreamingTimeout(r.max_tokens,e)}return this._client.post("/v1/messages?beta=true",{body:r,timeout:n??6e5,...t,headers:t_([{...s?.toString()!=null?{"anthropic-beta":s?.toString()}:void 0},t?.headers]),stream:e.stream??!1})}stream(e,t){return tq.createMessage(this,e,t)}countTokens(e,t){let{betas:s,...r}=e;return this._client.post("/v1/messages/count_tokens?beta=true",{body:r,...t,headers:t_([{"anthropic-beta":[...s??[],"token-counting-2024-11-01"].toString()},t?.headers])})}}tO.Batches=tM;class tN extends ty{constructor(){super(...arguments),this.models=new tx(this._client),this.messages=new tO(this._client),this.files=new tS(this._client)}}tN.Models=tx,tN.Messages=tO,tN.Files=tS;class tL extends ty{create(e,t){let{betas:s,...r}=e;return this._client.post("/v1/complete",{body:r,timeout:this._client._options.timeout??6e5,...t,headers:t_([{...s?.toString()!=null?{"anthropic-beta":s?.toString()}:void 0},t?.headers]),stream:e.stream??!1})}}let tU="__json_buf";class tW{constructor(){j.add(this),this.messages=[],this.receivedMessages=[],q.set(this,void 0),this.controller=new AbortController,I.set(this,void 0),F.set(this,()=>{}),O.set(this,()=>{}),N.set(this,void 0),L.set(this,()=>{}),U.set(this,()=>{}),W.set(this,{}),B.set(this,!1),C.set(this,!1),D.set(this,!1),X.set(this,!1),H.set(this,void 0),J.set(this,void 0),z.set(this,e=>{if(ec(this,C,!0,"f"),ed(e)&&(e=new em),e instanceof em)return ec(this,D,!0,"f"),this._emit("abort",e);if(e instanceof ef)return this._emit("error",e);if(e instanceof Error){let t=new ef(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new ef(String(e)))}),ec(this,I,new Promise((e,t)=>{ec(this,F,e,"f"),ec(this,O,t,"f")}),"f"),ec(this,N,new Promise((e,t)=>{ec(this,L,e,"f"),ec(this,U,t,"f")}),"f"),eu(this,I,"f").catch(()=>{}),eu(this,N,"f").catch(()=>{})}get response(){return eu(this,H,"f")}get request_id(){return eu(this,J,"f")}async withResponse(){let e=await eu(this,I,"f");if(!e)throw Error("Could not resolve a `Response` object");return{data:this,response:e,request_id:e.headers.get("request-id")}}static fromReadableStream(e){let t=new tW;return t._run(()=>t._fromReadableStream(e)),t}static createMessage(e,t,s){let r=new tW;for(let e of t.messages)r._addMessageParam(e);return r._run(()=>r._createMessage(e,{...t,stream:!0},{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),r}_run(e){e().then(()=>{this._emitFinal(),this._emit("end")},eu(this,z,"f"))}_addMessageParam(e){this.messages.push(e)}_addMessage(e,t=!0){this.receivedMessages.push(e),t&&this._emit("message",e)}async _createMessage(e,t,s){let r=s?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),eu(this,j,"m",G).call(this);let{response:n,data:a}=await e.create({...t,stream:!0},{...s,signal:this.controller.signal}).withResponse();for await(let e of(this._connected(n),a))eu(this,j,"m",Q).call(this,e);if(a.controller.signal?.aborted)throw new em;eu(this,j,"m",Y).call(this)}_connected(e){this.ended||(ec(this,H,e,"f"),ec(this,J,e?.headers.get("request-id"),"f"),eu(this,F,"f").call(this,e),this._emit("connect"))}get ended(){return eu(this,B,"f")}get errored(){return eu(this,C,"f")}get aborted(){return eu(this,D,"f")}abort(){this.controller.abort()}on(e,t){return(eu(this,W,"f")[e]||(eu(this,W,"f")[e]=[])).push({listener:t}),this}off(e,t){let s=eu(this,W,"f")[e];if(!s)return this;let r=s.findIndex(e=>e.listener===t);return r>=0&&s.splice(r,1),this}once(e,t){return(eu(this,W,"f")[e]||(eu(this,W,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,s)=>{ec(this,X,!0,"f"),"error"!==e&&this.once("error",s),this.once(e,t)})}async done(){ec(this,X,!0,"f"),await eu(this,N,"f")}get currentMessage(){return eu(this,q,"f")}async finalMessage(){return await this.done(),eu(this,j,"m",K).call(this)}async finalText(){return await this.done(),eu(this,j,"m",V).call(this)}_emit(e,...t){if(eu(this,B,"f"))return;"end"===e&&(ec(this,B,!0,"f"),eu(this,L,"f").call(this));let s=eu(this,W,"f")[e];if(s&&(eu(this,W,"f")[e]=s.filter(e=>!e.once),s.forEach(({listener:e})=>e(...t))),"abort"===e){let e=t[0];eu(this,X,"f")||s?.length||Promise.reject(e),eu(this,O,"f").call(this,e),eu(this,U,"f").call(this,e),this._emit("end");return}if("error"===e){let e=t[0];eu(this,X,"f")||s?.length||Promise.reject(e),eu(this,O,"f").call(this,e),eu(this,U,"f").call(this,e),this._emit("end")}}_emitFinal(){this.receivedMessages.at(-1)&&this._emit("finalMessage",eu(this,j,"m",K).call(this))}async _fromReadableStream(e,t){let s=t?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),eu(this,j,"m",G).call(this),this._connected(null);let r=e1.fromReadableStream(e,this.controller);for await(let e of r)eu(this,j,"m",Q).call(this,e);if(r.controller.signal?.aborted)throw new em;eu(this,j,"m",Y).call(this)}[(q=new WeakMap,I=new WeakMap,F=new WeakMap,O=new WeakMap,N=new WeakMap,L=new WeakMap,U=new WeakMap,W=new WeakMap,B=new WeakMap,C=new WeakMap,D=new WeakMap,X=new WeakMap,H=new WeakMap,J=new WeakMap,z=new WeakMap,j=new WeakSet,K=function(){if(0===this.receivedMessages.length)throw new ef("stream ended without producing a Message with role=assistant");return this.receivedMessages.at(-1)},V=function(){if(0===this.receivedMessages.length)throw new ef("stream ended without producing a Message with role=assistant");let e=this.receivedMessages.at(-1).content.filter(e=>"text"===e.type).map(e=>e.text);if(0===e.length)throw new ef("stream ended without producing a content block with type=text");return e.join(" ")},G=function(){this.ended||ec(this,q,void 0,"f")},Q=function(e){if(this.ended)return;let t=eu(this,j,"m",Z).call(this,e);switch(this._emit("streamEvent",e,t),e.type){case"content_block_delta":{let s=t.content.at(-1);switch(e.delta.type){case"text_delta":"text"===s.type&&this._emit("text",e.delta.text,s.text||"");break;case"citations_delta":"text"===s.type&&this._emit("citation",e.delta.citation,s.citations??[]);break;case"input_json_delta":"tool_use"===s.type&&s.input&&this._emit("inputJson",e.delta.partial_json,s.input);break;case"thinking_delta":"thinking"===s.type&&this._emit("thinking",e.delta.thinking,s.thinking);break;case"signature_delta":"thinking"===s.type&&this._emit("signature",s.signature);break;default:e.delta}break}case"message_stop":this._addMessageParam(t),this._addMessage(t,!0);break;case"content_block_stop":this._emit("contentBlock",t.content.at(-1));break;case"message_start":ec(this,q,t,"f")}},Y=function(){if(this.ended)throw new ef("stream has ended, this shouldn't happen");let e=eu(this,q,"f");if(!e)throw new ef("request ended without sending any chunks");return ec(this,q,void 0,"f"),e},Z=function(e){let t=eu(this,q,"f");if("message_start"===e.type){if(t)throw new ef(`Unexpected event order, got ${e.type} before receiving "message_stop"`);return e.message}if(!t)throw new ef(`Unexpected event order, got ${e.type} before "message_start"`);switch(e.type){case"message_stop":case"content_block_stop":return t;case"message_delta":return t.stop_reason=e.delta.stop_reason,t.stop_sequence=e.delta.stop_sequence,t.usage.output_tokens=e.usage.output_tokens,null!=e.usage.input_tokens&&(t.usage.input_tokens=e.usage.input_tokens),null!=e.usage.cache_creation_input_tokens&&(t.usage.cache_creation_input_tokens=e.usage.cache_creation_input_tokens),null!=e.usage.cache_read_input_tokens&&(t.usage.cache_read_input_tokens=e.usage.cache_read_input_tokens),null!=e.usage.server_tool_use&&(t.usage.server_tool_use=e.usage.server_tool_use),t;case"content_block_start":return t.content.push(e.content_block),t;case"content_block_delta":{let s=t.content.at(e.index);switch(e.delta.type){case"text_delta":s?.type==="text"&&(s.text+=e.delta.text);break;case"citations_delta":s?.type==="text"&&(s.citations??(s.citations=[]),s.citations.push(e.delta.citation));break;case"input_json_delta":if(s?.type==="tool_use"){let t=s[tU]||"";Object.defineProperty(s,tU,{value:t+=e.delta.partial_json,enumerable:!1,writable:!0}),t&&(s.input=tE(t))}break;case"thinking_delta":s?.type==="thinking"&&(s.thinking+=e.delta.thinking);break;case"signature_delta":s?.type==="thinking"&&(s.signature=e.delta.signature);break;default:e.delta}return t}}},Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("streamEvent",s=>{let r=t.shift();r?r.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),this.on("error",e=>{for(let r of(s=!0,t))r.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new e1(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}class tB extends ty{create(e,t){return this._client.post("/v1/messages/batches",{body:e,...t})}retrieve(e,t){return this._client.get(tv`/v1/messages/batches/${e}`,t)}list(e={},t){return this._client.getAPIList("/v1/messages/batches",te,{query:e,...t})}delete(e,t){return this._client.delete(tv`/v1/messages/batches/${e}`,t)}cancel(e,t){return this._client.post(tv`/v1/messages/batches/${e}/cancel`,t)}async results(e,t){let s=await this.retrieve(e);if(!s.results_url)throw new ef(`No batch \`results_url\`; Has it finished processing? ${s.processing_status} - ${s.id}`);return this._client.get(s.results_url,{...t,headers:t_([{Accept:"application/binary"},t?.headers]),stream:!0,__binaryResponse:!0})._thenUnwrap((e,t)=>tR.fromResponse(t.response,t.controller))}}class tC extends ty{constructor(){super(...arguments),this.batches=new tB(this._client)}create(e,t){e.model in tD&&console.warn(`The model '${e.model}' is deprecated and will reach end-of-life on ${tD[e.model]}
Please migrate to a newer model. Visit https://docs.anthropic.com/en/docs/resources/model-deprecations for more information.`);let s=this._client._options.timeout;if(!e.stream&&null==s){let t=tI[e.model]??void 0;s=this._client.calculateNonstreamingTimeout(e.max_tokens,t)}return this._client.post("/v1/messages",{body:e,timeout:s??6e5,...t,stream:e.stream??!1})}stream(e,t){return tW.createMessage(this,e,t)}countTokens(e,t){return this._client.post("/v1/messages/count_tokens",{body:e,...t})}}let tD={"claude-1.3":"November 6th, 2024","claude-1.3-100k":"November 6th, 2024","claude-instant-1.1":"November 6th, 2024","claude-instant-1.1-100k":"November 6th, 2024","claude-instant-1.2":"November 6th, 2024","claude-3-sonnet-20240229":"July 21st, 2025","claude-2.1":"July 21st, 2025","claude-2.0":"July 21st, 2025"};tC.Batches=tB;class tX extends ty{retrieve(e,t={},s){let{betas:r}=t??{};return this._client.get(tv`/v1/models/${e}`,{...s,headers:t_([{...r?.toString()!=null?{"anthropic-beta":r?.toString()}:void 0},s?.headers])})}list(e={},t){let{betas:s,...r}=e??{};return this._client.getAPIList("/v1/models",te,{query:r,...t,headers:t_([{...s?.toString()!=null?{"anthropic-beta":s?.toString()}:void 0},t?.headers])})}}let tH=e=>void 0!==globalThis.process?globalThis.process.env?.[e]?.trim()??void 0:void 0!==globalThis.Deno?globalThis.Deno.env?.get?.(e)?.trim():void 0;class tJ{constructor({baseURL:e=tH("ANTHROPIC_BASE_URL"),apiKey:t=tH("ANTHROPIC_API_KEY")??null,authToken:s=tH("ANTHROPIC_AUTH_TOKEN")??null,...r}={}){ee.set(this,void 0);let n={apiKey:t,authToken:s,...r,baseURL:e||"https://api.anthropic.com"};if(!n.dangerouslyAllowBrowser&&eC())throw new ef("It looks like you're running in a browser-like environment.\n\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\nIf you understand the risks and have appropriate mitigations in place,\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\n\nnew Anthropic({ apiKey, dangerouslyAllowBrowser: true });\n");this.baseURL=n.baseURL,this.timeout=n.timeout??tK.DEFAULT_TIMEOUT,this.logger=n.logger??console;let a="warn";this.logLevel=a,this.logLevel=eI(n.logLevel,"ClientOptions.logLevel",this)??eI(tH("ANTHROPIC_LOG"),"process.env['ANTHROPIC_LOG']",this)??a,this.fetchOptions=n.fetchOptions,this.maxRetries=n.maxRetries??2,this.fetch=n.fetch??function(){if("undefined"!=typeof fetch)return fetch;throw Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new Anthropic({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}(),ec(this,ee,eQ,"f"),this._options=n,this.apiKey=t,this.authToken=s}withOptions(e){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetchOptions:this.fetchOptions,apiKey:this.apiKey,authToken:this.authToken,...e})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:e,nulls:t}){if(!(this.apiKey&&e.get("x-api-key")||t.has("x-api-key")||this.authToken&&e.get("authorization"))&&!t.has("authorization"))throw Error('Could not resolve authentication method. Expected either apiKey or authToken to be set. Or for one of the "X-Api-Key" or "Authorization" headers to be explicitly omitted')}authHeaders(e){return t_([this.apiKeyAuth(e),this.bearerAuth(e)])}apiKeyAuth(e){if(null!=this.apiKey)return t_([{"X-Api-Key":this.apiKey}])}bearerAuth(e){if(null!=this.authToken)return t_([{Authorization:`Bearer ${this.authToken}`}])}stringifyQuery(e){return Object.entries(e).filter(([e,t])=>void 0!==t).map(([e,t])=>{if("string"==typeof t||"number"==typeof t||"boolean"==typeof t)return`${encodeURIComponent(e)}=${encodeURIComponent(t)}`;if(null===t)return`${encodeURIComponent(e)}=`;throw new ef(`Cannot stringify type ${typeof t}; Expected string, number, boolean, or null. If you need to pass nested query parameters, you can manually encode them, e.g. { query: { 'foo[key1]': value1, 'foo[key2]': value2 } }, and please open a GitHub issue requesting better support for your use case.`)}).join("&")}getUserAgent(){return`${this.constructor.name}/JS ${eB}`}defaultIdempotencyKey(){return`stainless-node-retry-${eh()}`}makeStatusError(e,t,s,r){return eg.generate(e,t,s,r)}buildURL(e,t){let s=new URL(e$(e)?e:this.baseURL+(this.baseURL.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),r=this.defaultQuery();return!function(e){if(!e)return!0;for(let t in e)return!1;return!0}(r)&&(t={...r,...t}),"object"==typeof t&&t&&!Array.isArray(t)&&(s.search=this.stringifyQuery(t)),s.toString()}_calculateNonstreamingTimeout(e){if(3600*e/128e3>600)throw new ef("Streaming is strongly recommended for operations that may take longer than 10 minutes. See https://github.com/anthropics/anthropic-sdk-python#streaming-responses for more details");return 6e5}async prepareOptions(e){}async prepareRequest(e,{url:t,options:s}){}get(e,t){return this.methodRequest("get",e,t)}post(e,t){return this.methodRequest("post",e,t)}patch(e,t){return this.methodRequest("patch",e,t)}put(e,t){return this.methodRequest("put",e,t)}delete(e,t){return this.methodRequest("delete",e,t)}methodRequest(e,t,s){return this.request(Promise.resolve(s).then(s=>({method:e,path:t,...s})))}request(e,t=null){return new e9(this,this.makeRequest(e,t,void 0))}async makeRequest(e,t,s){let r=await e,n=r.maxRetries??this.maxRetries;null==t&&(t=n),await this.prepareOptions(r);let{req:a,url:i,timeout:o}=this.buildRequest(r,{retryCount:n-t});await this.prepareRequest(a,{url:i,options:r});let l="log_"+(0x1000000*Math.random()|0).toString(16).padStart(6,"0"),c=void 0===s?"":`, retryOf: ${s}`,u=Date.now();if(eU(this).debug(`[${l}] sending request`,eW({retryOfRequestLogID:s,method:r.method,url:i,options:r,headers:a.headers})),r.signal?.aborted)throw new em;let h=new AbortController,d=await this.fetchWithTimeout(i,a,o,h).catch(ep),p=Date.now();if(d instanceof Error){let e=`retrying, ${t} attempts remaining`;if(r.signal?.aborted)throw new em;let n=ed(d)||/timed? ?out/i.test(String(d)+("cause"in d?String(d.cause):""));if(t)return eU(this).info(`[${l}] connection ${n?"timed out":"failed"} - ${e}`),eU(this).debug(`[${l}] connection ${n?"timed out":"failed"} (${e})`,eW({retryOfRequestLogID:s,url:i,durationMs:p-u,message:d.message})),this.retryRequest(r,t,s??l);if(eU(this).info(`[${l}] connection ${n?"timed out":"failed"} - error; no more retries left`),eU(this).debug(`[${l}] connection ${n?"timed out":"failed"} (error; no more retries left)`,eW({retryOfRequestLogID:s,url:i,durationMs:p-u,message:d.message})),n)throw new eb;throw new ey({cause:d})}let f=[...d.headers.entries()].filter(([e])=>"request-id"===e).map(([e,t])=>", "+e+": "+JSON.stringify(t)).join(""),g=`[${l}${c}${f}] ${a.method} ${i} ${d.ok?"succeeded":"failed"} with status ${d.status} in ${p-u}ms`;if(!d.ok){let e=this.shouldRetry(d);if(t&&e){let e=`retrying, ${t} attempts remaining`;return await eG(d.body),eU(this).info(`${g} - ${e}`),eU(this).debug(`[${l}] response error (${e})`,eW({retryOfRequestLogID:s,url:d.url,status:d.status,headers:d.headers,durationMs:p-u})),this.retryRequest(r,t,s??l,d.headers)}let n=e?"error; no more retries left":"error; not retryable";eU(this).info(`${g} - ${n}`);let a=await d.text().catch(e=>ep(e).message),i=eE(a),o=i?void 0:a;throw eU(this).debug(`[${l}] response error (${n})`,eW({retryOfRequestLogID:s,url:d.url,status:d.status,headers:d.headers,message:o,durationMs:Date.now()-u})),this.makeStatusError(d.status,i,o,d.headers)}return eU(this).info(g),eU(this).debug(`[${l}] response start`,eW({retryOfRequestLogID:s,url:d.url,status:d.status,headers:d.headers,durationMs:p-u})),{response:d,options:r,controller:h,requestLogID:l,retryOfRequestLogID:s,startTime:u}}getAPIList(e,t,s){return this.requestAPIList(t,{method:"get",path:e,...s})}requestAPIList(e,t){return new e7(this,this.makeRequest(t,null,void 0),e)}async fetchWithTimeout(e,t,s,r){let{signal:n,method:a,...i}=t||{};n&&n.addEventListener("abort",()=>r.abort());let o=setTimeout(()=>r.abort(),s),l=globalThis.ReadableStream&&i.body instanceof globalThis.ReadableStream||"object"==typeof i.body&&null!==i.body&&Symbol.asyncIterator in i.body,c={signal:r.signal,...l?{duplex:"half"}:{},method:"GET",...i};a&&(c.method=a.toUpperCase());try{return await this.fetch.call(void 0,e,c)}finally{clearTimeout(o)}}shouldRetry(e){let t=e.headers.get("x-should-retry");return"true"===t||"false"!==t&&(408===e.status||409===e.status||429===e.status||!!(e.status>=500))}async retryRequest(e,t,s,r){let n,a=r?.get("retry-after-ms");if(a){let e=parseFloat(a);Number.isNaN(e)||(n=e)}let i=r?.get("retry-after");if(i&&!n){let e=parseFloat(i);n=Number.isNaN(e)?Date.parse(i)-Date.now():1e3*e}if(!(n&&0<=n&&n<6e4)){let s=e.maxRetries??this.maxRetries;n=this.calculateDefaultRetryTimeoutMillis(t,s)}return await ej(n),this.makeRequest(e,t-1,s)}calculateDefaultRetryTimeoutMillis(e,t){return Math.min(.5*Math.pow(2,t-e),8)*(1-.25*Math.random())*1e3}calculateNonstreamingTimeout(e,t){if(36e5*e/128e3>6e5||null!=t&&e>t)throw new ef("Streaming is strongly recommended for operations that may token longer than 10 minutes. See https://github.com/anthropics/anthropic-sdk-typescript#long-requests for more details");return 6e5}buildRequest(e,{retryCount:t=0}={}){let s={...e},{method:r,path:n,query:a}=s,i=this.buildURL(n,a);"timeout"in s&&eA("timeout",s.timeout),s.timeout=s.timeout??this.timeout;let{bodyHeaders:o,body:l}=this.buildBody({options:s}),c=this.buildHeaders({options:e,method:r,bodyHeaders:o,retryCount:t});return{req:{method:r,headers:c,...s.signal&&{signal:s.signal},...globalThis.ReadableStream&&l instanceof globalThis.ReadableStream&&{duplex:"half"},...l&&{body:l},...this.fetchOptions??{},...s.fetchOptions??{}},url:i,timeout:s.timeout}}buildHeaders({options:e,method:t,bodyHeaders:s,retryCount:r}){let n={};this.idempotencyHeader&&"get"!==t&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),n[this.idempotencyHeader]=e.idempotencyKey);let a=t_([n,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(r),...e.timeout?{"X-Stainless-Timeout":String(Math.trunc(e.timeout/1e3))}:{},...eJ(),...this._options.dangerouslyAllowBrowser?{"anthropic-dangerous-direct-browser-access":"true"}:void 0,"anthropic-version":"2023-06-01"},this.authHeaders(e),this._options.defaultHeaders,s,e.headers]);return this.validateHeaders(a),a.values}buildBody({options:{body:e,headers:t}}){if(!e)return{bodyHeaders:void 0,body:void 0};let s=t_([t]);return ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof DataView||"string"==typeof e&&s.values.has("content-type")||e instanceof Blob||e instanceof FormData||e instanceof URLSearchParams||globalThis.ReadableStream&&e instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:e}:"object"==typeof e&&(Symbol.asyncIterator in e||Symbol.iterator in e&&"next"in e&&"function"==typeof e.next)?{bodyHeaders:void 0,body:eV(e)}:eu(this,ee,"f").call(this,{body:e,headers:s})}}ee=new WeakMap,tJ.Anthropic=tJ,tJ.HUMAN_PROMPT="\n\nHuman:",tJ.AI_PROMPT="\n\nAssistant:",tJ.DEFAULT_TIMEOUT=6e5,tJ.AnthropicError=ef,tJ.APIError=eg,tJ.APIConnectionError=ey,tJ.APIConnectionTimeoutError=eb,tJ.APIUserAbortError=em,tJ.NotFoundError=ev,tJ.ConflictError=eS,tJ.RateLimitError=eR,tJ.BadRequestError=ew,tJ.AuthenticationError=e_,tJ.InternalServerError=eM,tJ.PermissionDeniedError=ek,tJ.UnprocessableEntityError=ex,tJ.toFile=tg;class tK extends tJ{constructor(){super(...arguments),this.completions=new tL(this),this.messages=new tC(this),this.models=new tX(this),this.beta=new tN(this)}}tK.Completions=tL,tK.Messages=tC,tK.Models=tX,tK.Beta=tN;let{HUMAN_PROMPT:tV,AI_PROMPT:tz}=tK,tG=new tK({apiKey:process.env.ANTHROPIC_API_KEY});async function tQ(e){try{var t;let r=(t=e,`
You are an expert IELTS instructor providing detailed feedback for a candidate named ${t.candidateName}.

Test Results:
- Listening: ${t.listeningScore}/40 (Band ${t.listeningBandScore})
- Reading: ${t.readingScore}/40 (Band ${t.readingBandScore})
- Writing: Task 1: ${t.writingTask1Score}, Task 2: ${t.writingTask2Score} (Band ${t.writingBandScore})
- Speaking: Fluency: ${t.speakingFluencyScore}, Lexical: ${t.speakingLexicalScore}, Grammar: ${t.speakingGrammarScore}, Pronunciation: ${t.speakingPronunciationScore} (Band ${t.speakingBandScore})
- Overall Band Score: ${t.overallBandScore}

Please provide comprehensive feedback in the following JSON format:
{
  "listeningFeedback": "Detailed analysis of listening performance with specific improvement areas",
  "readingFeedback": "Detailed analysis of reading performance with specific improvement areas",
  "writingFeedback": "Detailed analysis of writing performance covering both tasks",
  "speakingFeedback": "Detailed analysis of speaking performance covering all criteria",
  "overallFeedback": "Overall performance summary and band score explanation",
  "studyRecommendations": "Specific study recommendations and resources",
  "strengths": ["strength1", "strength2", "strength3"],
  "weaknesses": ["weakness1", "weakness2", "weakness3"],
  "studyPlan": "Detailed 4-week study plan with daily activities"
}

Make the feedback constructive, specific, and actionable. Focus on concrete improvement strategies.
`),n=(await tG.messages.create({model:"claude-3-5-sonnet-20241022",max_tokens:4e3,temperature:.7,messages:[{role:"user",content:r}]})).content[0];if("text"===n.type){var s=n.text;try{let e=s.match(/\{[\s\S]*\}/);if(!e)throw Error("No JSON found in response");let t=JSON.parse(e[0]);for(let e of["listeningFeedback","readingFeedback","writingFeedback","speakingFeedback","overallFeedback","studyRecommendations","strengths","weaknesses","studyPlan"])if(!t[e])throw Error(`Missing required field: ${e}`);return t}catch(e){throw console.error("Error parsing feedback response:",e),Error("Failed to parse AI feedback response")}}throw Error("Invalid response format from Claude API")}catch(e){throw console.error("Error generating AI feedback:",e),Error("Failed to generate AI feedback")}}var tY=s(94634);async function tZ(e){let t=await (0,ei.j2)();if(!t?.user?.organizationId)return ea.NextResponse.json({error:"Unauthorized"},{status:401});let{testResultId:s}=await e.json();try{let e=await eo.db.select({testResult:el.testResults,candidate:el.candidates,testRegistration:el.testRegistrations}).from(el.testResults).innerJoin(el.testRegistrations,(0,tY.eq)(el.testResults.testRegistrationId,el.testRegistrations.id)).innerJoin(el.candidates,(0,tY.eq)(el.testRegistrations.candidateId,el.candidates.id)).where((0,tY.eq)(el.testResults.id,s)).limit(1);if(0===e.length)return ea.NextResponse.json({error:"Test result not found"},{status:404});let{testResult:t,candidate:r}=e[0],n=await eo.db.select().from(el.aiFeedback).where((0,tY.eq)(el.aiFeedback.testResultId,s)).limit(1);if(n.length>0)return ea.NextResponse.json(n[0]);let[a]=await eo.db.insert(el.aiFeedback).values({testResultId:s,listeningFeedback:"",readingFeedback:"",writingFeedback:"",speakingFeedback:"",overallFeedback:"",studyRecommendations:"",strengths:[],weaknesses:[],studyPlan:{}}).returning();return t0(s,t,r),ea.NextResponse.json(a,{status:201})}catch(e){return console.error("Error initiating feedback generation:",e),ea.NextResponse.json({error:"Failed to generate feedback"},{status:500})}}async function t0(e,t,s){try{let r={testResultId:e,candidateName:s.fullName,listeningScore:t.listeningScore||0,listeningBandScore:parseFloat(t.listeningBandScore)||0,readingScore:t.readingScore||0,readingBandScore:parseFloat(t.readingBandScore)||0,writingTask1Score:parseFloat(t.writingTask1Score)||0,writingTask2Score:parseFloat(t.writingTask2Score)||0,writingBandScore:parseFloat(t.writingBandScore)||0,speakingFluencyScore:parseFloat(t.speakingFluencyScore)||0,speakingLexicalScore:parseFloat(t.speakingLexicalScore)||0,speakingGrammarScore:parseFloat(t.speakingGrammarScore)||0,speakingPronunciationScore:parseFloat(t.speakingPronunciationScore)||0,speakingBandScore:parseFloat(t.speakingBandScore)||0,overallBandScore:parseFloat(t.overallBandScore)||0},n=await tQ(r);await eo.db.update(el.aiFeedback).set({listeningFeedback:n.listeningFeedback,readingFeedback:n.readingFeedback,writingFeedback:n.writingFeedback,speakingFeedback:n.speakingFeedback,overallFeedback:n.overallFeedback,studyRecommendations:n.studyRecommendations,strengths:n.strengths,weaknesses:n.weaknesses,studyPlan:{plan:n.studyPlan}}).where((0,tY.eq)(el.aiFeedback.testResultId,e))}catch(t){console.error("Error generating feedback:",t),await eo.db.update(el.aiFeedback).set({overallFeedback:"Failed to generate feedback. Please try again later."}).where((0,tY.eq)(el.aiFeedback.testResultId,e))}}async function t1(e){let t=await (0,ei.j2)();if(!t?.user?.organizationId)return ea.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:s}=new URL(e.url),r=s.get("testResultId");if(!r)return ea.NextResponse.json({error:"Test result ID required"},{status:400});try{let e=await eo.db.select().from(el.aiFeedback).where((0,tY.eq)(el.aiFeedback.testResultId,r)).limit(1);if(0===e.length)return ea.NextResponse.json({error:"Feedback not found"},{status:404});return ea.NextResponse.json(e[0])}catch(e){return console.error("Error fetching feedback:",e),ea.NextResponse.json({error:"Failed to fetch feedback"},{status:500})}}let t2=new es.AppRouteRouteModule({definition:{kind:er.RouteKind.APP_ROUTE,page:"/api/ai/feedback/route",pathname:"/api/ai/feedback",filename:"route",bundlePath:"app/api/ai/feedback/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\ai\\feedback\\route.ts",nextConfigOutput:"",userland:et}),{workAsyncStorage:t4,workUnitAsyncStorage:t5,serverHooks:t6}=t2;function t3(){return(0,en.patchFetch)({workAsyncStorage:t4,workUnitAsyncStorage:t5})}},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74998:e=>{"use strict";e.exports=require("perf_hooks")},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},91645:e=>{"use strict";e.exports=require("net")},96487:()=>{},96559:(e,t,s)=>{"use strict";e.exports=s(44870)}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,5552,2190,1057,6326],()=>s(48127));module.exports=r})();