exports.id=2140,exports.ids=[2140],exports.modules={25:(t,e,r)=>{"use strict";let n=!0,i=r(74075),o=r(19011);i.deflateSync||(n=!1);let a=r(30613),s=r(42008),h=r(43273),u=r(83182),l=r(38962);t.exports=function(t,e){let r,c,f,d;if(!n)throw Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");let p=[],g=new a(t);if(new h(e,{read:g.read.bind(g),error:function(t){r=t},metadata:function(t){c=t},gamma:function(t){f=t},palette:function(t){c.palette=t},transColor:function(t){c.transColor=t},inflateData:function(t){p.push(t)},simpleTransparency:function(){c.alpha=!0}}).start(),g.process(),r)throw r;let m=Buffer.concat(p);if(p.length=0,c.interlace)d=i.inflateSync(m);else{let t=((c.width*c.bpp*c.depth+7>>3)+1)*c.height;d=o(m,{chunkSize:t,maxLength:t})}if(m=null,!d||!d.length)throw Error("bad png - invalid inflate data response");let b=s.process(d,c);m=null;let v=u.dataToBitMap(b,c);b=null;let y=l(v,c);return c.data=y,c.gamma=f||0,c}},2913:t=>{"use strict";t.exports={PNG_SIGNATURE:[137,80,78,71,13,10,26,10],TYPE_IHDR:0x49484452,TYPE_IEND:0x49454e44,TYPE_IDAT:0x49444154,TYPE_PLTE:0x504c5445,TYPE_tRNS:0x74524e53,TYPE_gAMA:0x67414d41,COLORTYPE_GRAYSCALE:0,COLORTYPE_PALETTE:1,COLORTYPE_COLOR:2,COLORTYPE_ALPHA:4,COLORTYPE_PALETTE_COLOR:3,COLORTYPE_COLOR_ALPHA:6,COLORTYPE_TO_BPP_MAP:{0:1,2:3,3:1,4:2,6:4},GAMMA_DIVISION:1e5}},6617:(t,e,r)=>{"use strict";let n=r(2913);t.exports=function(t,e,r,i){let o=-1!==[n.COLORTYPE_COLOR_ALPHA,n.COLORTYPE_ALPHA].indexOf(i.colorType);if(i.colorType===i.inputColorType){let e,r=(new DataView(e=new ArrayBuffer(2)).setInt16(0,256,!0),256!==new Int16Array(e)[0]);if(8===i.bitDepth||16===i.bitDepth&&r)return t}let a=16!==i.bitDepth?t:new Uint16Array(t.buffer),s=255,h=n.COLORTYPE_TO_BPP_MAP[i.inputColorType];4!==h||i.inputHasAlpha||(h=3);let u=n.COLORTYPE_TO_BPP_MAP[i.colorType];16===i.bitDepth&&(s=65535,u*=2);let l=Buffer.alloc(e*r*u),c=0,f=0,d=i.bgColor||{};void 0===d.red&&(d.red=s),void 0===d.green&&(d.green=s),void 0===d.blue&&(d.blue=s);for(let t=0;t<r;t++)for(let t=0;t<e;t++){let t=function(){let t,e,r,h=s;switch(i.inputColorType){case n.COLORTYPE_COLOR_ALPHA:h=a[c+3],t=a[c],e=a[c+1],r=a[c+2];break;case n.COLORTYPE_COLOR:t=a[c],e=a[c+1],r=a[c+2];break;case n.COLORTYPE_ALPHA:h=a[c+1],e=t=a[c],r=t;break;case n.COLORTYPE_GRAYSCALE:e=t=a[c],r=t;break;default:throw Error("input color type:"+i.inputColorType+" is not supported at present")}return i.inputHasAlpha&&!o&&(h/=s,t=Math.min(Math.max(Math.round((1-h)*d.red+h*t),0),s),e=Math.min(Math.max(Math.round((1-h)*d.green+h*e),0),s),r=Math.min(Math.max(Math.round((1-h)*d.blue+h*r),0),s)),{red:t,green:e,blue:r,alpha:h}}(a,c);switch(i.colorType){case n.COLORTYPE_COLOR_ALPHA:case n.COLORTYPE_COLOR:8===i.bitDepth?(l[f]=t.red,l[f+1]=t.green,l[f+2]=t.blue,o&&(l[f+3]=t.alpha)):(l.writeUInt16BE(t.red,f),l.writeUInt16BE(t.green,f+2),l.writeUInt16BE(t.blue,f+4),o&&l.writeUInt16BE(t.alpha,f+6));break;case n.COLORTYPE_ALPHA:case n.COLORTYPE_GRAYSCALE:{let e=(t.red+t.green+t.blue)/3;8===i.bitDepth?(l[f]=e,o&&(l[f+1]=t.alpha)):(l.writeUInt16BE(e,f),o&&l.writeUInt16BE(t.alpha,f+2));break}default:throw Error("unrecognised color Type "+i.colorType)}c+=h,f+=u}return l}},8766:(t,e,r)=>{let n=r(92081);e.render=function(t,e,r){var i;let o=r,a=e;void 0!==o||e&&e.getContext||(o=e,e=void 0),e||(a=function(){try{return document.createElement("canvas")}catch(t){throw Error("You need to specify a canvas element")}}()),o=n.getOptions(o);let s=n.getImageWidth(t.modules.size,o),h=a.getContext("2d"),u=h.createImageData(s,s);return n.qrToImageData(u.data,t,o),i=a,h.clearRect(0,0,i.width,i.height),i.style||(i.style={}),i.height=s,i.width=s,i.style.height=s+"px",i.style.width=s+"px",h.putImageData(u,0,0),a},e.renderToDataURL=function(t,r,n){let i=n;void 0!==i||r&&r.getContext||(i=r,r=void 0),i||(i={});let o=e.render(t,r,i),a=i.type||"image/png",s=i.rendererOpts||{};return o.toDataURL(a,s.quality)}},11753:(t,e,r)=>{let n=r(53117).getSymbolSize;e.getPositions=function(t){let e=n(t);return[[0,0],[e-7,0],[0,e-7]]}},12831:t=>{"use strict";t.exports=function(t,e,r){let n=t+e-r,i=Math.abs(n-t),o=Math.abs(n-e),a=Math.abs(n-r);return i<=o&&i<=a?t:o<=a?e:r}},19011:(t,e,r)=>{"use strict";let n=r(12412).ok,i=r(74075),o=r(28354),a=r(79428).kMaxLength;function s(t){if(!(this instanceof s))return new s(t);t&&t.chunkSize<i.Z_MIN_CHUNK&&(t.chunkSize=i.Z_MIN_CHUNK),i.Inflate.call(this,t),this._offset=void 0===this._offset?this._outOffset:this._offset,this._buffer=this._buffer||this._outBuffer,t&&null!=t.maxLength&&(this._maxLength=t.maxLength)}function h(t,e){e&&process.nextTick(e),t._handle&&(t._handle.close(),t._handle=null)}function u(t,e){var r=new s(e),n=t;if("string"==typeof n&&(n=Buffer.from(n)),!(n instanceof Buffer))throw TypeError("Not a string or buffer");let o=r._finishFlushFlag;return null==o&&(o=i.Z_FINISH),r._processChunk(n,o)}s.prototype._processChunk=function(t,e,r){let o,s;if("function"==typeof r)return i.Inflate._processChunk.call(this,t,e,r);let u=this,l=t&&t.length,c=this._chunkSize-this._offset,f=this._maxLength,d=0,p=[],g=0;this.on("error",function(t){o=t}),n(this._handle,"zlib binding closed");do s=(s=this._handle.writeSync(e,t,d,l,this._buffer,this._offset,c))||this._writeState;while(!this._hadError&&function(t,e){if(u._hadError)return;let r=c-e;if(n(r>=0,"have should not go down"),r>0){let t=u._buffer.slice(u._offset,u._offset+r);if(u._offset+=r,t.length>f&&(t=t.slice(0,f)),p.push(t),g+=t.length,0==(f-=t.length))return!1}return(0===e||u._offset>=u._chunkSize)&&(c=u._chunkSize,u._offset=0,u._buffer=Buffer.allocUnsafe(u._chunkSize)),0===e&&(d+=l-t,l=t,!0)}(s[0],s[1]));if(this._hadError)throw o;if(g>=a)throw h(this),RangeError("Cannot create final Buffer. It would be larger than 0x"+a.toString(16)+" bytes");let m=Buffer.concat(p,g);return h(this),m},o.inherits(s,i.Inflate),t.exports=e=u,e.Inflate=s,e.createInflate=function(t){return new s(t)},e.inflateSync=u},22306:(t,e,r)=>{let n=r(65440),i=r(93032),o=r(8766),a=r(97359);function s(t,e,r,o,a){let s=[].slice.call(arguments,1),h=s.length,u="function"==typeof s[h-1];if(!u&&!n())throw Error("Callback required as last argument");if(u){if(h<2)throw Error("Too few arguments provided");2===h?(a=r,r=e,e=o=void 0):3===h&&(e.getContext&&void 0===a?(a=o,o=void 0):(a=o,o=r,r=e,e=void 0))}else{if(h<1)throw Error("Too few arguments provided");return 1===h?(r=e,e=o=void 0):2!==h||e.getContext||(o=r,r=e,e=void 0),new Promise(function(n,a){try{let a=i.create(r,o);n(t(a,e,o))}catch(t){a(t)}})}try{let n=i.create(r,o);a(null,t(n,e,o))}catch(t){a(t)}}i.create,e.toCanvas=s.bind(null,o.render),s.bind(null,o.renderToDataURL),s.bind(null,function(t,e,r){return a.render(t,r)})},23320:(t,e,r)=>{let n=r(85585);function i(t){this.mode=n.NUMERIC,this.data=t.toString()}i.getBitsLength=function(t){return 10*Math.floor(t/3)+(t%3?t%3*3+1:0)},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(t){let e,r,n;for(e=0;e+3<=this.data.length;e+=3)n=parseInt(this.data.substr(e,3),10),t.put(n,10);let i=this.data.length-e;i>0&&(n=parseInt(this.data.substr(e),10),t.put(n,3*i+1))},t.exports=i},24071:(t,e,r)=>{let n=r(29021),i=r(37611).O,o=r(92081);e.render=function(t,e){let r=o.getOptions(e),n=r.rendererOpts,a=o.getImageWidth(t.modules.size,r);n.width=a,n.height=a;let s=new i(n);return o.qrToImageData(s.data,t,r),s},e.renderToDataURL=function(t,r,n){void 0===n&&(n=r,r=void 0),e.renderToBuffer(t,r,function(t,e){t&&n(t);let r="data:image/png;base64,";r+=e.toString("base64"),n(null,r)})},e.renderToBuffer=function(t,r,n){void 0===n&&(n=r,r=void 0);let i=e.render(t,r),o=[];i.on("error",n),i.on("data",function(t){o.push(t)}),i.on("end",function(){n(null,Buffer.concat(o))}),i.pack()},e.renderToFile=function(t,r,i,o){void 0===o&&(o=i,i=void 0);let a=!1,s=(...t)=>{a||(a=!0,o.apply(null,t))},h=n.createWriteStream(t);h.on("error",s),h.on("close",s),e.renderToFileStream(h,r,i)},e.renderToFileStream=function(t,r,n){e.render(r,n).pack().pipe(t)}},24956:t=>{function e(r){return t.exports=e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t.exports.__esModule=!0,t.exports.default=t.exports,e(r)}t.exports=e,t.exports.__esModule=!0,t.exports.default=t.exports},27108:(t,e,r)=>{let n=r(85585),i=r(53117);function o(t){this.mode=n.KANJI,this.data=t}o.getBitsLength=function(t){return 13*t},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(t){let e;for(e=0;e<this.data.length;e++){let r=i.toSJIS(this.data[e]);if(r>=33088&&r<=40956)r-=33088;else if(r>=57408&&r<=60351)r-=49472;else throw Error("Invalid SJIS character: "+this.data[e]+"\nMake sure your charset is UTF-8");r=(r>>>8&255)*192+(255&r),t.put(r,13)}},t.exports=o},27782:(t,e,r)=>{e.render=r(97359).render,e.renderToFile=function(t,n,i,o){void 0===o&&(o=i,i=void 0);let a=r(29021),s=e.render(n,i);a.writeFile(t,'<?xml version="1.0" encoding="utf-8"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">'+s,o)}},28574:(t,e)=>{let r=new Uint8Array(512),n=new Uint8Array(256);!function(){let t=1;for(let e=0;e<255;e++)r[e]=t,n[t]=e,256&(t<<=1)&&(t^=285);for(let t=255;t<512;t++)r[t]=r[t-255]}(),e.log=function(t){if(t<1)throw Error("log("+t+")");return n[t]},e.exp=function(t){return r[t]},e.mul=function(t,e){return 0===t||0===e?0:r[n[t]+n[e]]}},30279:(t,e)=>{let r="\x1b[37m",n="\x1b[30m",i="\x1b[0m",o="\x1b[47m"+n,a="\x1b[40m"+r,s=function(t,e,r,n){let i=e+1;return r>=i||n>=i||n<-1||r<-1?"0":r>=e||n>=e||n<0||r<0?"1":t[n*e+r]?"2":"1"},h=function(t,e,r,n){return s(t,e,r,n)+s(t,e,r,n+1)};e.render=function(t,e,s){var u,l;let c=t.modules.size,f=t.modules.data,d=!!(e&&e.inverse),p=e&&e.inverse?a:o,g={"00":i+" "+p,"01":i+(u=d?n:r)+"▄"+p,"02":i+(l=d?r:n)+"▄"+p,10:i+u+"▀"+p,11:" ",12:"▄",20:i+l+"▀"+p,21:"▀",22:"█"},m=i+"\n"+p,b=p;for(let t=-1;t<c+1;t+=2){for(let e=-1;e<c;e++)b+=g[h(f,c,e,t)];b+=g[h(f,c,c,t)]+m}return b+=i,"function"==typeof s&&s(null,b),b}},30613:t=>{"use strict";let e=t.exports=function(t){this._buffer=t,this._reads=[]};e.prototype.read=function(t,e){this._reads.push({length:Math.abs(t),allowLess:t<0,func:e})},e.prototype.process=function(){for(;this._reads.length>0&&this._buffer.length;){let t=this._reads[0];if(this._buffer.length&&(this._buffer.length>=t.length||t.allowLess)){this._reads.shift();let e=this._buffer;this._buffer=e.slice(t.length),t.func.call(this,e.slice(0,t.length))}else break}return this._reads.length>0?Error("There are some read requests waitng on finished stream"):this._buffer.length>0?Error("unrecognised content at end of stream"):void 0}},35958:t=>{function e(){this.buffer=[],this.length=0}e.prototype={get:function(t){let e=Math.floor(t/8);return(this.buffer[e]>>>7-t%8&1)==1},put:function(t,e){for(let r=0;r<e;r++)this.putBit((t>>>e-r-1&1)==1)},getLengthInBits:function(){return this.length},putBit:function(t){let e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}},t.exports=e},37611:(t,e,r)=>{"use strict";let n=r(28354),i=r(27910),o=r(70480),a=r(70065),s=r(97651),h=e.O=function(t){i.call(this),t=t||{},this.width=0|t.width,this.height=0|t.height,this.data=this.width>0&&this.height>0?Buffer.alloc(4*this.width*this.height):null,t.fill&&this.data&&this.data.fill(0),this.gamma=0,this.readable=this.writable=!0,this._parser=new o(t),this._parser.on("error",this.emit.bind(this,"error")),this._parser.on("close",this._handleClose.bind(this)),this._parser.on("metadata",this._metadata.bind(this)),this._parser.on("gamma",this._gamma.bind(this)),this._parser.on("parsed",(function(t){this.data=t,this.emit("parsed",t)}).bind(this)),this._packer=new a(t),this._packer.on("data",this.emit.bind(this,"data")),this._packer.on("end",this.emit.bind(this,"end")),this._parser.on("close",this._handleClose.bind(this)),this._packer.on("error",this.emit.bind(this,"error"))};n.inherits(h,i),h.sync=s,h.prototype.pack=function(){return this.data&&this.data.length?process.nextTick((function(){this._packer.pack(this.data,this.width,this.height,this.gamma)}).bind(this)):this.emit("error","No data provided"),this},h.prototype.parse=function(t,e){if(e){let t,r;t=(function(t){this.removeListener("error",r),this.data=t,e(null,this)}).bind(this),r=(function(r){this.removeListener("parsed",t),e(r,null)}).bind(this),this.once("parsed",t),this.once("error",r)}return this.end(t),this},h.prototype.write=function(t){return this._parser.write(t),!0},h.prototype.end=function(t){this._parser.end(t)},h.prototype._metadata=function(t){this.width=t.width,this.height=t.height,this.emit("metadata",t)},h.prototype._gamma=function(t){this.gamma=t},h.prototype._handleClose=function(){this._parser.writable||this._packer.readable||this.emit("close")},h.bitblt=function(t,e,r,n,i,o,a,s){if(n|=0,i|=0,o|=0,a|=0,s|=0,(r|=0)>t.width||n>t.height||r+i>t.width||n+o>t.height)throw Error("bitblt reading outside image");if(a>e.width||s>e.height||a+i>e.width||s+o>e.height)throw Error("bitblt writing outside image");for(let h=0;h<o;h++)t.data.copy(e.data,(s+h)*e.width+a<<2,(n+h)*t.width+r<<2,(n+h)*t.width+r+i<<2)},h.prototype.bitblt=function(t,e,r,n,i,o,a){return h.bitblt(this,t,e,r,n,i,o,a),this},h.adjustGamma=function(t){if(t.gamma){for(let e=0;e<t.height;e++)for(let r=0;r<t.width;r++){let n=t.width*e+r<<2;for(let e=0;e<3;e++){let r=t.data[n+e]/255;r=Math.pow(r,1/2.2/t.gamma),t.data[n+e]=Math.round(255*r)}}t.gamma=0}},h.prototype.adjustGamma=function(){h.adjustGamma(this)}},37781:(t,e,r)=>{let n=r(85585);function i(t){this.mode=n.BYTE,"string"==typeof t?this.data=new TextEncoder().encode(t):this.data=new Uint8Array(t)}i.getBitsLength=function(t){return 8*t},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(t){for(let e=0,r=this.data.length;e<r;e++)t.put(this.data[e],8)},t.exports=i},38125:(t,e)=>{e.isValid=function(t){return!isNaN(t)&&t>=1&&t<=40}},38205:(t,e)=>{e.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};let r={N1:3,N2:3,N3:40,N4:10};e.isValid=function(t){return null!=t&&""!==t&&!isNaN(t)&&t>=0&&t<=7},e.from=function(t){return e.isValid(t)?parseInt(t,10):void 0},e.getPenaltyN1=function(t){let e=t.size,n=0,i=0,o=0,a=null,s=null;for(let h=0;h<e;h++){i=o=0,a=s=null;for(let u=0;u<e;u++){let e=t.get(h,u);e===a?i++:(i>=5&&(n+=r.N1+(i-5)),a=e,i=1),(e=t.get(u,h))===s?o++:(o>=5&&(n+=r.N1+(o-5)),s=e,o=1)}i>=5&&(n+=r.N1+(i-5)),o>=5&&(n+=r.N1+(o-5))}return n},e.getPenaltyN2=function(t){let e=t.size,n=0;for(let r=0;r<e-1;r++)for(let i=0;i<e-1;i++){let e=t.get(r,i)+t.get(r,i+1)+t.get(r+1,i)+t.get(r+1,i+1);(4===e||0===e)&&n++}return n*r.N2},e.getPenaltyN3=function(t){let e=t.size,n=0,i=0,o=0;for(let r=0;r<e;r++){i=o=0;for(let a=0;a<e;a++)i=i<<1&2047|t.get(r,a),a>=10&&(1488===i||93===i)&&n++,o=o<<1&2047|t.get(a,r),a>=10&&(1488===o||93===o)&&n++}return n*r.N3},e.getPenaltyN4=function(t){let e=0,n=t.data.length;for(let r=0;r<n;r++)e+=t.data[r];return Math.abs(Math.ceil(100*e/n/5)-10)*r.N4},e.applyMask=function(t,r){let n=r.size;for(let i=0;i<n;i++)for(let o=0;o<n;o++)r.isReserved(o,i)||r.xor(o,i,function(t,r,n){switch(t){case e.Patterns.PATTERN000:return(r+n)%2==0;case e.Patterns.PATTERN001:return r%2==0;case e.Patterns.PATTERN010:return n%3==0;case e.Patterns.PATTERN011:return(r+n)%3==0;case e.Patterns.PATTERN100:return(Math.floor(r/2)+Math.floor(n/3))%2==0;case e.Patterns.PATTERN101:return r*n%2+r*n%3==0;case e.Patterns.PATTERN110:return(r*n%2+r*n%3)%2==0;case e.Patterns.PATTERN111:return(r*n%3+(r+n)%2)%2==0;default:throw Error("bad maskPattern:"+t)}}(t,o,i))},e.getBestMask=function(t,r){let n=Object.keys(e.Patterns).length,i=0,o=1/0;for(let a=0;a<n;a++){r(a),e.applyMask(a,t);let n=e.getPenaltyN1(t)+e.getPenaltyN2(t)+e.getPenaltyN3(t)+e.getPenaltyN4(t);e.applyMask(a,t),n<o&&(o=n,i=a)}return i}},38962:t=>{"use strict";t.exports=function(t,e){let r=e.depth,n=e.width,i=e.height,o=e.colorType,a=e.transColor,s=e.palette,h=t;return 3===o?!function(t,e,r,n,i){let o=0;for(let a=0;a<n;a++)for(let n=0;n<r;n++){let r=i[t[o]];if(!r)throw Error("index "+t[o]+" not in palette");for(let t=0;t<4;t++)e[o+t]=r[t];o+=4}}(t,h,n,i,s):(a&&function(t,e,r,n,i){let o=0;for(let a=0;a<n;a++)for(let n=0;n<r;n++){let r=!1;if(1===i.length?i[0]===t[o]&&(r=!0):i[0]===t[o]&&i[1]===t[o+1]&&i[2]===t[o+2]&&(r=!0),r)for(let t=0;t<4;t++)e[o+t]=0;o+=4}}(t,h,n,i,a),8!==r&&(16===r&&(h=Buffer.alloc(n*i*4)),!function(t,e,r,n,i){let o=Math.pow(2,i)-1,a=0;for(let i=0;i<n;i++)for(let n=0;n<r;n++){for(let r=0;r<4;r++)e[a+r]=Math.floor(255*t[a+r]/o+.5);a+=4}}(t,h,n,i,r))),h}},41382:(t,e,r)=>{let n=r(53117),i=n.getBCHDigit(1335);e.getEncodedBits=function(t,e){let r=t.bit<<3|e,o=r<<10;for(;n.getBCHDigit(o)-i>=0;)o^=1335<<n.getBCHDigit(o)-i;return(r<<10|o)^21522}},42008:(t,e,r)=>{"use strict";let n=r(30613),i=r(44346);e.process=function(t,e){let r=[],o=new n(t);return new i(e,{read:o.read.bind(o),write:function(t){r.push(t)},complete:function(){}}).start(),o.process(),Buffer.concat(r)}},43273:(t,e,r)=>{"use strict";let n=r(2913),i=r(65746),o=t.exports=function(t,e){this._options=t,t.checkCRC=!1!==t.checkCRC,this._hasIHDR=!1,this._hasIEND=!1,this._emittedHeadersFinished=!1,this._palette=[],this._colorType=0,this._chunks={},this._chunks[n.TYPE_IHDR]=this._handleIHDR.bind(this),this._chunks[n.TYPE_IEND]=this._handleIEND.bind(this),this._chunks[n.TYPE_IDAT]=this._handleIDAT.bind(this),this._chunks[n.TYPE_PLTE]=this._handlePLTE.bind(this),this._chunks[n.TYPE_tRNS]=this._handleTRNS.bind(this),this._chunks[n.TYPE_gAMA]=this._handleGAMA.bind(this),this.read=e.read,this.error=e.error,this.metadata=e.metadata,this.gamma=e.gamma,this.transColor=e.transColor,this.palette=e.palette,this.parsed=e.parsed,this.inflateData=e.inflateData,this.finished=e.finished,this.simpleTransparency=e.simpleTransparency,this.headersFinished=e.headersFinished||function(){}};o.prototype.start=function(){this.read(n.PNG_SIGNATURE.length,this._parseSignature.bind(this))},o.prototype._parseSignature=function(t){let e=n.PNG_SIGNATURE;for(let r=0;r<e.length;r++)if(t[r]!==e[r])return void this.error(Error("Invalid file signature"));this.read(8,this._parseChunkBegin.bind(this))},o.prototype._parseChunkBegin=function(t){let e=t.readUInt32BE(0),r=t.readUInt32BE(4),o="";for(let e=4;e<8;e++)o+=String.fromCharCode(t[e]);let a=!!(32&t[4]);return this._hasIHDR||r===n.TYPE_IHDR?(this._crc=new i,this._crc.write(Buffer.from(o)),this._chunks[r])?this._chunks[r](e):a?void this.read(e+4,this._skipChunk.bind(this)):void this.error(Error("Unsupported critical chunk type "+o)):void this.error(Error("Expected IHDR on beggining"))},o.prototype._skipChunk=function(){this.read(8,this._parseChunkBegin.bind(this))},o.prototype._handleChunkEnd=function(){this.read(4,this._parseChunkEnd.bind(this))},o.prototype._parseChunkEnd=function(t){let e=t.readInt32BE(0),r=this._crc.crc32();if(this._options.checkCRC&&r!==e)return void this.error(Error("Crc error - "+e+" - "+r));this._hasIEND||this.read(8,this._parseChunkBegin.bind(this))},o.prototype._handleIHDR=function(t){this.read(t,this._parseIHDR.bind(this))},o.prototype._parseIHDR=function(t){this._crc.write(t);let e=t.readUInt32BE(0),r=t.readUInt32BE(4),i=t[8],o=t[9],a=t[10],s=t[11],h=t[12];if(8!==i&&4!==i&&2!==i&&1!==i&&16!==i)return void this.error(Error("Unsupported bit depth "+i));if(!(o in n.COLORTYPE_TO_BPP_MAP))return void this.error(Error("Unsupported color type"));if(0!==a)return void this.error(Error("Unsupported compression method"));if(0!==s)return void this.error(Error("Unsupported filter method"));if(0!==h&&1!==h)return void this.error(Error("Unsupported interlace method"));this._colorType=o;let u=n.COLORTYPE_TO_BPP_MAP[this._colorType];this._hasIHDR=!0,this.metadata({width:e,height:r,depth:i,interlace:!!h,palette:!!(o&n.COLORTYPE_PALETTE),color:!!(o&n.COLORTYPE_COLOR),alpha:!!(o&n.COLORTYPE_ALPHA),bpp:u,colorType:o}),this._handleChunkEnd()},o.prototype._handlePLTE=function(t){this.read(t,this._parsePLTE.bind(this))},o.prototype._parsePLTE=function(t){this._crc.write(t);let e=Math.floor(t.length/3);for(let r=0;r<e;r++)this._palette.push([t[3*r],t[3*r+1],t[3*r+2],255]);this.palette(this._palette),this._handleChunkEnd()},o.prototype._handleTRNS=function(t){this.simpleTransparency(),this.read(t,this._parseTRNS.bind(this))},o.prototype._parseTRNS=function(t){if(this._crc.write(t),this._colorType===n.COLORTYPE_PALETTE_COLOR){if(0===this._palette.length)return void this.error(Error("Transparency chunk must be after palette"));if(t.length>this._palette.length)return void this.error(Error("More transparent colors than palette size"));for(let e=0;e<t.length;e++)this._palette[e][3]=t[e];this.palette(this._palette)}this._colorType===n.COLORTYPE_GRAYSCALE&&this.transColor([t.readUInt16BE(0)]),this._colorType===n.COLORTYPE_COLOR&&this.transColor([t.readUInt16BE(0),t.readUInt16BE(2),t.readUInt16BE(4)]),this._handleChunkEnd()},o.prototype._handleGAMA=function(t){this.read(t,this._parseGAMA.bind(this))},o.prototype._parseGAMA=function(t){this._crc.write(t),this.gamma(t.readUInt32BE(0)/n.GAMMA_DIVISION),this._handleChunkEnd()},o.prototype._handleIDAT=function(t){this._emittedHeadersFinished||(this._emittedHeadersFinished=!0,this.headersFinished()),this.read(-t,this._parseIDAT.bind(this,t))},o.prototype._parseIDAT=function(t,e){if(this._crc.write(e),this._colorType===n.COLORTYPE_PALETTE_COLOR&&0===this._palette.length)throw Error("Expected palette not found");this.inflateData(e);let r=t-e.length;r>0?this._handleIDAT(r):this._handleChunkEnd()},o.prototype._handleIEND=function(t){this.read(t,this._parseIEND.bind(this))},o.prototype._parseIEND=function(t){this._crc.write(t),this._hasIEND=!0,this._handleChunkEnd(),this.finished&&this.finished()}},44346:(t,e,r)=>{"use strict";let n=r(98985),i=r(12831);function o(t,e,r){let n=t*e;return 8!==r&&(n=Math.ceil(n/(8/r))),n}let a=t.exports=function(t,e){let r=t.width,i=t.height,a=t.interlace,s=t.bpp,h=t.depth;if(this.read=e.read,this.write=e.write,this.complete=e.complete,this._imageIndex=0,this._images=[],a){let t=n.getImagePasses(r,i);for(let e=0;e<t.length;e++)this._images.push({byteWidth:o(t[e].width,s,h),height:t[e].height,lineIndex:0})}else this._images.push({byteWidth:o(r,s,h),height:i,lineIndex:0});8===h?this._xComparison=s:16===h?this._xComparison=2*s:this._xComparison=1};a.prototype.start=function(){this.read(this._images[this._imageIndex].byteWidth+1,this._reverseFilterLine.bind(this))},a.prototype._unFilterType1=function(t,e,r){let n=this._xComparison,i=n-1;for(let o=0;o<r;o++){let r=t[1+o],a=o>i?e[o-n]:0;e[o]=r+a}},a.prototype._unFilterType2=function(t,e,r){let n=this._lastLine;for(let i=0;i<r;i++){let r=t[1+i],o=n?n[i]:0;e[i]=r+o}},a.prototype._unFilterType3=function(t,e,r){let n=this._xComparison,i=n-1,o=this._lastLine;for(let a=0;a<r;a++){let r=t[1+a],s=o?o[a]:0,h=Math.floor(((a>i?e[a-n]:0)+s)/2);e[a]=r+h}},a.prototype._unFilterType4=function(t,e,r){let n=this._xComparison,o=n-1,a=this._lastLine;for(let s=0;s<r;s++){let r=t[1+s],h=a?a[s]:0,u=i(s>o?e[s-n]:0,h,s>o&&a?a[s-n]:0);e[s]=r+u}},a.prototype._reverseFilterLine=function(t){let e,r=t[0],n=this._images[this._imageIndex],i=n.byteWidth;if(0===r)e=t.slice(1,i+1);else switch(e=Buffer.alloc(i),r){case 1:this._unFilterType1(t,e,i);break;case 2:this._unFilterType2(t,e,i);break;case 3:this._unFilterType3(t,e,i);break;case 4:this._unFilterType4(t,e,i);break;default:throw Error("Unrecognised filter type - "+r)}this.write(e),n.lineIndex++,n.lineIndex>=n.height?(this._lastLine=null,this._imageIndex++,n=this._images[this._imageIndex]):this._lastLine=e,n?this.read(n.byteWidth+1,this._reverseFilterLine.bind(this)):(this._lastLine=null,this.complete())}},45686:(t,e,r)=>{"use strict";let n=!0,i=r(74075);i.deflateSync||(n=!1);let o=r(2913),a=r(57208);t.exports=function(t,e){if(!n)throw Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");let r=new a(e||{}),s=[];s.push(Buffer.from(o.PNG_SIGNATURE)),s.push(r.packIHDR(t.width,t.height)),t.gamma&&s.push(r.packGAMA(t.gamma));let h=r.filterData(t.data,t.width,t.height),u=i.deflateSync(h,r.getDeflateOptions());if(h=null,!u||!u.length)throw Error("bad png - invalid compressed data response");return s.push(r.packIDAT(u)),s.push(r.packIEND()),Buffer.concat(s)}},46073:t=>{function e(t){if(!t||t<1)throw Error("BitMatrix size must be defined and greater than 0");this.size=t,this.data=new Uint8Array(t*t),this.reservedBit=new Uint8Array(t*t)}e.prototype.set=function(t,e,r,n){let i=t*this.size+e;this.data[i]=r,n&&(this.reservedBit[i]=!0)},e.prototype.get=function(t,e){return this.data[t*this.size+e]},e.prototype.xor=function(t,e,r){this.data[t*this.size+e]^=r},e.prototype.isReserved=function(t,e){return this.reservedBit[t*this.size+e]},t.exports=e},51514:(t,e,r)=>{let n=r(85585),i=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function o(t){this.mode=n.ALPHANUMERIC,this.data=t}o.getBitsLength=function(t){return 11*Math.floor(t/2)+t%2*6},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(t){let e;for(e=0;e+2<=this.data.length;e+=2){let r=45*i.indexOf(this.data[e]);r+=i.indexOf(this.data[e+1]),t.put(r,11)}this.data.length%2&&t.put(i.indexOf(this.data[e]),6)},t.exports=o},51890:(t,e,r)=>{"use strict";t.exports=r(82031)},53117:(t,e)=>{let r,n=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];e.getSymbolSize=function(t){if(!t)throw Error('"version" cannot be null or undefined');if(t<1||t>40)throw Error('"version" should be in range from 1 to 40');return 4*t+17},e.getSymbolTotalCodewords=function(t){return n[t]},e.getBCHDigit=function(t){let e=0;for(;0!==t;)e++,t>>>=1;return e},e.setToSJISFunction=function(t){if("function"!=typeof t)throw Error('"toSJISFunc" is not a valid function.');r=t},e.isKanjiModeEnabled=function(){return void 0!==r},e.toSJIS=function(t){return r(t)}},57208:(t,e,r)=>{"use strict";let n=r(2913),i=r(65746),o=r(6617),a=r(88120),s=r(74075),h=t.exports=function(t){if(this._options=t,t.deflateChunkSize=t.deflateChunkSize||32768,t.deflateLevel=null!=t.deflateLevel?t.deflateLevel:9,t.deflateStrategy=null!=t.deflateStrategy?t.deflateStrategy:3,t.inputHasAlpha=null==t.inputHasAlpha||t.inputHasAlpha,t.deflateFactory=t.deflateFactory||s.createDeflate,t.bitDepth=t.bitDepth||8,t.colorType="number"==typeof t.colorType?t.colorType:n.COLORTYPE_COLOR_ALPHA,t.inputColorType="number"==typeof t.inputColorType?t.inputColorType:n.COLORTYPE_COLOR_ALPHA,-1===[n.COLORTYPE_GRAYSCALE,n.COLORTYPE_COLOR,n.COLORTYPE_COLOR_ALPHA,n.COLORTYPE_ALPHA].indexOf(t.colorType))throw Error("option color type:"+t.colorType+" is not supported at present");if(-1===[n.COLORTYPE_GRAYSCALE,n.COLORTYPE_COLOR,n.COLORTYPE_COLOR_ALPHA,n.COLORTYPE_ALPHA].indexOf(t.inputColorType))throw Error("option input color type:"+t.inputColorType+" is not supported at present");if(8!==t.bitDepth&&16!==t.bitDepth)throw Error("option bit depth:"+t.bitDepth+" is not supported at present")};h.prototype.getDeflateOptions=function(){return{chunkSize:this._options.deflateChunkSize,level:this._options.deflateLevel,strategy:this._options.deflateStrategy}},h.prototype.createDeflate=function(){return this._options.deflateFactory(this.getDeflateOptions())},h.prototype.filterData=function(t,e,r){let i=o(t,e,r,this._options),s=n.COLORTYPE_TO_BPP_MAP[this._options.colorType];return a(i,e,r,this._options,s)},h.prototype._packChunk=function(t,e){let r=e?e.length:0,n=Buffer.alloc(r+12);return n.writeUInt32BE(r,0),n.writeUInt32BE(t,4),e&&e.copy(n,8),n.writeInt32BE(i.crc32(n.slice(4,n.length-4)),n.length-4),n},h.prototype.packGAMA=function(t){let e=Buffer.alloc(4);return e.writeUInt32BE(Math.floor(t*n.GAMMA_DIVISION),0),this._packChunk(n.TYPE_gAMA,e)},h.prototype.packIHDR=function(t,e){let r=Buffer.alloc(13);return r.writeUInt32BE(t,0),r.writeUInt32BE(e,4),r[8]=this._options.bitDepth,r[9]=this._options.colorType,r[10]=0,r[11]=0,r[12]=0,this._packChunk(n.TYPE_IHDR,r)},h.prototype.packIDAT=function(t){return this._packChunk(n.TYPE_IDAT,t)},h.prototype.packIEND=function(){return this._packChunk(n.TYPE_IEND,null)}},59995:t=>{"use strict";var e={single_source_shortest_paths:function(t,r,n){var i,o,a,s,h,u,l,c={},f={};f[r]=0;var d=e.PriorityQueue.make();for(d.push(r,0);!d.empty();)for(a in o=(i=d.pop()).value,s=i.cost,h=t[o]||{})h.hasOwnProperty(a)&&(u=s+h[a],l=f[a],(void 0===f[a]||l>u)&&(f[a]=u,d.push(a,u),c[a]=o));if(void 0!==n&&void 0===f[n])throw Error(["Could not find a path from ",r," to ",n,"."].join(""));return c},extract_shortest_path_from_predecessor_list:function(t,e){for(var r=[],n=e;n;)r.push(n),t[n],n=t[n];return r.reverse(),r},find_path:function(t,r,n){var i=e.single_source_shortest_paths(t,r,n);return e.extract_shortest_path_from_predecessor_list(i,n)},PriorityQueue:{make:function(t){var r,n=e.PriorityQueue,i={};for(r in t=t||{},n)n.hasOwnProperty(r)&&(i[r]=n[r]);return i.queue=[],i.sorter=t.sorter||n.default_sorter,i},default_sorter:function(t,e){return t.cost-e.cost},push:function(t,e){this.queue.push({value:t,cost:e}),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return 0===this.queue.length}}};t.exports=e},61320:(t,e,r)=>{let n=r(53117),i=r(91257),o=r(62564),a=r(85585),s=r(38125),h=n.getBCHDigit(7973);function u(t,e){return a.getCharCountIndicator(t,e)+4}e.from=function(t,e){return s.isValid(t)?parseInt(t,10):e},e.getCapacity=function(t,e,r){if(!s.isValid(t))throw Error("Invalid QR Code version");void 0===r&&(r=a.BYTE);let o=(n.getSymbolTotalCodewords(t)-i.getTotalCodewordsCount(t,e))*8;if(r===a.MIXED)return o;let h=o-u(r,t);switch(r){case a.NUMERIC:return Math.floor(h/10*3);case a.ALPHANUMERIC:return Math.floor(h/11*2);case a.KANJI:return Math.floor(h/13);case a.BYTE:default:return Math.floor(h/8)}},e.getBestVersionForData=function(t,r){let n,i=o.from(r,o.M);if(Array.isArray(t)){if(t.length>1){for(let r=1;r<=40;r++)if(function(t,e){let r=0;return t.forEach(function(t){let n=u(t.mode,e);r+=n+t.getBitsLength()}),r}(t,r)<=e.getCapacity(r,i,a.MIXED))return r;return}if(0===t.length)return 1;n=t[0]}else n=t;return function(t,r,n){for(let i=1;i<=40;i++)if(r<=e.getCapacity(i,n,t))return i}(n.mode,n.getLength(),i)},e.getEncodedBits=function(t){if(!s.isValid(t)||t<7)throw Error("Invalid QR Code version");let e=t<<12;for(;n.getBCHDigit(e)-h>=0;)e^=7973<<n.getBCHDigit(e)-h;return t<<12|e}},62564:(t,e)=>{e.L={bit:1},e.M={bit:0},e.Q={bit:3},e.H={bit:2},e.isValid=function(t){return t&&void 0!==t.bit&&t.bit>=0&&t.bit<4},e.from=function(t,r){if(e.isValid(t))return t;try{if("string"!=typeof t)throw Error("Param is not a string");switch(t.toLowerCase()){case"l":case"low":return e.L;case"m":case"medium":return e.M;case"q":case"quartile":return e.Q;case"h":case"high":return e.H;default:throw Error("Unknown EC Level: "+t)}}catch(t){return r}}},64983:(t,e,r)=>{"use strict";let n=r(28354),i=r(85177),o=r(44346),a=t.exports=function(t){i.call(this);let e=[],r=this;this._filter=new o(t,{read:this.read.bind(this),write:function(t){e.push(t)},complete:function(){r.emit("complete",Buffer.concat(e))}}),this._filter.start()};n.inherits(a,i)},65440:t=>{t.exports=function(){return"function"==typeof Promise&&Promise.prototype&&Promise.prototype.then}},65746:t=>{"use strict";let e=[];!function(){for(let t=0;t<256;t++){let r=t;for(let t=0;t<8;t++)1&r?r=0xedb88320^r>>>1:r>>>=1;e[t]=r}}();let r=t.exports=function(){this._crc=-1};r.prototype.write=function(t){for(let r=0;r<t.length;r++)this._crc=e[(this._crc^t[r])&255]^this._crc>>>8;return!0},r.prototype.crc32=function(){return -1^this._crc},r.crc32=function(t){let r=-1;for(let n=0;n<t.length;n++)r=e[(r^t[n])&255]^r>>>8;return -1^r}},66631:(t,e)=>{let r="[0-9]+",n="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+",i="(?:(?![A-Z0-9 $%*+\\-./:]|"+(n=n.replace(/u/g,"\\u"))+")(?:.|[\r\n]))+";e.KANJI=RegExp(n,"g"),e.BYTE_KANJI=RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),e.BYTE=RegExp(i,"g"),e.NUMERIC=RegExp(r,"g"),e.ALPHANUMERIC=RegExp("[A-Z $%*+\\-./:]+","g");let o=RegExp("^"+n+"$"),a=RegExp("^"+r+"$"),s=RegExp("^[A-Z0-9 $%*+\\-./:]+$");e.testKanji=function(t){return o.test(t)},e.testNumeric=function(t){return a.test(t)},e.testAlphanumeric=function(t){return s.test(t)}},70065:(t,e,r)=>{"use strict";let n=r(28354),i=r(27910),o=r(2913),a=r(57208),s=t.exports=function(t){i.call(this),this._packer=new a(t||{}),this._deflate=this._packer.createDeflate(),this.readable=!0};n.inherits(s,i),s.prototype.pack=function(t,e,r,n){this.emit("data",Buffer.from(o.PNG_SIGNATURE)),this.emit("data",this._packer.packIHDR(e,r)),n&&this.emit("data",this._packer.packGAMA(n));let i=this._packer.filterData(t,e,r);this._deflate.on("error",this.emit.bind(this,"error")),this._deflate.on("data",(function(t){this.emit("data",this._packer.packIDAT(t))}).bind(this)),this._deflate.on("end",(function(){this.emit("data",this._packer.packIEND()),this.emit("end")}).bind(this)),this._deflate.end(i)}},70480:(t,e,r)=>{"use strict";let n=r(28354),i=r(74075),o=r(85177),a=r(64983),s=r(43273),h=r(83182),u=r(38962),l=t.exports=function(t){o.call(this),this._parser=new s(t,{read:this.read.bind(this),error:this._handleError.bind(this),metadata:this._handleMetaData.bind(this),gamma:this.emit.bind(this,"gamma"),palette:this._handlePalette.bind(this),transColor:this._handleTransColor.bind(this),finished:this._finished.bind(this),inflateData:this._inflateData.bind(this),simpleTransparency:this._simpleTransparency.bind(this),headersFinished:this._headersFinished.bind(this)}),this._options=t,this.writable=!0,this._parser.start()};n.inherits(l,o),l.prototype._handleError=function(t){this.emit("error",t),this.writable=!1,this.destroy(),this._inflate&&this._inflate.destroy&&this._inflate.destroy(),this._filter&&(this._filter.destroy(),this._filter.on("error",function(){})),this.errord=!0},l.prototype._inflateData=function(t){if(!this._inflate)if(this._bitmapInfo.interlace)this._inflate=i.createInflate(),this._inflate.on("error",this.emit.bind(this,"error")),this._filter.on("complete",this._complete.bind(this)),this._inflate.pipe(this._filter);else{let t=((this._bitmapInfo.width*this._bitmapInfo.bpp*this._bitmapInfo.depth+7>>3)+1)*this._bitmapInfo.height,e=Math.max(t,i.Z_MIN_CHUNK);this._inflate=i.createInflate({chunkSize:e});let r=t,n=this.emit.bind(this,"error");this._inflate.on("error",function(t){r&&n(t)}),this._filter.on("complete",this._complete.bind(this));let o=this._filter.write.bind(this._filter);this._inflate.on("data",function(t){r&&(t.length>r&&(t=t.slice(0,r)),r-=t.length,o(t))}),this._inflate.on("end",this._filter.end.bind(this._filter))}this._inflate.write(t)},l.prototype._handleMetaData=function(t){this._metaData=t,this._bitmapInfo=Object.create(t),this._filter=new a(this._bitmapInfo)},l.prototype._handleTransColor=function(t){this._bitmapInfo.transColor=t},l.prototype._handlePalette=function(t){this._bitmapInfo.palette=t},l.prototype._simpleTransparency=function(){this._metaData.alpha=!0},l.prototype._headersFinished=function(){this.emit("metadata",this._metaData)},l.prototype._finished=function(){this.errord||(this._inflate?this._inflate.end():this.emit("error","No Inflate block"))},l.prototype._complete=function(t){let e;if(!this.errord){try{let r=h.dataToBitMap(t,this._bitmapInfo);e=u(r,this._bitmapInfo),r=null}catch(t){this._handleError(t);return}this.emit("parsed",e)}}},71094:(t,e,r)=>{let n=r(74277),i=r(30279);e.render=function(t,e,r){return e&&e.small?i.render(t,e,r):n.render(t,e,r)}},72461:(t,e,r)=>{let n=r(92081),i={WW:" ",WB:"▄",BB:"█",BW:"▀"},o={BB:" ",BW:"▄",WW:"█",WB:"▀"};e.render=function(t,e,r){let a=n.getOptions(e),s=i;("#ffffff"===a.color.dark.hex||"#000000"===a.color.light.hex)&&(s=o);let h=t.modules.size,u=t.modules.data,l="",c=Array(h+2*a.margin+1).join(s.WW);c=Array(a.margin/2+1).join(c+"\n");let f=Array(a.margin+1).join(s.WW);l+=c;for(let t=0;t<h;t+=2){l+=f;for(let e=0;e<h;e++){var d;let r=u[t*h+e],n=u[(t+1)*h+e];l+=(d=s,r&&n?d.BB:r&&!n?d.BW:!r&&n?d.WB:d.WW)}l+=f+"\n"}return l+=c.slice(0,-1),"function"==typeof r&&r(null,l),l},e.renderToFile=function(t,n,i,o){void 0===o&&(o=i,i=void 0);let a=r(29021),s=e.render(n,i);a.writeFile(t,s,o)}},73854:(t,e,r)=>{let n=r(53117).getSymbolSize;e.getRowColCoords=function(t){if(1===t)return[];let e=Math.floor(t/7)+2,r=n(t),i=145===r?26:2*Math.ceil((r-13)/(2*e-2)),o=[r-7];for(let t=1;t<e-1;t++)o[t]=o[t-1]-i;return o.push(6),o.reverse()},e.getPositions=function(t){let r=[],n=e.getRowColCoords(t),i=n.length;for(let t=0;t<i;t++)for(let e=0;e<i;e++)(0!==t||0!==e)&&(0!==t||e!==i-1)&&(t!==i-1||0!==e)&&r.push([n[t],n[e]]);return r}},74277:(t,e)=>{e.render=function(t,e,r){let n=t.modules.size,i=t.modules.data,o="\x1b[47m  \x1b[0m",a="",s=Array(n+3).join(o),h=[,,].join(o);a+=s+"\n";for(let t=0;t<n;++t){a+=o;for(let e=0;e<n;e++)a+=i[t*n+e]?"\x1b[40m  \x1b[0m":o;a+=h+"\n"}return a+=s+"\n","function"==typeof r&&r(null,a),a}},77949:(t,e,r)=>{let n=r(79620);function i(t){this.genPoly=void 0,this.degree=t,this.degree&&this.initialize(this.degree)}i.prototype.initialize=function(t){this.degree=t,this.genPoly=n.generateECPolynomial(this.degree)},i.prototype.encode=function(t){if(!this.genPoly)throw Error("Encoder not initialized");let e=new Uint8Array(t.length+this.degree);e.set(t);let r=n.mod(e,this.genPoly),i=this.degree-r.length;if(i>0){let t=new Uint8Array(this.degree);return t.set(r,i),t}return r},t.exports=i},79620:(t,e,r)=>{let n=r(28574);e.mul=function(t,e){let r=new Uint8Array(t.length+e.length-1);for(let i=0;i<t.length;i++)for(let o=0;o<e.length;o++)r[i+o]^=n.mul(t[i],e[o]);return r},e.mod=function(t,e){let r=new Uint8Array(t);for(;r.length-e.length>=0;){let t=r[0];for(let i=0;i<e.length;i++)r[i]^=n.mul(e[i],t);let i=0;for(;i<r.length&&0===r[i];)i++;r=r.slice(i)}return r},e.generateECPolynomial=function(t){let r=new Uint8Array([1]);for(let i=0;i<t;i++)r=e.mul(r,new Uint8Array([1,n.exp(i)]));return r}},80137:(t,e,r)=>{"use strict";r.d(e,{Ay:()=>t1});var n=r(24956),i=r.n(n),o=(0,r(8086).createRequire)("/");try{o("worker_threads").Worker}catch(t){}var a=Uint8Array,s=Uint16Array,h=Int32Array,u=new a([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),l=new a([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),c=new a([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),f=function(t,e){for(var r=new s(31),n=0;n<31;++n)r[n]=e+=1<<t[n-1];for(var i=new h(r[30]),n=1;n<30;++n)for(var o=r[n];o<r[n+1];++o)i[o]=o-r[n]<<5|n;return{b:r,r:i}},d=f(u,2),p=d.b,g=d.r;p[28]=258,g[258]=28;for(var m=f(l,0),b=m.b,v=m.r,y=new s(32768),w=0;w<32768;++w){var x=(43690&w)>>1|(21845&w)<<1;x=(61680&(x=(52428&x)>>2|(13107&x)<<2))>>4|(3855&x)<<4,y[w]=((65280&x)>>8|(255&x)<<8)>>1}for(var _=function(t,e,r){for(var n,i=t.length,o=0,a=new s(e);o<i;++o)t[o]&&++a[t[o]-1];var h=new s(e);for(o=1;o<e;++o)h[o]=h[o-1]+a[o-1]<<1;if(r){n=new s(1<<e);var u=15-e;for(o=0;o<i;++o)if(t[o])for(var l=o<<4|t[o],c=e-t[o],f=h[t[o]-1]++<<c,d=f|(1<<c)-1;f<=d;++f)n[y[f]>>u]=l}else for(o=0,n=new s(i);o<i;++o)t[o]&&(n[o]=y[h[t[o]-1]++]>>15-t[o]);return n},A=new a(288),w=0;w<144;++w)A[w]=8;for(var w=144;w<256;++w)A[w]=9;for(var w=256;w<280;++w)A[w]=7;for(var w=280;w<288;++w)A[w]=8;for(var L=new a(32),w=0;w<32;++w)L[w]=5;var N=_(A,9,0),S=_(A,9,1),P=_(L,5,0),k=_(L,5,1),I=function(t){for(var e=t[0],r=1;r<t.length;++r)t[r]>e&&(e=t[r]);return e},C=function(t,e,r){var n=e/8|0;return(t[n]|t[n+1]<<8)>>(7&e)&r},E=function(t,e){var r=e/8|0;return(t[r]|t[r+1]<<8|t[r+2]<<16)>>(7&e)},F=function(t){return(t+7)/8|0},O=function(t,e,r){return(null==e||e<0)&&(e=0),(null==r||r>t.length)&&(r=t.length),new a(t.subarray(e,r))},T=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],M=function(t,e,r){var n=Error(e||T[t]);if(n.code=t,Error.captureStackTrace&&Error.captureStackTrace(n,M),!r)throw n;return n},j=function(t,e,r,n){var i=t.length,o=n?n.length:0;if(!i||e.f&&!e.l)return r||new a(0);var s=!r,h=s||2!=e.i,f=e.i;s&&(r=new a(3*i));var d=function(t){var e=r.length;if(t>e){var n=new a(Math.max(2*e,t));n.set(r),r=n}},g=e.f||0,m=e.p||0,v=e.b||0,y=e.l,w=e.d,x=e.m,A=e.n,L=8*i;do{if(!y){g=C(t,m,1);var N=C(t,m+1,3);if(m+=3,N)if(1==N)y=S,w=k,x=9,A=5;else if(2==N){var P=C(t,m,31)+257,T=C(t,m+10,15)+4,j=P+C(t,m+5,31)+1;m+=14;for(var B=new a(j),R=new a(19),D=0;D<T;++D)R[c[D]]=C(t,m+3*D,7);m+=3*T;for(var q=I(R),U=(1<<q)-1,z=_(R,q,1),D=0;D<j;){var H=z[C(t,m,U)];m+=15&H;var W=H>>4;if(W<16)B[D++]=W;else{var V=0,G=0;for(16==W?(G=3+C(t,m,3),m+=2,V=B[D-1]):17==W?(G=3+C(t,m,7),m+=3):18==W&&(G=11+C(t,m,127),m+=7);G--;)B[D++]=V}}var Y=B.subarray(0,P),J=B.subarray(P);x=I(Y),A=I(J),y=_(Y,x,1),w=_(J,A,1)}else M(1);else{var W=F(m)+4,K=t[W-4]|t[W-3]<<8,X=W+K;if(X>i){f&&M(0);break}h&&d(v+K),r.set(t.subarray(W,X),v),e.b=v+=K,e.p=m=8*X,e.f=g;continue}if(m>L){f&&M(0);break}}h&&d(v+131072);for(var Z=(1<<x)-1,$=(1<<A)-1,Q=m;;Q=m){var V=y[E(t,m)&Z],tt=V>>4;if((m+=15&V)>L){f&&M(0);break}if(V||M(2),tt<256)r[v++]=tt;else if(256==tt){Q=m,y=null;break}else{var te=tt-254;if(tt>264){var D=tt-257,tr=u[D];te=C(t,m,(1<<tr)-1)+p[D],m+=tr}var tn=w[E(t,m)&$],ti=tn>>4;tn||M(3),m+=15&tn;var J=b[ti];if(ti>3){var tr=l[ti];J+=E(t,m)&(1<<tr)-1,m+=tr}if(m>L){f&&M(0);break}h&&d(v+131072);var to=v+te;if(v<J){var ta=o-J,ts=Math.min(J,to);for(ta+v<0&&M(3);v<ts;++v)r[v]=n[ta+v]}for(;v<to;++v)r[v]=r[v-J]}}e.l=y,e.p=Q,e.b=v,e.f=g,y&&(g=1,e.m=x,e.d=w,e.n=A)}while(!g);return v!=r.length&&s?O(r,0,v):r.subarray(0,v)},B=function(t,e,r){r<<=7&e;var n=e/8|0;t[n]|=r,t[n+1]|=r>>8},R=function(t,e,r){r<<=7&e;var n=e/8|0;t[n]|=r,t[n+1]|=r>>8,t[n+2]|=r>>16},D=function(t,e){for(var r=[],n=0;n<t.length;++n)t[n]&&r.push({s:n,f:t[n]});var i=r.length,o=r.slice();if(!i)return{t:G,l:0};if(1==i){var h=new a(r[0].s+1);return h[r[0].s]=1,{t:h,l:1}}r.sort(function(t,e){return t.f-e.f}),r.push({s:-1,f:25001});var u=r[0],l=r[1],c=0,f=1,d=2;for(r[0]={s:-1,f:u.f+l.f,l:u,r:l};f!=i-1;)u=r[r[c].f<r[d].f?c++:d++],l=r[c!=f&&r[c].f<r[d].f?c++:d++],r[f++]={s:-1,f:u.f+l.f,l:u,r:l};for(var p=o[0].s,n=1;n<i;++n)o[n].s>p&&(p=o[n].s);var g=new s(p+1),m=q(r[f-1],g,0);if(m>e){var n=0,b=0,v=m-e,y=1<<v;for(o.sort(function(t,e){return g[e.s]-g[t.s]||t.f-e.f});n<i;++n){var w=o[n].s;if(g[w]>e)b+=y-(1<<m-g[w]),g[w]=e;else break}for(b>>=v;b>0;){var x=o[n].s;g[x]<e?b-=1<<e-g[x]++-1:++n}for(;n>=0&&b;--n){var _=o[n].s;g[_]==e&&(--g[_],++b)}m=e}return{t:new a(g),l:m}},q=function(t,e,r){return -1==t.s?Math.max(q(t.l,e,r+1),q(t.r,e,r+1)):e[t.s]=r},U=function(t){for(var e=t.length;e&&!t[--e];);for(var r=new s(++e),n=0,i=t[0],o=1,a=function(t){r[n++]=t},h=1;h<=e;++h)if(t[h]==i&&h!=e)++o;else{if(!i&&o>2){for(;o>138;o-=138)a(32754);o>2&&(a(o>10?o-11<<5|28690:o-3<<5|12305),o=0)}else if(o>3){for(a(i),--o;o>6;o-=6)a(8304);o>2&&(a(o-3<<5|8208),o=0)}for(;o--;)a(i);o=1,i=t[h]}return{c:r.subarray(0,n),n:e}},z=function(t,e){for(var r=0,n=0;n<e.length;++n)r+=t[n]*e[n];return r},H=function(t,e,r){var n=r.length,i=F(e+2);t[i]=255&n,t[i+1]=n>>8,t[i+2]=255^t[i],t[i+3]=255^t[i+1];for(var o=0;o<n;++o)t[i+o+4]=r[o];return(i+4+n)*8},W=function(t,e,r,n,i,o,a,h,f,d,p){B(e,p++,r),++i[256];for(var g,m,b,v,y=D(i,15),w=y.t,x=y.l,S=D(o,15),k=S.t,I=S.l,C=U(w),E=C.c,F=C.n,O=U(k),T=O.c,M=O.n,j=new s(19),q=0;q<E.length;++q)++j[31&E[q]];for(var q=0;q<T.length;++q)++j[31&T[q]];for(var W=D(j,7),V=W.t,G=W.l,Y=19;Y>4&&!V[c[Y-1]];--Y);var J=d+5<<3,K=z(i,A)+z(o,L)+a,X=z(i,w)+z(o,k)+a+14+3*Y+z(j,V)+2*j[16]+3*j[17]+7*j[18];if(f>=0&&J<=K&&J<=X)return H(e,p,t.subarray(f,f+d));if(B(e,p,1+(X<K)),p+=2,X<K){g=_(w,x,0),m=w,b=_(k,I,0),v=k;var Z=_(V,G,0);B(e,p,F-257),B(e,p+5,M-1),B(e,p+10,Y-4),p+=14;for(var q=0;q<Y;++q)B(e,p+3*q,V[c[q]]);p+=3*Y;for(var $=[E,T],Q=0;Q<2;++Q)for(var tt=$[Q],q=0;q<tt.length;++q){var te=31&tt[q];B(e,p,Z[te]),p+=V[te],te>15&&(B(e,p,tt[q]>>5&127),p+=tt[q]>>12)}}else g=N,m=A,b=P,v=L;for(var q=0;q<h;++q){var tr=n[q];if(tr>255){var te=tr>>18&31;R(e,p,g[te+257]),p+=m[te+257],te>7&&(B(e,p,tr>>23&31),p+=u[te]);var tn=31&tr;R(e,p,b[tn]),p+=v[tn],tn>3&&(R(e,p,tr>>5&8191),p+=l[tn])}else R(e,p,g[tr]),p+=m[tr]}return R(e,p,g[256]),p+m[256]},V=new h([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),G=new a(0),Y=function(t,e,r,n,i,o){var c=o.z||t.length,f=new a(n+c+5*(1+Math.ceil(c/7e3))+i),d=f.subarray(n,f.length-i),p=o.l,m=7&(o.r||0);if(e){m&&(d[0]=o.r>>3);for(var b=V[e-1],y=b>>13,w=8191&b,x=(1<<r)-1,_=o.p||new s(32768),A=o.h||new s(x+1),L=Math.ceil(r/3),N=2*L,S=function(e){return(t[e]^t[e+1]<<L^t[e+2]<<N)&x},P=new h(25e3),k=new s(288),I=new s(32),C=0,E=0,T=o.i||0,M=0,j=o.w||0,B=0;T+2<c;++T){var R=S(T),D=32767&T,q=A[R];if(_[D]=q,A[R]=D,j<=T){var U=c-T;if((C>7e3||M>24576)&&(U>423||!p)){m=W(t,d,0,P,k,I,E,M,B,T-B,m),M=C=E=0,B=T;for(var z=0;z<286;++z)k[z]=0;for(var z=0;z<30;++z)I[z]=0}var G=2,Y=0,J=w,K=D-q&32767;if(U>2&&R==S(T-K))for(var X=Math.min(y,U)-1,Z=Math.min(32767,T),$=Math.min(258,U);K<=Z&&--J&&D!=q;){if(t[T+G]==t[T+G-K]){for(var Q=0;Q<$&&t[T+Q]==t[T+Q-K];++Q);if(Q>G){if(G=Q,Y=K,Q>X)break;for(var tt=Math.min(K,Q-2),te=0,z=0;z<tt;++z){var tr=T-K+z&32767,tn=_[tr],ti=tr-tn&32767;ti>te&&(te=ti,q=tr)}}}q=_[D=q],K+=D-q&32767}if(Y){P[M++]=0x10000000|g[G]<<18|v[Y];var to=31&g[G],ta=31&v[Y];E+=u[to]+l[ta],++k[257+to],++I[ta],j=T+G,++C}else P[M++]=t[T],++k[t[T]]}}for(T=Math.max(T,j);T<c;++T)P[M++]=t[T],++k[t[T]];m=W(t,d,p,P,k,I,E,M,B,T-B,m),p||(o.r=7&m|d[m/8|0]<<3,m-=7,o.h=A,o.p=_,o.i=T,o.w=j)}else{for(var T=o.w||0;T<c+p;T+=65535){var ts=T+65535;ts>=c&&(d[m/8|0]=p,ts=c),m=H(d,m+1,t.subarray(T,ts))}o.i=c}return O(f,0,n+F(m)+i)},J=function(){var t=-1;return{p:function(e){for(var r=t,n=0;n<e.length;++n)r=null[255&r^e[n]]^r>>>8;t=r},d:function(){return~t}}},K=function(){var t=1,e=0;return{p:function(r){for(var n=t,i=e,o=0|r.length,a=0;a!=o;){for(var s=Math.min(a+2655,o);a<s;++a)i+=n+=r[a];n=(65535&n)+15*(n>>16),i=(65535&i)+15*(i>>16)}t=n,e=i},d:function(){return t%=65521,e%=65521,(255&t)<<24|(65280&t)<<8|(255&e)<<8|e>>8}}},X=function(t,e,r,n,i){if(!i&&(i={l:1},e.dictionary)){var o=e.dictionary.subarray(-32768),s=new a(o.length+t.length);s.set(o),s.set(t,o.length),t=s,i.w=o.length}return Y(t,null==e.level?6:e.level,null==e.mem?i.l?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(t.length)))):20:12+e.mem,r,n,i)},Z=function(t,e){var r={};for(var n in t)r[n]=t[n];for(var n in e)r[n]=e[n];return r},$=function(t,e,r){for(var n=t(),i=t.toString(),o=i.slice(i.indexOf("[")+1,i.lastIndexOf("]")).replace(/\s+/g,"").split(","),a=0;a<n.length;++a){var s=n[a],h=o[a];if("function"==typeof s){e+=";"+h+"=";var u=s.toString();if(s.prototype)if(-1!=u.indexOf("[native code]")){var l=u.indexOf(" ",8)+1;e+=u.slice(l,u.indexOf("(",l))}else for(var c in e+=u,s.prototype)e+=";"+h+".prototype."+c+"="+s.prototype[c].toString();else e+=u}else r[h]=s}return e},Q=function(t){var e=[];for(var r in t)t[r].buffer&&e.push((t[r]=new t[r].constructor(t[r])).buffer);return e},tt=function(t,e,r,n){if(!null[r]){for(var i="",o={},a=t.length-1,s=0;s<a;++s)i=$(t[s],i,o);null[r]={c:$(t[a],i,o),e:o}}var h=Z({},null[r].e);return(null)(null[r].c+";onmessage=function(e){for(var k in e.data)self[k]=e.data[k];onmessage="+e.toString()+"}",r,h,Q(h),n)},te=function(){return[a,s,h,u,l,c,p,b,S,k,y,T,_,I,C,E,F,O,M,j,tm,tr,tn]},tr=function(t){return postMessage(t,[t.buffer])},tn=function(t){return t&&{out:t.size&&new a(t.size),dictionary:t.dictionary}},ti=function(t,e,r,n,i,o){var a=tt(r,n,i,function(t,e){a.terminate(),o(t,e)});return a.postMessage([t,e],e.consume?[t.buffer]:[]),function(){a.terminate()}},to=function(t,e){return t[e]|t[e+1]<<8},ta=function(t,e){return(t[e]|t[e+1]<<8|t[e+2]<<16|t[e+3]<<24)>>>0},ts=function(t,e){return ta(t,e)+0x100000000*ta(t,e+4)},th=function(t,e,r){for(;r;++e)t[e]=r,r>>>=8},tu=function(t,e){var r=e.filename;if(t[0]=31,t[1]=139,t[2]=8,t[8]=e.level<2?4:2*(9==e.level),t[9]=3,0!=e.mtime&&th(t,4,Math.floor(new Date(e.mtime||Date.now())/1e3)),r){t[3]=8;for(var n=0;n<=r.length;++n)t[n+10]=r.charCodeAt(n)}},tl=function(t){(31!=t[0]||139!=t[1]||8!=t[2])&&M(6,"invalid gzip data");var e=t[3],r=10;4&e&&(r+=(t[10]|t[11]<<8)+2);for(var n=(e>>3&1)+(e>>4&1);n>0;n-=!t[r++]);return r+(2&e)},tc=function(t){var e=t.length;return(t[e-4]|t[e-3]<<8|t[e-2]<<16|t[e-1]<<24)>>>0},tf=function(t){return 10+(t.filename?t.filename.length+1:0)},td=function(t,e){var r=e.level;if(t[0]=120,t[1]=(0==r?0:r<6?1:9==r?3:2)<<6|(e.dictionary&&32),t[1]|=31-(t[0]<<8|t[1])%31,e.dictionary){var n=K();n.p(e.dictionary),th(t,2,n.d())}},tp=function(t,e){return((15&t[0])!=8||t[0]>>4>7||(t[0]<<8|t[1])%31)&&M(6,"invalid zlib data"),(t[1]>>5&1)==+!e&&M(6,"invalid zlib data: "+(32&t[1]?"need":"unexpected")+" dictionary"),(t[1]>>3&4)+2};function tg(t,e){return X(t,e||{},0,0)}function tm(t,e){return j(t,{i:2},e&&e.out,e&&e.dictionary)}function tb(t,e){e||(e={});var r=K();r.p(t);var n=X(t,e,e.dictionary?6:2,4);return td(n,e),th(n,n.length-4,r.d()),n}var tv="undefined"!=typeof TextEncoder&&new TextEncoder,ty="undefined"!=typeof TextDecoder&&new TextDecoder;try{ty.decode(G,{stream:!0})}catch(t){}var tw=function(t){for(var e="",r=0;;){var n=t[r++],i=(n>127)+(n>223)+(n>239);if(r+i>t.length)return{s:e,r:O(t,r-1)};i?3==i?e+=String.fromCharCode(55296|(n=((15&n)<<18|(63&t[r++])<<12|(63&t[r++])<<6|63&t[r++])-65536)>>10,56320|1023&n):1&i?e+=String.fromCharCode((31&n)<<6|63&t[r++]):e+=String.fromCharCode((15&n)<<12|(63&t[r++])<<6|63&t[r++]):e+=String.fromCharCode(n)}},tx=function(t,e){for(;1!=to(t,e);e+=4+to(t,e+2));return[ts(t,e+12),ts(t,e+4),ts(t,e+20)]},t_=function(t){var e=0;if(t)for(var r in t){var n=t[r].length;n>65535&&M(9),e+=n+4}return e},tA=("function"==typeof queueMicrotask?queueMicrotask:"function"==typeof setTimeout,function(){return"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this}());function tL(){tA.console&&"function"==typeof tA.console.log&&tA.console.log.apply(tA.console,arguments)}var tN={log:tL,warn:function(t){tA.console&&("function"==typeof tA.console.warn?tA.console.warn.apply(tA.console,arguments):tL.call(null,arguments))},error:function(t){tA.console&&("function"==typeof tA.console.error?tA.console.error.apply(tA.console,arguments):tL(t))}};function tS(t,e,r){var n=new XMLHttpRequest;n.open("GET",t),n.responseType="blob",n.onload=function(){tE(n.response,e,r)},n.onerror=function(){tN.error("could not download file")},n.send()}function tP(t){var e=new XMLHttpRequest;e.open("HEAD",t,!1);try{e.send()}catch(t){}return e.status>=200&&e.status<=299}function tk(t){try{t.dispatchEvent(new MouseEvent("click"))}catch(r){var e=document.createEvent("MouseEvents");e.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),t.dispatchEvent(e)}}var tI,tC,tE=tA.saveAs||("object"!==("undefined"==typeof window?"undefined":i()(window))||window!==tA?function(){}:"undefined"!=typeof HTMLAnchorElement&&"download"in HTMLAnchorElement.prototype?function(t,e,r){var n=tA.URL||tA.webkitURL,i=document.createElement("a");i.download=e=e||t.name||"download",i.rel="noopener","string"==typeof t?(i.href=t,i.origin!==location.origin?tP(i.href)?tS(t,e,r):tk(i,i.target="_blank"):tk(i)):(i.href=n.createObjectURL(t),setTimeout(function(){n.revokeObjectURL(i.href)},4e4),setTimeout(function(){tk(i)},0))}:"msSaveOrOpenBlob"in navigator?function(t,e,r){if(e=e||t.name||"download","string"==typeof t)if(tP(t))tS(t,e,r);else{var n,o=document.createElement("a");o.href=t,o.target="_blank",setTimeout(function(){tk(o)})}else navigator.msSaveOrOpenBlob((void 0===(n=r)?n={autoBom:!1}:"object"!==i()(n)&&(tN.warn("Deprecated: Expected third argument to be a object"),n={autoBom:!n}),n.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(t.type)?new Blob([String.fromCharCode(65279),t],{type:t.type}):t),e)}:function(t,e,r,n){if((n=n||open("","_blank"))&&(n.document.title=n.document.body.innerText="downloading..."),"string"==typeof t)return tS(t,e,r);var o="application/octet-stream"===t.type,a=/constructor/i.test(tA.HTMLElement)||tA.safari,s=/CriOS\/[\d]+/.test(navigator.userAgent);if((s||o&&a)&&"object"===("undefined"==typeof FileReader?"undefined":i()(FileReader))){var h=new FileReader;h.onloadend=function(){var t=h.result;t=s?t:t.replace(/^data:[^;]*;/,"data:attachment/file;"),n?n.location.href=t:location=t,n=null},h.readAsDataURL(t)}else{var u=tA.URL||tA.webkitURL,l=u.createObjectURL(t);n?n.location=l:location.href=l,n=null,setTimeout(function(){u.revokeObjectURL(l)},4e4)}});function tF(t){var e;t=t||"",this.ok=!1,"#"==t.charAt(0)&&(t=t.substr(1,6)),t=({aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"})[t=(t=t.replace(/ /g,"")).toLowerCase()]||t;for(var r=[{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3])]}},{re:/^(\w{2})(\w{2})(\w{2})$/,example:["#00ff00","336699"],process:function(t){return[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16)]}},{re:/^(\w{1})(\w{1})(\w{1})$/,example:["#fb0","f0f"],process:function(t){return[parseInt(t[1]+t[1],16),parseInt(t[2]+t[2],16),parseInt(t[3]+t[3],16)]}}],n=0;n<r.length;n++){var i=r[n].re,o=r[n].process,a=i.exec(t);a&&(e=o(a),this.r=e[0],this.g=e[1],this.b=e[2],this.ok=!0)}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toHex=function(){var t=this.r.toString(16),e=this.g.toString(16),r=this.b.toString(16);return 1==t.length&&(t="0"+t),1==e.length&&(e="0"+e),1==r.length&&(r="0"+r),"#"+t+e+r}}function tO(t,e){var r=t[0],n=t[1],i=t[2],o=t[3];r=tM(r,n,i,o,e[0],7,-0x28955b88),o=tM(o,r,n,i,e[1],12,-0x173848aa),i=tM(i,o,r,n,e[2],17,0x242070db),n=tM(n,i,o,r,e[3],22,-0x3e423112),r=tM(r,n,i,o,e[4],7,-0xa83f051),o=tM(o,r,n,i,e[5],12,0x4787c62a),i=tM(i,o,r,n,e[6],17,-0x57cfb9ed),n=tM(n,i,o,r,e[7],22,-0x2b96aff),r=tM(r,n,i,o,e[8],7,0x698098d8),o=tM(o,r,n,i,e[9],12,-0x74bb0851),i=tM(i,o,r,n,e[10],17,-42063),n=tM(n,i,o,r,e[11],22,-0x76a32842),r=tM(r,n,i,o,e[12],7,0x6b901122),o=tM(o,r,n,i,e[13],12,-0x2678e6d),i=tM(i,o,r,n,e[14],17,-0x5986bc72),r=tj(r,n=tM(n,i,o,r,e[15],22,0x49b40821),i,o,e[1],5,-0x9e1da9e),o=tj(o,r,n,i,e[6],9,-0x3fbf4cc0),i=tj(i,o,r,n,e[11],14,0x265e5a51),n=tj(n,i,o,r,e[0],20,-0x16493856),r=tj(r,n,i,o,e[5],5,-0x29d0efa3),o=tj(o,r,n,i,e[10],9,0x2441453),i=tj(i,o,r,n,e[15],14,-0x275e197f),n=tj(n,i,o,r,e[4],20,-0x182c0438),r=tj(r,n,i,o,e[9],5,0x21e1cde6),o=tj(o,r,n,i,e[14],9,-0x3cc8f82a),i=tj(i,o,r,n,e[3],14,-0xb2af279),n=tj(n,i,o,r,e[8],20,0x455a14ed),r=tj(r,n,i,o,e[13],5,-0x561c16fb),o=tj(o,r,n,i,e[2],9,-0x3105c08),i=tj(i,o,r,n,e[7],14,0x676f02d9),r=tB(r,n=tj(n,i,o,r,e[12],20,-0x72d5b376),i,o,e[5],4,-378558),o=tB(o,r,n,i,e[8],11,-0x788e097f),i=tB(i,o,r,n,e[11],16,0x6d9d6122),n=tB(n,i,o,r,e[14],23,-0x21ac7f4),r=tB(r,n,i,o,e[1],4,-0x5b4115bc),o=tB(o,r,n,i,e[4],11,0x4bdecfa9),i=tB(i,o,r,n,e[7],16,-0x944b4a0),n=tB(n,i,o,r,e[10],23,-0x41404390),r=tB(r,n,i,o,e[13],4,0x289b7ec6),o=tB(o,r,n,i,e[0],11,-0x155ed806),i=tB(i,o,r,n,e[3],16,-0x2b10cf7b),n=tB(n,i,o,r,e[6],23,0x4881d05),r=tB(r,n,i,o,e[9],4,-0x262b2fc7),o=tB(o,r,n,i,e[12],11,-0x1924661b),i=tB(i,o,r,n,e[15],16,0x1fa27cf8),r=tR(r,n=tB(n,i,o,r,e[2],23,-0x3b53a99b),i,o,e[0],6,-0xbd6ddbc),o=tR(o,r,n,i,e[7],10,0x432aff97),i=tR(i,o,r,n,e[14],15,-0x546bdc59),n=tR(n,i,o,r,e[5],21,-0x36c5fc7),r=tR(r,n,i,o,e[12],6,0x655b59c3),o=tR(o,r,n,i,e[3],10,-0x70f3336e),i=tR(i,o,r,n,e[10],15,-1051523),n=tR(n,i,o,r,e[1],21,-0x7a7ba22f),r=tR(r,n,i,o,e[8],6,0x6fa87e4f),o=tR(o,r,n,i,e[15],10,-0x1d31920),i=tR(i,o,r,n,e[6],15,-0x5cfebcec),n=tR(n,i,o,r,e[13],21,0x4e0811a1),r=tR(r,n,i,o,e[4],6,-0x8ac817e),o=tR(o,r,n,i,e[11],10,-0x42c50dcb),i=tR(i,o,r,n,e[2],15,0x2ad7d2bb),n=tR(n,i,o,r,e[9],21,-0x14792c6f),t[0]=tW(r,t[0]),t[1]=tW(n,t[1]),t[2]=tW(i,t[2]),t[3]=tW(o,t[3])}function tT(t,e,r,n,i,o){return e=tW(tW(e,t),tW(n,o)),tW(e<<i|e>>>32-i,r)}function tM(t,e,r,n,i,o,a){return tT(e&r|~e&n,t,e,i,o,a)}function tj(t,e,r,n,i,o,a){return tT(e&n|r&~n,t,e,i,o,a)}function tB(t,e,r,n,i,o,a){return tT(e^r^n,t,e,i,o,a)}function tR(t,e,r,n,i,o,a){return tT(r^(e|~n),t,e,i,o,a)}function tD(t){var e,r=t.length,n=[0x67452301,-0x10325477,-0x67452302,0x10325476];for(e=64;e<=t.length;e+=64)tO(n,function(t){var e,r=[];for(e=0;e<64;e+=4)r[e>>2]=t.charCodeAt(e)+(t.charCodeAt(e+1)<<8)+(t.charCodeAt(e+2)<<16)+(t.charCodeAt(e+3)<<24);return r}(t.substring(e-64,e)));t=t.substring(e-64);var i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(e=0;e<t.length;e++)i[e>>2]|=t.charCodeAt(e)<<(e%4<<3);if(i[e>>2]|=128<<(e%4<<3),e>55)for(tO(n,i),e=0;e<16;e++)i[e]=0;return i[14]=8*r,tO(n,i),n}tI=tA.atob.bind(tA),tC=tA.btoa.bind(tA);var tq="0123456789abcdef".split("");function tU(t){return String.fromCharCode(255&t,(65280&t)>>8,(0xff0000&t)>>16,(0xff000000&t)>>24)}function tz(t){return tD(t).map(tU).join("")}var tH="5d41402abc4b2a76b9719d911017c592"!=function(t){for(var e=0;e<t.length;e++)t[e]=function(t){for(var e="",r=0;r<4;r++)e+=tq[t>>8*r+4&15]+tq[t>>8*r&15];return e}(t[e]);return t.join("")}(tD("hello"));function tW(t,e){if(tH){var r=(65535&t)+(65535&e);return(t>>16)+(e>>16)+(r>>16)<<16|65535&r}return t+e|0}function tV(t,e){if(t!==r){for(var r,n,i=Array(1+(256/t.length|0)+1).join(t),o=[],a=0;a<256;a++)o[a]=a;var s=0;for(a=0;a<256;a++){var h=o[a];s=(s+h+i.charCodeAt(a))%256,o[a]=o[s],o[s]=h}r=t,n=o}else o=n;var u=e.length,l=0,c=0,f="";for(a=0;a<u;a++)c=(c+(h=o[l=(l+1)%256]))%256,o[l]=o[c],o[c]=h,i=o[(o[l]+o[c])%256],f+=String.fromCharCode(e.charCodeAt(a)^i);return f}var tG={print:4,modify:8,copy:16,"annot-forms":32};function tY(t,e,r,n){this.v=1,this.r=2;var i=192;t.forEach(function(t){if(void 0!==tG.perm)throw Error("Invalid permission: "+t);i+=tG[t]}),this.padding="(\xbfN^Nu\x8aAd\0NV\xff\xfa\x01\b..\0\xb6\xd0h>\x80/\f\xa9\xfedSiz";var o=(e+this.padding).substr(0,32),a=(r+this.padding).substr(0,32);this.O=this.processOwnerPassword(o,a),this.P=-(1+(255^i)),this.encryptionKey=tz(o+this.O+this.lsbFirstWord(this.P)+this.hexToBytes(n)).substr(0,5),this.U=tV(this.encryptionKey,this.padding)}function tJ(t){if(/[^\u0000-\u00ff]/.test(t))throw Error("Invalid PDF Name Object: "+t+", Only accept ASCII characters.");for(var e="",r=t.length,n=0;n<r;n++){var i=t.charCodeAt(n);i<33||35===i||37===i||40===i||41===i||47===i||60===i||62===i||91===i||93===i||123===i||125===i||i>126?e+="#"+("0"+i.toString(16)).slice(-2):e+=t[n]}return e}function tK(t){if("object"!==i()(t))throw Error("Invalid Context passed to initialize PubSub (jsPDF-module)");var e={};this.subscribe=function(t,r,n){if(n=n||!1,"string"!=typeof t||"function"!=typeof r||"boolean"!=typeof n)throw Error("Invalid arguments passed to PubSub.subscribe (jsPDF-module)");e.hasOwnProperty(t)||(e[t]={});var i=Math.random().toString(35);return e[t][i]=[r,!!n],i},this.unsubscribe=function(t){for(var r in e)if(e[r][t])return delete e[r][t],0===Object.keys(e[r]).length&&delete e[r],!0;return!1},this.publish=function(r){if(e.hasOwnProperty(r)){var n=Array.prototype.slice.call(arguments,1),i=[];for(var o in e[r]){var a=e[r][o];try{a[0].apply(t,n)}catch(t){tA.console&&tN.error("jsPDF PubSub Error",t.message,t)}a[1]&&i.push(o)}i.length&&i.forEach(this.unsubscribe)}},this.getTopics=function(){return e}}function tX(t){if(!(this instanceof tX))return new tX(t);var e="opacity,stroke-opacity".split(",");for(var r in t)t.hasOwnProperty(r)&&e.indexOf(r)>=0&&(this[r]=t[r]);this.id="",this.objectNumber=-1}function tZ(t,e){this.gState=t,this.matrix=e,this.id="",this.objectNumber=-1}function t$(t,e,r,n,i){if(!(this instanceof t$))return new t$(t,e,r,n,i);this.type="axial"===t?2:3,this.coords=e,this.colors=r,tZ.call(this,n,i)}function tQ(t,e,r,n,i){if(!(this instanceof tQ))return new tQ(t,e,r,n,i);this.boundingBox=t,this.xStep=e,this.yStep=r,this.stream="",this.cloneIndex=0,tZ.call(this,n,i)}function t1(t){var e,r="string"==typeof arguments[0]?arguments[0]:"p",n=arguments[1],o=arguments[2],a=arguments[3],s=[],h=1,u=16,l="S",c=null;"object"===i()(t=t||{})&&(r=t.orientation,n=t.unit||n,o=t.format||o,a=t.compress||t.compressPdf||a,null!==(c=t.encryption||null)&&(c.userPassword=c.userPassword||"",c.ownerPassword=c.ownerPassword||"",c.userPermissions=c.userPermissions||[]),h="number"==typeof t.userUnit?Math.abs(t.userUnit):1,void 0!==t.precision&&(e=t.precision),void 0!==t.floatPrecision&&(u=t.floatPrecision),l=t.defaultPathOperation||"S"),s=t.filters||(!0===a?["FlateEncode"]:s),n=n||"mm",r=(""+(r||"P")).toLowerCase();var f=t.putOnlyUsedFonts||!1,d={},p={internal:{},__private__:{}};p.__private__.PubSub=tK;var g="1.3",m=p.__private__.getPdfVersion=function(){return g};p.__private__.setPdfVersion=function(t){g=t};var b={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};p.__private__.getPageFormats=function(){return b};var v=p.__private__.getPageFormat=function(t){return b[t]};o=o||"a4";var y={COMPAT:"compat",ADVANCED:"advanced"},w=y.COMPAT;function x(){this.saveGraphicsState(),$(new tB(td,0,0,-td,0,rm()*td).toString()+" cm"),this.setFontSize(this.getFontSize()/td),l="n",w=y.ADVANCED}function _(){this.restoreGraphicsState(),l="S",w=y.COMPAT}var A=p.__private__.combineFontStyleAndFontWeight=function(t,e){if("bold"==t&&"normal"==e||"bold"==t&&400==e||"normal"==t&&"italic"==e||"bold"==t&&"italic"==e)throw Error("Invalid Combination of fontweight and fontstyle");return e&&(t=400==e||"normal"===e?"italic"===t?"italic":"normal":700!=e&&"bold"!==e||"normal"!==t?(700==e?"bold":e)+""+t:"bold"),t};p.advancedAPI=function(t){var e=w===y.COMPAT;return e&&x.call(this),"function"!=typeof t||(t(this),e&&_.call(this)),this},p.compatAPI=function(t){var e=w===y.ADVANCED;return e&&_.call(this),"function"!=typeof t||(t(this),e&&x.call(this)),this},p.isAdvancedAPI=function(){return w===y.ADVANCED};var L,N=function(t){if(w!==y.ADVANCED)throw Error(t+" is only available in 'advanced' API mode. You need to call advancedAPI() first.")},S=p.roundToPrecision=p.__private__.roundToPrecision=function(t,r){var n=e||r;if(isNaN(t)||isNaN(n))throw Error("Invalid argument passed to jsPDF.roundToPrecision");return t.toFixed(n).replace(/0+$/,"")};L=p.hpf=p.__private__.hpf="number"==typeof u?function(t){if(isNaN(t))throw Error("Invalid argument passed to jsPDF.hpf");return S(t,u)}:"smart"===u?function(t){if(isNaN(t))throw Error("Invalid argument passed to jsPDF.hpf");return S(t,t>-1&&t<1?16:5)}:function(t){if(isNaN(t))throw Error("Invalid argument passed to jsPDF.hpf");return S(t,16)};var P=p.f2=p.__private__.f2=function(t){if(isNaN(t))throw Error("Invalid argument passed to jsPDF.f2");return S(t,2)},k=p.__private__.f3=function(t){if(isNaN(t))throw Error("Invalid argument passed to jsPDF.f3");return S(t,3)},I=p.scale=p.__private__.scale=function(t){if(isNaN(t))throw Error("Invalid argument passed to jsPDF.scale");return w===y.COMPAT?t*td:w===y.ADVANCED?t:void 0},C=function(t){return I(w===y.COMPAT?rm()-t:w===y.ADVANCED?t:void 0)};p.__private__.setPrecision=p.setPrecision=function(t){"number"==typeof parseInt(t,10)&&(e=parseInt(t,10))};var E,F="00000000000000000000000000000000",O=p.__private__.getFileId=function(){return F},T=p.__private__.setFileId=function(t){return F=void 0!==t&&/^[a-fA-F0-9]{32}$/.test(t)?t.toUpperCase():F.split("").map(function(){return"ABCDEF0123456789".charAt(Math.floor(16*Math.random()))}).join(""),null!==c&&(eT=new tY(c.userPermissions,c.userPassword,c.ownerPassword,F)),F};p.setFileId=function(t){return T(t),this},p.getFileId=function(){return O()};var M=p.__private__.convertDateToPDFDate=function(t){var e=t.getTimezoneOffset(),r=Math.floor(Math.abs(e/60)),n=Math.abs(e%60),i=[e<0?"+":"-",q(r),"'",q(n),"'"].join("");return["D:",t.getFullYear(),q(t.getMonth()+1),q(t.getDate()),q(t.getHours()),q(t.getMinutes()),q(t.getSeconds()),i].join("")},j=p.__private__.convertPDFDateToDate=function(t){return new Date(parseInt(t.substr(2,4),10),parseInt(t.substr(6,2),10)-1,parseInt(t.substr(8,2),10),parseInt(t.substr(10,2),10),parseInt(t.substr(12,2),10),parseInt(t.substr(14,2),10),0)},B=p.__private__.setCreationDate=function(t){var e;if(void 0===t&&(t=new Date),t instanceof Date)e=M(t);else{if(!/^D:(20[0-2][0-9]|203[0-7]|19[7-9][0-9])(0[0-9]|1[0-2])([0-2][0-9]|3[0-1])(0[0-9]|1[0-9]|2[0-3])(0[0-9]|[1-5][0-9])(0[0-9]|[1-5][0-9])(\+0[0-9]|\+1[0-4]|-0[0-9]|-1[0-1])'(0[0-9]|[1-5][0-9])'?$/.test(t))throw Error("Invalid argument passed to jsPDF.setCreationDate");e=t}return E=e},R=p.__private__.getCreationDate=function(t){var e=E;return"jsDate"===t&&(e=j(E)),e};p.setCreationDate=function(t){return B(t),this},p.getCreationDate=function(t){return R(t)};var D,q=p.__private__.padd2=function(t){return("0"+parseInt(t)).slice(-2)},U=p.__private__.padd2Hex=function(t){return("00"+(t=t.toString())).substr(t.length)},z=0,H=[],W=[],V=0,G=[],Y=[],J=!1,K=W,X=function(){z=0,V=0,W=[],H=[],G=[],tV=tz(),tG=tz()};p.__private__.setCustomOutputDestination=function(t){J=!0,K=t};var Z=function(t){J||(K=t)};p.__private__.resetCustomOutputDestination=function(){J=!1,K=W};var $=p.__private__.out=function(t){return t=t.toString(),V+=t.length+1,K.push(t),K},Q=p.__private__.write=function(t){return $(1==arguments.length?t.toString():Array.prototype.join.call(arguments," "))},tt=p.__private__.getArrayBuffer=function(t){for(var e=t.length,r=new ArrayBuffer(e),n=new Uint8Array(r);e--;)n[e]=t.charCodeAt(e);return r},te=[["Helvetica","helvetica","normal","WinAnsiEncoding"],["Helvetica-Bold","helvetica","bold","WinAnsiEncoding"],["Helvetica-Oblique","helvetica","italic","WinAnsiEncoding"],["Helvetica-BoldOblique","helvetica","bolditalic","WinAnsiEncoding"],["Courier","courier","normal","WinAnsiEncoding"],["Courier-Bold","courier","bold","WinAnsiEncoding"],["Courier-Oblique","courier","italic","WinAnsiEncoding"],["Courier-BoldOblique","courier","bolditalic","WinAnsiEncoding"],["Times-Roman","times","normal","WinAnsiEncoding"],["Times-Bold","times","bold","WinAnsiEncoding"],["Times-Italic","times","italic","WinAnsiEncoding"],["Times-BoldItalic","times","bolditalic","WinAnsiEncoding"],["ZapfDingbats","zapfdingbats","normal",null],["Symbol","symbol","normal",null]];p.__private__.getStandardFonts=function(){return te};var tr=t.fontSize||16;p.__private__.setFontSize=p.setFontSize=function(t){return tr=w===y.ADVANCED?t/td:t,this};var tn,ti=p.__private__.getFontSize=p.getFontSize=function(){return w===y.COMPAT?tr:tr*td},to=t.R2L||!1;p.__private__.setR2L=p.setR2L=function(t){return to=t,this},p.__private__.getR2L=p.getR2L=function(){return to};var ta,ts=p.__private__.setZoomMode=function(t){if(/^(?:\d+\.\d*|\d*\.\d+|\d+)%$/.test(t))tn=t;else if(isNaN(t)){if(-1===[void 0,null,"fullwidth","fullheight","fullpage","original"].indexOf(t))throw Error('zoom must be Integer (e.g. 2), a percentage Value (e.g. 300%) or fullwidth, fullheight, fullpage, original. "'+t+'" is not recognized.');tn=t}else tn=parseInt(t,10)};p.__private__.getZoomMode=function(){return tn};var th,tu=p.__private__.setPageMode=function(t){if(-1==[void 0,null,"UseNone","UseOutlines","UseThumbs","FullScreen"].indexOf(t))throw Error('Page mode must be one of UseNone, UseOutlines, UseThumbs, or FullScreen. "'+t+'" is not recognized.');ta=t};p.__private__.getPageMode=function(){return ta};var tl=p.__private__.setLayoutMode=function(t){if(-1==[void 0,null,"continuous","single","twoleft","tworight","two"].indexOf(t))throw Error('Layout mode must be one of continuous, single, twoleft, tworight. "'+t+'" is not recognized.');th=t};p.__private__.getLayoutMode=function(){return th},p.__private__.setDisplayMode=p.setDisplayMode=function(t,e,r){return ts(t),tl(e),tu(r),this};var tc={title:"",subject:"",author:"",keywords:"",creator:""};p.__private__.getDocumentProperty=function(t){if(-1===Object.keys(tc).indexOf(t))throw Error("Invalid argument passed to jsPDF.getDocumentProperty");return tc[t]},p.__private__.getDocumentProperties=function(){return tc},p.__private__.setDocumentProperties=p.setProperties=p.setDocumentProperties=function(t){for(var e in tc)tc.hasOwnProperty(e)&&t[e]&&(tc[e]=t[e]);return this},p.__private__.setDocumentProperty=function(t,e){if(-1===Object.keys(tc).indexOf(t))throw Error("Invalid arguments passed to jsPDF.setDocumentProperty");return tc[t]=e};var tf,td,tp,tg,tm,tb={},tv={},ty=[],tw={},tx={},t_={},tL={},tS=null,tP=0,tk=[],tI=new tK(p),tO=t.hotfixes||[],tT={},tM={},tj=[],tB=function t(e,r,n,i,o,a){if(!(this instanceof t))return new t(e,r,n,i,o,a);isNaN(e)&&(e=1),isNaN(r)&&(r=0),isNaN(n)&&(n=0),isNaN(i)&&(i=1),isNaN(o)&&(o=0),isNaN(a)&&(a=0),this._matrix=[e,r,n,i,o,a]};Object.defineProperty(tB.prototype,"sx",{get:function(){return this._matrix[0]},set:function(t){this._matrix[0]=t}}),Object.defineProperty(tB.prototype,"shy",{get:function(){return this._matrix[1]},set:function(t){this._matrix[1]=t}}),Object.defineProperty(tB.prototype,"shx",{get:function(){return this._matrix[2]},set:function(t){this._matrix[2]=t}}),Object.defineProperty(tB.prototype,"sy",{get:function(){return this._matrix[3]},set:function(t){this._matrix[3]=t}}),Object.defineProperty(tB.prototype,"tx",{get:function(){return this._matrix[4]},set:function(t){this._matrix[4]=t}}),Object.defineProperty(tB.prototype,"ty",{get:function(){return this._matrix[5]},set:function(t){this._matrix[5]=t}}),Object.defineProperty(tB.prototype,"a",{get:function(){return this._matrix[0]},set:function(t){this._matrix[0]=t}}),Object.defineProperty(tB.prototype,"b",{get:function(){return this._matrix[1]},set:function(t){this._matrix[1]=t}}),Object.defineProperty(tB.prototype,"c",{get:function(){return this._matrix[2]},set:function(t){this._matrix[2]=t}}),Object.defineProperty(tB.prototype,"d",{get:function(){return this._matrix[3]},set:function(t){this._matrix[3]=t}}),Object.defineProperty(tB.prototype,"e",{get:function(){return this._matrix[4]},set:function(t){this._matrix[4]=t}}),Object.defineProperty(tB.prototype,"f",{get:function(){return this._matrix[5]},set:function(t){this._matrix[5]=t}}),Object.defineProperty(tB.prototype,"rotation",{get:function(){return Math.atan2(this.shx,this.sx)}}),Object.defineProperty(tB.prototype,"scaleX",{get:function(){return this.decompose().scale.sx}}),Object.defineProperty(tB.prototype,"scaleY",{get:function(){return this.decompose().scale.sy}}),Object.defineProperty(tB.prototype,"isIdentity",{get:function(){return 1===this.sx&&0===this.shy&&0===this.shx&&1===this.sy&&0===this.tx&&0===this.ty}}),tB.prototype.join=function(t){return[this.sx,this.shy,this.shx,this.sy,this.tx,this.ty].map(L).join(t)},tB.prototype.multiply=function(t){return new tB(t.sx*this.sx+t.shy*this.shx,t.sx*this.shy+t.shy*this.sy,t.shx*this.sx+t.sy*this.shx,t.shx*this.shy+t.sy*this.sy,t.tx*this.sx+t.ty*this.shx+this.tx,t.tx*this.shy+t.ty*this.sy+this.ty)},tB.prototype.decompose=function(){var t=this.sx,e=this.shy,r=this.shx,n=this.sy,i=this.tx,o=this.ty,a=Math.sqrt(t*t+e*e),s=(t/=a)*r+(e/=a)*n,h=Math.sqrt((r-=t*s)*r+(n-=e*s)*n);return s/=h,t*(n/=h)<e*(r/=h)&&(t=-t,e=-e,s=-s,a=-a),{scale:new tB(a,0,0,h,0,0),translate:new tB(1,0,0,1,i,o),rotate:new tB(t,e,-e,t,0,0),skew:new tB(1,0,s,1,0,0)}},tB.prototype.toString=function(t){return this.join(" ")},tB.prototype.inversed=function(){var t=this.sx,e=this.shy,r=this.shx,n=this.sy,i=this.tx,o=this.ty,a=1/(t*n-e*r),s=n*a,h=-e*a,u=-r*a,l=t*a;return new tB(s,h,u,l,-s*i-u*o,-h*i-l*o)},tB.prototype.applyToPoint=function(t){return new rh(t.x*this.sx+t.y*this.shx+this.tx,t.x*this.shy+t.y*this.sy+this.ty)},tB.prototype.applyToRectangle=function(t){var e=this.applyToPoint(t),r=this.applyToPoint(new rh(t.x+t.w,t.y+t.h));return new ru(e.x,e.y,r.x-e.x,r.y-e.y)},tB.prototype.clone=function(){return new tB(this.sx,this.shy,this.shx,this.sy,this.tx,this.ty)},p.Matrix=tB;var tR=p.matrixMult=function(t,e){return e.multiply(t)},tD=new tB(1,0,0,1,0,0);p.unitMatrix=p.identityMatrix=tD;var tq=function(t,e){if(!tx[t]){var r=(e instanceof t$?"Sh":"P")+(Object.keys(tw).length+1).toString(10);e.id=r,tx[t]=r,tw[r]=e,tI.publish("addPattern",e)}};p.ShadingPattern=t$,p.TilingPattern=tQ,p.addShadingPattern=function(t,e){return N("addShadingPattern()"),tq(t,e),this},p.beginTilingPattern=function(t){N("beginTilingPattern()"),rc(t.boundingBox[0],t.boundingBox[1],t.boundingBox[2]-t.boundingBox[0],t.boundingBox[3]-t.boundingBox[1],t.matrix)},p.endTilingPattern=function(t,e){N("endTilingPattern()"),e.stream=Y[D].join("\n"),tq(t,e),tI.publish("endTilingPattern",e),tj.pop().restore()};var tU=p.__private__.newObject=function(){var t=tz();return tH(t,!0),t},tz=p.__private__.newObjectDeferred=function(){return H[++z]=function(){return V},z},tH=function(t,e){return e="boolean"==typeof e&&e,H[t]=V,e&&$(t+" 0 obj"),t},tW=p.__private__.newAdditionalObject=function(){var t={objId:tz(),content:""};return G.push(t),t},tV=tz(),tG=tz(),tZ=p.__private__.decodeColorString=function(t){var e=t.split(" ");if(2!==e.length||"g"!==e[1]&&"G"!==e[1])5===e.length&&("k"===e[4]||"K"===e[4])&&(e=[(1-e[0])*(1-e[3]),(1-e[1])*(1-e[3]),(1-e[2])*(1-e[3]),"r"]);else{var r=parseFloat(e[0]);e=[r,r,r,"r"]}for(var n="#",i=0;i<3;i++)n+=("0"+Math.floor(255*parseFloat(e[i])).toString(16)).slice(-2);return n},t2=p.__private__.encodeColorString=function(t){"string"==typeof t&&(t={ch1:t});var e,r=t.ch1,n=t.ch2,o=t.ch3,a=t.ch4,s="draw"===t.pdfColorType?["G","RG","K"]:["g","rg","k"];if("string"==typeof r&&"#"!==r.charAt(0)){var h=new tF(r);if(h.ok)r=h.toHex();else if(!/^\d*\.?\d*$/.test(r))throw Error('Invalid color "'+r+'" passed to jsPDF.encodeColorString.')}if("string"==typeof r&&/^#[0-9A-Fa-f]{3}$/.test(r)&&(r="#"+r[1]+r[1]+r[2]+r[2]+r[3]+r[3]),"string"==typeof r&&/^#[0-9A-Fa-f]{6}$/.test(r)){var u=parseInt(r.substr(1),16);r=u>>16&255,n=u>>8&255,o=255&u}if(void 0===n||void 0===a&&r===n&&n===o)e="string"==typeof r?r+" "+s[0]:2===t.precision?P(r/255)+" "+s[0]:k(r/255)+" "+s[0];else if(void 0===a||"object"===i()(a)){if(a&&!isNaN(a.a)&&0===a.a)return["1.","1.","1.",s[1]].join(" ");e="string"==typeof r?[r,n,o,s[1]].join(" "):2===t.precision?[P(r/255),P(n/255),P(o/255),s[1]].join(" "):[k(r/255),k(n/255),k(o/255),s[1]].join(" ")}else e="string"==typeof r?[r,n,o,a,s[2]].join(" "):2===t.precision?[P(r),P(n),P(o),P(a),s[2]].join(" "):[k(r),k(n),k(o),k(a),s[2]].join(" ");return e},t0=p.__private__.getFilters=function(){return s},t5=p.__private__.putStream=function(t){var e=(t=t||{}).data||"",r=t.filters||t0(),n=t.alreadyAppliedFilters||[],i=t.addLength1||!1,o=e.length,a=t.objectId,s=function(t){return t};if(null!==c&&void 0===a)throw Error("ObjectId must be passed to putStream for file encryption");null!==c&&(s=eT.encryptor(a,0));var h={};!0===r&&(r=["FlateEncode"]);var u=t.additionalKeyValues||[],l=(h=void 0!==t1.API.processDataByFilters?t1.API.processDataByFilters(e,r):{data:e,reverseChain:[]}).reverseChain+(Array.isArray(n)?n.join(" "):n.toString());if(0!==h.data.length&&(u.push({key:"Length",value:h.data.length}),!0===i&&u.push({key:"Length1",value:o})),0!=l.length)if(l.split("/").length-1==1)u.push({key:"Filter",value:l});else{u.push({key:"Filter",value:"["+l+"]"});for(var f=0;f<u.length;f+=1)if("DecodeParms"===u[f].key){for(var d=[],p=0;p<h.reverseChain.split("/").length-1;p+=1)d.push("null");d.push(u[f].value),u[f].value="["+d.join(" ")+"]"}}$("<<");for(var g=0;g<u.length;g++)$("/"+u[g].key+" "+u[g].value);$(">>"),0!==h.data.length&&($("stream"),$(s(h.data)),$("endstream"))},t3=p.__private__.putPage=function(t){var e=t.number,r=t.data,n=t.objId,i=t.contentsObjId;tH(n,!0),$("<</Type /Page"),$("/Parent "+t.rootDictionaryObjId+" 0 R"),$("/Resources "+t.resourceDictionaryObjId+" 0 R"),$("/MediaBox ["+parseFloat(L(t.mediaBox.bottomLeftX))+" "+parseFloat(L(t.mediaBox.bottomLeftY))+" "+L(t.mediaBox.topRightX)+" "+L(t.mediaBox.topRightY)+"]"),null!==t.cropBox&&$("/CropBox ["+L(t.cropBox.bottomLeftX)+" "+L(t.cropBox.bottomLeftY)+" "+L(t.cropBox.topRightX)+" "+L(t.cropBox.topRightY)+"]"),null!==t.bleedBox&&$("/BleedBox ["+L(t.bleedBox.bottomLeftX)+" "+L(t.bleedBox.bottomLeftY)+" "+L(t.bleedBox.topRightX)+" "+L(t.bleedBox.topRightY)+"]"),null!==t.trimBox&&$("/TrimBox ["+L(t.trimBox.bottomLeftX)+" "+L(t.trimBox.bottomLeftY)+" "+L(t.trimBox.topRightX)+" "+L(t.trimBox.topRightY)+"]"),null!==t.artBox&&$("/ArtBox ["+L(t.artBox.bottomLeftX)+" "+L(t.artBox.bottomLeftY)+" "+L(t.artBox.topRightX)+" "+L(t.artBox.topRightY)+"]"),"number"==typeof t.userUnit&&1!==t.userUnit&&$("/UserUnit "+t.userUnit),tI.publish("putPage",{objId:n,pageContext:tk[e],pageNumber:e,page:r}),$("/Contents "+i+" 0 R"),$(">>"),$("endobj");var o=r.join("\n");return w===y.ADVANCED&&(o+="\nQ"),tH(i,!0),t5({data:o,filters:t0(),objectId:i}),$("endobj"),n},t4=p.__private__.putPages=function(){var t,e,r=[];for(t=1;t<=tP;t++)tk[t].objId=tz(),tk[t].contentsObjId=tz();for(t=1;t<=tP;t++)r.push(t3({number:t,data:Y[t],objId:tk[t].objId,contentsObjId:tk[t].contentsObjId,mediaBox:tk[t].mediaBox,cropBox:tk[t].cropBox,bleedBox:tk[t].bleedBox,trimBox:tk[t].trimBox,artBox:tk[t].artBox,userUnit:tk[t].userUnit,rootDictionaryObjId:tV,resourceDictionaryObjId:tG}));tH(tV,!0),$("<</Type /Pages");var n="/Kids [";for(e=0;e<tP;e++)n+=r[e]+" 0 R ";$(n+"]"),$("/Count "+tP),$(">>"),$("endobj"),tI.publish("postPutPages")},t6=function(t){tI.publish("putFont",{font:t,out:$,newObject:tU,putStream:t5}),!0!==t.isAlreadyPutted&&(t.objectNumber=tU(),$("<<"),$("/Type /Font"),$("/BaseFont /"+tJ(t.postScriptName)),$("/Subtype /Type1"),"string"==typeof t.encoding&&$("/Encoding /"+t.encoding),$("/FirstChar 32"),$("/LastChar 255"),$(">>"),$("endobj"))},t8=function(){for(var t in tb)tb.hasOwnProperty(t)&&(!1===f||!0===f&&d.hasOwnProperty(t))&&t6(tb[t])},t7=function(t){t.objectNumber=tU();var e=[];e.push({key:"Type",value:"/XObject"}),e.push({key:"Subtype",value:"/Form"}),e.push({key:"BBox",value:"["+[L(t.x),L(t.y),L(t.x+t.width),L(t.y+t.height)].join(" ")+"]"}),e.push({key:"Matrix",value:"["+t.matrix.toString()+"]"}),t5({data:t.pages[1].join("\n"),additionalKeyValues:e,objectId:t.objectNumber}),$("endobj")},t9=function(){for(var t in tT)tT.hasOwnProperty(t)&&t7(tT[t])},et=function(t,e){var r,n=[],i=1/(e-1);for(r=0;r<1;r+=i)n.push(r);if(n.push(1),0!=t[0].offset){var o={offset:0,color:t[0].color};t.unshift(o)}if(1!=t[t.length-1].offset){var a={offset:1,color:t[t.length-1].color};t.push(a)}for(var s="",h=0,u=0;u<n.length;u++){for(r=n[u];r>t[h+1].offset;)h++;var l=t[h].offset,c=(r-l)/(t[h+1].offset-l),f=t[h].color,d=t[h+1].color;s+=U(Math.round((1-c)*f[0]+c*d[0]).toString(16))+U(Math.round((1-c)*f[1]+c*d[1]).toString(16))+U(Math.round((1-c)*f[2]+c*d[2]).toString(16))}return s.trim()},ee=function(t,e){e||(e=21);var r=tU(),n=et(t.colors,e),i=[];i.push({key:"FunctionType",value:"0"}),i.push({key:"Domain",value:"[0.0 1.0]"}),i.push({key:"Size",value:"["+e+"]"}),i.push({key:"BitsPerSample",value:"8"}),i.push({key:"Range",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),i.push({key:"Decode",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),t5({data:n,additionalKeyValues:i,alreadyAppliedFilters:["/ASCIIHexDecode"],objectId:r}),$("endobj"),t.objectNumber=tU(),$("<< /ShadingType "+t.type),$("/ColorSpace /DeviceRGB");var o="/Coords ["+L(parseFloat(t.coords[0]))+" "+L(parseFloat(t.coords[1]))+" ";2===t.type?o+=L(parseFloat(t.coords[2]))+" "+L(parseFloat(t.coords[3])):o+=L(parseFloat(t.coords[2]))+" "+L(parseFloat(t.coords[3]))+" "+L(parseFloat(t.coords[4]))+" "+L(parseFloat(t.coords[5])),$(o+="]"),t.matrix&&$("/Matrix ["+t.matrix.toString()+"]"),$("/Function "+r+" 0 R"),$("/Extend [true true]"),$(">>"),$("endobj")},er=function(t,e){var r=tz(),n=tU();e.push({resourcesOid:r,objectOid:n}),t.objectNumber=n;var i=[];i.push({key:"Type",value:"/Pattern"}),i.push({key:"PatternType",value:"1"}),i.push({key:"PaintType",value:"1"}),i.push({key:"TilingType",value:"1"}),i.push({key:"BBox",value:"["+t.boundingBox.map(L).join(" ")+"]"}),i.push({key:"XStep",value:L(t.xStep)}),i.push({key:"YStep",value:L(t.yStep)}),i.push({key:"Resources",value:r+" 0 R"}),t.matrix&&i.push({key:"Matrix",value:"["+t.matrix.toString()+"]"}),t5({data:t.stream,additionalKeyValues:i,objectId:t.objectNumber}),$("endobj")},en=function(t){var e;for(e in tw)tw.hasOwnProperty(e)&&(tw[e]instanceof t$?ee(tw[e]):tw[e]instanceof tQ&&er(tw[e],t))},ei=function(t){for(var e in t.objectNumber=tU(),$("<<"),t)switch(e){case"opacity":$("/ca "+P(t[e]));break;case"stroke-opacity":$("/CA "+P(t[e]))}$(">>"),$("endobj")},eo=function(){var t;for(t in t_)t_.hasOwnProperty(t)&&ei(t_[t])},ea=function(){for(var t in $("/XObject <<"),tT)tT.hasOwnProperty(t)&&tT[t].objectNumber>=0&&$("/"+t+" "+tT[t].objectNumber+" 0 R");tI.publish("putXobjectDict"),$(">>")},es=function(){eT.oid=tU(),$("<<"),$("/Filter /Standard"),$("/V "+eT.v),$("/R "+eT.r),$("/U <"+eT.toHexString(eT.U)+">"),$("/O <"+eT.toHexString(eT.O)+">"),$("/P "+eT.P),$(">>"),$("endobj")},eh=function(){for(var t in $("/Font <<"),tb)tb.hasOwnProperty(t)&&(!1===f||!0===f&&d.hasOwnProperty(t))&&$("/"+t+" "+tb[t].objectNumber+" 0 R");$(">>")},eu=function(){if(Object.keys(tw).length>0){for(var t in $("/Shading <<"),tw)tw.hasOwnProperty(t)&&tw[t]instanceof t$&&tw[t].objectNumber>=0&&$("/"+t+" "+tw[t].objectNumber+" 0 R");tI.publish("putShadingPatternDict"),$(">>")}},el=function(t){if(Object.keys(tw).length>0){for(var e in $("/Pattern <<"),tw)tw.hasOwnProperty(e)&&tw[e]instanceof p.TilingPattern&&tw[e].objectNumber>=0&&tw[e].objectNumber<t&&$("/"+e+" "+tw[e].objectNumber+" 0 R");tI.publish("putTilingPatternDict"),$(">>")}},ec=function(){if(Object.keys(t_).length>0){var t;for(t in $("/ExtGState <<"),t_)t_.hasOwnProperty(t)&&t_[t].objectNumber>=0&&$("/"+t+" "+t_[t].objectNumber+" 0 R");tI.publish("putGStateDict"),$(">>")}},ef=function(t){tH(t.resourcesOid,!0),$("<<"),$("/ProcSet [/PDF /Text /ImageB /ImageC /ImageI]"),eh(),eu(),el(t.objectOid),ec(),ea(),$(">>"),$("endobj")},ed=function(){var t=[];t8(),eo(),t9(),en(t),tI.publish("putResources"),t.forEach(ef),ef({resourcesOid:tG,objectOid:Number.MAX_SAFE_INTEGER}),tI.publish("postPutResources")},ep=function(){tI.publish("putAdditionalObjects");for(var t=0;t<G.length;t++){var e=G[t];tH(e.objId,!0),$(e.content),$("endobj")}tI.publish("postPutAdditionalObjects")},eg=function(t){tv[t.fontName]=tv[t.fontName]||{},tv[t.fontName][t.fontStyle]=t.id},em=function(t,e,r,n,i){var o={id:"F"+(Object.keys(tb).length+1).toString(10),postScriptName:t,fontName:e,fontStyle:r,encoding:n,isStandardFont:i||!1,metadata:{}};return tI.publish("addFont",{font:o,instance:this}),tb[o.id]=o,eg(o),o.id},eb=function(t,e){var r,n,i,o,a,s,h,u,l;if(i=(e=e||{}).sourceEncoding||"Unicode",a=e.outputEncoding,(e.autoencode||a)&&tb[tf].metadata&&tb[tf].metadata[i]&&tb[tf].metadata[i].encoding&&(o=tb[tf].metadata[i].encoding,!a&&tb[tf].encoding&&(a=tb[tf].encoding),!a&&o.codePages&&(a=o.codePages[0]),"string"==typeof a&&(a=o[a]),a)){for(h=!1,s=[],r=0,n=t.length;r<n;r++)(u=a[t.charCodeAt(r)])?s.push(String.fromCharCode(u)):s.push(t[r]),s[r].charCodeAt(0)>>8&&(h=!0);t=s.join("")}for(r=t.length;void 0===h&&0!==r;)t.charCodeAt(r-1)>>8&&(h=!0),r--;if(!h)return t;for(s=e.noBOM?[]:[254,255],r=0,n=t.length;r<n;r++){if((l=(u=t.charCodeAt(r))>>8)>>8)throw Error("Character at position "+r+" of string '"+t+"' exceeds 16bits. Cannot be encoded into UCS-2 BE");s.push(l),s.push(u-(l<<8))}return String.fromCharCode.apply(void 0,s)},ev=p.__private__.pdfEscape=p.pdfEscape=function(t,e){return eb(t,e).replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},ey=p.__private__.beginPage=function(t){Y[++tP]=[],tk[tP]={objId:0,contentsObjId:0,userUnit:Number(h),artBox:null,bleedBox:null,cropBox:null,trimBox:null,mediaBox:{bottomLeftX:0,bottomLeftY:0,topRightX:Number(t[0]),topRightY:Number(t[1])}},e_(tP),Z(Y[D])},ew=function(t,e){var n,i,a;switch(r=e||r,"string"==typeof t&&Array.isArray(n=v(t.toLowerCase()))&&(i=n[0],a=n[1]),Array.isArray(t)&&(i=t[0]*td,a=t[1]*td),isNaN(i)&&(i=o[0],a=o[1]),(i>14400||a>14400)&&(tN.warn("A page in a PDF can not be wider or taller than 14400 userUnit. jsPDF limits the width/height to 14400"),i=Math.min(14400,i),a=Math.min(14400,a)),o=[i,a],r.substr(0,1)){case"l":a>i&&(o=[a,i]);break;case"p":i>a&&(o=[a,i])}ey(o),e2(eQ),$(e9),0!==ro&&$(ro+" J"),0!==ra&&$(ra+" j"),tI.publish("addPage",{pageNumber:tP})},ex=function(t){t>0&&t<=tP&&(Y.splice(t,1),tk.splice(t,1),tP--,D>tP&&(D=tP),this.setPage(D))},e_=function(t){t>0&&t<=tP&&(D=t)},eA=p.__private__.getNumberOfPages=p.getNumberOfPages=function(){return Y.length-1},eL=function(t,e,r){var n,i=void 0;return r=r||{},t=void 0!==t?t:tb[tf].fontName,e=void 0!==e?e:tb[tf].fontStyle,void 0!==tv[n=t.toLowerCase()]&&void 0!==tv[n][e]?i=tv[n][e]:void 0!==tv[t]&&void 0!==tv[t][e]?i=tv[t][e]:!1===r.disableWarning&&tN.warn("Unable to look up font label for font '"+t+"', '"+e+"'. Refer to getFontList() for available fonts."),i||r.noFallback||null==(i=tv.times[e])&&(i=tv.times.normal),i},eN=p.__private__.putInfo=function(){var t=tU(),e=function(t){return t};for(var r in null!==c&&(e=eT.encryptor(t,0)),$("<<"),$("/Producer ("+ev(e("jsPDF "+t1.version))+")"),tc)tc.hasOwnProperty(r)&&tc[r]&&$("/"+r.substr(0,1).toUpperCase()+r.substr(1)+" ("+ev(e(tc[r]))+")");$("/CreationDate ("+ev(e(E))+")"),$(">>"),$("endobj")},eS=p.__private__.putCatalog=function(t){var e=(t=t||{}).rootDictionaryObjId||tV;switch(tU(),$("<<"),$("/Type /Catalog"),$("/Pages "+e+" 0 R"),tn||(tn="fullwidth"),tn){case"fullwidth":$("/OpenAction [3 0 R /FitH null]");break;case"fullheight":$("/OpenAction [3 0 R /FitV null]");break;case"fullpage":$("/OpenAction [3 0 R /Fit]");break;case"original":$("/OpenAction [3 0 R /XYZ null null 1]");break;default:var r=""+tn;"%"===r.substr(r.length-1)&&(tn=parseInt(tn)/100),"number"==typeof tn&&$("/OpenAction [3 0 R /XYZ null null "+P(tn)+"]")}switch(th||(th="continuous"),th){case"continuous":$("/PageLayout /OneColumn");break;case"single":$("/PageLayout /SinglePage");break;case"two":case"twoleft":$("/PageLayout /TwoColumnLeft");break;case"tworight":$("/PageLayout /TwoColumnRight")}ta&&$("/PageMode /"+ta),tI.publish("putCatalog"),$(">>"),$("endobj")},eP=p.__private__.putTrailer=function(){$("trailer"),$("<<"),$("/Size "+(z+1)),$("/Root "+z+" 0 R"),$("/Info "+(z-1)+" 0 R"),null!==c&&$("/Encrypt "+eT.oid+" 0 R"),$("/ID [ <"+F+"> <"+F+"> ]"),$(">>")},ek=p.__private__.putHeader=function(){$("%PDF-"+g),$("%\xba\xdf\xac\xe0")},eI=p.__private__.putXRef=function(){var t="0000000000";$("xref"),$("0 "+(z+1)),$("0000000000 65535 f ");for(var e=1;e<=z;e++)"function"==typeof H[e]?$((t+H[e]()).slice(-10)+" 00000 n "):void 0!==H[e]?$((t+H[e]).slice(-10)+" 00000 n "):$("0000000000 00000 n ")},eC=p.__private__.buildDocument=function(){X(),Z(W),tI.publish("buildDocument"),ek(),t4(),ep(),ed(),null!==c&&es(),eN(),eS();var t=V;return eI(),eP(),$("startxref"),$(""+t),$("%%EOF"),Z(Y[D]),W.join("\n")},eE=p.__private__.getBlob=function(t){return new Blob([tt(t)],{type:"application/pdf"})},eF=p.output=p.__private__.output=((eZ=function(t,e){switch("string"==typeof(e=e||{})?e={filename:e}:e.filename=e.filename||"generated.pdf",t){case void 0:return eC();case"save":p.save(e.filename);break;case"arraybuffer":return tt(eC());case"blob":return eE(eC());case"bloburi":case"bloburl":if(void 0!==tA.URL&&"function"==typeof tA.URL.createObjectURL)return tA.URL&&tA.URL.createObjectURL(eE(eC()))||void 0;tN.warn("bloburl is not supported by your system, because URL.createObjectURL is not supported by your browser.");break;case"datauristring":case"dataurlstring":var r="",n=eC();try{r=tC(n)}catch(t){r=tC(unescape(encodeURIComponent(n)))}return"data:application/pdf;filename="+e.filename+";base64,"+r;case"pdfobjectnewwindow":if("[object Window]"===Object.prototype.toString.call(tA)){var i="https://cdnjs.cloudflare.com/ajax/libs/pdfobject/2.1.1/pdfobject.min.js",o=' integrity="sha512-4ze/a9/4jqu+tX9dfOqJYSvyYd5M6qum/3HpCLr+/Jqf0whc37VUbkpNGHR7/8pSnCFw47T1fmIpwBV7UySh3g==" crossorigin="anonymous"';e.pdfObjectUrl&&(i=e.pdfObjectUrl,o="");var a='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><script src="'+i+'"'+o+'><\/script><script >PDFObject.embed("'+this.output("dataurlstring")+'", '+JSON.stringify(e)+");<\/script></body></html>",s=tA.open();return null!==s&&s.document.write(a),s}throw Error("The option pdfobjectnewwindow just works in a browser-environment.");case"pdfjsnewwindow":if("[object Window]"===Object.prototype.toString.call(tA)){var h='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe id="pdfViewer" src="'+(e.pdfJsUrl||"examples/PDF.js/web/viewer.html")+"?file=&downloadName="+e.filename+'" width="500px" height="400px" /></body></html>',u=tA.open();if(null!==u){u.document.write(h);var l=this;u.document.documentElement.querySelector("#pdfViewer").onload=function(){u.document.title=e.filename,u.document.documentElement.querySelector("#pdfViewer").contentWindow.PDFViewerApplication.open(l.output("bloburl"))}}return u}throw Error("The option pdfjsnewwindow just works in a browser-environment.");case"dataurlnewwindow":if("[object Window]"!==Object.prototype.toString.call(tA))throw Error("The option dataurlnewwindow just works in a browser-environment.");var c='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe src="'+this.output("datauristring",e)+'"></iframe></body></html>',f=tA.open();if(null!==f&&(f.document.write(c),f.document.title=e.filename),f||"undefined"==typeof safari)return f;break;case"datauri":case"dataurl":return tA.document.location.href=this.output("datauristring",e);default:return null}}).foo=function(){try{return eZ.apply(this,arguments)}catch(r){var t=r.stack||"";~t.indexOf(" at ")&&(t=t.split(" at ")[1]);var e="Error in function "+t.split("\n")[0].split("<")[0]+": "+r.message;if(!tA.console)throw Error(e);tA.console.error(e,r),tA.alert&&alert(e)}},eZ.foo.bar=eZ,eZ.foo),eO=function(t){return!0===Array.isArray(tO)&&tO.indexOf(t)>-1};switch(n){case"pt":td=1;break;case"mm":td=72/25.4;break;case"cm":td=72/2.54;break;case"in":td=72;break;case"px":td=1==eO("px_scaling")?.75:96/72;break;case"pc":case"em":td=12;break;case"ex":td=6;break;default:if("number"!=typeof n)throw Error("Invalid unit: "+n);td=n}var eT=null;B(),T();var eM=p.__private__.getPageInfo=p.getPageInfo=function(t){if(isNaN(t)||t%1!=0)throw Error("Invalid argument passed to jsPDF.getPageInfo");return{objId:tk[t].objId,pageNumber:t,pageContext:tk[t]}},ej=p.__private__.getPageInfoByObjId=function(t){if(isNaN(t)||t%1!=0)throw Error("Invalid argument passed to jsPDF.getPageInfoByObjId");for(var e in tk)if(tk[e].objId===t)break;return eM(e)},eB=p.__private__.getCurrentPageInfo=p.getCurrentPageInfo=function(){return{objId:tk[D].objId,pageNumber:D,pageContext:tk[D]}};p.addPage=function(){return ew.apply(this,arguments),this},p.setPage=function(){return e_.apply(this,arguments),Z.call(this,Y[D]),this},p.insertPage=function(t){return this.addPage(),this.movePage(D,t),this},p.movePage=function(t,e){var r,n;if(t>e){r=Y[t],n=tk[t];for(var i=t;i>e;i--)Y[i]=Y[i-1],tk[i]=tk[i-1];Y[e]=r,tk[e]=n,this.setPage(e)}else if(t<e){r=Y[t],n=tk[t];for(var o=t;o<e;o++)Y[o]=Y[o+1],tk[o]=tk[o+1];Y[e]=r,tk[e]=n,this.setPage(e)}return this},p.deletePage=function(){return ex.apply(this,arguments),this},p.__private__.text=p.text=function(t,e,r,n,o){var a,s,h,u,l,c,f,p,g,m=(n=n||{}).scope||this;if("number"==typeof t&&"number"==typeof e&&("string"==typeof r||Array.isArray(r))){var b=r;r=e,e=t,t=b}if(arguments[3]instanceof tB==!1?(h=arguments[4],u=arguments[5],"object"===i()(f=arguments[3])&&null!==f||("string"==typeof h&&(u=h,h=null),"string"==typeof f&&(u=f,f=null),"number"==typeof f&&(h=f,f=null),n={flags:f,angle:h,align:u})):(N("The transform parameter of text() with a Matrix value"),g=o),isNaN(e)||isNaN(r)||null==t)throw Error("Invalid arguments passed to jsPDF.text");if(0===t.length)return m;var v="",x=!1,_="number"==typeof n.lineHeightFactor?n.lineHeightFactor:e$,A=m.internal.scaleFactor;function S(t){for(var e,r=t.concat(),n=[],i=r.length;i--;)"string"==typeof(e=r.shift())?n.push(e):Array.isArray(t)&&(1===e.length||void 0===e[1]&&void 0===e[2])?n.push(e[0]):n.push([e[0],e[1],e[2]]);return n}function P(t,e){var r;if("string"==typeof t)r=e(t)[0];else if(Array.isArray(t)){for(var n,i,o=t.concat(),a=[],s=o.length;s--;)"string"==typeof(n=o.shift())?a.push(e(n)[0]):Array.isArray(n)&&"string"==typeof n[0]&&a.push([(i=e(n[0],n[1],n[2]))[0],i[1],i[2]]);r=a}return r}var k=!1,C=!0;if("string"==typeof t)k=!0;else if(Array.isArray(t)){var E=t.concat();s=[];for(var F,O=E.length;O--;)("string"!=typeof(F=E.shift())||Array.isArray(F)&&"string"!=typeof F[0])&&(C=!1);k=C}if(!1===k)throw Error('Type of text must be string or Array. "'+t+'" is not recognized.');"string"==typeof t&&(t=t.match(/[\r?\n]/)?t.split(/\r\n|\r|\n/g):[t]);var T=tr/m.internal.scaleFactor,M=T*(_-1);switch(n.baseline){case"bottom":r-=M;break;case"top":r+=T-M;break;case"hanging":r+=T-2*M;break;case"middle":r+=T/2-M}if((c=n.maxWidth||0)>0&&("string"==typeof t?t=m.splitTextToSize(t,c):"[object Array]"===Object.prototype.toString.call(t)&&(t=t.reduce(function(t,e){return t.concat(m.splitTextToSize(e,c))},[]))),a={text:t,x:e,y:r,options:n,mutex:{pdfEscape:ev,activeFontKey:tf,fonts:tb,activeFontSize:tr}},tI.publish("preProcessText",a),t=a.text,h=(n=a.options).angle,g instanceof tB==!1&&h&&"number"==typeof h){h*=Math.PI/180,0===n.rotationDirection&&(h=-h),w===y.ADVANCED&&(h=-h);var j=Math.cos(h),B=Math.sin(h);g=new tB(j,B,-B,j,0,0)}else h&&h instanceof tB&&(g=h);w!==y.ADVANCED||g||(g=tD),void 0!==(l=n.charSpace||rn)&&(v+=L(I(l))+" Tc\n",this.setCharSpace(this.getCharSpace()||0)),void 0!==(p=n.horizontalScale)&&(v+=L(100*p)+" Tz\n"),n.lang;var R=-1,D=void 0!==n.renderingMode?n.renderingMode:n.stroke,q=m.internal.getCurrentPageInfo().pageContext;switch(D){case 0:case!1:case"fill":R=0;break;case 1:case!0:case"stroke":R=1;break;case 2:case"fillThenStroke":R=2;break;case 3:case"invisible":R=3;break;case 4:case"fillAndAddForClipping":R=4;break;case 5:case"strokeAndAddPathForClipping":R=5;break;case 6:case"fillThenStrokeAndAddToPathForClipping":R=6;break;case 7:case"addToPathForClipping":R=7}var U=void 0!==q.usedRenderingMode?q.usedRenderingMode:-1;-1!==R?v+=R+" Tr\n":-1!==U&&(v+="0 Tr\n"),-1!==R&&(q.usedRenderingMode=R),u=n.align||"left";var z,H=tr*_,W=m.internal.pageSize.getWidth(),V=tb[tf];l=n.charSpace||rn,c=n.maxWidth||0,f=Object.assign({autoencode:!0,noBOM:!0},n.flags);var G=[],Y=function(t){return m.getStringUnitWidth(t,{font:V,charSpace:l,fontSize:tr,doKerning:!1})*tr/A};if("[object Array]"===Object.prototype.toString.call(t)){s=S(t),"left"!==u&&(z=s.map(Y));var J,K,X=0;if("right"===u){e-=z[0],t=[],O=s.length;for(var Z=0;Z<O;Z++)0===Z?(K=e4(e),J=e6(r)):(K=I(X-z[Z]),J=-H),t.push([s[Z],K,J]),X=z[Z]}else if("center"===u){e-=z[0]/2,t=[],O=s.length;for(var Q=0;Q<O;Q++)0===Q?(K=e4(e),J=e6(r)):(K=I((X-z[Q])/2),J=-H),t.push([s[Q],K,J]),X=z[Q]}else if("left"===u){t=[],O=s.length;for(var tt=0;tt<O;tt++)t.push(s[tt])}else if("justify"===u&&"Identity-H"===V.encoding){t=[],O=s.length,c=0!==c?c:W;for(var te=0,tn=0;tn<O;tn++)if(J=0===tn?e6(r):-H,K=0===tn?e4(e):te,tn<O-1){var ti=I((c-z[tn])/(s[tn].split(" ").length-1)),ta=s[tn].split(" ");t.push([ta[0]+" ",K,J]),te=0;for(var ts=1;ts<ta.length;ts++){var th=(Y(ta[ts-1]+" "+ta[ts])-Y(ta[ts]))*A+ti;ts==ta.length-1?t.push([ta[ts],th,0]):t.push([ta[ts]+" ",th,0]),te-=th}}else t.push([s[tn],K,J]);t.push(["",te,0])}else{if("justify"!==u)throw Error('Unrecognized alignment option, use "left", "center", "right" or "justify".');for(t=[],O=s.length,c=0!==c?c:W,tn=0;tn<O;tn++)J=0===tn?e6(r):-H,K=0===tn?e4(e):0,tn<O-1?G.push(L(I((c-z[tn])/(s[tn].split(" ").length-1)))):G.push(0),t.push([s[tn],K,J])}}!0===("boolean"==typeof n.R2L?n.R2L:to)&&(t=P(t,function(t,e,r){return[t.split("").reverse().join(""),e,r]})),a={text:t,x:e,y:r,options:n,mutex:{pdfEscape:ev,activeFontKey:tf,fonts:tb,activeFontSize:tr}},tI.publish("postProcessText",a),t=a.text,x=a.mutex.isHex||!1;var tu=tb[tf].encoding;"WinAnsiEncoding"!==tu&&"StandardEncoding"!==tu||(t=P(t,function(t,e,r){var i;return[ev(t.split("	").join(Array(n.TabLen||9).join(" ")),f),e,r]})),s=S(t),t=[];for(var tl,tc,td,tp=+!!Array.isArray(s[0]),tg="",tm=function(t,e,r){var i="";return r instanceof tB?(r="number"==typeof n.angle?tR(r,new tB(1,0,0,1,t,e)):tR(new tB(1,0,0,1,t,e),r),w===y.ADVANCED&&(r=tR(new tB(1,0,0,-1,0,0),r)),i=r.join(" ")+" Tm\n"):i=L(t)+" "+L(e)+" Td\n",i},tv=0;tv<s.length;tv++){switch(tg="",tp){case 1:td=(x?"<":"(")+s[tv][0]+(x?">":")"),tl=parseFloat(s[tv][1]),tc=parseFloat(s[tv][2]);break;case 0:td=(x?"<":"(")+s[tv]+(x?">":")"),tl=e4(e),tc=e6(r)}void 0!==G&&void 0!==G[tv]&&(tg=G[tv]+" Tw\n"),0===tv?t.push(tg+tm(tl,tc,g)+td):0===tp?t.push(tg+td):1===tp&&t.push(tg+tm(tl,tc,g)+td)}t=(0===tp?t.join(" Tj\nT* "):t.join(" Tj\n"))+" Tj\n";var ty="BT\n/";return ty+=tf+" "+tr+" Tf\n",ty+=L(tr*_)+" TL\n",ty+=re+"\n",ty+=v,ty+=t,$(ty+="ET"),d[tf]=!0,m};var eR=p.__private__.clip=p.clip=function(t){return $("evenodd"===t?"W*":"W"),this};p.clipEvenOdd=function(){return eR("evenodd")},p.__private__.discardPath=p.discardPath=function(){return $("n"),this};var eD=p.__private__.isValidStyle=function(t){var e=!1;return -1!==[void 0,null,"S","D","F","DF","FD","f","f*","B","B*","n"].indexOf(t)&&(e=!0),e};p.__private__.setDefaultPathOperation=p.setDefaultPathOperation=function(t){return eD(t)&&(l=t),this};var eq=p.__private__.getStyle=p.getStyle=function(t){var e=l;switch(t){case"D":case"S":e="S";break;case"F":e="f";break;case"FD":case"DF":e="B";break;case"f":case"f*":case"B":case"B*":e=t}return e},eU=p.close=function(){return $("h"),this};p.stroke=function(){return $("S"),this},p.fill=function(t){return ez("f",t),this},p.fillEvenOdd=function(t){return ez("f*",t),this},p.fillStroke=function(t){return ez("B",t),this},p.fillStrokeEvenOdd=function(t){return ez("B*",t),this};var ez=function(t,e){"object"===i()(e)?eV(e,t):$(t)},eH=function(t){null===t||w===y.ADVANCED&&void 0===t||$(t=eq(t))};function eW(t,e,r,n,i){var o=new tQ(e||this.boundingBox,r||this.xStep,n||this.yStep,this.gState,i||this.matrix);return o.stream=this.stream,tq(t+"$$"+this.cloneIndex+++"$$",o),o}var eV=function(t,e){var r=tx[t.key],n=tw[r];if(n instanceof t$)$("q"),$(eG(e)),n.gState&&p.setGState(n.gState),$(t.matrix.toString()+" cm"),$("/"+r+" sh"),$("Q");else if(n instanceof tQ){var i=new tB(1,0,0,-1,0,rm());t.matrix&&(i=i.multiply(t.matrix||tD),r=eW.call(n,t.key,t.boundingBox,t.xStep,t.yStep,i).id),$("q"),$("/Pattern cs"),$("/"+r+" scn"),n.gState&&p.setGState(n.gState),$(e),$("Q")}},eG=function(t){switch(t){case"f":case"F":case"n":return"W n";case"f*":return"W* n";case"B":case"S":return"W S";case"B*":return"W* S"}},eY=p.moveTo=function(t,e){return $(L(I(t))+" "+L(C(e))+" m"),this},eJ=p.lineTo=function(t,e){return $(L(I(t))+" "+L(C(e))+" l"),this},eK=p.curveTo=function(t,e,r,n,i,o){return $([L(I(t)),L(C(e)),L(I(r)),L(C(n)),L(I(i)),L(C(o)),"c"].join(" ")),this};p.__private__.line=p.line=function(t,e,r,n,i){if(isNaN(t)||isNaN(e)||isNaN(r)||isNaN(n)||!eD(i))throw Error("Invalid arguments passed to jsPDF.line");return w===y.COMPAT?this.lines([[r-t,n-e]],t,e,[1,1],i||"S"):this.lines([[r-t,n-e]],t,e,[1,1]).stroke()},p.__private__.lines=p.lines=function(t,e,r,n,i,o){var a,s,h,u,l,c,f,d,p,g,m,b;if("number"==typeof t&&(b=r,r=e,e=t,t=b),n=n||[1,1],o=o||!1,isNaN(e)||isNaN(r)||!Array.isArray(t)||!Array.isArray(n)||!eD(i)||"boolean"!=typeof o)throw Error("Invalid arguments passed to jsPDF.lines");for(eY(e,r),a=n[0],s=n[1],u=t.length,g=e,m=r,h=0;h<u;h++)2===(l=t[h]).length?eJ(g=l[0]*a+g,m=l[1]*s+m):(c=l[0]*a+g,f=l[1]*s+m,d=l[2]*a+g,p=l[3]*s+m,eK(c,f,d,p,g=l[4]*a+g,m=l[5]*s+m));return o&&eU(),eH(i),this},p.path=function(t){for(var e=0;e<t.length;e++){var r=t[e],n=r.c;switch(r.op){case"m":eY(n[0],n[1]);break;case"l":eJ(n[0],n[1]);break;case"c":eK.apply(this,n);break;case"h":eU()}}return this},p.__private__.rect=p.rect=function(t,e,r,n,i){if(isNaN(t)||isNaN(e)||isNaN(r)||isNaN(n)||!eD(i))throw Error("Invalid arguments passed to jsPDF.rect");return w===y.COMPAT&&(n=-n),$([L(I(t)),L(C(e)),L(I(r)),L(I(n)),"re"].join(" ")),eH(i),this},p.__private__.triangle=p.triangle=function(t,e,r,n,i,o,a){if(isNaN(t)||isNaN(e)||isNaN(r)||isNaN(n)||isNaN(i)||isNaN(o)||!eD(a))throw Error("Invalid arguments passed to jsPDF.triangle");return this.lines([[r-t,n-e],[i-r,o-n],[t-i,e-o]],t,e,[1,1],a,!0),this},p.__private__.roundedRect=p.roundedRect=function(t,e,r,n,i,o,a){if(isNaN(t)||isNaN(e)||isNaN(r)||isNaN(n)||isNaN(i)||isNaN(o)||!eD(a))throw Error("Invalid arguments passed to jsPDF.roundedRect");var s=4/3*(Math.SQRT2-1);return i=Math.min(i,.5*r),o=Math.min(o,.5*n),this.lines([[r-2*i,0],[i*s,0,i,o-o*s,i,o],[0,n-2*o],[0,o*s,-i*s,o,-i,o],[2*i-r,0],[-i*s,0,-i,-o*s,-i,-o],[0,2*o-n],[0,-o*s,i*s,-o,i,-o]],t+i,e,[1,1],a,!0),this},p.__private__.ellipse=p.ellipse=function(t,e,r,n,i){if(isNaN(t)||isNaN(e)||isNaN(r)||isNaN(n)||!eD(i))throw Error("Invalid arguments passed to jsPDF.ellipse");var o=4/3*(Math.SQRT2-1)*r,a=4/3*(Math.SQRT2-1)*n;return eY(t+r,e),eK(t+r,e-a,t+o,e-n,t,e-n),eK(t-o,e-n,t-r,e-a,t-r,e),eK(t-r,e+a,t-o,e+n,t,e+n),eK(t+o,e+n,t+r,e+a,t+r,e),eH(i),this},p.__private__.circle=p.circle=function(t,e,r,n){if(isNaN(t)||isNaN(e)||isNaN(r)||!eD(n))throw Error("Invalid arguments passed to jsPDF.circle");return this.ellipse(t,e,r,r,n)},p.setFont=function(t,e,r){return r&&(e=A(e,r)),tf=eL(t,e,{disableWarning:!1}),this};var eX=p.__private__.getFont=p.getFont=function(){return tb[eL.apply(p,arguments)]};p.__private__.getFontList=p.getFontList=function(){var t,e,r={};for(t in tv)if(tv.hasOwnProperty(t))for(e in r[t]=[],tv[t])tv[t].hasOwnProperty(e)&&r[t].push(e);return r},p.addFont=function(t,e,r,n,i){var o=["StandardEncoding","MacRomanEncoding","Identity-H","WinAnsiEncoding"];return arguments[3]&&-1!==o.indexOf(arguments[3])?i=arguments[3]:arguments[3]&&-1==o.indexOf(arguments[3])&&(r=A(r,n)),i=i||"Identity-H",em.call(this,t,e,r,i)};var eZ,e$,eQ=t.lineWidth||.200025,e1=p.__private__.getLineWidth=p.getLineWidth=function(){return eQ},e2=p.__private__.setLineWidth=p.setLineWidth=function(t){return eQ=t,$(L(I(t))+" w"),this};p.__private__.setLineDash=t1.API.setLineDash=t1.API.setLineDashPattern=function(t,e){if(t=t||[],isNaN(e=e||0)||!Array.isArray(t))throw Error("Invalid arguments passed to jsPDF.setLineDash");return $("["+(t=t.map(function(t){return L(I(t))}).join(" "))+"] "+(e=L(I(e)))+" d"),this};var e0=p.__private__.getLineHeight=p.getLineHeight=function(){return tr*e$};p.__private__.getLineHeight=p.getLineHeight=function(){return tr*e$};var e5=p.__private__.setLineHeightFactor=p.setLineHeightFactor=function(t){return"number"==typeof(t=t||1.15)&&(e$=t),this},e3=p.__private__.getLineHeightFactor=p.getLineHeightFactor=function(){return e$};e5(t.lineHeight);var e4=p.__private__.getHorizontalCoordinate=function(t){return I(t)},e6=p.__private__.getVerticalCoordinate=function(t){return w===y.ADVANCED?t:tk[D].mediaBox.topRightY-tk[D].mediaBox.bottomLeftY-I(t)},e8=p.__private__.getHorizontalCoordinateString=p.getHorizontalCoordinateString=function(t){return L(e4(t))},e7=p.__private__.getVerticalCoordinateString=p.getVerticalCoordinateString=function(t){return L(e6(t))},e9=t.strokeColor||"0 G";p.__private__.getStrokeColor=p.getDrawColor=function(){return tZ(e9)},p.__private__.setStrokeColor=p.setDrawColor=function(t,e,r,n){return $(e9=t2({ch1:t,ch2:e,ch3:r,ch4:n,pdfColorType:"draw",precision:2})),this};var rt=t.fillColor||"0 g";p.__private__.getFillColor=p.getFillColor=function(){return tZ(rt)},p.__private__.setFillColor=p.setFillColor=function(t,e,r,n){return $(rt=t2({ch1:t,ch2:e,ch3:r,ch4:n,pdfColorType:"fill",precision:2})),this};var re=t.textColor||"0 g",rr=p.__private__.getTextColor=p.getTextColor=function(){return tZ(re)};p.__private__.setTextColor=p.setTextColor=function(t,e,r,n){return re=t2({ch1:t,ch2:e,ch3:r,ch4:n,pdfColorType:"text",precision:3}),this};var rn=t.charSpace,ri=p.__private__.getCharSpace=p.getCharSpace=function(){return parseFloat(rn||0)};p.__private__.setCharSpace=p.setCharSpace=function(t){if(isNaN(t))throw Error("Invalid argument passed to jsPDF.setCharSpace");return rn=t,this};var ro=0;p.CapJoinStyles={0:0,butt:0,but:0,miter:0,1:1,round:1,rounded:1,circle:1,2:2,projecting:2,project:2,square:2,bevel:2},p.__private__.setLineCap=p.setLineCap=function(t){var e=p.CapJoinStyles[t];if(void 0===e)throw Error("Line cap style of '"+t+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return ro=e,$(e+" J"),this};var ra=0;p.__private__.setLineJoin=p.setLineJoin=function(t){var e=p.CapJoinStyles[t];if(void 0===e)throw Error("Line join style of '"+t+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return ra=e,$(e+" j"),this},p.__private__.setLineMiterLimit=p.__private__.setMiterLimit=p.setLineMiterLimit=p.setMiterLimit=function(t){if(isNaN(t=t||0))throw Error("Invalid argument passed to jsPDF.setLineMiterLimit");return $(L(I(t))+" M"),this},p.GState=tX,p.setGState=function(t){(t="string"==typeof t?t_[tL[t]]:rs(null,t)).equals(tS)||($("/"+t.id+" gs"),tS=t)};var rs=function(t,e){if(!t||!tL[t]){var r=!1;for(var n in t_)if(t_.hasOwnProperty(n)&&t_[n].equals(e)){r=!0;break}if(r)e=t_[n];else{var i="GS"+(Object.keys(t_).length+1).toString(10);t_[i]=e,e.id=i}return t&&(tL[t]=e.id),tI.publish("addGState",e),e}};p.addGState=function(t,e){return rs(t,e),this},p.saveGraphicsState=function(){return $("q"),ty.push({key:tf,size:tr,color:re}),this},p.restoreGraphicsState=function(){$("Q");var t=ty.pop();return tf=t.key,tr=t.size,re=t.color,tS=null,this},p.setCurrentTransformationMatrix=function(t){return $(t.toString()+" cm"),this},p.comment=function(t){return $("#"+t),this};var rh=function(t,e){var r=t||0;Object.defineProperty(this,"x",{enumerable:!0,get:function(){return r},set:function(t){isNaN(t)||(r=parseFloat(t))}});var n=e||0;Object.defineProperty(this,"y",{enumerable:!0,get:function(){return n},set:function(t){isNaN(t)||(n=parseFloat(t))}});var i="pt";return Object.defineProperty(this,"type",{enumerable:!0,get:function(){return i},set:function(t){i=t.toString()}}),this},ru=function(t,e,r,n){rh.call(this,t,e),this.type="rect";var i=r||0;Object.defineProperty(this,"w",{enumerable:!0,get:function(){return i},set:function(t){isNaN(t)||(i=parseFloat(t))}});var o=n||0;return Object.defineProperty(this,"h",{enumerable:!0,get:function(){return o},set:function(t){isNaN(t)||(o=parseFloat(t))}}),this},rl=function(){this.page=tP,this.currentPage=D,this.pages=Y.slice(0),this.pagesContext=tk.slice(0),this.x=tp,this.y=tg,this.matrix=tm,this.width=rp(D),this.height=rm(D),this.outputDestination=K,this.id="",this.objectNumber=-1};rl.prototype.restore=function(){tP=this.page,D=this.currentPage,tk=this.pagesContext,Y=this.pages,tp=this.x,tg=this.y,tm=this.matrix,rg(D,this.width),rb(D,this.height),K=this.outputDestination};var rc=function(t,e,r,n,i){tj.push(new rl),tP=D=0,Y=[],tp=t,tg=e,tm=i,ey([r,n])},rf=function(t){if(tM[t])tj.pop().restore();else{var e=new rl,r="Xo"+(Object.keys(tT).length+1).toString(10);e.id=r,tM[t]=r,tT[r]=e,tI.publish("addFormObject",e),tj.pop().restore()}};for(var rd in p.beginFormObject=function(t,e,r,n,i){return rc(t,e,r,n,i),this},p.endFormObject=function(t){return rf(t),this},p.doFormObject=function(t,e){var r=tT[tM[t]];return $("q"),$(e.toString()+" cm"),$("/"+r.id+" Do"),$("Q"),this},p.getFormObject=function(t){var e=tT[tM[t]];return{x:e.x,y:e.y,width:e.width,height:e.height,matrix:e.matrix}},p.save=function(t,e){return t=t||"generated.pdf",(e=e||{}).returnPromise=e.returnPromise||!1,!1===e.returnPromise?(tE(eE(eC()),t),"function"==typeof tE.unload&&tA.setTimeout&&setTimeout(tE.unload,911),this):new Promise(function(e,r){try{var n=tE(eE(eC()),t);"function"==typeof tE.unload&&tA.setTimeout&&setTimeout(tE.unload,911),e(n)}catch(t){r(t.message)}})},t1.API)t1.API.hasOwnProperty(rd)&&("events"===rd&&t1.API.events.length?function(t,e){var r,n,i;for(i=e.length-1;-1!==i;i--)r=e[i][0],n=e[i][1],t.subscribe.apply(t,[r].concat("function"==typeof n?[n]:n))}(tI,t1.API.events):p[rd]=t1.API[rd]);var rp=p.getPageWidth=function(t){return(tk[t=t||D].mediaBox.topRightX-tk[t].mediaBox.bottomLeftX)/td},rg=p.setPageWidth=function(t,e){tk[t].mediaBox.topRightX=e*td+tk[t].mediaBox.bottomLeftX},rm=p.getPageHeight=function(t){return(tk[t=t||D].mediaBox.topRightY-tk[t].mediaBox.bottomLeftY)/td},rb=p.setPageHeight=function(t,e){tk[t].mediaBox.topRightY=e*td+tk[t].mediaBox.bottomLeftY};return p.internal={pdfEscape:ev,getStyle:eq,getFont:eX,getFontSize:ti,getCharSpace:ri,getTextColor:rr,getLineHeight:e0,getLineHeightFactor:e3,getLineWidth:e1,write:Q,getHorizontalCoordinate:e4,getVerticalCoordinate:e6,getCoordinateString:e8,getVerticalCoordinateString:e7,collections:{},newObject:tU,newAdditionalObject:tW,newObjectDeferred:tz,newObjectDeferredBegin:tH,getFilters:t0,putStream:t5,events:tI,scaleFactor:td,pageSize:{getWidth:function(){return rp(D)},setWidth:function(t){rg(D,t)},getHeight:function(){return rm(D)},setHeight:function(t){rb(D,t)}},encryptionOptions:c,encryption:eT,getEncryptor:function(t){return null!==c?eT.encryptor(t,0):function(t){return t}},output:eF,getNumberOfPages:eA,pages:Y,out:$,f2:P,f3:k,getPageInfo:eM,getPageInfoByObjId:ej,getCurrentPageInfo:eB,getPDFVersion:m,Point:rh,Rectangle:ru,Matrix:tB,hasHotfix:eO},Object.defineProperty(p.internal.pageSize,"width",{get:function(){return rp(D)},set:function(t){rg(D,t)},enumerable:!0,configurable:!0}),Object.defineProperty(p.internal.pageSize,"height",{get:function(){return rm(D)},set:function(t){rb(D,t)},enumerable:!0,configurable:!0}),(function(t){for(var e=0,r=te.length;e<r;e++){var n=em.call(this,t[e][0],t[e][1],t[e][2],te[e][3],!0);!1===f&&(d[n]=!0);var i=t[e][0].split("-");eg({id:n,fontName:i[0],fontStyle:i[1]||""})}tI.publish("addFonts",{fonts:tb,dictionary:tv})}).call(p,te),tf="F1",ew(o,r),tI.publish("initialized"),p}tY.prototype.lsbFirstWord=function(t){return String.fromCharCode((0|t)&255,t>>8&255,t>>16&255,t>>24&255)},tY.prototype.toHexString=function(t){return t.split("").map(function(t){return("0"+(255&t.charCodeAt(0)).toString(16)).slice(-2)}).join("")},tY.prototype.hexToBytes=function(t){for(var e=[],r=0;r<t.length;r+=2)e.push(String.fromCharCode(parseInt(t.substr(r,2),16)));return e.join("")},tY.prototype.processOwnerPassword=function(t,e){return tV(tz(e).substr(0,5),t)},tY.prototype.encryptor=function(t,e){var r=tz(this.encryptionKey+String.fromCharCode(255&t,t>>8&255,t>>16&255,255&e,e>>8&255)).substr(0,10);return function(t){return tV(r,t)}},tX.prototype.equals=function(t){var e,r="id,objectNumber,equals";if(!t||i()(t)!==i()(this))return!1;var n=0;for(e in this)if(!(r.indexOf(e)>=0)){if(this.hasOwnProperty(e)&&!t.hasOwnProperty(e)||this[e]!==t[e])return!1;n++}for(e in t)t.hasOwnProperty(e)&&0>r.indexOf(e)&&n--;return 0===n},t1.API={events:[]},t1.version="3.0.1";var t2=t1.API,t0=1,t5=function(t){return t.replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},t3=function(t){return t.replace(/\\\\/g,"\\").replace(/\\\(/g,"(").replace(/\\\)/g,")")},t4=function(t){return t.toFixed(2)},t6=function(t){return t.toFixed(5)};t2.__acroform__={};var t8=function(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t},t7=function(t){return t*t0},t9=function(t){var e=new eb,r=eE.internal.getHeight(t)||0;return e.BBox=[0,0,Number(t4(eE.internal.getWidth(t)||0)),Number(t4(r))],e},et=t2.__acroform__.setBit=function(t,e){if(e=e||0,isNaN(t=t||0)||isNaN(e))throw Error("Invalid arguments passed to jsPDF.API.__acroform__.setBit");return t|1<<e},ee=t2.__acroform__.clearBit=function(t,e){if(e=e||0,isNaN(t=t||0)||isNaN(e))throw Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBit");return t&~(1<<e)},er=t2.__acroform__.getBit=function(t,e){if(isNaN(t)||isNaN(e))throw Error("Invalid arguments passed to jsPDF.API.__acroform__.getBit");return+(0!=(t&1<<e))},en=t2.__acroform__.getBitForPdf=function(t,e){if(isNaN(t)||isNaN(e))throw Error("Invalid arguments passed to jsPDF.API.__acroform__.getBitForPdf");return er(t,e-1)},ei=t2.__acroform__.setBitForPdf=function(t,e){if(isNaN(t)||isNaN(e))throw Error("Invalid arguments passed to jsPDF.API.__acroform__.setBitForPdf");return et(t,e-1)},eo=t2.__acroform__.clearBitForPdf=function(t,e){if(isNaN(t)||isNaN(e))throw Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBitForPdf");return ee(t,e-1)},ea=t2.__acroform__.calculateCoordinates=function(t,e){var r=e.internal.getHorizontalCoordinate,n=e.internal.getVerticalCoordinate,i=t[0],o=t[1],a=t[2],s=t[3],h={};return h.lowerLeft_X=r(i)||0,h.lowerLeft_Y=n(o+s)||0,h.upperRight_X=r(i+a)||0,h.upperRight_Y=n(o)||0,[Number(t4(h.lowerLeft_X)),Number(t4(h.lowerLeft_Y)),Number(t4(h.upperRight_X)),Number(t4(h.upperRight_Y))]},es=function(t){if(t.appearanceStreamContent)return t.appearanceStreamContent;if(t.V||t.DV){var e=[],r=t._V||t.DV,n=eh(t,r),i=t.scope.internal.getFont(t.fontName,t.fontStyle).id;e.push("/Tx BMC"),e.push("q"),e.push("BT"),e.push(t.scope.__private__.encodeColorString(t.color)),e.push("/"+i+" "+t4(n.fontSize)+" Tf"),e.push("1 0 0 1 0 0 Tm"),e.push(n.text),e.push("ET"),e.push("Q"),e.push("EMC");var o=t9(t);return o.scope=t.scope,o.stream=e.join("\n"),o}},eh=function(t,e){var r=0===t.fontSize?t.maxFontSize:t.fontSize,n={text:"",fontSize:""},i=(e=")"==(e="("==e.substr(0,1)?e.substr(1):e).substr(e.length-1)?e.substr(0,e.length-1):e).split(" ");i=t.multiline?i.map(function(t){return t.split("\n")}):i.map(function(t){return[t]});var o=r,a=eE.internal.getHeight(t)||0;a=a<0?-a:a;var s=eE.internal.getWidth(t)||0;s=s<0?-s:s,o++;t:for(;o>0;){e="";var h,u,l=eu("3",t,--o).height,c=t.multiline?a-o:(a-l)/2,f=c+=2,d=0,p=0,g=0;if(o<=0){e="(...) Tj\n",e+="% Width of Text: "+eu(e,t,o=12).width+", FieldWidth:"+s+"\n";break}for(var m="",b=0,v=0;v<i.length;v++)if(i.hasOwnProperty(v)){var y=!1;if(1!==i[v].length&&g!==i[v].length-1){if((l+2)*(b+2)+2>a)continue t;m+=i[v][g],y=!0,p=v,v--}else{m=" "==(m+=i[v][g]+" ").substr(m.length-1)?m.substr(0,m.length-1):m;var w,x,_=parseInt(v),A=(w=m,x=o,_+1<i.length&&eu(w+" "+i[_+1][0],t,x).width<=s-4),L=v>=i.length-1;if(A&&!L){m+=" ",g=0;continue}if(A||L){if(L)p=_;else if(t.multiline&&(l+2)*(b+2)+2>a)continue t}else{if(!t.multiline||(l+2)*(b+2)+2>a)continue t;p=_}}for(var N="",S=d;S<=p;S++){var P=i[S];if(t.multiline){if(S===p){N+=P[g]+" ",g=(g+1)%P.length;continue}if(S===d){N+=P[P.length-1]+" ";continue}}N+=P[0]+" "}switch(u=eu(N=" "==N.substr(N.length-1)?N.substr(0,N.length-1):N,t,o).width,t.textAlign){case"right":h=s-u-2;break;case"center":h=(s-u)/2;break;default:h=2}e+=t4(h)+" "+t4(f)+" Td\n",e+="("+t5(N)+") Tj\n",e+=-t4(h)+" 0 Td\n",f=-(o+2),u=0,d=y?p:p+1,b++,m=""}break}return n.text=e,n.fontSize=o,n},eu=function(t,e,r){var n=e.scope.internal.getFont(e.fontName,e.fontStyle),i=e.scope.getStringUnitWidth(t,{font:n,fontSize:parseFloat(r),charSpace:0})*parseFloat(r);return{height:e.scope.getStringUnitWidth("3",{font:n,fontSize:parseFloat(r),charSpace:0})*parseFloat(r)*1.5,width:i}},el={fields:[],xForms:[],acroFormDictionaryRoot:null,printedOut:!1,internal:null,isInitialized:!1},ec=function(t,e){var r={type:"reference",object:t};void 0===e.internal.getPageInfo(t.page).pageContext.annotations.find(function(t){return t.type===r.type&&t.object===r.object})&&e.internal.getPageInfo(t.page).pageContext.annotations.push(r)},ef=function(t,e){for(var r in t)if(t.hasOwnProperty(r)){var n=t[r];e.internal.newObjectDeferredBegin(n.objId,!0),"object"===i()(n)&&"function"==typeof n.putStream&&n.putStream(),delete t[r]}},ed=function(t,e){if(e.scope=t,void 0!==t.internal&&(void 0===t.internal.acroformPlugin||!1===t.internal.acroformPlugin.isInitialized)){if(ey.FieldNum=0,t.internal.acroformPlugin=JSON.parse(JSON.stringify(el)),t.internal.acroformPlugin.acroFormDictionaryRoot)throw Error("Exception while creating AcroformDictionary");t0=t.internal.scaleFactor,t.internal.acroformPlugin.acroFormDictionaryRoot=new ev,t.internal.acroformPlugin.acroFormDictionaryRoot.scope=t,t.internal.acroformPlugin.acroFormDictionaryRoot._eventID=t.internal.events.subscribe("postPutResources",function(){t.internal.events.unsubscribe(t.internal.acroformPlugin.acroFormDictionaryRoot._eventID),delete t.internal.acroformPlugin.acroFormDictionaryRoot._eventID,t.internal.acroformPlugin.printedOut=!0}),t.internal.events.subscribe("buildDocument",function(){t.internal.acroformPlugin.acroFormDictionaryRoot.objId=void 0;var e=t.internal.acroformPlugin.acroFormDictionaryRoot.Fields;for(var r in e)if(e.hasOwnProperty(r)){var n=e[r];n.objId=void 0,n.hasAnnotation&&ec(n,t)}}),t.internal.events.subscribe("putCatalog",function(){if(void 0===t.internal.acroformPlugin.acroFormDictionaryRoot)throw Error("putCatalogCallback: Root missing.");t.internal.write("/AcroForm "+t.internal.acroformPlugin.acroFormDictionaryRoot.objId+" 0 R")}),t.internal.events.subscribe("postPutPages",function(e){!function(t,e){var r=!t;for(var n in t||(e.internal.newObjectDeferredBegin(e.internal.acroformPlugin.acroFormDictionaryRoot.objId,!0),e.internal.acroformPlugin.acroFormDictionaryRoot.putStream()),t=t||e.internal.acroformPlugin.acroFormDictionaryRoot.Kids)if(t.hasOwnProperty(n)){var o=t[n],a=[],s=o.Rect;if(o.Rect&&(o.Rect=ea(o.Rect,e)),e.internal.newObjectDeferredBegin(o.objId,!0),o.DA=eE.createDefaultAppearanceStream(o),"object"===i()(o)&&"function"==typeof o.getKeyValueListForStream&&(a=o.getKeyValueListForStream()),o.Rect=s,o.hasAppearanceStream&&!o.appearanceStreamContent){var h=es(o);a.push({key:"AP",value:"<</N "+h+">>"}),e.internal.acroformPlugin.xForms.push(h)}if(o.appearanceStreamContent){var u="";for(var l in o.appearanceStreamContent)if(o.appearanceStreamContent.hasOwnProperty(l)){var c=o.appearanceStreamContent[l];if(u+="/"+l+" <<",Object.keys(c).length>=1||Array.isArray(c)){for(var n in c)if(c.hasOwnProperty(n)){var f=c[n];"function"==typeof f&&(f=f.call(e,o)),u+="/"+n+" "+f+" ",e.internal.acroformPlugin.xForms.indexOf(f)>=0||e.internal.acroformPlugin.xForms.push(f)}}else"function"==typeof(f=c)&&(f=f.call(e,o)),u+="/"+n+" "+f,e.internal.acroformPlugin.xForms.indexOf(f)>=0||e.internal.acroformPlugin.xForms.push(f);u+=">>"}a.push({key:"AP",value:"<<\n"+u+">>"})}e.internal.putStream({additionalKeyValues:a,objectId:o.objId}),e.internal.out("endobj")}r&&ef(e.internal.acroformPlugin.xForms,e)}(e,t)}),t.internal.acroformPlugin.isInitialized=!0}},ep=t2.__acroform__.arrayToPdfArray=function(t,e,r){var n=function(t){return t};if(Array.isArray(t)){for(var o="[",a=0;a<t.length;a++)switch(0!==a&&(o+=" "),i()(t[a])){case"boolean":case"number":case"object":o+=t[a].toString();break;case"string":"/"!==t[a].substr(0,1)?(void 0!==e&&r&&(n=r.internal.getEncryptor(e)),o+="("+t5(n(t[a].toString()))+")"):o+=t[a].toString()}return o+"]"}throw Error("Invalid argument passed to jsPDF.__acroform__.arrayToPdfArray")},eg=function(t,e,r){var n=function(t){return t};return void 0!==e&&r&&(n=r.internal.getEncryptor(e)),(t=t||"").toString(),t="("+t5(n(t))+")"},em=function(){this._objId=void 0,this._scope=void 0,Object.defineProperty(this,"objId",{get:function(){if(void 0===this._objId){if(void 0===this.scope)return;this._objId=this.scope.internal.newObjectDeferred()}return this._objId},set:function(t){this._objId=t}}),Object.defineProperty(this,"scope",{value:this._scope,writable:!0})};em.prototype.toString=function(){return this.objId+" 0 R"},em.prototype.putStream=function(){var t=this.getKeyValueListForStream();this.scope.internal.putStream({data:this.stream,additionalKeyValues:t,objectId:this.objId}),this.scope.internal.out("endobj")},em.prototype.getKeyValueListForStream=function(){var t=[],e=Object.getOwnPropertyNames(this).filter(function(t){return"content"!=t&&"appearanceStreamContent"!=t&&"scope"!=t&&"objId"!=t&&"_"!=t.substring(0,1)});for(var r in e)if(!1===Object.getOwnPropertyDescriptor(this,e[r]).configurable){var n=e[r],i=this[n];i&&(Array.isArray(i)?t.push({key:n,value:ep(i,this.objId,this.scope)}):i instanceof em?(i.scope=this.scope,t.push({key:n,value:i.objId+" 0 R"})):"function"!=typeof i&&t.push({key:n,value:i}))}return t};var eb=function(){em.call(this),Object.defineProperty(this,"Type",{value:"/XObject",configurable:!1,writable:!0}),Object.defineProperty(this,"Subtype",{value:"/Form",configurable:!1,writable:!0}),Object.defineProperty(this,"FormType",{value:1,configurable:!1,writable:!0});var t,e=[];Object.defineProperty(this,"BBox",{configurable:!1,get:function(){return e},set:function(t){e=t}}),Object.defineProperty(this,"Resources",{value:"2 0 R",configurable:!1,writable:!0}),Object.defineProperty(this,"stream",{enumerable:!1,configurable:!0,set:function(e){t=e.trim()},get:function(){return t||null}})};t8(eb,em);var ev=function(){em.call(this);var t,e=[];Object.defineProperty(this,"Kids",{enumerable:!1,configurable:!0,get:function(){return e.length>0?e:void 0}}),Object.defineProperty(this,"Fields",{enumerable:!1,configurable:!1,get:function(){return e}}),Object.defineProperty(this,"DA",{enumerable:!1,configurable:!1,get:function(){if(t){var e=function(t){return t};return this.scope&&(e=this.scope.internal.getEncryptor(this.objId)),"("+t5(e(t))+")"}},set:function(e){t=e}})};t8(ev,em);var ey=function t(){em.call(this);var e=4;Object.defineProperty(this,"F",{enumerable:!1,configurable:!1,get:function(){return e},set:function(t){if(isNaN(t))throw Error('Invalid value "'+t+'" for attribute F supplied.');e=t}}),Object.defineProperty(this,"showWhenPrinted",{enumerable:!0,configurable:!0,get:function(){return!!en(e,3)},set:function(t){!0==!!t?this.F=ei(e,3):this.F=eo(e,3)}});var r=0;Object.defineProperty(this,"Ff",{enumerable:!1,configurable:!1,get:function(){return r},set:function(t){if(isNaN(t))throw Error('Invalid value "'+t+'" for attribute Ff supplied.');r=t}});var n=[];Object.defineProperty(this,"Rect",{enumerable:!1,configurable:!1,get:function(){if(0!==n.length)return n},set:function(t){n=void 0!==t?t:[]}}),Object.defineProperty(this,"x",{enumerable:!0,configurable:!0,get:function(){return!n||isNaN(n[0])?0:n[0]},set:function(t){n[0]=t}}),Object.defineProperty(this,"y",{enumerable:!0,configurable:!0,get:function(){return!n||isNaN(n[1])?0:n[1]},set:function(t){n[1]=t}}),Object.defineProperty(this,"width",{enumerable:!0,configurable:!0,get:function(){return!n||isNaN(n[2])?0:n[2]},set:function(t){n[2]=t}}),Object.defineProperty(this,"height",{enumerable:!0,configurable:!0,get:function(){return!n||isNaN(n[3])?0:n[3]},set:function(t){n[3]=t}});var i="";Object.defineProperty(this,"FT",{enumerable:!0,configurable:!1,get:function(){return i},set:function(t){switch(t){case"/Btn":case"/Tx":case"/Ch":case"/Sig":i=t;break;default:throw Error('Invalid value "'+t+'" for attribute FT supplied.')}}});var o=null;Object.defineProperty(this,"T",{enumerable:!0,configurable:!1,get:function(){if(!o||o.length<1){if(this instanceof eP)return;o="FieldObject"+t.FieldNum++}var e=function(t){return t};return this.scope&&(e=this.scope.internal.getEncryptor(this.objId)),"("+t5(e(o))+")"},set:function(t){o=t.toString()}}),Object.defineProperty(this,"fieldName",{configurable:!0,enumerable:!0,get:function(){return o},set:function(t){o=t}});var a="helvetica";Object.defineProperty(this,"fontName",{enumerable:!0,configurable:!0,get:function(){return a},set:function(t){a=t}});var s="normal";Object.defineProperty(this,"fontStyle",{enumerable:!0,configurable:!0,get:function(){return s},set:function(t){s=t}});var h=0;Object.defineProperty(this,"fontSize",{enumerable:!0,configurable:!0,get:function(){return h},set:function(t){h=t}});var u=void 0;Object.defineProperty(this,"maxFontSize",{enumerable:!0,configurable:!0,get:function(){return void 0===u?50/t0:u},set:function(t){u=t}});var l="black";Object.defineProperty(this,"color",{enumerable:!0,configurable:!0,get:function(){return l},set:function(t){l=t}});var c="/F1 0 Tf 0 g";Object.defineProperty(this,"DA",{enumerable:!0,configurable:!1,get:function(){if(!(!c||this instanceof eP||this instanceof eI))return eg(c,this.objId,this.scope)},set:function(t){c=t=t.toString()}});var f=null;Object.defineProperty(this,"DV",{enumerable:!1,configurable:!1,get:function(){if(f)return this instanceof eL==!1?eg(f,this.objId,this.scope):f},set:function(t){t=t.toString(),f=this instanceof eL==!1?"("===t.substr(0,1)?t3(t.substr(1,t.length-2)):t3(t):t}}),Object.defineProperty(this,"defaultValue",{enumerable:!0,configurable:!0,get:function(){return this instanceof eL==!0?t3(f.substr(1,f.length-1)):f},set:function(t){t=t.toString(),f=this instanceof eL==!0?"/"+t:t}});var d=null;Object.defineProperty(this,"_V",{enumerable:!1,configurable:!1,get:function(){if(d)return d},set:function(t){this.V=t}}),Object.defineProperty(this,"V",{enumerable:!1,configurable:!1,get:function(){if(d)return this instanceof eL==!1?eg(d,this.objId,this.scope):d},set:function(t){t=t.toString(),d=this instanceof eL==!1?"("===t.substr(0,1)?t3(t.substr(1,t.length-2)):t3(t):t}}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,get:function(){return this instanceof eL==!0?t3(d.substr(1,d.length-1)):d},set:function(t){t=t.toString(),d=this instanceof eL==!0?"/"+t:t}}),Object.defineProperty(this,"hasAnnotation",{enumerable:!0,configurable:!0,get:function(){return this.Rect}}),Object.defineProperty(this,"Type",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Annot":null}}),Object.defineProperty(this,"Subtype",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Widget":null}});var p,g=!1;Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return g},set:function(t){g=t=!!t}}),Object.defineProperty(this,"page",{enumerable:!0,configurable:!0,get:function(){if(p)return p},set:function(t){p=t}}),Object.defineProperty(this,"readOnly",{enumerable:!0,configurable:!0,get:function(){return!!en(this.Ff,1)},set:function(t){!0==!!t?this.Ff=ei(this.Ff,1):this.Ff=eo(this.Ff,1)}}),Object.defineProperty(this,"required",{enumerable:!0,configurable:!0,get:function(){return!!en(this.Ff,2)},set:function(t){!0==!!t?this.Ff=ei(this.Ff,2):this.Ff=eo(this.Ff,2)}}),Object.defineProperty(this,"noExport",{enumerable:!0,configurable:!0,get:function(){return!!en(this.Ff,3)},set:function(t){!0==!!t?this.Ff=ei(this.Ff,3):this.Ff=eo(this.Ff,3)}});var m=null;Object.defineProperty(this,"Q",{enumerable:!0,configurable:!1,get:function(){if(null!==m)return m},set:function(t){if(-1===[0,1,2].indexOf(t))throw Error('Invalid value "'+t+'" for attribute Q supplied.');m=t}}),Object.defineProperty(this,"textAlign",{get:function(){var t;switch(m){case 0:default:t="left";break;case 1:t="center";break;case 2:t="right"}return t},configurable:!0,enumerable:!0,set:function(t){switch(t){case"right":case 2:m=2;break;case"center":case 1:m=1;break;default:m=0}}})};t8(ey,em);var ew=function(){ey.call(this),this.FT="/Ch",this.V="()",this.fontName="zapfdingbats";var t=0;Object.defineProperty(this,"TI",{enumerable:!0,configurable:!1,get:function(){return t},set:function(e){t=e}}),Object.defineProperty(this,"topIndex",{enumerable:!0,configurable:!0,get:function(){return t},set:function(e){t=e}});var e=[];Object.defineProperty(this,"Opt",{enumerable:!0,configurable:!1,get:function(){return ep(e,this.objId,this.scope)},set:function(t){var r;r=[],"string"==typeof t&&(r=function(t,e,r){r||(r=1);for(var n,i=[];n=e.exec(t);)i.push(n[r]);return i}(t,/\((.*?)\)/g)),e=r}}),this.getOptions=function(){return e},this.setOptions=function(t){e=t,this.sort&&e.sort()},this.addOption=function(t){t=(t=t||"").toString(),e.push(t),this.sort&&e.sort()},this.removeOption=function(t,r){for(r=r||!1,t=(t=t||"").toString();-1!==e.indexOf(t)&&(e.splice(e.indexOf(t),1),!1!==r););},Object.defineProperty(this,"combo",{enumerable:!0,configurable:!0,get:function(){return!!en(this.Ff,18)},set:function(t){!0==!!t?this.Ff=ei(this.Ff,18):this.Ff=eo(this.Ff,18)}}),Object.defineProperty(this,"edit",{enumerable:!0,configurable:!0,get:function(){return!!en(this.Ff,19)},set:function(t){!0===this.combo&&(!0==!!t?this.Ff=ei(this.Ff,19):this.Ff=eo(this.Ff,19))}}),Object.defineProperty(this,"sort",{enumerable:!0,configurable:!0,get:function(){return!!en(this.Ff,20)},set:function(t){!0==!!t?(this.Ff=ei(this.Ff,20),e.sort()):this.Ff=eo(this.Ff,20)}}),Object.defineProperty(this,"multiSelect",{enumerable:!0,configurable:!0,get:function(){return!!en(this.Ff,22)},set:function(t){!0==!!t?this.Ff=ei(this.Ff,22):this.Ff=eo(this.Ff,22)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return!!en(this.Ff,23)},set:function(t){!0==!!t?this.Ff=ei(this.Ff,23):this.Ff=eo(this.Ff,23)}}),Object.defineProperty(this,"commitOnSelChange",{enumerable:!0,configurable:!0,get:function(){return!!en(this.Ff,27)},set:function(t){!0==!!t?this.Ff=ei(this.Ff,27):this.Ff=eo(this.Ff,27)}}),this.hasAppearanceStream=!1};t8(ew,ey);var ex=function(){ew.call(this),this.fontName="helvetica",this.combo=!1};t8(ex,ew);var e_=function(){ex.call(this),this.combo=!0};t8(e_,ex);var eA=function(){e_.call(this),this.edit=!0};t8(eA,e_);var eL=function(){ey.call(this),this.FT="/Btn",Object.defineProperty(this,"noToggleToOff",{enumerable:!0,configurable:!0,get:function(){return!!en(this.Ff,15)},set:function(t){!0==!!t?this.Ff=ei(this.Ff,15):this.Ff=eo(this.Ff,15)}}),Object.defineProperty(this,"radio",{enumerable:!0,configurable:!0,get:function(){return!!en(this.Ff,16)},set:function(t){!0==!!t?this.Ff=ei(this.Ff,16):this.Ff=eo(this.Ff,16)}}),Object.defineProperty(this,"pushButton",{enumerable:!0,configurable:!0,get:function(){return!!en(this.Ff,17)},set:function(t){!0==!!t?this.Ff=ei(this.Ff,17):this.Ff=eo(this.Ff,17)}}),Object.defineProperty(this,"radioIsUnison",{enumerable:!0,configurable:!0,get:function(){return!!en(this.Ff,26)},set:function(t){!0==!!t?this.Ff=ei(this.Ff,26):this.Ff=eo(this.Ff,26)}});var t,e={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var t=function(t){return t};if(this.scope&&(t=this.scope.internal.getEncryptor(this.objId)),0!==Object.keys(e).length){var r,n=[];for(r in n.push("<<"),e)n.push("/"+r+" ("+t5(t(e[r]))+")");return n.push(">>"),n.join("\n")}},set:function(t){"object"===i()(t)&&(e=t)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return e.CA||""},set:function(t){"string"==typeof t&&(e.CA=t)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return t},set:function(e){t=e}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return t.substr(1,t.length-1)},set:function(e){t="/"+e}})};t8(eL,ey);var eN=function(){eL.call(this),this.pushButton=!0};t8(eN,eL);var eS=function(){eL.call(this),this.radio=!0,this.pushButton=!1;var t=[];Object.defineProperty(this,"Kids",{enumerable:!0,configurable:!1,get:function(){return t},set:function(e){t=void 0!==e?e:[]}})};t8(eS,eL);var eP=function(){ey.call(this),Object.defineProperty(this,"Parent",{enumerable:!1,configurable:!1,get:function(){return t},set:function(e){t=e}}),Object.defineProperty(this,"optionName",{enumerable:!1,configurable:!0,get:function(){return e},set:function(t){e=t}});var t,e,r,n={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var t=function(t){return t};this.scope&&(t=this.scope.internal.getEncryptor(this.objId));var e,r=[];for(e in r.push("<<"),n)r.push("/"+e+" ("+t5(t(n[e]))+")");return r.push(">>"),r.join("\n")},set:function(t){"object"===i()(t)&&(n=t)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return n.CA||""},set:function(t){"string"==typeof t&&(n.CA=t)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return r},set:function(t){r=t}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return r.substr(1,r.length-1)},set:function(t){r="/"+t}}),this.caption="l",this.appearanceState="Off",this._AppearanceType=eE.RadioButton.Circle,this.appearanceStreamContent=this._AppearanceType.createAppearanceStream(this.optionName)};t8(eP,ey),eS.prototype.setAppearance=function(t){if(!("createAppearanceStream"in t)||!("getCA"in t))throw Error("Couldn't assign Appearance to RadioButton. Appearance was Invalid!");for(var e in this.Kids)if(this.Kids.hasOwnProperty(e)){var r=this.Kids[e];r.appearanceStreamContent=t.createAppearanceStream(r.optionName),r.caption=t.getCA()}},eS.prototype.createOption=function(t){var e=new eP;return e.Parent=this,e.optionName=t,this.Kids.push(e),eF.call(this.scope,e),e};var ek=function(){eL.call(this),this.fontName="zapfdingbats",this.caption="3",this.appearanceState="On",this.value="On",this.textAlign="center",this.appearanceStreamContent=eE.CheckBox.createAppearanceStream()};t8(ek,eL);var eI=function(){ey.call(this),this.FT="/Tx",Object.defineProperty(this,"multiline",{enumerable:!0,configurable:!0,get:function(){return!!en(this.Ff,13)},set:function(t){!0==!!t?this.Ff=ei(this.Ff,13):this.Ff=eo(this.Ff,13)}}),Object.defineProperty(this,"fileSelect",{enumerable:!0,configurable:!0,get:function(){return!!en(this.Ff,21)},set:function(t){!0==!!t?this.Ff=ei(this.Ff,21):this.Ff=eo(this.Ff,21)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return!!en(this.Ff,23)},set:function(t){!0==!!t?this.Ff=ei(this.Ff,23):this.Ff=eo(this.Ff,23)}}),Object.defineProperty(this,"doNotScroll",{enumerable:!0,configurable:!0,get:function(){return!!en(this.Ff,24)},set:function(t){!0==!!t?this.Ff=ei(this.Ff,24):this.Ff=eo(this.Ff,24)}}),Object.defineProperty(this,"comb",{enumerable:!0,configurable:!0,get:function(){return!!en(this.Ff,25)},set:function(t){!0==!!t?this.Ff=ei(this.Ff,25):this.Ff=eo(this.Ff,25)}}),Object.defineProperty(this,"richText",{enumerable:!0,configurable:!0,get:function(){return!!en(this.Ff,26)},set:function(t){!0==!!t?this.Ff=ei(this.Ff,26):this.Ff=eo(this.Ff,26)}});var t=null;Object.defineProperty(this,"MaxLen",{enumerable:!0,configurable:!1,get:function(){return t},set:function(e){t=e}}),Object.defineProperty(this,"maxLength",{enumerable:!0,configurable:!0,get:function(){return t},set:function(e){Number.isInteger(e)&&(t=e)}}),Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return this.V||this.DV}})};t8(eI,ey);var eC=function(){eI.call(this),Object.defineProperty(this,"password",{enumerable:!0,configurable:!0,get:function(){return!!en(this.Ff,14)},set:function(t){!0==!!t?this.Ff=ei(this.Ff,14):this.Ff=eo(this.Ff,14)}}),this.password=!0};t8(eC,eI);var eE={CheckBox:{createAppearanceStream:function(){return{N:{On:eE.CheckBox.YesNormal},D:{On:eE.CheckBox.YesPushDown,Off:eE.CheckBox.OffPushDown}}},YesPushDown:function(t){var e=t9(t);e.scope=t.scope;var r=[],n=t.scope.internal.getFont(t.fontName,t.fontStyle).id,i=t.scope.__private__.encodeColorString(t.color),o=eh(t,t.caption);return r.push("0.749023 g"),r.push("0 0 "+t4(eE.internal.getWidth(t))+" "+t4(eE.internal.getHeight(t))+" re"),r.push("f"),r.push("BMC"),r.push("q"),r.push("0 0 1 rg"),r.push("/"+n+" "+t4(o.fontSize)+" Tf "+i),r.push("BT"),r.push(o.text),r.push("ET"),r.push("Q"),r.push("EMC"),e.stream=r.join("\n"),e},YesNormal:function(t){var e=t9(t);e.scope=t.scope;var r=t.scope.internal.getFont(t.fontName,t.fontStyle).id,n=t.scope.__private__.encodeColorString(t.color),i=[],o=eE.internal.getHeight(t),a=eE.internal.getWidth(t),s=eh(t,t.caption);return i.push("1 g"),i.push("0 0 "+t4(a)+" "+t4(o)+" re"),i.push("f"),i.push("q"),i.push("0 0 1 rg"),i.push("0 0 "+t4(a-1)+" "+t4(o-1)+" re"),i.push("W"),i.push("n"),i.push("0 g"),i.push("BT"),i.push("/"+r+" "+t4(s.fontSize)+" Tf "+n),i.push(s.text),i.push("ET"),i.push("Q"),e.stream=i.join("\n"),e},OffPushDown:function(t){var e=t9(t);e.scope=t.scope;var r=[];return r.push("0.749023 g"),r.push("0 0 "+t4(eE.internal.getWidth(t))+" "+t4(eE.internal.getHeight(t))+" re"),r.push("f"),e.stream=r.join("\n"),e}},RadioButton:{Circle:{createAppearanceStream:function(t){var e={D:{Off:eE.RadioButton.Circle.OffPushDown},N:{}};return e.N[t]=eE.RadioButton.Circle.YesNormal,e.D[t]=eE.RadioButton.Circle.YesPushDown,e},getCA:function(){return"l"},YesNormal:function(t){var e=t9(t);e.scope=t.scope;var r=[],n=eE.internal.getWidth(t)<=eE.internal.getHeight(t)?eE.internal.getWidth(t)/4:eE.internal.getHeight(t)/4,i=Number(((n=Number((.9*n).toFixed(5)))*eE.internal.Bezier_C).toFixed(5));return r.push("q"),r.push("1 0 0 1 "+t6(eE.internal.getWidth(t)/2)+" "+t6(eE.internal.getHeight(t)/2)+" cm"),r.push(n+" 0 m"),r.push(n+" "+i+" "+i+" "+n+" 0 "+n+" c"),r.push("-"+i+" "+n+" -"+n+" "+i+" -"+n+" 0 c"),r.push("-"+n+" -"+i+" -"+i+" -"+n+" 0 -"+n+" c"),r.push(i+" -"+n+" "+n+" -"+i+" "+n+" 0 c"),r.push("f"),r.push("Q"),e.stream=r.join("\n"),e},YesPushDown:function(t){var e=t9(t);e.scope=t.scope;var r=[],n=eE.internal.getWidth(t)<=eE.internal.getHeight(t)?eE.internal.getWidth(t)/4:eE.internal.getHeight(t)/4,i=Number((2*(n=Number((.9*n).toFixed(5)))).toFixed(5)),o=Number((i*eE.internal.Bezier_C).toFixed(5)),a=Number((n*eE.internal.Bezier_C).toFixed(5));return r.push("0.749023 g"),r.push("q"),r.push("1 0 0 1 "+t6(eE.internal.getWidth(t)/2)+" "+t6(eE.internal.getHeight(t)/2)+" cm"),r.push(i+" 0 m"),r.push(i+" "+o+" "+o+" "+i+" 0 "+i+" c"),r.push("-"+o+" "+i+" -"+i+" "+o+" -"+i+" 0 c"),r.push("-"+i+" -"+o+" -"+o+" -"+i+" 0 -"+i+" c"),r.push(o+" -"+i+" "+i+" -"+o+" "+i+" 0 c"),r.push("f"),r.push("Q"),r.push("0 g"),r.push("q"),r.push("1 0 0 1 "+t6(eE.internal.getWidth(t)/2)+" "+t6(eE.internal.getHeight(t)/2)+" cm"),r.push(n+" 0 m"),r.push(n+" "+a+" "+a+" "+n+" 0 "+n+" c"),r.push("-"+a+" "+n+" -"+n+" "+a+" -"+n+" 0 c"),r.push("-"+n+" -"+a+" -"+a+" -"+n+" 0 -"+n+" c"),r.push(a+" -"+n+" "+n+" -"+a+" "+n+" 0 c"),r.push("f"),r.push("Q"),e.stream=r.join("\n"),e},OffPushDown:function(t){var e=t9(t);e.scope=t.scope;var r=[],n=eE.internal.getWidth(t)<=eE.internal.getHeight(t)?eE.internal.getWidth(t)/4:eE.internal.getHeight(t)/4,i=Number((2*(n=Number((.9*n).toFixed(5)))).toFixed(5)),o=Number((i*eE.internal.Bezier_C).toFixed(5));return r.push("0.749023 g"),r.push("q"),r.push("1 0 0 1 "+t6(eE.internal.getWidth(t)/2)+" "+t6(eE.internal.getHeight(t)/2)+" cm"),r.push(i+" 0 m"),r.push(i+" "+o+" "+o+" "+i+" 0 "+i+" c"),r.push("-"+o+" "+i+" -"+i+" "+o+" -"+i+" 0 c"),r.push("-"+i+" -"+o+" -"+o+" -"+i+" 0 -"+i+" c"),r.push(o+" -"+i+" "+i+" -"+o+" "+i+" 0 c"),r.push("f"),r.push("Q"),e.stream=r.join("\n"),e}},Cross:{createAppearanceStream:function(t){var e={D:{Off:eE.RadioButton.Cross.OffPushDown},N:{}};return e.N[t]=eE.RadioButton.Cross.YesNormal,e.D[t]=eE.RadioButton.Cross.YesPushDown,e},getCA:function(){return"8"},YesNormal:function(t){var e=t9(t);e.scope=t.scope;var r=[],n=eE.internal.calculateCross(t);return r.push("q"),r.push("1 1 "+t4(eE.internal.getWidth(t)-2)+" "+t4(eE.internal.getHeight(t)-2)+" re"),r.push("W"),r.push("n"),r.push(t4(n.x1.x)+" "+t4(n.x1.y)+" m"),r.push(t4(n.x2.x)+" "+t4(n.x2.y)+" l"),r.push(t4(n.x4.x)+" "+t4(n.x4.y)+" m"),r.push(t4(n.x3.x)+" "+t4(n.x3.y)+" l"),r.push("s"),r.push("Q"),e.stream=r.join("\n"),e},YesPushDown:function(t){var e=t9(t);e.scope=t.scope;var r=eE.internal.calculateCross(t),n=[];return n.push("0.749023 g"),n.push("0 0 "+t4(eE.internal.getWidth(t))+" "+t4(eE.internal.getHeight(t))+" re"),n.push("f"),n.push("q"),n.push("1 1 "+t4(eE.internal.getWidth(t)-2)+" "+t4(eE.internal.getHeight(t)-2)+" re"),n.push("W"),n.push("n"),n.push(t4(r.x1.x)+" "+t4(r.x1.y)+" m"),n.push(t4(r.x2.x)+" "+t4(r.x2.y)+" l"),n.push(t4(r.x4.x)+" "+t4(r.x4.y)+" m"),n.push(t4(r.x3.x)+" "+t4(r.x3.y)+" l"),n.push("s"),n.push("Q"),e.stream=n.join("\n"),e},OffPushDown:function(t){var e=t9(t);e.scope=t.scope;var r=[];return r.push("0.749023 g"),r.push("0 0 "+t4(eE.internal.getWidth(t))+" "+t4(eE.internal.getHeight(t))+" re"),r.push("f"),e.stream=r.join("\n"),e}}},createDefaultAppearanceStream:function(t){var e=t.scope.internal.getFont(t.fontName,t.fontStyle).id,r=t.scope.__private__.encodeColorString(t.color);return"/"+e+" "+t.fontSize+" Tf "+r}};eE.internal={Bezier_C:.551915024494,calculateCross:function(t){var e=eE.internal.getWidth(t),r=eE.internal.getHeight(t),n=Math.min(e,r);return{x1:{x:(e-n)/2,y:(r-n)/2+n},x2:{x:(e-n)/2+n,y:(r-n)/2},x3:{x:(e-n)/2,y:(r-n)/2},x4:{x:(e-n)/2+n,y:(r-n)/2+n}}}},eE.internal.getWidth=function(t){var e=0;return"object"===i()(t)&&(e=t7(t.Rect[2])),e},eE.internal.getHeight=function(t){var e=0;return"object"===i()(t)&&(e=t7(t.Rect[3])),e};var eF=t2.addField=function(t){if(ed(this,t),!(t instanceof ey))throw Error("Invalid argument passed to jsPDF.addField.");return t.scope.internal.acroformPlugin.printedOut&&(t.scope.internal.acroformPlugin.printedOut=!1,t.scope.internal.acroformPlugin.acroFormDictionaryRoot=null),t.scope.internal.acroformPlugin.acroFormDictionaryRoot.Fields.push(t),t.page=t.scope.internal.getCurrentPageInfo().pageNumber,this};function eO(t){return t.reduce(function(t,e,r){return t[e]=r,t},{})}t2.AcroFormChoiceField=ew,t2.AcroFormListBox=ex,t2.AcroFormComboBox=e_,t2.AcroFormEditBox=eA,t2.AcroFormButton=eL,t2.AcroFormPushButton=eN,t2.AcroFormRadioButton=eS,t2.AcroFormCheckBox=ek,t2.AcroFormTextField=eI,t2.AcroFormPasswordField=eC,t2.AcroFormAppearance=eE,t2.AcroForm={ChoiceField:ew,ListBox:ex,ComboBox:e_,EditBox:eA,Button:eL,PushButton:eN,RadioButton:eS,CheckBox:ek,TextField:eI,PasswordField:eC,Appearance:eE},t1.AcroForm={ChoiceField:ew,ListBox:ex,ComboBox:e_,EditBox:eA,Button:eL,PushButton:eN,RadioButton:eS,CheckBox:ek,TextField:eI,PasswordField:eC,Appearance:eE},t1.AcroForm,function(t){t.__addimage__={};var e="UNKNOWN",r={PNG:[[137,80,78,71]],TIFF:[[77,77,0,42],[73,73,42,0]],JPEG:[[255,216,255,224,void 0,void 0,74,70,73,70,0],[255,216,255,225,void 0,void 0,69,120,105,102,0,0],[255,216,255,219],[255,216,255,238]],JPEG2000:[[0,0,0,12,106,80,32,32]],GIF87a:[[71,73,70,56,55,97]],GIF89a:[[71,73,70,56,57,97]],WEBP:[[82,73,70,70,void 0,void 0,void 0,void 0,87,69,66,80]],BMP:[[66,77],[66,65],[67,73],[67,80],[73,67],[80,84]]},n=t.__addimage__.getImageFileTypeByImageData=function(t,n){var i,o,a,s,h,u=e;if("RGBA"===(n=n||e)||void 0!==t.data&&t.data instanceof Uint8ClampedArray&&"height"in t&&"width"in t)return"RGBA";if(A(t))for(h in r)for(a=r[h],i=0;i<a.length;i+=1){for(s=!0,o=0;o<a[i].length;o+=1)if(void 0!==a[i][o]&&a[i][o]!==t[o]){s=!1;break}if(!0===s){u=h;break}}else for(h in r)for(a=r[h],i=0;i<a.length;i+=1){for(s=!0,o=0;o<a[i].length;o+=1)if(void 0!==a[i][o]&&a[i][o]!==t.charCodeAt(o)){s=!1;break}if(!0===s){u=h;break}}return u===e&&n!==e&&(u=n),u},o=function t(e){for(var r=this.internal.write,n=this.internal.putStream,i=(0,this.internal.getFilters)();-1!==i.indexOf("FlateEncode");)i.splice(i.indexOf("FlateEncode"),1);e.objectId=this.internal.newObject();var o=[];if(o.push({key:"Type",value:"/XObject"}),o.push({key:"Subtype",value:"/Image"}),o.push({key:"Width",value:e.width}),o.push({key:"Height",value:e.height}),e.colorSpace===b.INDEXED?o.push({key:"ColorSpace",value:"[/Indexed /DeviceRGB "+(e.palette.length/3-1)+" "+("sMask"in e&&void 0!==e.sMask?e.objectId+2:e.objectId+1)+" 0 R]"}):(o.push({key:"ColorSpace",value:"/"+e.colorSpace}),e.colorSpace===b.DEVICE_CMYK&&o.push({key:"Decode",value:"[1 0 1 0 1 0 1 0]"})),o.push({key:"BitsPerComponent",value:e.bitsPerComponent}),"decodeParameters"in e&&void 0!==e.decodeParameters&&o.push({key:"DecodeParms",value:"<<"+e.decodeParameters+">>"}),"transparency"in e&&Array.isArray(e.transparency)){for(var a="",s=0,h=e.transparency.length;s<h;s++)a+=e.transparency[s]+" "+e.transparency[s]+" ";o.push({key:"Mask",value:"["+a+"]"})}void 0!==e.sMask&&o.push({key:"SMask",value:e.objectId+1+" 0 R"});var u=void 0!==e.filter?["/"+e.filter]:void 0;if(n({data:e.data,additionalKeyValues:o,alreadyAppliedFilters:u,objectId:e.objectId}),r("endobj"),"sMask"in e&&void 0!==e.sMask){var l="/Predictor "+e.predictor+" /Colors 1 /BitsPerComponent "+e.bitsPerComponent+" /Columns "+e.width,c={width:e.width,height:e.height,colorSpace:"DeviceGray",bitsPerComponent:e.bitsPerComponent,decodeParameters:l,data:e.sMask};"filter"in e&&(c.filter=e.filter),t.call(this,c)}if(e.colorSpace===b.INDEXED){var f=this.internal.newObject();n({data:N(new Uint8Array(e.palette)),objectId:f}),r("endobj")}},a=function(){var t=this.internal.collections.addImage_images;for(var e in t)o.call(this,t[e])},s=function(){var t,e=this.internal.collections.addImage_images,r=this.internal.write;for(var n in e)r("/I"+(t=e[n]).index,t.objectId,"0","R")},h=function(){this.internal.collections.addImage_images||(this.internal.collections.addImage_images={},this.internal.events.subscribe("putResources",a),this.internal.events.subscribe("putXobjectDict",s))},u=function(){var t=this.internal.collections.addImage_images;return h.call(this),t},l=function(){return Object.keys(this.internal.collections.addImage_images).length},c=function(e){return"function"==typeof t["process"+e.toUpperCase()]},f=function(t){return"object"===i()(t)&&1===t.nodeType},d=function(e,r){if("IMG"===e.nodeName&&e.hasAttribute("src")){var n,i=""+e.getAttribute("src");if(0===i.indexOf("data:image/"))return tI(unescape(i).split("base64,").pop());var o=t.loadFile(i,!0);if(void 0!==o)return o}if("CANVAS"===e.nodeName){if(0===e.width||0===e.height)throw Error("Given canvas must have data. Canvas width: "+e.width+", height: "+e.height);switch(r){case"PNG":n="image/png";break;case"WEBP":n="image/webp";break;default:n="image/jpeg"}return tI(e.toDataURL(n,1).split("base64,").pop())}},p=function(t){var e=this.internal.collections.addImage_images;if(e){for(var r in e)if(t===e[r].alias)return e[r]}},g=function(t,e,r){return t||e||(t=-96,e=-96),t<0&&(t=-1*r.width*72/t/this.internal.scaleFactor),e<0&&(e=-1*r.height*72/e/this.internal.scaleFactor),0===t&&(t=e*r.width/r.height),0===e&&(e=t*r.height/r.width),[t,e]},m=function(t,e,r,n,i,o){var a=g.call(this,r,n,i),s=this.internal.getCoordinateString,h=this.internal.getVerticalCoordinateString,l=u.call(this);if(r=a[0],n=a[1],l[i.index]=i,o)var c=Math.cos(o*=Math.PI/180),f=Math.sin(o),d=function(t){return t.toFixed(4)},p=[d(c),d(f),d(-1*f),d(c),0,0,"cm"];this.internal.write("q"),o?(this.internal.write([1,"0","0",1,s(t),h(e+n),"cm"].join(" ")),this.internal.write(p.join(" ")),this.internal.write([s(r),"0","0",s(n),"0","0","cm"].join(" "))):this.internal.write([s(r),"0","0",s(n),s(t),h(e+n),"cm"].join(" ")),this.isAdvancedAPI()&&this.internal.write("1 0 0 -1 0 0 cm"),this.internal.write("/I"+i.index+" Do"),this.internal.write("Q")},b=t.color_spaces={DEVICE_RGB:"DeviceRGB",DEVICE_GRAY:"DeviceGray",DEVICE_CMYK:"DeviceCMYK",CAL_GREY:"CalGray",CAL_RGB:"CalRGB",LAB:"Lab",ICC_BASED:"ICCBased",INDEXED:"Indexed",PATTERN:"Pattern",SEPARATION:"Separation",DEVICE_N:"DeviceN"};t.decode={DCT_DECODE:"DCTDecode",FLATE_DECODE:"FlateDecode",LZW_DECODE:"LZWDecode",JPX_DECODE:"JPXDecode",JBIG2_DECODE:"JBIG2Decode",ASCII85_DECODE:"ASCII85Decode",ASCII_HEX_DECODE:"ASCIIHexDecode",RUN_LENGTH_DECODE:"RunLengthDecode",CCITT_FAX_DECODE:"CCITTFaxDecode"};var v=t.image_compression={NONE:"NONE",FAST:"FAST",MEDIUM:"MEDIUM",SLOW:"SLOW"},y=t.__addimage__.sHashCode=function(t){var e,r,n=0;if("string"==typeof t)for(r=t.length,e=0;e<r;e++)n=(n<<5)-n+t.charCodeAt(e)|0;else if(A(t))for(r=t.byteLength/2,e=0;e<r;e++)n=(n<<5)-n+t[e]|0;return n},w=t.__addimage__.validateStringAsBase64=function(t){(t=t||"").toString().trim();var e=!0;return 0===t.length&&(e=!1),t.length%4!=0&&(e=!1),!1===/^[A-Za-z0-9+/]+$/.test(t.substr(0,t.length-2))&&(e=!1),!1===/^[A-Za-z0-9/][A-Za-z0-9+/]|[A-Za-z0-9+/]=|==$/.test(t.substr(-2))&&(e=!1),e},x=t.__addimage__.extractImageFromDataUrl=function(t){if(null==t||!(t=t.trim()).startsWith("data:"))return null;var e=t.indexOf(",");return e<0?null:t.substring(0,e).trim().endsWith("base64")?t.substring(e+1):null},_=t.__addimage__.supportsArrayBuffer=function(){return"undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array};t.__addimage__.isArrayBuffer=function(t){return _()&&t instanceof ArrayBuffer};var A=t.__addimage__.isArrayBufferView=function(t){return _()&&"undefined"!=typeof Uint32Array&&(t instanceof Int8Array||t instanceof Uint8Array||"undefined"!=typeof Uint8ClampedArray&&t instanceof Uint8ClampedArray||t instanceof Int16Array||t instanceof Uint16Array||t instanceof Int32Array||t instanceof Uint32Array||t instanceof Float32Array||t instanceof Float64Array)},L=t.__addimage__.binaryStringToUint8Array=function(t){for(var e=t.length,r=new Uint8Array(e),n=0;n<e;n++)r[n]=t.charCodeAt(n);return r},N=t.__addimage__.arrayBufferToBinaryString=function(t){for(var e="",r=A(t)?t:new Uint8Array(t),n=0;n<r.length;n+=8192)e+=String.fromCharCode.apply(null,r.subarray(n,n+8192));return e};t.addImage=function(){if("number"==typeof arguments[1]?(r=e,n=arguments[1],o=arguments[2],a=arguments[3],s=arguments[4],u=arguments[5],l=arguments[6],c=arguments[7]):(r=arguments[1],n=arguments[2],o=arguments[3],a=arguments[4],s=arguments[5],u=arguments[6],l=arguments[7],c=arguments[8]),"object"===i()(t=arguments[0])&&!f(t)&&"imageData"in t){var t,r,n,o,a,s,u,l,c,d=t;t=d.imageData,r=d.format||r||e,n=d.x||n||0,o=d.y||o||0,a=d.w||d.width||a,s=d.h||d.height||s,u=d.alias||u,l=d.compression||l,c=d.rotation||d.angle||c}var p=this.internal.getFilters();if(void 0===l&&-1!==p.indexOf("FlateEncode")&&(l="SLOW"),isNaN(n)||isNaN(o))throw Error("Invalid coordinates passed to jsPDF.addImage");h.call(this);var g=S.call(this,t,r,u,l);return m.call(this,n,o,a,s,g,c),this};var S=function(r,i,o,a){if("string"==typeof r&&n(r)===e){var s,h,u,g,m,b=P(r=unescape(r),!1);(""!==b||void 0!==(b=t.loadFile(r,!0)))&&(r=b)}if(f(r)&&(r=d(r,i)),!c(i=n(r,i)))throw Error("addImage does not support files of type '"+i+"', please ensure that a plugin for '"+i+"' support is added.");if((null==(u=o)||0===u.length)&&(o="string"==typeof(g=r)||A(g)?y(g):A(g.data)?y(g.data):null),(s=p.call(this,o))||(_()&&(r instanceof Uint8Array||"RGBA"===i||(h=r,r=L(r))),s=this["process"+i.toUpperCase()](r,l.call(this),o,((m=a)&&"string"==typeof m&&(m=m.toUpperCase()),m in t.image_compression?m:v.NONE),h)),!s)throw Error("An unknown error occurred whilst processing the image.");return s},P=t.__addimage__.convertBase64ToBinaryString=function(t,e){e="boolean"!=typeof e||e;var r,n,i="";if("string"==typeof t){n=null!=(r=x(t))?r:t;try{i=tI(n)}catch(t){if(e)throw w(n)?Error("atob-Error in jsPDF.convertBase64ToBinaryString "+t.message):Error("Supplied Data is not a valid base64-String jsPDF.convertBase64ToBinaryString ")}}return i};t.getImageProperties=function(r){var i,o,a="";if(f(r)&&(r=d(r)),"string"==typeof r&&n(r)===e&&(""===(a=P(r,!1))&&(a=t.loadFile(r)||""),r=a),!c(o=n(r)))throw Error("addImage does not support files of type '"+o+"', please ensure that a plugin for '"+o+"' support is added.");if(!_()||r instanceof Uint8Array||(r=L(r)),!(i=this["process"+o.toUpperCase()](r)))throw Error("An unknown error occurred whilst processing the image");return i.fileType=o,i}}(t1.API),function(t){var e=function(t){if(void 0!==t&&""!=t)return!0};t1.API.events.push(["addPage",function(t){this.internal.getPageInfo(t.pageNumber).pageContext.annotations=[]}]),t.events.push(["putPage",function(t){for(var r,n,i,o=this.internal.getCoordinateString,a=this.internal.getVerticalCoordinateString,s=this.internal.getPageInfoByObjId(t.objId),h=t.pageContext.annotations,u=!1,l=0;l<h.length&&!u;l++)switch((r=h[l]).type){case"link":(e(r.options.url)||e(r.options.pageNumber))&&(u=!0);break;case"reference":case"text":case"freetext":u=!0}if(0!=u){this.internal.write("/Annots [");for(var c=0;c<h.length;c++){r=h[c];var f=this.internal.pdfEscape,d=this.internal.getEncryptor(t.objId);switch(r.type){case"reference":this.internal.write(" "+r.object.objId+" 0 R ");break;case"text":var p=this.internal.newAdditionalObject(),g=this.internal.newAdditionalObject(),m=this.internal.getEncryptor(p.objId),b=r.title||"Note";p.content=i="<</Type /Annot /Subtype /Text "+(n="/Rect ["+o(r.bounds.x)+" "+a(r.bounds.y+r.bounds.h)+" "+o(r.bounds.x+r.bounds.w)+" "+a(r.bounds.y)+"] ")+"/Contents ("+f(m(r.contents))+")"+(" /Popup "+g.objId)+" 0 R"+(" /P "+s.objId)+" 0 R"+(" /T ("+f(m(b)))+") >>";var v=p.objId+" 0 R";i="<</Type /Annot /Subtype /Popup "+(n="/Rect ["+o(r.bounds.x+30)+" "+a(r.bounds.y+r.bounds.h)+" "+o(r.bounds.x+r.bounds.w+30)+" "+a(r.bounds.y)+"] ")+" /Parent "+v,r.open&&(i+=" /Open true"),g.content=i+=" >>",this.internal.write(p.objId,"0 R",g.objId,"0 R");break;case"freetext":n="/Rect ["+o(r.bounds.x)+" "+a(r.bounds.y)+" "+o(r.bounds.x+r.bounds.w)+" "+a(r.bounds.y+r.bounds.h)+"] ";var y=r.color||"#000000";i="<</Type /Annot /Subtype /FreeText "+n+"/Contents ("+f(d(r.contents))+")"+(" /DS(font: Helvetica,sans-serif 12.0pt; text-align:left; color:#"+y)+") /Border [0 0 0] >>",this.internal.write(i);break;case"link":if(r.options.name){var w=this.annotations._nameMap[r.options.name];r.options.pageNumber=w.page,r.options.top=w.y}else r.options.top||(r.options.top=0);if(n="/Rect ["+r.finalBounds.x+" "+r.finalBounds.y+" "+r.finalBounds.w+" "+r.finalBounds.h+"] ",i="",r.options.url)i="<</Type /Annot /Subtype /Link "+n+"/Border [0 0 0] /A <</S /URI /URI ("+f(d(r.options.url))+") >>";else if(r.options.pageNumber)switch(i="<</Type /Annot /Subtype /Link "+n+"/Border [0 0 0] /Dest ["+this.internal.getPageInfo(r.options.pageNumber).objId+" 0 R",r.options.magFactor=r.options.magFactor||"XYZ",r.options.magFactor){case"Fit":i+=" /Fit]";break;case"FitH":i+=" /FitH "+r.options.top+"]";break;case"FitV":r.options.left=r.options.left||0,i+=" /FitV "+r.options.left+"]";break;default:var x=a(r.options.top);r.options.left=r.options.left||0,void 0===r.options.zoom&&(r.options.zoom=0),i+=" /XYZ "+r.options.left+" "+x+" "+r.options.zoom+"]"}""!=i&&(i+=" >>",this.internal.write(i))}}this.internal.write("]")}}]),t.createAnnotation=function(t){var e=this.internal.getCurrentPageInfo();switch(t.type){case"link":this.link(t.bounds.x,t.bounds.y,t.bounds.w,t.bounds.h,t);break;case"text":case"freetext":e.pageContext.annotations.push(t)}},t.link=function(t,e,r,n,i){var o=this.internal.getCurrentPageInfo(),a=this.internal.getCoordinateString,s=this.internal.getVerticalCoordinateString;o.pageContext.annotations.push({finalBounds:{x:a(t),y:s(e),w:a(t+r),h:s(e+n)},options:i,type:"link"})},t.textWithLink=function(t,e,r,n){var i,o,a=this.getTextWidth(t),s=this.internal.getLineHeight()/this.internal.scaleFactor;return void 0!==n.maxWidth?(o=n.maxWidth,i=Math.ceil(s*this.splitTextToSize(t,o).length)):(o=a,i=s),this.text(t,e,r,n),r+=.2*s,"center"===n.align&&(e-=a/2),"right"===n.align&&(e-=a),this.link(e,r-s,o,i,n),a},t.getTextWidth=function(t){var e=this.internal.getFontSize();return this.getStringUnitWidth(t)*e/this.internal.scaleFactor}}(t1.API),function(t){var e={1569:[65152],1570:[65153,65154],1571:[65155,65156],1572:[65157,65158],1573:[65159,65160],1574:[65161,65162,65163,65164],1575:[65165,65166],1576:[65167,65168,65169,65170],1577:[65171,65172],1578:[65173,65174,65175,65176],1579:[65177,65178,65179,65180],1580:[65181,65182,65183,65184],1581:[65185,65186,65187,65188],1582:[65189,65190,65191,65192],1583:[65193,65194],1584:[65195,65196],1585:[65197,65198],1586:[65199,65200],1587:[65201,65202,65203,65204],1588:[65205,65206,65207,65208],1589:[65209,65210,65211,65212],1590:[65213,65214,65215,65216],1591:[65217,65218,65219,65220],1592:[65221,65222,65223,65224],1593:[65225,65226,65227,65228],1594:[65229,65230,65231,65232],1601:[65233,65234,65235,65236],1602:[65237,65238,65239,65240],1603:[65241,65242,65243,65244],1604:[65245,65246,65247,65248],1605:[65249,65250,65251,65252],1606:[65253,65254,65255,65256],1607:[65257,65258,65259,65260],1608:[65261,65262],1609:[65263,65264,64488,64489],1610:[65265,65266,65267,65268],1649:[64336,64337],1655:[64477],1657:[64358,64359,64360,64361],1658:[64350,64351,64352,64353],1659:[64338,64339,64340,64341],1662:[64342,64343,64344,64345],1663:[64354,64355,64356,64357],1664:[64346,64347,64348,64349],1667:[64374,64375,64376,64377],1668:[64370,64371,64372,64373],1670:[64378,64379,64380,64381],1671:[64382,64383,64384,64385],1672:[64392,64393],1676:[64388,64389],1677:[64386,64387],1678:[64390,64391],1681:[64396,64397],1688:[64394,64395],1700:[64362,64363,64364,64365],1702:[64366,64367,64368,64369],1705:[64398,64399,64400,64401],1709:[64467,64468,64469,64470],1711:[64402,64403,64404,64405],1713:[64410,64411,64412,64413],1715:[64406,64407,64408,64409],1722:[64414,64415],1723:[64416,64417,64418,64419],1726:[64426,64427,64428,64429],1728:[64420,64421],1729:[64422,64423,64424,64425],1733:[64480,64481],1734:[64473,64474],1735:[64471,64472],1736:[64475,64476],1737:[64482,64483],1739:[64478,64479],1740:[64508,64509,64510,64511],1744:[64484,64485,64486,64487],1746:[64430,64431],1747:[64432,64433]},r={65247:{65154:65269,65156:65271,65160:65273,65166:65275},65248:{65154:65270,65156:65272,65160:65274,65166:65276},65165:{65247:{65248:{65258:65010}}},1617:{1612:64606,1613:64607,1614:64608,1615:64609,1616:64610}},n={1612:64606,1613:64607,1614:64608,1615:64609,1616:64610},i=[1570,1571,1573,1575];t.__arabicParser__={};var o=t.__arabicParser__.isInArabicSubstitutionA=function(t){return void 0!==e[t.charCodeAt(0)]},a=t.__arabicParser__.isArabicLetter=function(t){return"string"==typeof t&&/^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+$/.test(t)},s=t.__arabicParser__.isArabicEndLetter=function(t){return a(t)&&o(t)&&e[t.charCodeAt(0)].length<=2},h=t.__arabicParser__.isArabicAlfLetter=function(t){return a(t)&&i.indexOf(t.charCodeAt(0))>=0};t.__arabicParser__.arabicLetterHasIsolatedForm=function(t){return a(t)&&o(t)&&e[t.charCodeAt(0)].length>=1};var u=t.__arabicParser__.arabicLetterHasFinalForm=function(t){return a(t)&&o(t)&&e[t.charCodeAt(0)].length>=2};t.__arabicParser__.arabicLetterHasInitialForm=function(t){return a(t)&&o(t)&&e[t.charCodeAt(0)].length>=3};var l=t.__arabicParser__.arabicLetterHasMedialForm=function(t){return a(t)&&o(t)&&4==e[t.charCodeAt(0)].length},c=t.__arabicParser__.resolveLigatures=function(t){var e=0,n=r,i="",o=0;for(e=0;e<t.length;e+=1)void 0!==n[t.charCodeAt(e)]?(o++,"number"==typeof(n=n[t.charCodeAt(e)])&&(i+=String.fromCharCode(n),n=r,o=0),e===t.length-1&&(n=r,i+=t.charAt(e-(o-1)),e-=o-1,o=0)):(n=r,i+=t.charAt(e-o),e-=o,o=0);return i};t.__arabicParser__.isArabicDiacritic=function(t){return void 0!==t&&void 0!==n[t.charCodeAt(0)]};var f=t.__arabicParser__.getCorrectForm=function(t,e,r){return a(t)?!1===o(t)?-1:!u(t)||!a(e)&&!a(r)||!a(r)&&s(e)||s(t)&&!a(e)||s(t)&&h(e)||s(t)&&s(e)?0:l(t)&&a(e)&&!s(e)&&a(r)&&u(r)?3:s(t)||!a(r)?1:2:-1},d=function(t){var r=0,n=0,i=0,o="",s="",h="",u=(t=t||"").split("\\s+"),l=[];for(r=0;r<u.length;r+=1){for(l.push(""),n=0;n<u[r].length;n+=1)o=u[r][n],s=u[r][n-1],h=u[r][n+1],a(o)?(i=f(o,s,h),l[r]+=-1!==i?String.fromCharCode(e[o.charCodeAt(0)][i]):o):l[r]+=o;l[r]=c(l[r])}return l.join(" ")},p=t.__arabicParser__.processArabic=t.processArabic=function(){var t,e="string"==typeof arguments[0]?arguments[0]:arguments[0].text,r=[];if(Array.isArray(e)){var n=0;for(r=[],n=0;n<e.length;n+=1)Array.isArray(e[n])?r.push([d(e[n][0]),e[n][1],e[n][2]]):r.push([d(e[n])]);t=r}else t=d(e);return"string"==typeof arguments[0]?t:(arguments[0].text=t,arguments[0])};t.events.push(["preProcessText",p])}(t1.API),t1.API.autoPrint=function(t){var e;return((t=t||{}).variant=t.variant||"non-conform","javascript"===t.variant)?this.addJS("print({});"):(this.internal.events.subscribe("postPutResources",function(){e=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /Named"),this.internal.out("/Type /Action"),this.internal.out("/N /Print"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){this.internal.out("/OpenAction "+e+" 0 R")})),this},function(t){var e=function(){var t=void 0;Object.defineProperty(this,"pdf",{get:function(){return t},set:function(e){t=e}});var e=150;Object.defineProperty(this,"width",{get:function(){return e},set:function(t){e=isNaN(t)||!1===Number.isInteger(t)||t<0?150:t,this.getContext("2d").pageWrapXEnabled&&(this.getContext("2d").pageWrapX=e+1)}});var r=300;Object.defineProperty(this,"height",{get:function(){return r},set:function(t){r=isNaN(t)||!1===Number.isInteger(t)||t<0?300:t,this.getContext("2d").pageWrapYEnabled&&(this.getContext("2d").pageWrapY=r+1)}});var n=[];Object.defineProperty(this,"childNodes",{get:function(){return n},set:function(t){n=t}});var i={};Object.defineProperty(this,"style",{get:function(){return i},set:function(t){i=t}}),Object.defineProperty(this,"parentNode",{})};e.prototype.getContext=function(t,e){var r;if("2d"!==(t=t||"2d"))return null;for(r in e)this.pdf.context2d.hasOwnProperty(r)&&(this.pdf.context2d[r]=e[r]);return this.pdf.context2d._canvas=this,this.pdf.context2d},e.prototype.toDataURL=function(){throw Error("toDataURL is not implemented.")},t.events.push(["initialized",function(){this.canvas=new e,this.canvas.pdf=this}])}(t1.API),function(t){var e={left:0,top:0,bottom:0,right:0},r=!1,n=function(){void 0===this.internal.__cell__&&(this.internal.__cell__={},this.internal.__cell__.padding=3,this.internal.__cell__.headerFunction=void 0,this.internal.__cell__.margins=Object.assign({},e),this.internal.__cell__.margins.width=this.getPageWidth(),o.call(this))},o=function(){this.internal.__cell__.lastCell=new a,this.internal.__cell__.pages=1},a=function(){var t=arguments[0];Object.defineProperty(this,"x",{enumerable:!0,get:function(){return t},set:function(e){t=e}});var e=arguments[1];Object.defineProperty(this,"y",{enumerable:!0,get:function(){return e},set:function(t){e=t}});var r=arguments[2];Object.defineProperty(this,"width",{enumerable:!0,get:function(){return r},set:function(t){r=t}});var n=arguments[3];Object.defineProperty(this,"height",{enumerable:!0,get:function(){return n},set:function(t){n=t}});var i=arguments[4];Object.defineProperty(this,"text",{enumerable:!0,get:function(){return i},set:function(t){i=t}});var o=arguments[5];Object.defineProperty(this,"lineNumber",{enumerable:!0,get:function(){return o},set:function(t){o=t}});var a=arguments[6];return Object.defineProperty(this,"align",{enumerable:!0,get:function(){return a},set:function(t){a=t}}),this};a.prototype.clone=function(){return new a(this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align)},a.prototype.toArray=function(){return[this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align]},t.setHeaderFunction=function(t){return n.call(this),this.internal.__cell__.headerFunction="function"==typeof t?t:void 0,this},t.getTextDimensions=function(t,e){n.call(this);var r=(e=e||{}).fontSize||this.getFontSize(),i=e.font||this.getFont(),o=e.scaleFactor||this.internal.scaleFactor,a=0,s=0,h=0,u=this;if(!Array.isArray(t)&&"string"!=typeof t){if("number"!=typeof t)throw Error("getTextDimensions expects text-parameter to be of type String or type Number or an Array of Strings.");t=String(t)}var l=e.maxWidth;l>0?"string"==typeof t?t=this.splitTextToSize(t,l):"[object Array]"===Object.prototype.toString.call(t)&&(t=t.reduce(function(t,e){return t.concat(u.splitTextToSize(e,l))},[])):t=Array.isArray(t)?t:[t];for(var c=0;c<t.length;c++)a<(h=this.getStringUnitWidth(t[c],{font:i})*r)&&(a=h);return 0!==a&&(s=t.length),{w:a/=o,h:Math.max((s*r*this.getLineHeightFactor()-r*(this.getLineHeightFactor()-1))/o,0)}},t.cellAddPage=function(){n.call(this),this.addPage();var t=this.internal.__cell__.margins||e;return this.internal.__cell__.lastCell=new a(t.left,t.top,void 0,void 0),this.internal.__cell__.pages+=1,this};var s=t.cell=function(){t=arguments[0]instanceof a?arguments[0]:new a(arguments[0],arguments[1],arguments[2],arguments[3],arguments[4],arguments[5]),n.call(this);var t,i=this.internal.__cell__.lastCell,o=this.internal.__cell__.padding,s=this.internal.__cell__.margins||e,h=this.internal.__cell__.tableHeaderRow,u=this.internal.__cell__.printHeaders;return void 0!==i.lineNumber&&(i.lineNumber===t.lineNumber?(t.x=(i.x||0)+(i.width||0),t.y=i.y||0):i.y+i.height+t.height+s.bottom>this.getPageHeight()?(this.cellAddPage(),t.y=s.top,u&&h&&(this.printHeaderRow(t.lineNumber,!0),t.y+=h[0].height)):t.y=i.y+i.height||t.y),void 0!==t.text[0]&&(this.rect(t.x,t.y,t.width,t.height,!0===r?"FD":void 0),"right"===t.align?this.text(t.text,t.x+t.width-o,t.y+o,{align:"right",baseline:"top"}):"center"===t.align?this.text(t.text,t.x+t.width/2,t.y+o,{align:"center",baseline:"top",maxWidth:t.width-o-o}):this.text(t.text,t.x+o,t.y+o,{align:"left",baseline:"top",maxWidth:t.width-o-o})),this.internal.__cell__.lastCell=t,this};t.table=function(t,r,u,l,c){if(n.call(this),!u)throw Error("No data for PDF table.");var f,d,p,g,m=[],b=[],v=[],y={},w={},x=[],_=[],A=(c=c||{}).autoSize||!1,L=!1!==c.printHeaders,N=c.css&&void 0!==c.css["font-size"]?16*c.css["font-size"]:c.fontSize||12,S=c.margins||Object.assign({width:this.getPageWidth()},e),P="number"==typeof c.padding?c.padding:3,k=c.headerBackgroundColor||"#c8c8c8",I=c.headerTextColor||"#000";if(o.call(this),this.internal.__cell__.printHeaders=L,this.internal.__cell__.margins=S,this.internal.__cell__.table_font_size=N,this.internal.__cell__.padding=P,this.internal.__cell__.headerBackgroundColor=k,this.internal.__cell__.headerTextColor=I,this.setFontSize(N),null==l)b=m=Object.keys(u[0]),v=m.map(function(){return"left"});else if(Array.isArray(l)&&"object"===i()(l[0]))for(m=l.map(function(t){return t.name}),b=l.map(function(t){return t.prompt||t.name||""}),v=l.map(function(t){return t.align||"left"}),f=0;f<l.length;f+=1)w[l[f].name]=l[f].width*(19.049976/25.4);else Array.isArray(l)&&"string"==typeof l[0]&&(b=m=l,v=m.map(function(){return"left"}));if(A||Array.isArray(l)&&"string"==typeof l[0])for(f=0;f<m.length;f+=1){for(y[g=m[f]]=u.map(function(t){return t[g]}),this.setFont(void 0,"bold"),x.push(this.getTextDimensions(b[f],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w),d=y[g],this.setFont(void 0,"normal"),p=0;p<d.length;p+=1)x.push(this.getTextDimensions(d[p],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w);w[g]=Math.max.apply(null,x)+P+P,x=[]}if(L){var C={};for(f=0;f<m.length;f+=1)C[m[f]]={},C[m[f]].text=b[f],C[m[f]].align=v[f];var E=h.call(this,C,w);_=m.map(function(e){return new a(t,r,w[e],E,C[e].text,void 0,C[e].align)}),this.setTableHeaderRow(_),this.printHeaderRow(1,!1)}var F=l.reduce(function(t,e){return t[e.name]=e.align,t},{});for(f=0;f<u.length;f+=1){"rowStart"in c&&c.rowStart instanceof Function&&c.rowStart({row:f,data:u[f]},this);var O=h.call(this,u[f],w);for(p=0;p<m.length;p+=1){var T=u[f][m[p]];"cellStart"in c&&c.cellStart instanceof Function&&c.cellStart({row:f,col:p,data:T},this),s.call(this,new a(t,r,w[m[p]],O,T,f+2,F[m[p]]))}}return this.internal.__cell__.table_x=t,this.internal.__cell__.table_y=r,this};var h=function(t,e){var r=this.internal.__cell__.padding,n=this.internal.__cell__.table_font_size,i=this.internal.scaleFactor;return Object.keys(t).map(function(n){var i=t[n];return this.splitTextToSize(i.hasOwnProperty("text")?i.text:i,e[n]-r-r)},this).map(function(t){return this.getLineHeightFactor()*t.length*n/i+r+r},this).reduce(function(t,e){return Math.max(t,e)},0)};t.setTableHeaderRow=function(t){n.call(this),this.internal.__cell__.tableHeaderRow=t},t.printHeaderRow=function(t,e){if(n.call(this),!this.internal.__cell__.tableHeaderRow)throw Error("Property tableHeaderRow does not exist.");if(r=!0,"function"==typeof this.internal.__cell__.headerFunction){var i,o=this.internal.__cell__.headerFunction(this,this.internal.__cell__.pages);this.internal.__cell__.lastCell=new a(o[0],o[1],o[2],o[3],void 0,-1)}this.setFont(void 0,"bold");for(var h=[],u=0;u<this.internal.__cell__.tableHeaderRow.length;u+=1){i=this.internal.__cell__.tableHeaderRow[u].clone(),e&&(i.y=this.internal.__cell__.margins.top||0,h.push(i)),i.lineNumber=t;var l=this.getTextColor();this.setTextColor(this.internal.__cell__.headerTextColor),this.setFillColor(this.internal.__cell__.headerBackgroundColor),s.call(this,i),this.setTextColor(l)}h.length>0&&this.setTableHeaderRow(h),this.setFont(void 0,"normal"),r=!1}}(t1.API);var eT={italic:["italic","oblique","normal"],oblique:["oblique","italic","normal"],normal:["normal","oblique","italic"]},eM=["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded"],ej=eO(eM),eB=[100,200,300,400,500,600,700,800,900],eR=eO(eB);function eD(t){var e,r,n,i=t.family.replace(/"|'/g,"").toLowerCase(),o=eT[e=(e=t.style)||"normal"]?e:"normal",a=(r=t.weight)?"number"==typeof r?r>=100&&r<=900&&r%100==0?r:400:/^\d00$/.test(r)?parseInt(r):"bold"===r?700:400:400,s="number"==typeof ej[n=(n=t.stretch)||"normal"]?n:"normal";return{family:i,style:o,weight:a,stretch:s,src:t.src||[],ref:t.ref||{name:i,style:[s,o,a].join(" ")}}}function eq(t,e,r,n){var i;for(i=r;i>=0&&i<e.length;i+=n)if(t[e[i]])return t[e[i]];for(i=r;i>=0&&i<e.length;i-=n)if(t[e[i]])return t[e[i]]}var eU={"sans-serif":"helvetica",fixed:"courier",monospace:"courier",terminal:"courier",cursive:"times",fantasy:"times",serif:"times"},ez={caption:"times",icon:"times",menu:"times","message-box":"times","small-caption":"times","status-bar":"times"};function eH(t){return[t.stretch,t.style,t.weight,t.family].join(" ")}function eW(t){return t.trimLeft()}var eV,eG,eY,eJ=["times"];!function(t){var e,r,n,o,a,s,h,u,l,c=function(t){return t=t||{},this.isStrokeTransparent=t.isStrokeTransparent||!1,this.strokeOpacity=t.strokeOpacity||1,this.strokeStyle=t.strokeStyle||"#000000",this.fillStyle=t.fillStyle||"#000000",this.isFillTransparent=t.isFillTransparent||!1,this.fillOpacity=t.fillOpacity||1,this.font=t.font||"10px sans-serif",this.textBaseline=t.textBaseline||"alphabetic",this.textAlign=t.textAlign||"left",this.lineWidth=t.lineWidth||1,this.lineJoin=t.lineJoin||"miter",this.lineCap=t.lineCap||"butt",this.path=t.path||[],this.transform=void 0!==t.transform?t.transform.clone():new u,this.globalCompositeOperation=t.globalCompositeOperation||"normal",this.globalAlpha=t.globalAlpha||1,this.clip_path=t.clip_path||[],this.currentPoint=t.currentPoint||new s,this.miterLimit=t.miterLimit||10,this.lastPoint=t.lastPoint||new s,this.lineDashOffset=t.lineDashOffset||0,this.lineDash=t.lineDash||[],this.margin=t.margin||[0,0,0,0],this.prevPageLastElemOffset=t.prevPageLastElemOffset||0,this.ignoreClearRect="boolean"!=typeof t.ignoreClearRect||t.ignoreClearRect,this};t.events.push(["initialized",function(){this.context2d=new f(this),e=this.internal.f2,r=this.internal.getCoordinateString,n=this.internal.getVerticalCoordinateString,o=this.internal.getHorizontalCoordinate,a=this.internal.getVerticalCoordinate,s=this.internal.Point,h=this.internal.Rectangle,u=this.internal.Matrix,l=new c}]);var f=function(t){Object.defineProperty(this,"canvas",{get:function(){return{parentNode:!1,style:!1}}}),Object.defineProperty(this,"pdf",{get:function(){return t}});var e=!1;Object.defineProperty(this,"pageWrapXEnabled",{get:function(){return e},set:function(t){e=!!t}});var r=!1;Object.defineProperty(this,"pageWrapYEnabled",{get:function(){return r},set:function(t){r=!!t}});var n=0;Object.defineProperty(this,"posX",{get:function(){return n},set:function(t){isNaN(t)||(n=t)}});var i=0;Object.defineProperty(this,"posY",{get:function(){return i},set:function(t){isNaN(t)||(i=t)}}),Object.defineProperty(this,"margin",{get:function(){return l.margin},set:function(t){var e;"number"==typeof t?e=[t,t,t,t]:((e=[,,,,])[0]=t[0],e[1]=t.length>=2?t[1]:e[0],e[2]=t.length>=3?t[2]:e[0],e[3]=t.length>=4?t[3]:e[1]),l.margin=e}});var o=!1;Object.defineProperty(this,"autoPaging",{get:function(){return o},set:function(t){o=t}});var a=0;Object.defineProperty(this,"lastBreak",{get:function(){return a},set:function(t){a=t}});var s=[];Object.defineProperty(this,"pageBreaks",{get:function(){return s},set:function(t){s=t}}),Object.defineProperty(this,"ctx",{get:function(){return l},set:function(t){t instanceof c&&(l=t)}}),Object.defineProperty(this,"path",{get:function(){return l.path},set:function(t){l.path=t}});var h=[];Object.defineProperty(this,"ctxStack",{get:function(){return h},set:function(t){h=t}}),Object.defineProperty(this,"fillStyle",{get:function(){return this.ctx.fillStyle},set:function(t){var e;e=d(t),this.ctx.fillStyle=e.style,this.ctx.isFillTransparent=0===e.a,this.ctx.fillOpacity=e.a,this.pdf.setFillColor(e.r,e.g,e.b,{a:e.a}),this.pdf.setTextColor(e.r,e.g,e.b,{a:e.a})}}),Object.defineProperty(this,"strokeStyle",{get:function(){return this.ctx.strokeStyle},set:function(t){var e=d(t);this.ctx.strokeStyle=e.style,this.ctx.isStrokeTransparent=0===e.a,this.ctx.strokeOpacity=e.a,0===e.a?this.pdf.setDrawColor(255,255,255):(e.a,this.pdf.setDrawColor(e.r,e.g,e.b))}}),Object.defineProperty(this,"lineCap",{get:function(){return this.ctx.lineCap},set:function(t){-1!==["butt","round","square"].indexOf(t)&&(this.ctx.lineCap=t,this.pdf.setLineCap(t))}}),Object.defineProperty(this,"lineWidth",{get:function(){return this.ctx.lineWidth},set:function(t){isNaN(t)||(this.ctx.lineWidth=t,this.pdf.setLineWidth(t))}}),Object.defineProperty(this,"lineJoin",{get:function(){return this.ctx.lineJoin},set:function(t){-1!==["bevel","round","miter"].indexOf(t)&&(this.ctx.lineJoin=t,this.pdf.setLineJoin(t))}}),Object.defineProperty(this,"miterLimit",{get:function(){return this.ctx.miterLimit},set:function(t){isNaN(t)||(this.ctx.miterLimit=t,this.pdf.setMiterLimit(t))}}),Object.defineProperty(this,"textBaseline",{get:function(){return this.ctx.textBaseline},set:function(t){this.ctx.textBaseline=t}}),Object.defineProperty(this,"textAlign",{get:function(){return this.ctx.textAlign},set:function(t){-1!==["right","end","center","left","start"].indexOf(t)&&(this.ctx.textAlign=t)}});var u=null,f=null;Object.defineProperty(this,"fontFaces",{get:function(){return f},set:function(t){u=null,f=t}}),Object.defineProperty(this,"font",{get:function(){return this.ctx.font},set:function(t){var e;if(this.ctx.font=t,null!==(e=/^\s*(?=(?:(?:[-a-z]+\s*){0,2}(italic|oblique))?)(?=(?:(?:[-a-z]+\s*){0,2}(small-caps))?)(?=(?:(?:[-a-z]+\s*){0,2}(bold(?:er)?|lighter|[1-9]00))?)(?:(?:normal|\1|\2|\3)\s*){0,3}((?:xx?-)?(?:small|large)|medium|smaller|larger|[.\d]+(?:\%|in|[cem]m|ex|p[ctx]))(?:\s*\/\s*(normal|[.\d]+(?:\%|in|[cem]m|ex|p[ctx])))?\s*([-_,\"\'\sa-z]+?)\s*$/i.exec(t))){var r=e[1];e[2];var n=e[3],i=e[4];e[5];var o=e[6],a=/^([.\d]+)((?:%|in|[cem]m|ex|p[ctx]))$/i.exec(i)[2];i="px"===a?Math.floor(parseFloat(i)*this.pdf.internal.scaleFactor):"em"===a?Math.floor(parseFloat(i)*this.pdf.getFontSize()):Math.floor(parseFloat(i)*this.pdf.internal.scaleFactor),this.pdf.setFontSize(i);var s=function(t){var e,r,n=[],i=t.trim();if(""===i)return eJ;if(i in ez)return[ez[i]];for(;""!==i;){switch(r=null,e=(i=eW(i)).charAt(0)){case'"':case"'":r=function(t,e){for(var r=0;r<t.length;){if(t.charAt(r)===e)return[t.substring(0,r),t.substring(r+1)];r+=1}return null}(i.substring(1),e);break;default:r=function(t){var e=t.match(/^(-[a-z_]|[a-z_])[a-z0-9_-]*/i);return null===e?null:[e[0],t.substring(e[0].length)]}(i)}if(null===r||(n.push(r[0]),""!==(i=eW(r[1]))&&","!==i.charAt(0)))return eJ;i=i.replace(/^,/,"")}return n}(o);if(this.fontFaces){var h=function(t,e,r){for(var n=(r=r||{}).defaultFontFamily||"times",i=Object.assign({},eU,r.genericFontFamilies||{}),o=null,a=null,s=0;s<e.length;++s)if(i[(o=eD(e[s])).family]&&(o.family=i[o.family]),t.hasOwnProperty(o.family)){a=t[o.family];break}if(!(a=a||t[n]))throw Error("Could not find a font-family for the rule '"+eH(o)+"' and default family '"+n+"'.");if(a=function(t,e){if(e[t])return e[t];var r=ej[t],n=r<=ej.normal?-1:1,i=eq(e,eM,r,n);if(!i)throw Error("Could not find a matching font-stretch value for "+t);return i}(o.stretch,a),a=function(t,e){if(e[t])return e[t];for(var r=eT[t],n=0;n<r.length;++n)if(e[r[n]])return e[r[n]];throw Error("Could not find a matching font-style for "+t)}(o.style,a),!(a=function(t,e){if(e[t])return e[t];if(400===t&&e[500])return e[500];if(500===t&&e[400])return e[400];var r=eq(e,eB,eR[t],t<400?-1:1);if(!r)throw Error("Could not find a matching font-weight for value "+t);return r}(o.weight,a)))throw Error("Failed to resolve a font for the rule '"+eH(o)+"'.");return a}(function(t,e){if(null===u){var r,n;u=function(t){for(var e={},r=0;r<t.length;++r){var n=eD(t[r]),i=n.family,o=n.stretch,a=n.style,s=n.weight;e[i]=e[i]||{},e[i][o]=e[i][o]||{},e[i][o][a]=e[i][o][a]||{},e[i][o][a][s]=n}return e}((r=t.getFontList(),n=[],Object.keys(r).forEach(function(t){r[t].forEach(function(e){var r=null;switch(e){case"bold":r={family:t,weight:"bold"};break;case"italic":r={family:t,style:"italic"};break;case"bolditalic":r={family:t,weight:"bold",style:"italic"};break;case"":case"normal":r={family:t}}null!==r&&(r.ref={name:t,style:e},n.push(r))})}),n).concat(e))}return u}(this.pdf,this.fontFaces),s.map(function(t){return{family:t,stretch:"normal",weight:n,style:r}}));this.pdf.setFont(h.ref.name,h.ref.style)}else{var l="";("bold"===n||parseInt(n,10)>=700||"bold"===r)&&(l="bold"),"italic"===r&&(l+="italic"),0===l.length&&(l="normal");for(var c="",f={arial:"Helvetica",Arial:"Helvetica",verdana:"Helvetica",Verdana:"Helvetica",helvetica:"Helvetica",Helvetica:"Helvetica","sans-serif":"Helvetica",fixed:"Courier",monospace:"Courier",terminal:"Courier",cursive:"Times",fantasy:"Times",serif:"Times"},d=0;d<s.length;d++){if(void 0!==this.pdf.internal.getFont(s[d],l,{noFallback:!0,disableWarning:!0})){c=s[d];break}if("bolditalic"===l&&void 0!==this.pdf.internal.getFont(s[d],"bold",{noFallback:!0,disableWarning:!0}))c=s[d],l="bold";else if(void 0!==this.pdf.internal.getFont(s[d],"normal",{noFallback:!0,disableWarning:!0})){c=s[d],l="normal";break}}if(""===c){for(var p=0;p<s.length;p++)if(f[s[p]]){c=f[s[p]];break}}c=""===c?"Times":c,this.pdf.setFont(c,l)}}}}),Object.defineProperty(this,"globalCompositeOperation",{get:function(){return this.ctx.globalCompositeOperation},set:function(t){this.ctx.globalCompositeOperation=t}}),Object.defineProperty(this,"globalAlpha",{get:function(){return this.ctx.globalAlpha},set:function(t){this.ctx.globalAlpha=t}}),Object.defineProperty(this,"lineDashOffset",{get:function(){return this.ctx.lineDashOffset},set:function(t){this.ctx.lineDashOffset=t,R.call(this)}}),Object.defineProperty(this,"lineDash",{get:function(){return this.ctx.lineDash},set:function(t){this.ctx.lineDash=t,R.call(this)}}),Object.defineProperty(this,"ignoreClearRect",{get:function(){return this.ctx.ignoreClearRect},set:function(t){this.ctx.ignoreClearRect=!!t}})};f.prototype.setLineDash=function(t){this.lineDash=t},f.prototype.getLineDash=function(){return this.lineDash.length%2?this.lineDash.concat(this.lineDash):this.lineDash.slice()},f.prototype.fill=function(){x.call(this,"fill",!1)},f.prototype.stroke=function(){x.call(this,"stroke",!1)},f.prototype.beginPath=function(){this.path=[{type:"begin"}]},f.prototype.moveTo=function(t,e){if(isNaN(t)||isNaN(e))throw tN.error("jsPDF.context2d.moveTo: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.moveTo");var r=this.ctx.transform.applyToPoint(new s(t,e));this.path.push({type:"mt",x:r.x,y:r.y}),this.ctx.lastPoint=new s(t,e)},f.prototype.closePath=function(){var t=new s(0,0),e=0;for(e=this.path.length-1;-1!==e;e--)if("begin"===this.path[e].type&&"object"===i()(this.path[e+1])&&"number"==typeof this.path[e+1].x){t=new s(this.path[e+1].x,this.path[e+1].y);break}this.path.push({type:"close"}),this.ctx.lastPoint=new s(t.x,t.y)},f.prototype.lineTo=function(t,e){if(isNaN(t)||isNaN(e))throw tN.error("jsPDF.context2d.lineTo: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.lineTo");var r=this.ctx.transform.applyToPoint(new s(t,e));this.path.push({type:"lt",x:r.x,y:r.y}),this.ctx.lastPoint=new s(r.x,r.y)},f.prototype.clip=function(){this.ctx.clip_path=JSON.parse(JSON.stringify(this.path)),x.call(this,null,!0)},f.prototype.quadraticCurveTo=function(t,e,r,n){if(isNaN(r)||isNaN(n)||isNaN(t)||isNaN(e))throw tN.error("jsPDF.context2d.quadraticCurveTo: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.quadraticCurveTo");var i=this.ctx.transform.applyToPoint(new s(r,n)),o=this.ctx.transform.applyToPoint(new s(t,e));this.path.push({type:"qct",x1:o.x,y1:o.y,x:i.x,y:i.y}),this.ctx.lastPoint=new s(i.x,i.y)},f.prototype.bezierCurveTo=function(t,e,r,n,i,o){if(isNaN(i)||isNaN(o)||isNaN(t)||isNaN(e)||isNaN(r)||isNaN(n))throw tN.error("jsPDF.context2d.bezierCurveTo: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.bezierCurveTo");var a=this.ctx.transform.applyToPoint(new s(i,o)),h=this.ctx.transform.applyToPoint(new s(t,e)),u=this.ctx.transform.applyToPoint(new s(r,n));this.path.push({type:"bct",x1:h.x,y1:h.y,x2:u.x,y2:u.y,x:a.x,y:a.y}),this.ctx.lastPoint=new s(a.x,a.y)},f.prototype.arc=function(t,e,r,n,i,o){if(isNaN(t)||isNaN(e)||isNaN(r)||isNaN(n)||isNaN(i))throw tN.error("jsPDF.context2d.arc: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.arc");if(o=!!o,!this.ctx.transform.isIdentity){var a=this.ctx.transform.applyToPoint(new s(t,e));t=a.x,e=a.y;var h=this.ctx.transform.applyToPoint(new s(0,r)),u=this.ctx.transform.applyToPoint(new s(0,0));r=Math.sqrt(Math.pow(h.x-u.x,2)+Math.pow(h.y-u.y,2))}Math.abs(i-n)>=2*Math.PI&&(n=0,i=2*Math.PI),this.path.push({type:"arc",x:t,y:e,radius:r,startAngle:n,endAngle:i,counterclockwise:o})},f.prototype.arcTo=function(t,e,r,n,i){throw Error("arcTo not implemented.")},f.prototype.rect=function(t,e,r,n){if(isNaN(t)||isNaN(e)||isNaN(r)||isNaN(n))throw tN.error("jsPDF.context2d.rect: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.rect");this.moveTo(t,e),this.lineTo(t+r,e),this.lineTo(t+r,e+n),this.lineTo(t,e+n),this.lineTo(t,e),this.lineTo(t+r,e),this.lineTo(t,e)},f.prototype.fillRect=function(t,e,r,n){if(isNaN(t)||isNaN(e)||isNaN(r)||isNaN(n))throw tN.error("jsPDF.context2d.fillRect: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.fillRect");if(!p.call(this)){var i={};"butt"!==this.lineCap&&(i.lineCap=this.lineCap,this.lineCap="butt"),"miter"!==this.lineJoin&&(i.lineJoin=this.lineJoin,this.lineJoin="miter"),this.beginPath(),this.rect(t,e,r,n),this.fill(),i.hasOwnProperty("lineCap")&&(this.lineCap=i.lineCap),i.hasOwnProperty("lineJoin")&&(this.lineJoin=i.lineJoin)}},f.prototype.strokeRect=function(t,e,r,n){if(isNaN(t)||isNaN(e)||isNaN(r)||isNaN(n))throw tN.error("jsPDF.context2d.strokeRect: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.strokeRect");g.call(this)||(this.beginPath(),this.rect(t,e,r,n),this.stroke())},f.prototype.clearRect=function(t,e,r,n){if(isNaN(t)||isNaN(e)||isNaN(r)||isNaN(n))throw tN.error("jsPDF.context2d.clearRect: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.clearRect");this.ignoreClearRect||(this.fillStyle="#ffffff",this.fillRect(t,e,r,n))},f.prototype.save=function(t){t="boolean"!=typeof t||t;for(var e=this.pdf.internal.getCurrentPageInfo().pageNumber,r=0;r<this.pdf.internal.getNumberOfPages();r++)this.pdf.setPage(r+1),this.pdf.internal.out("q");if(this.pdf.setPage(e),t){this.ctx.fontSize=this.pdf.internal.getFontSize();var n=new c(this.ctx);this.ctxStack.push(this.ctx),this.ctx=n}},f.prototype.restore=function(t){t="boolean"!=typeof t||t;for(var e=this.pdf.internal.getCurrentPageInfo().pageNumber,r=0;r<this.pdf.internal.getNumberOfPages();r++)this.pdf.setPage(r+1),this.pdf.internal.out("Q");this.pdf.setPage(e),t&&0!==this.ctxStack.length&&(this.ctx=this.ctxStack.pop(),this.fillStyle=this.ctx.fillStyle,this.strokeStyle=this.ctx.strokeStyle,this.font=this.ctx.font,this.lineCap=this.ctx.lineCap,this.lineWidth=this.ctx.lineWidth,this.lineJoin=this.ctx.lineJoin,this.lineDash=this.ctx.lineDash,this.lineDashOffset=this.ctx.lineDashOffset)},f.prototype.toDataURL=function(){throw Error("toDataUrl not implemented.")};var d=function(t){var e,r,n,i;if(!0===t.isCanvasGradient&&(t=t.getColor()),!t)return{r:0,g:0,b:0,a:0,style:t};if(/transparent|rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*0+\s*\)/.test(t))e=0,r=0,n=0,i=0;else{var o=/rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/.exec(t);if(null!==o)e=parseInt(o[1]),r=parseInt(o[2]),n=parseInt(o[3]),i=1;else if(null!==(o=/rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)/.exec(t)))e=parseInt(o[1]),r=parseInt(o[2]),n=parseInt(o[3]),i=parseFloat(o[4]);else{if(i=1,"string"==typeof t&&"#"!==t.charAt(0)){var a=new tF(t);t=a.ok?a.toHex():"#000000"}4===t.length?(e=t.substring(1,2),e+=e,r=t.substring(2,3),r+=r,n=t.substring(3,4),n+=n):(e=t.substring(1,3),r=t.substring(3,5),n=t.substring(5,7)),e=parseInt(e,16),r=parseInt(r,16),n=parseInt(n,16)}}return{r:e,g:r,b:n,a:i,style:t}},p=function(){return this.ctx.isFillTransparent||0==this.globalAlpha},g=function(){return!!(this.ctx.isStrokeTransparent||0==this.globalAlpha)};f.prototype.fillText=function(t,e,r,n){if(isNaN(e)||isNaN(r)||"string"!=typeof t)throw tN.error("jsPDF.context2d.fillText: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.fillText");if(n=isNaN(n)?void 0:n,!p.call(this)){var i=M(this.ctx.transform.rotation);I.call(this,{text:t,x:e,y:r,scale:this.ctx.transform.scaleX,angle:i,align:this.textAlign,maxWidth:n})}},f.prototype.strokeText=function(t,e,r,n){if(isNaN(e)||isNaN(r)||"string"!=typeof t)throw tN.error("jsPDF.context2d.strokeText: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.strokeText");if(!g.call(this)){n=isNaN(n)?void 0:n;var i=M(this.ctx.transform.rotation);I.call(this,{text:t,x:e,y:r,scale:this.ctx.transform.scaleX,renderingMode:"stroke",angle:i,align:this.textAlign,maxWidth:n})}},f.prototype.measureText=function(t){if("string"!=typeof t)throw tN.error("jsPDF.context2d.measureText: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.measureText");var e=this.pdf,r=this.pdf.internal.scaleFactor,n=e.internal.getFontSize(),i=e.getStringUnitWidth(t)*n/e.internal.scaleFactor;return new function(t){var e=(t=t||{}).width||0;return Object.defineProperty(this,"width",{get:function(){return e}}),this}({width:i*=Math.round(96*r/72*1e4)/1e4})},f.prototype.scale=function(t,e){if(isNaN(t)||isNaN(e))throw tN.error("jsPDF.context2d.scale: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.scale");var r=new u(t,0,0,e,0,0);this.ctx.transform=this.ctx.transform.multiply(r)},f.prototype.rotate=function(t){if(isNaN(t))throw tN.error("jsPDF.context2d.rotate: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.rotate");var e=new u(Math.cos(t),Math.sin(t),-Math.sin(t),Math.cos(t),0,0);this.ctx.transform=this.ctx.transform.multiply(e)},f.prototype.translate=function(t,e){if(isNaN(t)||isNaN(e))throw tN.error("jsPDF.context2d.translate: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.translate");var r=new u(1,0,0,1,t,e);this.ctx.transform=this.ctx.transform.multiply(r)},f.prototype.transform=function(t,e,r,n,i,o){if(isNaN(t)||isNaN(e)||isNaN(r)||isNaN(n)||isNaN(i)||isNaN(o))throw tN.error("jsPDF.context2d.transform: Invalid arguments",arguments),Error("Invalid arguments passed to jsPDF.context2d.transform");var a=new u(t,e,r,n,i,o);this.ctx.transform=this.ctx.transform.multiply(a)},f.prototype.setTransform=function(t,e,r,n,i,o){t=isNaN(t)?1:t,e=isNaN(e)?0:e,r=isNaN(r)?0:r,n=isNaN(n)?1:n,i=isNaN(i)?0:i,o=isNaN(o)?0:o,this.ctx.transform=new u(t,e,r,n,i,o)};var m=function(){return this.margin[0]>0||this.margin[1]>0||this.margin[2]>0||this.margin[3]>0};f.prototype.drawImage=function(t,e,r,n,i,o,a,s,l){var c=this.pdf.getImageProperties(t),f=1,d=1,p=1,g=1;void 0!==n&&void 0!==s&&(p=s/n,g=l/i,f=c.width/n*s/n,d=c.height/i*l/i),void 0===o&&(o=e,a=r,e=0,r=0),void 0!==n&&void 0===s&&(s=n,l=i),void 0===n&&void 0===s&&(s=c.width,l=c.height);for(var v,x=this.ctx.transform.decompose(),A=M(x.rotate.shx),L=new u,N=(L=(L=(L=L.multiply(x.translate)).multiply(x.skew)).multiply(x.scale)).applyToRectangle(new h(o-e*p,a-r*g,n*f,i*d)),S=b.call(this,N),P=[],k=0;k<S.length;k+=1)-1===P.indexOf(S[k])&&P.push(S[k]);if(w(P),this.autoPaging)for(var I=P[0],C=P[P.length-1],E=I;E<C+1;E++){this.pdf.setPage(E);var F=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],O=1===E?this.posY+this.margin[0]:this.margin[0],T=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],j=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],B=1===E?0:T+(E-2)*j;if(0!==this.ctx.clip_path.length){var R=this.path;v=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=y(v,this.posX+this.margin[3],-B+O+this.ctx.prevPageLastElemOffset),_.call(this,"fill",!0),this.path=R}var D=JSON.parse(JSON.stringify(N));D=y([D],this.posX+this.margin[3],-B+O+this.ctx.prevPageLastElemOffset)[0];var q=(E>I||E<C)&&m.call(this);q&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],F,j,null).clip().discardPath()),this.pdf.addImage(t,"JPEG",D.x,D.y,D.w,D.h,null,null,A),q&&this.pdf.restoreGraphicsState()}else this.pdf.addImage(t,"JPEG",N.x,N.y,N.w,N.h,null,null,A)};var b=function(t,e,r){var n=[];e=e||this.pdf.internal.pageSize.width,r=r||this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2];var i=this.posY+this.ctx.prevPageLastElemOffset;switch(t.type){default:case"mt":case"lt":n.push(Math.floor((t.y+i)/r)+1);break;case"arc":n.push(Math.floor((t.y+i-t.radius)/r)+1),n.push(Math.floor((t.y+i+t.radius)/r)+1);break;case"qct":var o=j(this.ctx.lastPoint.x,this.ctx.lastPoint.y,t.x1,t.y1,t.x,t.y);n.push(Math.floor((o.y+i)/r)+1),n.push(Math.floor((o.y+o.h+i)/r)+1);break;case"bct":var a=B(this.ctx.lastPoint.x,this.ctx.lastPoint.y,t.x1,t.y1,t.x2,t.y2,t.x,t.y);n.push(Math.floor((a.y+i)/r)+1),n.push(Math.floor((a.y+a.h+i)/r)+1);break;case"rect":n.push(Math.floor((t.y+i)/r)+1),n.push(Math.floor((t.y+t.h+i)/r)+1)}for(var s=0;s<n.length;s+=1)for(;this.pdf.internal.getNumberOfPages()<n[s];)v.call(this);return n},v=function(){var t=this.fillStyle,e=this.strokeStyle,r=this.font,n=this.lineCap,i=this.lineWidth,o=this.lineJoin;this.pdf.addPage(),this.fillStyle=t,this.strokeStyle=e,this.font=r,this.lineCap=n,this.lineWidth=i,this.lineJoin=o},y=function(t,e,r){for(var n=0;n<t.length;n++)switch(t[n].type){case"bct":t[n].x2+=e,t[n].y2+=r;case"qct":t[n].x1+=e,t[n].y1+=r;default:t[n].x+=e,t[n].y+=r}return t},w=function(t){return t.sort(function(t,e){return t-e})},x=function(t,e){for(var r,n,i=this.fillStyle,o=this.strokeStyle,a=this.lineCap,s=this.lineWidth,h=Math.abs(s*this.ctx.transform.scaleX),u=this.lineJoin,l=JSON.parse(JSON.stringify(this.path)),c=JSON.parse(JSON.stringify(this.path)),f=[],d=0;d<c.length;d++)if(void 0!==c[d].x)for(var p=b.call(this,c[d]),g=0;g<p.length;g+=1)-1===f.indexOf(p[g])&&f.push(p[g]);for(var x=0;x<f.length;x++)for(;this.pdf.internal.getNumberOfPages()<f[x];)v.call(this);if(w(f),this.autoPaging)for(var A=f[0],L=f[f.length-1],N=A;N<L+1;N++){this.pdf.setPage(N),this.fillStyle=i,this.strokeStyle=o,this.lineCap=a,this.lineWidth=h,this.lineJoin=u;var S=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],P=1===N?this.posY+this.margin[0]:this.margin[0],k=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],I=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],C=1===N?0:k+(N-2)*I;if(0!==this.ctx.clip_path.length){var E=this.path;r=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=y(r,this.posX+this.margin[3],-C+P+this.ctx.prevPageLastElemOffset),_.call(this,t,!0),this.path=E}if(n=JSON.parse(JSON.stringify(l)),this.path=y(n,this.posX+this.margin[3],-C+P+this.ctx.prevPageLastElemOffset),!1===e||0===N){var F=(N>A||N<L)&&m.call(this);F&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],S,I,null).clip().discardPath()),_.call(this,t,e),F&&this.pdf.restoreGraphicsState()}this.lineWidth=s}else this.lineWidth=h,_.call(this,t,e),this.lineWidth=s;this.path=l},_=function(t,e){if(("stroke"!==t||e||!g.call(this))&&("stroke"===t||e||!p.call(this))){for(var r,n,i=[],o=this.path,a=0;a<o.length;a++){var s=o[a];switch(s.type){case"begin":i.push({begin:!0});break;case"close":i.push({close:!0});break;case"mt":i.push({start:s,deltas:[],abs:[]});break;case"lt":var h=i.length;if(o[a-1]&&!isNaN(o[a-1].x)&&(r=[s.x-o[a-1].x,s.y-o[a-1].y],h>0)){for(;h>=0;h--)if(!0!==i[h-1].close&&!0!==i[h-1].begin){i[h-1].deltas.push(r),i[h-1].abs.push(s);break}}break;case"bct":r=[s.x1-o[a-1].x,s.y1-o[a-1].y,s.x2-o[a-1].x,s.y2-o[a-1].y,s.x-o[a-1].x,s.y-o[a-1].y],i[i.length-1].deltas.push(r);break;case"qct":var u=o[a-1].x+2/3*(s.x1-o[a-1].x),l=o[a-1].y+2/3*(s.y1-o[a-1].y),c=s.x+2/3*(s.x1-s.x),f=s.y+2/3*(s.y1-s.y),d=s.x,m=s.y;r=[u-o[a-1].x,l-o[a-1].y,c-o[a-1].x,f-o[a-1].y,d-o[a-1].x,m-o[a-1].y],i[i.length-1].deltas.push(r);break;case"arc":i.push({deltas:[],abs:[],arc:!0}),Array.isArray(i[i.length-1].abs)&&i[i.length-1].abs.push(s)}}n=e?null:"stroke"===t?"stroke":"fill";for(var b=!1,v=0;v<i.length;v++)if(i[v].arc)for(var y=i[v].abs,w=0;w<y.length;w++){var x=y[w];"arc"===x.type?N.call(this,x.x,x.y,x.radius,x.startAngle,x.endAngle,x.counterclockwise,void 0,e,!b):C.call(this,x.x,x.y),b=!0}else if(!0===i[v].close)this.pdf.internal.out("h"),b=!1;else if(!0!==i[v].begin){var _=i[v].start.x,A=i[v].start.y;E.call(this,i[v].deltas,_,A),b=!0}n&&S.call(this,n),e&&P.call(this)}},A=function(t){var e=this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor,r=e*(this.pdf.internal.getLineHeightFactor()-1);switch(this.ctx.textBaseline){case"bottom":return t-r;case"top":return t+e-r;case"hanging":return t+e-2*r;case"middle":return t+e/2-r;default:return t}},L=function(t){return t+this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor*(this.pdf.internal.getLineHeightFactor()-1)};f.prototype.createLinearGradient=function(){var t=function(){};return t.colorStops=[],t.addColorStop=function(t,e){this.colorStops.push([t,e])},t.getColor=function(){return 0===this.colorStops.length?"#000000":this.colorStops[0][1]},t.isCanvasGradient=!0,t},f.prototype.createPattern=function(){return this.createLinearGradient()},f.prototype.createRadialGradient=function(){return this.createLinearGradient()};var N=function(t,e,r,n,i,o,a,s,h){for(var u=O.call(this,r,n,i,o),l=0;l<u.length;l++){var c=u[l];0===l&&(h?k.call(this,c.x1+t,c.y1+e):C.call(this,c.x1+t,c.y1+e)),F.call(this,t,e,c.x2,c.y2,c.x3,c.y3,c.x4,c.y4)}s?P.call(this):S.call(this,a)},S=function(t){switch(t){case"stroke":this.pdf.internal.out("S");break;case"fill":this.pdf.internal.out("f")}},P=function(){this.pdf.clip(),this.pdf.discardPath()},k=function(t,e){this.pdf.internal.out(r(t)+" "+n(e)+" m")},I=function(t){switch(t.align){case"right":case"end":l="right";break;case"center":l="center";break;default:l="left"}var e=this.pdf.getTextDimensions(t.text),r=A.call(this,t.y),n=L.call(this,r)-e.h,i=this.ctx.transform.applyToPoint(new s(t.x,r)),o=this.ctx.transform.decompose(),a=new u;a=(a=(a=a.multiply(o.translate)).multiply(o.skew)).multiply(o.scale);for(var l,c,f,d,p=this.ctx.transform.applyToRectangle(new h(t.x,r,e.w,e.h)),g=a.applyToRectangle(new h(t.x,n,e.w,e.h)),v=b.call(this,g),x=[],N=0;N<v.length;N+=1)-1===x.indexOf(v[N])&&x.push(v[N]);if(w(x),this.autoPaging)for(var S=x[0],P=x[x.length-1],k=S;k<P+1;k++){this.pdf.setPage(k);var I=1===k?this.posY+this.margin[0]:this.margin[0],C=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],E=this.pdf.internal.pageSize.height-this.margin[2],F=E-this.margin[0],O=this.pdf.internal.pageSize.width-this.margin[1],T=O-this.margin[3],M=1===k?0:C+(k-2)*F;if(0!==this.ctx.clip_path.length){var j=this.path;c=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=y(c,this.posX+this.margin[3],-1*M+I),_.call(this,"fill",!0),this.path=j}var B=y([JSON.parse(JSON.stringify(g))],this.posX+this.margin[3],-M+I+this.ctx.prevPageLastElemOffset)[0];t.scale>=.01&&(f=this.pdf.internal.getFontSize(),this.pdf.setFontSize(f*t.scale),d=this.lineWidth,this.lineWidth=d*t.scale);var R="text"!==this.autoPaging;if(R||B.y+B.h<=E){if(R||B.y>=I&&B.x<=O){var D=R?t.text:this.pdf.splitTextToSize(t.text,t.maxWidth||O-B.x)[0],q=y([JSON.parse(JSON.stringify(p))],this.posX+this.margin[3],-M+I+this.ctx.prevPageLastElemOffset)[0],U=R&&(k>S||k<P)&&m.call(this);U&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],T,F,null).clip().discardPath()),this.pdf.text(D,q.x,q.y,{angle:t.angle,align:l,renderingMode:t.renderingMode}),U&&this.pdf.restoreGraphicsState()}}else B.y<E&&(this.ctx.prevPageLastElemOffset+=E-B.y);t.scale>=.01&&(this.pdf.setFontSize(f),this.lineWidth=d)}else t.scale>=.01&&(f=this.pdf.internal.getFontSize(),this.pdf.setFontSize(f*t.scale),d=this.lineWidth,this.lineWidth=d*t.scale),this.pdf.text(t.text,i.x+this.posX,i.y+this.posY,{angle:t.angle,align:l,renderingMode:t.renderingMode,maxWidth:t.maxWidth}),t.scale>=.01&&(this.pdf.setFontSize(f),this.lineWidth=d)},C=function(t,e,i,o){i=i||0,o=o||0,this.pdf.internal.out(r(t+i)+" "+n(e+o)+" l")},E=function(t,e,r){return this.pdf.lines(t,e,r,null,null)},F=function(t,r,n,i,s,h,u,l){this.pdf.internal.out([e(o(n+t)),e(a(i+r)),e(o(s+t)),e(a(h+r)),e(o(u+t)),e(a(l+r)),"c"].join(" "))},O=function(t,e,r,n){for(var i=2*Math.PI,o=Math.PI/2;e>r;)e-=i;var a=Math.abs(r-e);a<i&&n&&(a=i-a);for(var s=[],h=n?-1:1,u=e;a>1e-5;){var l=u+h*Math.min(a,o);s.push(T.call(this,t,u,l)),a-=Math.abs(l-u),u=l}return s},T=function(t,e,r){var n=(r-e)/2,i=t*Math.cos(n),o=t*Math.sin(n),a=-o,s=i*i+a*a,h=s+i*i+a*o,u=4/3*(Math.sqrt(2*s*h)-h)/(i*o-a*i),l=i-u*a,c=a+u*i,f=-c,d=n+e,p=Math.cos(d),g=Math.sin(d);return{x1:t*Math.cos(e),y1:t*Math.sin(e),x2:l*p-c*g,y2:l*g+c*p,x3:l*p-f*g,y3:l*g+f*p,x4:t*Math.cos(r),y4:t*Math.sin(r)}},M=function(t){return 180*t/Math.PI},j=function(t,e,r,n,i,o){var a=t+.5*(r-t),s=e+.5*(n-e),u=i+.5*(r-i),l=o+.5*(n-o),c=Math.min(t,i,a,u),f=Math.min(e,o,s,l);return new h(c,f,Math.max(t,i,a,u)-c,Math.max(e,o,s,l)-f)},B=function(t,e,r,n,i,o,a,s){var u,l,c,f,d,p,g,m,b,v,y,w,x,_,A=r-t,L=n-e,N=i-r,S=o-n,P=a-i,k=s-o;for(l=0;l<41;l++)b=(g=(c=t+(u=l/40)*A)+u*((d=r+u*N)-c))+u*(d+u*(i+u*P-d)-g),v=(m=(f=e+u*L)+u*((p=n+u*S)-f))+u*(p+u*(o+u*k-p)-m),0==l?(y=b,w=v,x=b,_=v):(y=Math.min(y,b),w=Math.min(w,v),x=Math.max(x,b),_=Math.max(_,v));return new h(Math.round(y),Math.round(w),Math.round(x-y),Math.round(_-w))},R=function(){if(this.prevLineDash||this.ctx.lineDash.length||this.ctx.lineDashOffset){var t=JSON.stringify({lineDash:this.ctx.lineDash,lineDashOffset:this.ctx.lineDashOffset});this.prevLineDash!==t&&(this.pdf.setLineDash(this.ctx.lineDash,this.ctx.lineDashOffset),this.prevLineDash=t)}}}(t1.API),function(t){var e=function(t){var e,r,n,i,o,a,s,h,u,l;for(/[^\x00-\xFF]/.test(t),r=[],n=0,i=(t+=e="\0\0\0\0".slice(t.length%4||4)).length;i>n;n+=4)0!==(o=(t.charCodeAt(n)<<24)+(t.charCodeAt(n+1)<<16)+(t.charCodeAt(n+2)<<8)+t.charCodeAt(n+3))?(a=(o=((o=((o=((o=(o-(l=o%85))/85)-(u=o%85))/85)-(h=o%85))/85)-(s=o%85))/85)%85,r.push(a+33,s+33,h+33,u+33,l+33)):r.push(122);return function(t,e){for(var r=e;r>0;r--)t.pop()}(r,e.length),String.fromCharCode.apply(String,r)+"~>"},r=function(t){var e,r,n,i,o,a=String,s="length",h="charCodeAt",u="slice",l="replace";for(t[u](-2),t=t[u](0,-2)[l](/\s/g,"")[l]("z","!!!!!"),n=[],i=0,o=(t+=e="uuuuu"[u](t[s]%5||5))[s];o>i;i+=5)r=0x31c84b1*(t[h](i)-33)+614125*(t[h](i+1)-33)+7225*(t[h](i+2)-33)+85*(t[h](i+3)-33)+(t[h](i+4)-33),n.push(255&r>>24,255&r>>16,255&r>>8,255&r);return function(t,e){for(var r=e;r>0;r--)t.pop()}(n,e[s]),a.fromCharCode.apply(a,n)},n=function(t){var e=new RegExp(/^([0-9A-Fa-f]{2})+$/);if(-1!==(t=t.replace(/\s/g,"")).indexOf(">")&&(t=t.substr(0,t.indexOf(">"))),t.length%2&&(t+="0"),!1===e.test(t))return"";for(var r="",n=0;n<t.length;n+=2)r+=String.fromCharCode("0x"+(t[n]+t[n+1]));return r},i=function(t){for(var e=new Uint8Array(t.length),r=t.length;r--;)e[r]=t.charCodeAt(r);return t=(e=tb(e)).reduce(function(t,e){return t+String.fromCharCode(e)},"")};t.processDataByFilters=function(t,o){var a=0,s=t||"",h=[];for("string"==typeof(o=o||[])&&(o=[o]),a=0;a<o.length;a+=1)switch(o[a]){case"ASCII85Decode":case"/ASCII85Decode":s=r(s),h.push("/ASCII85Encode");break;case"ASCII85Encode":case"/ASCII85Encode":s=e(s),h.push("/ASCII85Decode");break;case"ASCIIHexDecode":case"/ASCIIHexDecode":s=n(s),h.push("/ASCIIHexEncode");break;case"ASCIIHexEncode":case"/ASCIIHexEncode":s=s.split("").map(function(t){return("0"+t.charCodeAt().toString(16)).slice(-2)}).join("")+">",h.push("/ASCIIHexDecode");break;case"FlateEncode":case"/FlateEncode":s=i(s),h.push("/FlateDecode");break;default:throw Error('The filter: "'+o[a]+'" is not implemented')}return{data:s,reverseChain:h.reverse().join(" ")}}}(t1.API),function(t){t.loadFile=function(t,e,r){var n=e,i=r;n=!1!==n,i="function"==typeof i?i:function(){};var o=void 0;try{o=function(t,e,r){var n=new XMLHttpRequest,i=0,o=function(t){var e=t.length,r=[],n=String.fromCharCode;for(i=0;i<e;i+=1)r.push(n(255&t.charCodeAt(i)));return r.join("")};if(n.open("GET",t,!e),n.overrideMimeType("text/plain; charset=x-user-defined"),!1===e&&(n.onload=function(){200===n.status?r(o(this.responseText)):r(void 0)}),n.send(null),e&&200===n.status)return o(n.responseText)}(t,n,i)}catch(t){}return o},t.loadImageFile=t.loadFile}(t1.API),function(t){function e(){return(tA.html2canvas?Promise.resolve(tA.html2canvas):r.e(5846).then(r.bind(r,65846))).catch(function(t){return Promise.reject(Error("Could not load html2canvas: "+t))}).then(function(t){return t.default?t.default:t})}function n(){return(tA.DOMPurify?Promise.resolve(tA.DOMPurify):r.e(9296).then(r.bind(r,39296))).catch(function(t){return Promise.reject(Error("Could not load dompurify: "+t))}).then(function(t){return t.default?t.default:t})}var o=function(t){var e=i()(t);return"undefined"===e?"undefined":"string"===e||t instanceof String?"string":"number"===e||t instanceof Number?"number":"function"===e||t instanceof Function?"function":t&&t.constructor===Array?"array":t&&1===t.nodeType?"element":"object"===e?"object":"unknown"},a=function(t,e){var r=document.createElement(t);for(var n in e.className&&(r.className=e.className),e.innerHTML&&e.dompurify&&(r.innerHTML=e.dompurify.sanitize(e.innerHTML)),e.style)r.style[n]=e.style[n];return r},s=function t(e){var r=Object.assign(t.convert(Promise.resolve()),JSON.parse(JSON.stringify(t.template))),n=t.convert(Promise.resolve(),r);return(n=n.setProgress(1,t,1,[t])).set(e)};(s.prototype=Object.create(Promise.prototype)).constructor=s,s.convert=function(t,e){return t.__proto__=e||s.prototype,t},s.template={prop:{src:null,container:null,overlay:null,canvas:null,img:null,pdf:null,pageSize:null,callback:function(){}},progress:{val:0,state:null,n:0,stack:[]},opt:{filename:"file.pdf",margin:[0,0,0,0],enableLinks:!0,x:0,y:0,html2canvas:{},jsPDF:{},backgroundColor:"transparent"}},s.prototype.from=function(t,e){return this.then(function(){switch(e=e||function(t){switch(o(t)){case"string":return"string";case"element":return"canvas"===t.nodeName.toLowerCase()?"canvas":"element";default:return"unknown"}}(t)){case"string":return this.then(n).then(function(e){return this.set({src:a("div",{innerHTML:t,dompurify:e})})});case"element":return this.set({src:t});case"canvas":return this.set({canvas:t});case"img":return this.set({img:t});default:return this.error("Unknown source type.")}})},s.prototype.to=function(t){switch(t){case"container":return this.toContainer();case"canvas":return this.toCanvas();case"img":return this.toImg();case"pdf":return this.toPdf();default:return this.error("Invalid target.")}},s.prototype.toContainer=function(){return this.thenList([function(){return this.prop.src||this.error("Cannot duplicate - no source HTML.")},function(){return this.prop.pageSize||this.setPageSize()}]).then(function(){var t={position:"relative",display:"inline-block",width:("number"!=typeof this.opt.width||isNaN(this.opt.width)||"number"!=typeof this.opt.windowWidth||isNaN(this.opt.windowWidth)?Math.max(this.prop.src.clientWidth,this.prop.src.scrollWidth,this.prop.src.offsetWidth):this.opt.windowWidth)+"px",left:0,right:0,top:0,margin:"auto",backgroundColor:this.opt.backgroundColor},e=function t(e,r){for(var n=3===e.nodeType?document.createTextNode(e.nodeValue):e.cloneNode(!1),i=e.firstChild;i;i=i.nextSibling)!0!==r&&1===i.nodeType&&"SCRIPT"===i.nodeName||n.appendChild(t(i,r));return 1===e.nodeType&&("CANVAS"===e.nodeName?(n.width=e.width,n.height=e.height,n.getContext("2d").drawImage(e,0,0)):"TEXTAREA"!==e.nodeName&&"SELECT"!==e.nodeName||(n.value=e.value),n.addEventListener("load",function(){n.scrollTop=e.scrollTop,n.scrollLeft=e.scrollLeft},!0)),n}(this.prop.src,this.opt.html2canvas.javascriptEnabled);"BODY"===e.tagName&&(t.height=Math.max(document.body.scrollHeight,document.body.offsetHeight,document.documentElement.clientHeight,document.documentElement.scrollHeight,document.documentElement.offsetHeight)+"px"),this.prop.overlay=a("div",{className:"html2pdf__overlay",style:{position:"fixed",overflow:"hidden",zIndex:1e3,left:"-100000px",right:0,bottom:0,top:0}}),this.prop.container=a("div",{className:"html2pdf__container",style:t}),this.prop.container.appendChild(e),this.prop.container.firstChild.appendChild(a("div",{style:{clear:"both",border:"0 none transparent",margin:0,padding:0,height:0}})),this.prop.container.style.float="none",this.prop.overlay.appendChild(this.prop.container),document.body.appendChild(this.prop.overlay),this.prop.container.firstChild.style.position="relative",this.prop.container.height=Math.max(this.prop.container.firstChild.clientHeight,this.prop.container.firstChild.scrollHeight,this.prop.container.firstChild.offsetHeight)+"px"})},s.prototype.toCanvas=function(){var t=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(t).then(e).then(function(t){var e=Object.assign({},this.opt.html2canvas);return delete e.onrendered,t(this.prop.container,e)}).then(function(t){(this.opt.html2canvas.onrendered||function(){})(t),this.prop.canvas=t,document.body.removeChild(this.prop.overlay)})},s.prototype.toContext2d=function(){var t=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(t).then(e).then(function(t){var e=this.opt.jsPDF,r=this.opt.fontFaces,n=Object.assign({async:!0,allowTaint:!0,scale:"number"!=typeof this.opt.width||isNaN(this.opt.width)||"number"!=typeof this.opt.windowWidth||isNaN(this.opt.windowWidth)?1:this.opt.width/this.opt.windowWidth,scrollX:this.opt.scrollX||0,scrollY:this.opt.scrollY||0,backgroundColor:"#ffffff",imageTimeout:15e3,logging:!0,proxy:null,removeContainer:!0,foreignObjectRendering:!1,useCORS:!1},this.opt.html2canvas);if(delete n.onrendered,e.context2d.autoPaging=void 0===this.opt.autoPaging||this.opt.autoPaging,e.context2d.posX=this.opt.x,e.context2d.posY=this.opt.y,e.context2d.margin=this.opt.margin,e.context2d.fontFaces=r,r)for(var i=0;i<r.length;++i){var o=r[i],a=o.src.find(function(t){return"truetype"===t.format});a&&e.addFont(a.url,o.ref.name,o.ref.style)}return n.windowHeight=n.windowHeight||0,n.windowHeight=0==n.windowHeight?Math.max(this.prop.container.clientHeight,this.prop.container.scrollHeight,this.prop.container.offsetHeight):n.windowHeight,e.context2d.save(!0),t(this.prop.container,n)}).then(function(t){this.opt.jsPDF.context2d.restore(!0),(this.opt.html2canvas.onrendered||function(){})(t),this.prop.canvas=t,document.body.removeChild(this.prop.overlay)})},s.prototype.toImg=function(){return this.thenList([function(){return this.prop.canvas||this.toCanvas()}]).then(function(){var t=this.prop.canvas.toDataURL("image/"+this.opt.image.type,this.opt.image.quality);this.prop.img=document.createElement("img"),this.prop.img.src=t})},s.prototype.toPdf=function(){return this.thenList([function(){return this.toContext2d()}]).then(function(){this.prop.pdf=this.prop.pdf||this.opt.jsPDF})},s.prototype.output=function(t,e,r){return"img"===(r=r||"pdf").toLowerCase()||"image"===r.toLowerCase()?this.outputImg(t,e):this.outputPdf(t,e)},s.prototype.outputPdf=function(t,e){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){return this.prop.pdf.output(t,e)})},s.prototype.outputImg=function(t){return this.thenList([function(){return this.prop.img||this.toImg()}]).then(function(){switch(t){case void 0:case"img":return this.prop.img;case"datauristring":case"dataurlstring":return this.prop.img.src;case"datauri":case"dataurl":return document.location.href=this.prop.img.src;default:throw'Image output type "'+t+'" is not supported.'}})},s.prototype.save=function(t){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).set(t?{filename:t}:null).then(function(){this.prop.pdf.save(this.opt.filename)})},s.prototype.doCallback=function(){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){this.prop.callback(this.prop.pdf)})},s.prototype.set=function(t){if("object"!==o(t))return this;var e=Object.keys(t||{}).map(function(e){if(e in s.template.prop)return function(){this.prop[e]=t[e]};switch(e){case"margin":return this.setMargin.bind(this,t.margin);case"jsPDF":return function(){return this.opt.jsPDF=t.jsPDF,this.setPageSize()};case"pageSize":return this.setPageSize.bind(this,t.pageSize);default:return function(){this.opt[e]=t[e]}}},this);return this.then(function(){return this.thenList(e)})},s.prototype.get=function(t,e){return this.then(function(){var r=t in s.template.prop?this.prop[t]:this.opt[t];return e?e(r):r})},s.prototype.setMargin=function(t){return this.then(function(){switch(o(t)){case"number":t=[t,t,t,t];case"array":if(2===t.length&&(t=[t[0],t[1],t[0],t[1]]),4===t.length)break;default:return this.error("Invalid margin array.")}this.opt.margin=t}).then(this.setPageSize)},s.prototype.setPageSize=function(t){function e(t,e){return Math.floor(t*e/72*96)}return this.then(function(){(t=t||t1.getPageSize(this.opt.jsPDF)).hasOwnProperty("inner")||(t.inner={width:t.width-this.opt.margin[1]-this.opt.margin[3],height:t.height-this.opt.margin[0]-this.opt.margin[2]},t.inner.px={width:e(t.inner.width,t.k),height:e(t.inner.height,t.k)},t.inner.ratio=t.inner.height/t.inner.width),this.prop.pageSize=t})},s.prototype.setProgress=function(t,e,r,n){return null!=t&&(this.progress.val=t),null!=e&&(this.progress.state=e),null!=r&&(this.progress.n=r),null!=n&&(this.progress.stack=n),this.progress.ratio=this.progress.val/this.progress.state,this},s.prototype.updateProgress=function(t,e,r,n){return this.setProgress(t?this.progress.val+t:null,e||null,r?this.progress.n+r:null,n?this.progress.stack.concat(n):null)},s.prototype.then=function(t,e){var r=this;return this.thenCore(t,e,function(t,e){return r.updateProgress(null,null,1,[t]),Promise.prototype.then.call(this,function(e){return r.updateProgress(null,t),e}).then(t,e).then(function(t){return r.updateProgress(1),t})})},s.prototype.thenCore=function(t,e,r){r=r||Promise.prototype.then,t&&(t=t.bind(this)),e&&(e=e.bind(this));var n=-1!==Promise.toString().indexOf("[native code]")&&"Promise"===Promise.name?this:s.convert(Object.assign({},this),Promise.prototype),i=r.call(n,t,e);return s.convert(i,this.__proto__)},s.prototype.thenExternal=function(t,e){return Promise.prototype.then.call(this,t,e)},s.prototype.thenList=function(t){var e=this;return t.forEach(function(t){e=e.thenCore(t)}),e},s.prototype.catch=function(t){t&&(t=t.bind(this));var e=Promise.prototype.catch.call(this,t);return s.convert(e,this)},s.prototype.catchExternal=function(t){return Promise.prototype.catch.call(this,t)},s.prototype.error=function(t){return this.then(function(){throw Error(t)})},s.prototype.using=s.prototype.set,s.prototype.saveAs=s.prototype.save,s.prototype.export=s.prototype.output,s.prototype.run=s.prototype.then,t1.getPageSize=function(t,e,r){if("object"===i()(t)){var n=t;t=n.orientation,e=n.unit||e,r=n.format||r}e=e||"mm",r=r||"a4",t=(""+(t||"P")).toLowerCase();var o,a=(""+r).toLowerCase(),s={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};switch(e){case"pt":o=1;break;case"mm":o=72/25.4;break;case"cm":o=72/2.54;break;case"in":o=72;break;case"px":o=.75;break;case"pc":case"em":o=12;break;case"ex":o=6;break;default:throw"Invalid unit: "+e}var h,u=0,l=0;if(s.hasOwnProperty(a))u=s[a][1]/o,l=s[a][0]/o;else try{u=r[1],l=r[0]}catch(t){throw Error("Invalid format: "+r)}if("p"===t||"portrait"===t)t="p",l>u&&(h=l,l=u,u=h);else{if("l"!==t&&"landscape"!==t)throw"Invalid orientation: "+t;t="l",u>l&&(h=l,l=u,u=h)}return{width:l,height:u,unit:e,k:o,orientation:t}},t.html=function(t,e){(e=e||{}).callback=e.callback||function(){},e.html2canvas=e.html2canvas||{},e.html2canvas.canvas=e.html2canvas.canvas||this.canvas,e.jsPDF=e.jsPDF||this,e.fontFaces=e.fontFaces?e.fontFaces.map(eD):null;var r=new s(e);return e.worker?r:r.from(t).doCallback()}}(t1.API),t1.API.addJS=function(t){return eY=t,this.internal.events.subscribe("postPutResources",function(){eV=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/Names [(EmbeddedJS) "+(eV+1)+" 0 R]"),this.internal.out(">>"),this.internal.out("endobj"),eG=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /JavaScript"),this.internal.out("/JS ("+eY+")"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){void 0!==eV&&void 0!==eG&&this.internal.out("/Names <</JavaScript "+eV+" 0 R>>")}),this},function(t){var e;t.events.push(["postPutResources",function(){var t=/^(\d+) 0 obj$/;if(this.outline.root.children.length>0)for(var r=this.outline.render().split(/\r\n/),n=0;n<r.length;n++){var i=r[n],o=t.exec(i);if(null!=o){var a=o[1];this.internal.newObjectDeferredBegin(a,!1)}this.internal.write(i)}if(this.outline.createNamedDestinations){var s=this.internal.pages.length,h=[];for(n=0;n<s;n++){var u=this.internal.newObject();h.push(u);var l=this.internal.getPageInfo(n+1);this.internal.write("<< /D["+l.objId+" 0 R /XYZ null null null]>> endobj")}var c=this.internal.newObject();for(this.internal.write("<< /Names [ "),n=0;n<h.length;n++)this.internal.write("(page_"+(n+1)+")"+h[n]+" 0 R");this.internal.write(" ] >>","endobj"),e=this.internal.newObject(),this.internal.write("<< /Dests "+c+" 0 R"),this.internal.write(">>","endobj")}}]),t.events.push(["putCatalog",function(){this.outline.root.children.length>0&&(this.internal.write("/Outlines",this.outline.makeRef(this.outline.root)),this.outline.createNamedDestinations&&this.internal.write("/Names "+e+" 0 R"))}]),t.events.push(["initialized",function(){var t=this;t.outline={createNamedDestinations:!1,root:{children:[]}},t.outline.add=function(t,e,r){var n={title:e,options:r,children:[]};return null==t&&(t=this.root),t.children.push(n),n},t.outline.render=function(){return this.ctx={},this.ctx.val="",this.ctx.pdf=t,this.genIds_r(this.root),this.renderRoot(this.root),this.renderItems(this.root),this.ctx.val},t.outline.genIds_r=function(e){e.id=t.internal.newObjectDeferred();for(var r=0;r<e.children.length;r++)this.genIds_r(e.children[r])},t.outline.renderRoot=function(t){this.objStart(t),this.line("/Type /Outlines"),t.children.length>0&&(this.line("/First "+this.makeRef(t.children[0])),this.line("/Last "+this.makeRef(t.children[t.children.length-1]))),this.line("/Count "+this.count_r({count:0},t)),this.objEnd()},t.outline.renderItems=function(e){for(var r=this.ctx.pdf.internal.getVerticalCoordinateString,n=0;n<e.children.length;n++){var i=e.children[n];this.objStart(i),this.line("/Title "+this.makeString(i.title)),this.line("/Parent "+this.makeRef(e)),n>0&&this.line("/Prev "+this.makeRef(e.children[n-1])),n<e.children.length-1&&this.line("/Next "+this.makeRef(e.children[n+1])),i.children.length>0&&(this.line("/First "+this.makeRef(i.children[0])),this.line("/Last "+this.makeRef(i.children[i.children.length-1])));var o=this.count=this.count_r({count:0},i);if(o>0&&this.line("/Count "+o),i.options&&i.options.pageNumber){var a=t.internal.getPageInfo(i.options.pageNumber);this.line("/Dest ["+a.objId+" 0 R /XYZ 0 "+r(0)+" 0]")}this.objEnd()}for(var s=0;s<e.children.length;s++)this.renderItems(e.children[s])},t.outline.line=function(t){this.ctx.val+=t+"\r\n"},t.outline.makeRef=function(t){return t.id+" 0 R"},t.outline.makeString=function(e){return"("+t.internal.pdfEscape(e)+")"},t.outline.objStart=function(t){this.ctx.val+="\r\n"+t.id+" 0 obj\r\n<<\r\n"},t.outline.objEnd=function(){this.ctx.val+=">> \r\nendobj\r\n"},t.outline.count_r=function(t,e){for(var r=0;r<e.children.length;r++)t.count++,this.count_r(t,e.children[r]);return t.count}}])}(t1.API),function(t){var e=[192,193,194,195,196,197,198,199];t.processJPEG=function(t,r,n,i,o,a){var s,h=this.decode.DCT_DECODE,u=null;if("string"==typeof t||this.__addimage__.isArrayBuffer(t)||this.__addimage__.isArrayBufferView(t)){switch(t=o||t,t=this.__addimage__.isArrayBuffer(t)?new Uint8Array(t):t,(s=function(t){for(var r,n=256*t.charCodeAt(4)+t.charCodeAt(5),i=t.length,o={width:0,height:0,numcomponents:1},a=4;a<i;a+=2){if(a+=n,-1!==e.indexOf(t.charCodeAt(a+1))){r=256*t.charCodeAt(a+5)+t.charCodeAt(a+6),o={width:256*t.charCodeAt(a+7)+t.charCodeAt(a+8),height:r,numcomponents:t.charCodeAt(a+9)};break}n=256*t.charCodeAt(a+2)+t.charCodeAt(a+3)}return o}(t=this.__addimage__.isArrayBufferView(t)?this.__addimage__.arrayBufferToBinaryString(t):t)).numcomponents){case 1:a=this.color_spaces.DEVICE_GRAY;break;case 4:a=this.color_spaces.DEVICE_CMYK;break;case 3:a=this.color_spaces.DEVICE_RGB}u={data:t,width:s.width,height:s.height,colorSpace:a,bitsPerComponent:8,filter:h,index:r,alias:n}}return u}}(t1.API);var eK,eX,eZ,e$,eQ,e1=function(){function t(t){var e,r,n,i,o,a,s,h,u,l,c,f,d,p;for(this.data=t,this.pos=8,this.palette=[],this.imgData=[],this.transparency={},this.animation=null,this.text={},a=null;;){switch(e=this.readUInt32(),u=(function(){var t,e;for(e=[],t=0;t<4;++t)e.push(String.fromCharCode(this.data[this.pos++]));return e}).call(this).join("")){case"IHDR":this.width=this.readUInt32(),this.height=this.readUInt32(),this.bits=this.data[this.pos++],this.colorType=this.data[this.pos++],this.compressionMethod=this.data[this.pos++],this.filterMethod=this.data[this.pos++],this.interlaceMethod=this.data[this.pos++];break;case"acTL":this.animation={numFrames:this.readUInt32(),numPlays:this.readUInt32()||1/0,frames:[]};break;case"PLTE":this.palette=this.read(e);break;case"fcTL":a&&this.animation.frames.push(a),this.pos+=4,a={width:this.readUInt32(),height:this.readUInt32(),xOffset:this.readUInt32(),yOffset:this.readUInt32()},o=this.readUInt16(),i=this.readUInt16()||100,a.delay=1e3*o/i,a.disposeOp=this.data[this.pos++],a.blendOp=this.data[this.pos++],a.data=[];break;case"IDAT":case"fdAT":for("fdAT"===u&&(this.pos+=4,e-=4),t=(null!=a?a.data:void 0)||this.imgData,f=0;0<=e?f<e:f>e;0<=e?++f:--f)t.push(this.data[this.pos++]);break;case"tRNS":switch(this.transparency={},this.colorType){case 3:if(n=this.palette.length/3,this.transparency.indexed=this.read(e),this.transparency.indexed.length>n)throw Error("More transparent colors than palette size");if((l=n-this.transparency.indexed.length)>0)for(d=0;0<=l?d<l:d>l;0<=l?++d:--d)this.transparency.indexed.push(255);break;case 0:this.transparency.grayscale=this.read(e)[0];break;case 2:this.transparency.rgb=this.read(e)}break;case"tEXt":s=(c=this.read(e)).indexOf(0),h=String.fromCharCode.apply(String,c.slice(0,s)),this.text[h]=String.fromCharCode.apply(String,c.slice(s+1));break;case"IEND":return a&&this.animation.frames.push(a),this.colors=(function(){switch(this.colorType){case 0:case 3:case 4:return 1;case 2:case 6:return 3}}).call(this),this.hasAlphaChannel=4===(p=this.colorType)||6===p,r=this.colors+ +!!this.hasAlphaChannel,this.pixelBitlength=this.bits*r,this.colorSpace=(function(){switch(this.colors){case 1:return"DeviceGray";case 3:return"DeviceRGB"}}).call(this),void(this.imgData=new Uint8Array(this.imgData));default:this.pos+=e}if(this.pos+=4,this.pos>this.data.length)throw Error("Incomplete or corrupt PNG file")}}t.prototype.read=function(t){var e,r;for(r=[],e=0;0<=t?e<t:e>t;0<=t?++e:--e)r.push(this.data[this.pos++]);return r},t.prototype.readUInt32=function(){return this.data[this.pos++]<<24|this.data[this.pos++]<<16|this.data[this.pos++]<<8|this.data[this.pos++]},t.prototype.readUInt16=function(){return this.data[this.pos++]<<8|this.data[this.pos++]},t.prototype.decodePixels=function(t){var e,r,n=this.pixelBitlength/8,i=new Uint8Array(this.width*this.height*n),o=0,a=this;if(null==t&&(t=this.imgData),0===t.length)return new Uint8Array(0);function s(e,r,s,h){var u,l,c,f,d,p,g,m,b,v,y,w,x,_,A,L,N,S,P,k,I,C=Math.ceil((a.width-e)/s),E=Math.ceil((a.height-r)/h),F=a.width==C&&a.height==E;for(_=n*C,w=F?i:new Uint8Array(_*E),p=t.length,x=0,l=0;x<E&&o<p;){switch(t[o++]){case 0:for(f=N=0;N<_;f=N+=1)w[l++]=t[o++];break;case 1:for(f=S=0;S<_;f=S+=1)u=t[o++],d=f<n?0:w[l-n],w[l++]=(u+d)%256;break;case 2:for(f=P=0;P<_;f=P+=1)u=t[o++],c=(f-f%n)/n,A=x&&w[(x-1)*_+c*n+f%n],w[l++]=(A+u)%256;break;case 3:for(f=k=0;k<_;f=k+=1)u=t[o++],c=(f-f%n)/n,d=f<n?0:w[l-n],A=x&&w[(x-1)*_+c*n+f%n],w[l++]=(u+Math.floor((d+A)/2))%256;break;case 4:for(f=I=0;I<_;f=I+=1)u=t[o++],c=(f-f%n)/n,d=f<n?0:w[l-n],0===x?A=L=0:(A=w[(x-1)*_+c*n+f%n],L=c&&w[(x-1)*_+(c-1)*n+f%n]),m=Math.abs((g=d+A-L)-d),v=Math.abs(g-A),y=Math.abs(g-L),b=m<=v&&m<=y?d:v<=y?A:L,w[l++]=(u+b)%256;break;default:throw Error("Invalid filter algorithm: "+t[o-1])}if(!F){var O=((r+x*h)*a.width+e)*n,T=x*_;for(f=0;f<C;f+=1){for(var M=0;M<n;M+=1)i[O++]=w[T++];O+=(s-1)*n}}x++}}return t=j((e=t).subarray(tp(e,void 0),-4),{i:2},r&&r.out,r&&r.dictionary),1==a.interlaceMethod?(s(0,0,8,8),s(4,0,8,8),s(0,4,4,8),s(2,0,4,4),s(0,2,2,4),s(1,0,2,2),s(0,1,1,2)):s(0,0,1,1),i},t.prototype.decodePalette=function(){var t,e,r,n,i,o,a,s,h;for(r=this.palette,i=new Uint8Array(((o=this.transparency.indexed||[]).length||0)+r.length),n=0,t=0,e=a=0,s=r.length;a<s;e=a+=3)i[n++]=r[e],i[n++]=r[e+1],i[n++]=r[e+2],i[n++]=null!=(h=o[t++])?h:255;return i},t.prototype.copyToImageData=function(t,e){var r,n,i,o,a,s,h,u,l,c,f;if(n=this.colors,l=null,r=this.hasAlphaChannel,this.palette.length&&(l=null!=(f=this._decodedPalette)?f:this._decodedPalette=this.decodePalette(),n=4,r=!0),u=(i=t.data||t).length,a=l||e,o=s=0,1===n)for(;o<u;)h=l?4*e[o/4]:s,c=a[h++],i[o++]=c,i[o++]=c,i[o++]=c,i[o++]=r?a[h++]:255,s=h;else for(;o<u;)h=l?4*e[o/4]:s,i[o++]=a[h++],i[o++]=a[h++],i[o++]=a[h++],i[o++]=r?a[h++]:255,s=h},t.prototype.decode=function(){var t;return t=new Uint8Array(this.width*this.height*4),this.copyToImageData(t,this.decodePixels()),t};var e,r,n,i=function(){if("[object Window]"===Object.prototype.toString.call(tA)){try{n=(r=tA.document.createElement("canvas")).getContext("2d")}catch(t){return!1}return!0}return!1};return i(),e=function(t){var e;if(!0===i())return n.width=t.width,n.height=t.height,n.clearRect(0,0,t.width,t.height),n.putImageData(t,0,0),(e=new Image).src=r.toDataURL(),e;throw Error("This method requires a Browser with Canvas-capability.")},t.prototype.decodeFrames=function(t){var r,n,i,o,a,s,h,u;if(this.animation){for(u=[],n=a=0,s=(h=this.animation.frames).length;a<s;n=++a)r=h[n],i=t.createImageData(r.width,r.height),o=this.decodePixels(new Uint8Array(r.data)),this.copyToImageData(i,o),r.imageData=i,u.push(r.image=e(i));return u}},t.prototype.renderFrame=function(t,e){var r,n,i;return r=(n=this.animation.frames)[e],i=n[e-1],0===e&&t.clearRect(0,0,this.width,this.height),1===(null!=i?i.disposeOp:void 0)?t.clearRect(i.xOffset,i.yOffset,i.width,i.height):2===(null!=i?i.disposeOp:void 0)&&t.putImageData(i.imageData,i.xOffset,i.yOffset),0===r.blendOp&&t.clearRect(r.xOffset,r.yOffset,r.width,r.height),t.drawImage(r.image,r.xOffset,r.yOffset)},t.prototype.animate=function(t){var e,r,n,i,o,a,s=this;return r=0,i=(a=this.animation).numFrames,n=a.frames,o=a.numPlays,(e=function(){var a,h;if(h=n[a=r++%i],s.renderFrame(t,a),i>1&&r/i<o)return s.animation._timeout=setTimeout(e,h.delay)})()},t.prototype.stopAnimation=function(){var t;return clearTimeout(null!=(t=this.animation)?t._timeout:void 0)},t.prototype.render=function(t){var e,r;return t._png&&t._png.stopAnimation(),t._png=this,t.width=this.width,t.height=this.height,e=t.getContext("2d"),this.animation?(this.decodeFrames(e),this.animate(e)):(r=e.createImageData(this.width,this.height),this.copyToImageData(r,this.decodePixels()),e.putImageData(r,0,0))},t}();function e2(t){var e=0;if(71!==t[e++]||73!==t[e++]||70!==t[e++]||56!==t[e++]||56!=(t[e++]+1&253)||97!==t[e++])throw Error("Invalid GIF 87a/89a header.");var r=t[e++]|t[e++]<<8,n=t[e++]|t[e++]<<8,i=t[e++],o=1<<(7&i)+1;t[e++],t[e++];var a=null,s=null;i>>7&&(a=e,s=o,e+=3*o);var h=!0,u=[],l=0,c=null,f=0,d=null;for(this.width=r,this.height=n;h&&e<t.length;)switch(t[e++]){case 33:switch(t[e++]){case 255:if(11!==t[e]||78==t[e+1]&&69==t[e+2]&&84==t[e+3]&&83==t[e+4]&&67==t[e+5]&&65==t[e+6]&&80==t[e+7]&&69==t[e+8]&&50==t[e+9]&&46==t[e+10]&&48==t[e+11]&&3==t[e+12]&&1==t[e+13]&&0==t[e+16])e+=14,d=t[e++]|t[e++]<<8,e++;else for(e+=12;;){if(!((g=t[e++])>=0))throw Error("Invalid block size");if(0===g)break;e+=g}break;case 249:if(4!==t[e++]||0!==t[e+4])throw Error("Invalid graphics extension block.");var p=t[e++];l=t[e++]|t[e++]<<8,c=t[e++],0==(1&p)&&(c=null),f=p>>2&7,e++;break;case 254:for(;;){if(!((g=t[e++])>=0))throw Error("Invalid block size");if(0===g)break;e+=g}break;default:throw Error("Unknown graphic control label: 0x"+t[e-1].toString(16))}break;case 44:var g,m=t[e++]|t[e++]<<8,b=t[e++]|t[e++]<<8,v=t[e++]|t[e++]<<8,y=t[e++]|t[e++]<<8,w=t[e++],x=w>>6&1,_=1<<(7&w)+1,A=a,L=s,N=!1;w>>7&&(N=!0,A=e,L=_,e+=3*_);var S=e;for(e++;;){if(!((g=t[e++])>=0))throw Error("Invalid block size");if(0===g)break;e+=g}u.push({x:m,y:b,width:v,height:y,has_local_palette:N,palette_offset:A,palette_size:L,data_offset:S,data_length:e-S,transparent_index:c,interlaced:!!x,delay:l,disposal:f});break;case 59:h=!1;break;default:throw Error("Unknown gif block: 0x"+t[e-1].toString(16))}this.numFrames=function(){return u.length},this.loopCount=function(){return d},this.frameInfo=function(t){if(t<0||t>=u.length)throw Error("Frame index out of range.");return u[t]},this.decodeAndBlitFrameBGRA=function(e,n){var i=this.frameInfo(e),o=i.width*i.height,a=new Uint8Array(o);e0(t,i.data_offset,a,o);var s=i.palette_offset,h=i.transparent_index;null===h&&(h=256);var u=i.width,l=r-u,c=u,f=4*(i.y*r+i.x),d=4*((i.y+i.height)*r+i.x),p=f,g=4*l;!0===i.interlaced&&(g+=4*r*7);for(var m=8,b=0,v=a.length;b<v;++b){var y=a[b];if(0===c&&(c=u,(p+=g)>=d&&(g=4*l+4*r*(m-1),p=f+(u+l)*(m<<1),m>>=1)),y===h)p+=4;else{var w=t[s+3*y],x=t[s+3*y+1],_=t[s+3*y+2];n[p++]=_,n[p++]=x,n[p++]=w,n[p++]=255}--c}},this.decodeAndBlitFrameRGBA=function(e,n){var i=this.frameInfo(e),o=i.width*i.height,a=new Uint8Array(o);e0(t,i.data_offset,a,o);var s=i.palette_offset,h=i.transparent_index;null===h&&(h=256);var u=i.width,l=r-u,c=u,f=4*(i.y*r+i.x),d=4*((i.y+i.height)*r+i.x),p=f,g=4*l;!0===i.interlaced&&(g+=4*r*7);for(var m=8,b=0,v=a.length;b<v;++b){var y=a[b];if(0===c&&(c=u,(p+=g)>=d&&(g=4*l+4*r*(m-1),p=f+(u+l)*(m<<1),m>>=1)),y===h)p+=4;else{var w=t[s+3*y],x=t[s+3*y+1],_=t[s+3*y+2];n[p++]=w,n[p++]=x,n[p++]=_,n[p++]=255}--c}}}function e0(t,e,r,n){for(var i=t[e++],o=1<<i,a=o+1,s=a+1,h=i+1,u=(1<<h)-1,l=0,c=0,f=0,d=t[e++],p=new Int32Array(4096),g=null;;){for(;l<16&&0!==d;)c|=t[e++]<<l,l+=8,1===d?d=t[e++]:--d;if(l<h)break;var m=c&u;if(c>>=h,l-=h,m!==o){if(m===a)break;for(var b=m<s?m:g,v=0,y=b;y>o;)y=p[y]>>8,++v;var w=y;if(f+v+ +(b!==m)>n)return void tN.log("Warning, gif stream longer than expected.");r[f++]=w;var x=f+=v;for(b!==m&&(r[f++]=w),y=b;v--;)y=p[y],r[--x]=255&y,y>>=8;null!==g&&s<4096&&(p[s++]=g<<8|w,s>=u+1&&h<12&&(++h,u=u<<1|1)),g=m}else s=a+1,u=(1<<(h=i+1))-1,g=null}return f!==n&&tN.log("Warning, gif stream shorter than expected."),r}function e5(t){var e,r,n,i,o,a=Math.floor,s=Array(64),h=Array(64),u=Array(64),l=Array(64),c=Array(65535),f=Array(65535),d=Array(64),p=Array(64),g=[],m=0,b=7,v=Array(64),y=Array(64),w=Array(64),x=Array(256),_=Array(2048),A=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],L=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],N=[0,1,2,3,4,5,6,7,8,9,10,11],S=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],P=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],k=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],I=[0,1,2,3,4,5,6,7,8,9,10,11],C=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],E=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];function F(t,e){for(var r=0,n=0,i=[],o=1;o<=16;o++){for(var a=1;a<=t[o];a++)i[e[n]]=[],i[e[n]][0]=r,i[e[n]][1]=o,n++,r++;r*=2}return i}function O(t){for(var e=t[0],r=t[1]-1;r>=0;)e&1<<r&&(m|=1<<b),r--,--b<0&&(255==m?(T(255),T(0)):T(m),b=7,m=0)}function T(t){g.push(t)}function M(t){T(t>>8&255),T(255&t)}function j(t,e,r,n,i){for(var o,a=i[0],s=i[240],h=function(t,e){var r,n,i,o,a,s,h,u,l,c,f=0;for(l=0;l<8;++l){r=t[f],n=t[f+1],i=t[f+2],o=t[f+3],a=t[f+4],s=t[f+5],h=t[f+6];var p=r+(u=t[f+7]),g=r-u,m=n+h,b=n-h,v=i+s,y=i-s,w=o+a,x=o-a,_=p+w,A=p-w,L=m+v,N=m-v;t[f]=_+L,t[f+4]=_-L;var S=.707106781*(N+A);t[f+2]=A+S,t[f+6]=A-S;var P=.382683433*((_=x+y)-(N=b+g)),k=.5411961*_+P,I=1.306562965*N+P,C=.707106781*(L=y+b),E=g+C,F=g-C;t[f+5]=F+k,t[f+3]=F-k,t[f+1]=E+I,t[f+7]=E-I,f+=8}for(f=0,l=0;l<8;++l){r=t[f],n=t[f+8],i=t[f+16],o=t[f+24],a=t[f+32],s=t[f+40],h=t[f+48];var O=r+(u=t[f+56]),T=r-u,M=n+h,j=n-h,B=i+s,R=i-s,D=o+a,q=o-a,U=O+D,z=O-D,H=M+B,W=M-B;t[f]=U+H,t[f+32]=U-H;var V=.707106781*(W+z);t[f+16]=z+V,t[f+48]=z-V;var G=.382683433*((U=q+R)-(W=j+T)),Y=.5411961*U+G,J=1.306562965*W+G,K=.707106781*(H=R+j),X=T+K,Z=T-K;t[f+40]=Z+Y,t[f+24]=Z-Y,t[f+8]=X+J,t[f+56]=X-J,f++}for(l=0;l<64;++l)c=t[l]*e[l],d[l]=c>0?c+.5|0:c-.5|0;return d}(t,e),u=0;u<64;++u)p[A[u]]=h[u];var l=p[0]-r;r=p[0],0==l?O(n[0]):(O(n[f[o=32767+l]]),O(c[o]));for(var g=63;g>0&&0==p[g];)g--;if(0==g)return O(a),r;for(var m,b=1;b<=g;){for(var v=b;0==p[b]&&b<=g;)++b;var y=b-v;if(y>=16){m=y>>4;for(var w=1;w<=m;++w)O(s);y&=15}O(i[(y<<4)+f[o=32767+p[b]]]),O(c[o]),b++}return 63!=g&&O(a),r}function B(t){o!=(t=Math.min(Math.max(t,1),100))&&(!function(t){for(var e=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],r=0;r<64;r++){var n=a((e[r]*t+50)/100);n=Math.min(Math.max(n,1),255),s[A[r]]=n}for(var i=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],o=0;o<64;o++){var c=a((i[o]*t+50)/100);c=Math.min(Math.max(c,1),255),h[A[o]]=c}for(var f=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],d=0,p=0;p<8;p++)for(var g=0;g<8;g++)u[d]=1/(s[A[d]]*f[p]*f[g]*8),l[d]=1/(h[A[d]]*f[p]*f[g]*8),d++}(t<50?Math.floor(5e3/t):Math.floor(200-2*t)),o=t)}this.encode=function(t,o){o&&B(o),g=[],m=0,b=7,M(65496),M(65504),M(16),T(74),T(70),T(73),T(70),T(0),T(1),T(1),T(0),M(1),M(1),T(0),T(0),function(){M(65499),M(132),T(0);for(var t=0;t<64;t++)T(s[t]);T(1);for(var e=0;e<64;e++)T(h[e])}(),d=t.width,p=t.height,M(65472),M(17),T(8),M(p),M(d),T(3),T(1),T(17),T(0),T(2),T(17),T(1),T(3),T(17),T(1),function(){M(65476),M(418),T(0);for(var t=0;t<16;t++)T(L[t+1]);for(var e=0;e<=11;e++)T(N[e]);T(16);for(var r=0;r<16;r++)T(S[r+1]);for(var n=0;n<=161;n++)T(P[n]);T(1);for(var i=0;i<16;i++)T(k[i+1]);for(var o=0;o<=11;o++)T(I[o]);T(17);for(var a=0;a<16;a++)T(C[a+1]);for(var s=0;s<=161;s++)T(E[s])}(),M(65498),M(12),T(3),T(1),T(0),T(2),T(17),T(3),T(17),T(0),T(63),T(0);var a=0,c=0,f=0;m=0,b=7,this.encode.displayName="_encode_";for(var d,p,x,A,F,R,D,q,U,z,H,W=t.data,V=t.width,G=t.height,Y=4*V,J=0;J<G;){for(x=0;x<Y;){for(D=Y*J+x,U=-1,z=0,H=0;H<64;H++)q=D+(z=H>>3)*Y+(U=4*(7&H)),J+z>=G&&(q-=Y*(J+1+z-G)),x+U>=Y&&(q-=x+U-Y+4),A=W[q++],F=W[q++],R=W[q++],v[H]=(_[A]+_[F+256|0]+_[R+512|0]>>16)-128,y[H]=(_[A+768|0]+_[F+1024|0]+_[R+1280|0]>>16)-128,w[H]=(_[A+1280|0]+_[F+1536|0]+_[R+1792|0]>>16)-128;a=j(v,u,a,e,n),c=j(y,l,c,r,i),f=j(w,l,f,r,i),x+=32}J+=8}if(b>=0){var K=[];K[1]=b+1,K[0]=(1<<b+1)-1,O(K)}return M(65497),new Uint8Array(g)},t=t||50,function(){for(var t=String.fromCharCode,e=0;e<256;e++)x[e]=t(e)}(),e=F(L,N),r=F(k,I),n=F(S,P),i=F(C,E),function(){for(var t=1,e=2,r=1;r<=15;r++){for(var n=t;n<e;n++)f[32767+n]=r,c[32767+n]=[],c[32767+n][1]=r,c[32767+n][0]=n;for(var i=-(e-1);i<=-t;i++)f[32767+i]=r,c[32767+i]=[],c[32767+i][1]=r,c[32767+i][0]=e-1+i;t<<=1,e<<=1}}(),function(){for(var t=0;t<256;t++)_[t]=19595*t,_[t+256|0]=38470*t,_[t+512|0]=7471*t+32768,_[t+768|0]=-11059*t,_[t+1024|0]=-21709*t,_[t+1280|0]=32768*t+8421375,_[t+1536|0]=-27439*t,_[t+1792|0]=-5329*t}(),B(t)}function e3(t,e){if(this.pos=0,this.buffer=t,this.datav=new DataView(t.buffer),this.is_with_alpha=!!e,this.bottom_up=!0,this.flag=String.fromCharCode(this.buffer[0])+String.fromCharCode(this.buffer[1]),this.pos+=2,-1===["BM","BA","CI","CP","IC","PT"].indexOf(this.flag))throw Error("Invalid BMP File");this.parseHeader(),this.parseBGR()}function e4(t){function e(t){if(!t)throw Error("assert :P")}function r(t,e,r){for(var n=0;4>n;n++)if(t[e+n]!=r.charCodeAt(n))return!0;return!1}function n(t,e,r,n,i){for(var o=0;o<i;o++)t[e+o]=r[n+o]}function i(t,e,r,n){for(var i=0;i<n;i++)t[e+i]=r}function o(t){return new Int32Array(t)}function a(t,e){for(var r=[],n=0;n<t;n++)r.push(new e);return r}function s(t,e){var r=[];return function t(r,n,i){for(var o=i[n],a=0;a<o&&(r.push(i.length>n+1?[]:new e),!(i.length<n+1));a++)t(r[a],n+1,i)}(r,0,t),r}var h=function(){var t=this;function h(t,e){for(var r=1<<e-1>>>0;t&r;)r>>>=1;return r?(t&r-1)+r:t}function u(t,r,n,i,o){e(!(i%n));do t[r+(i-=n)]=o;while(0<i)}function l(t,r,n,i,a){if(e(2328>=a),512>=a)var s=o(512);else if(null==(s=o(a)))return 0;return function(t,r,n,i,a,s){var l,f,d=r,p=1<<n,g=o(16),m=o(16);for(e(0!=a),e(null!=i),e(null!=t),e(0<n),f=0;f<a;++f){if(15<i[f])return 0;++g[i[f]]}if(g[0]==a)return 0;for(m[1]=0,l=1;15>l;++l){if(g[l]>1<<l)return 0;m[l+1]=m[l]+g[l]}for(f=0;f<a;++f)l=i[f],0<i[f]&&(s[m[l]++]=f);if(1==m[15])return(i=new c).g=0,i.value=s[0],u(t,d,1,p,i),p;var b,v=-1,y=p-1,w=0,x=1,_=1,A=1<<n;for(f=0,l=1,a=2;l<=n;++l,a<<=1){if(x+=_<<=1,0>(_-=g[l]))return 0;for(;0<g[l];--g[l])(i=new c).g=l,i.value=s[f++],u(t,d+w,a,A,i),w=h(w,l)}for(l=n+1,a=2;15>=l;++l,a<<=1){if(x+=_<<=1,0>(_-=g[l]))return 0;for(;0<g[l];--g[l]){if(i=new c,(w&y)!=v){for(d+=A,b=1<<(v=l)-n;15>v&&!(0>=(b-=g[v]));)++v,b<<=1;p+=A=1<<(b=v-n),t[r+(v=w&y)].g=b+n,t[r+v].value=d-r-v}i.g=l-n,i.value=s[f++],u(t,d+(w>>n),a,A,i),w=h(w,l)}}return x!=2*m[15]-1?0:p}(t,r,n,i,a,s)}function c(){this.value=this.g=0}function f(){this.value=this.g=0}function d(){this.G=a(5,c),this.H=o(5),this.jc=this.Qb=this.qb=this.nd=0,this.pd=a(rm,f)}function p(t,r,n,i){e(null!=t),e(null!=r),e(0x80000000>i),t.Ca=254,t.I=0,t.b=-8,t.Ka=0,t.oa=r,t.pa=n,t.Jd=r,t.Yc=n+i,t.Zc=4<=i?n+i-4+1:n,S(t)}function g(t,e){for(var r=0;0<e--;)r|=k(t,128)<<e;return r}function m(t,e){var r=g(t,e);return P(t)?-r:r}function b(t,r,n,i){var o,a=0;for(e(null!=t),e(null!=r),e(0xfffffff8>i),t.Sb=i,t.Ra=0,t.u=0,t.h=0,4<i&&(i=4),o=0;o<i;++o)a+=r[n+o]<<8*o;t.Ra=a,t.bb=i,t.oa=r,t.pa=n}function v(t){for(;8<=t.u&&t.bb<t.Sb;)t.Ra>>>=8,t.Ra+=t.oa[t.pa+t.bb]<<ry-8>>>0,++t.bb,t.u-=8;A(t)&&(t.h=1,t.u=0)}function y(t,r){if(e(0<=r),!t.h&&r<=rv){var n=_(t)&rb[r];return t.u+=r,v(t),n}return t.h=1,t.u=0}function w(){this.b=this.Ca=this.I=0,this.oa=[],this.pa=0,this.Jd=[],this.Yc=0,this.Zc=[],this.Ka=0}function x(){this.Ra=0,this.oa=[],this.h=this.u=this.bb=this.Sb=this.pa=0}function _(t){return t.Ra>>>(t.u&ry-1)>>>0}function A(t){return e(t.bb<=t.Sb),t.h||t.bb==t.Sb&&t.u>ry}function L(t,e){t.u=e,t.h=A(t)}function N(t){t.u>=rw&&(e(t.u>=rw),v(t))}function S(t){e(null!=t&&null!=t.oa),t.pa<t.Zc?(t.I=(t.oa[t.pa++]|t.I<<8)>>>0,t.b+=8):(e(null!=t&&null!=t.oa),t.pa<t.Yc?(t.b+=8,t.I=t.oa[t.pa++]|t.I<<8):t.Ka?t.b=0:(t.I<<=8,t.b+=8,t.Ka=1))}function P(t){return g(t,1)}function k(t,e){var r=t.Ca;0>t.b&&S(t);var n=t.b,i=r*e>>>8,o=(t.I>>>n>i)+0;for(o?(r-=i,t.I-=i+1<<n>>>0):r=i+1,n=r,i=0;256<=n;)i+=8,n>>=8;return n=7^i+rx[n],t.b-=n,t.Ca=(r<<n)-1,o}function I(t,e,r){t[e+0]=r>>24&255,t[e+1]=r>>16&255,t[e+2]=r>>8&255,t[e+3]=(0|r)&255}function C(t,e){return 0|t[e+0]|t[e+1]<<8}function E(t,e){return C(t,e)|t[e+2]<<16}function F(t,e){return C(t,e)|C(t,e+2)<<16}function O(t,r){return e(null!=t),e(0<r),t.X=o(1<<r),null==t.X?0:(t.Mb=32-r,t.Xa=r,1)}function T(t,r){e(null!=t),e(null!=r),e(t.Xa==r.Xa),n(r.X,0,t.X,0,1<<r.Xa)}function M(){this.X=[],this.Xa=this.Mb=0}function j(t,r,n,i){e(null!=n),e(null!=i);var o=n[0],a=i[0];return 0==o&&(o=(t*a+r/2)/r),0==a&&(a=(r*o+t/2)/t),0>=o||0>=a?0:(n[0]=o,i[0]=a,1)}function B(t,e){return t+(1<<e)-1>>>e}function R(t,e){return((0xff00ff00&t)+(0xff00ff00&e)>>>0&0xff00ff00)+((0xff00ff&t)+(0xff00ff&e)>>>0&0xff00ff)>>>0}function D(e,r){t[r]=function(r,n,i,o,a,s,h){var u;for(u=0;u<a;++u){var l=t[e](s[h+u-1],i,o+u);s[h+u]=R(r[n+u],l)}}}function q(){this.ud=this.hd=this.jd=0}function U(t,e){return((0xfefefefe&(t^e))>>>1)+(t&e)>>>0}function z(t){return 0<=t&&256>t?t:0>t?0:255<t?255:void 0}function H(t,e){return z(t+(t-e+.5>>1))}function W(t,e,r){return Math.abs(e-r)-Math.abs(t-r)}function V(t,e,r,n,i,o,a){for(n=o[a-1],r=0;r<i;++r)o[a+r]=n=R(t[e+r],n)}function G(t,e,r,n,i){var o;for(o=0;o<r;++o){var a=t[e+o],s=a>>8&255,h=0xff00ff&(h=(h=0xff00ff&a)+((s<<16)+s));n[i+o]=(0xff00ff00&a)+h>>>0}}function Y(t,e){e.jd=(0|t)&255,e.hd=t>>8&255,e.ud=t>>16&255}function J(t,e,r,n,i,o){var a;for(a=0;a<n;++a){var s=e[r+a],h=s>>>8,u=s,l=255&(l=(l=s>>>16)+((t.jd<<24>>24)*(h<<24>>24)>>>5));u=255&(u=(u+=(t.hd<<24>>24)*(h<<24>>24)>>>5)+((t.ud<<24>>24)*(l<<24>>24)>>>5)),i[o+a]=(0xff00ff00&s)+(l<<16)+u}}function K(e,r,n,i,o){t[r]=function(t,e,r,n,a,s,h,u,l){for(n=h;n<u;++n)for(h=0;h<l;++h)a[s++]=o(r[i(t[e++])])},t[e]=function(e,r,a,s,h,u,l){var c=8>>e.b,f=e.Ea,d=e.K[0],p=e.w;if(8>c)for(e=(1<<e.b)-1,p=(1<<c)-1;r<a;++r){var g,m=0;for(g=0;g<f;++g)g&e||(m=i(s[h++])),u[l++]=o(d[m&p]),m>>=c}else t["VP8LMapColor"+n](s,h,d,p,u,l,r,a,f)}}function X(t,e,r,n,i){for(r=e+r;e<r;){var o=t[e++];n[i++]=o>>16&255,n[i++]=o>>8&255,n[i++]=(0|o)&255}}function Z(t,e,r,n,i){for(r=e+r;e<r;){var o=t[e++];n[i++]=o>>16&255,n[i++]=o>>8&255,n[i++]=(0|o)&255,n[i++]=o>>24&255}}function $(t,e,r,n,i){for(r=e+r;e<r;){var o=(a=t[e++])>>16&240|a>>12&15,a=(0|a)&240|a>>28&15;n[i++]=o,n[i++]=a}}function Q(t,e,r,n,i){for(r=e+r;e<r;){var o=(a=t[e++])>>16&248|a>>13&7,a=a>>5&224|a>>3&31;n[i++]=o,n[i++]=a}}function tt(t,e,r,n,i){for(r=e+r;e<r;){var o=t[e++];n[i++]=(0|o)&255,n[i++]=o>>8&255,n[i++]=o>>16&255}}function te(t,e,r,i,o,a){if(0==a)for(r=e+r;e<r;)I(i,((a=t[e++])[0]>>24|a[1]>>8&65280|a[2]<<8&0xff0000|a[3]<<24)>>>0),o+=32;else n(i,o,t,e,r)}function tr(e,r){t[r][0]=t[e+"0"],t[r][1]=t[e+"1"],t[r][2]=t[e+"2"],t[r][3]=t[e+"3"],t[r][4]=t[e+"4"],t[r][5]=t[e+"5"],t[r][6]=t[e+"6"],t[r][7]=t[e+"7"],t[r][8]=t[e+"8"],t[r][9]=t[e+"9"],t[r][10]=t[e+"10"],t[r][11]=t[e+"11"],t[r][12]=t[e+"12"],t[r][13]=t[e+"13"],t[r][14]=t[e+"0"],t[r][15]=t[e+"0"]}function tn(t){return t==nl||t==nc||t==nf||t==nd}function ti(){this.eb=[],this.size=this.A=this.fb=0}function to(){this.y=[],this.f=[],this.ea=[],this.F=[],this.Tc=this.Ed=this.Cd=this.Fd=this.lb=this.Db=this.Ab=this.fa=this.J=this.W=this.N=this.O=0}function ta(){this.Rd=this.height=this.width=this.S=0,this.f={},this.f.RGBA=new ti,this.f.kb=new to,this.sd=null}function ts(){this.width=[0],this.height=[0],this.Pd=[0],this.Qd=[0],this.format=[0]}function th(){this.Id=this.fd=this.Md=this.hb=this.ib=this.da=this.bd=this.cd=this.j=this.v=this.Da=this.Sd=this.ob=0}function tu(t){return alert("todo:WebPSamplerProcessPlane"),t.T}function tl(t,e){var r=t.T,i=e.ba.f.RGBA,o=i.eb,a=i.fb+t.ka*i.A,s=nR[e.ba.S],h=t.y,u=t.O,l=t.f,c=t.N,f=t.ea,d=t.W,p=e.cc,g=e.dc,m=e.Mc,b=e.Nc,v=t.ka,y=t.ka+t.T,w=t.U,x=w+1>>1;for(0==v?s(h,u,null,null,l,c,f,d,l,c,f,d,o,a,null,null,w):(s(e.ec,e.fc,h,u,p,g,m,b,l,c,f,d,o,a-i.A,o,a,w),++r);v+2<y;v+=2)p=l,g=c,m=f,b=d,c+=t.Rc,d+=t.Rc,a+=2*i.A,s(h,(u+=2*t.fa)-t.fa,h,u,p,g,m,b,l,c,f,d,o,a-i.A,o,a,w);return u+=t.fa,t.j+y<t.o?(n(e.ec,e.fc,h,u,w),n(e.cc,e.dc,l,c,x),n(e.Mc,e.Nc,f,d,x),r--):1&y||s(h,u,null,null,l,c,f,d,l,c,f,d,o,a+i.A,null,null,w),r}function tc(t,r,n){var i=t.F,o=[t.J];if(null!=i){var a=t.U,s=r.ba.S,h=s==ns||s==nf;r=r.ba.f.RGBA;var u=[0],l=t.ka;u[0]=t.T,t.Kb&&(0==l?--u[0]:(--l,o[0]-=t.width),t.j+t.ka+t.T==t.o&&(u[0]=t.o-t.j-l));var c=r.eb;l=r.fb+l*r.A,t=r0(i,o[0],t.width,a,u,c,l+3*!h,r.A),e(n==u),t&&tn(s)&&r1(c,l,h,a,u,r.A)}return 0}function tf(t){var e=t.ma,r=e.ba.S,n=11>r,i=r==ni||r==na||r==ns||r==nh||12==r||tn(r);if(e.memory=null,e.Ib=null,e.Jb=null,e.Nd=null,!rd(e.Oa,t,i?11:12))return 0;if(i&&tn(r)&&e6(),t.da)alert("todo:use_scaling");else{if(n){if(e.Ib=tu,t.Kb){if(r=t.U+1>>1,e.memory=o(t.U+2*r),null==e.memory)return 0;e.ec=e.memory,e.fc=0,e.cc=e.ec,e.dc=e.fc+t.U,e.Mc=e.cc,e.Nc=e.dc+r,e.Ib=tl,e6()}}else alert("todo:EmitYUV");i&&(e.Jb=tc,n&&e3())}if(n&&!n$){for(t=0;256>t;++t)nQ[t]=89858*(t-128)+nY>>nG,n0[t]=-22014*(t-128)+nY,n2[t]=-45773*(t-128),n1[t]=113618*(t-128)+nY>>nG;for(t=nJ;t<nK;++t)e=76283*(t-16)+nY>>nG,n5[t-nJ]=tV(e,255),n3[t-nJ]=tV(e+8>>4,15);n$=1}return 1}function td(t){var r=t.ma,n=t.U,i=t.T;return e(!(1&t.ka)),0>=n||0>=i?0:(n=r.Ib(t,r),null!=r.Jb&&r.Jb(t,r,n),r.Dc+=n,1)}function tp(t){t.ma.memory=null}function tg(t,e,r,n){return 47!=y(t,8)?0:(e[0]=y(t,14)+1,r[0]=y(t,14)+1,n[0]=y(t,1),0!=y(t,3)?0:!t.h)}function tm(t,e){if(4>t)return t+1;var r=t-2>>1;return(2+(1&t)<<r)+y(e,r)+1}function tb(t,e){var r;return 120<e?e-120:1<=(r=((r=ny[e-1])>>4)*t+(8-(15&r)))?r:1}function tv(t,e,r){var n=_(r),i=t[e+=255&n].g-8;return 0<i&&(L(r,r.u+8),n=_(r),e+=t[e].value,e+=n&(1<<i)-1),L(r,r.u+t[e].g),t[e].value}function ty(t,r,n){return n.g+=t.g,n.value+=t.value<<r>>>0,e(8>=n.g),t.g}function tw(t,r,n){var i=t.xc;return e((r=0==i?0:t.vc[t.md*(n>>i)+(r>>i)])<t.Wb),t.Ya[r]}function tx(t,r,i,o){var a=t.ab,s=t.c*r,h=t.C;r=h+r;var u=i,l=o;for(o=t.Ta,i=t.Ua;0<a--;){var c=t.gc[a],f=h,d=r,p=u,g=l,m=(l=o,u=i,c.Ea);switch(e(f<d),e(d<=c.nc),c.hc){case 2:rL(p,g,(d-f)*m,l,u);break;case 0:var b=f,v=d,y=l,w=u,x=(S=c).Ea;0==b&&(r_(p,g,null,null,1,y,w),V(p,g+1,0,0,x-1,y,w+1),g+=x,w+=x,++b);for(var _=1<<S.b,A=_-1,L=B(x,S.b),N=S.K,S=S.w+(b>>S.b)*L;b<v;){var P=N,k=S,I=1;for(rA(p,g,y,w-x,1,y,w);I<x;){var C=(I&~A)+_;C>x&&(C=x),(0,rI[P[k++]>>8&15])(p,g+ +I,y,w+I-x,C-I,y,w+I),I=C}g+=x,w+=x,++b&A||(S+=L)}d!=c.nc&&n(l,u-m,l,u+(d-f-1)*m,m);break;case 1:for(m=p,v=g,x=(p=c.Ea)-(w=p&~(y=(g=1<<c.b)-1)),b=B(p,c.b),_=c.K,c=c.w+(f>>c.b)*b;f<d;){for(A=_,L=c,N=new q,S=v+w,P=v+p;v<S;)Y(A[L++],N),rC(N,m,v,g,l,u),v+=g,u+=g;v<P&&(Y(A[L++],N),rC(N,m,v,x,l,u),v+=x,u+=x),++f&y||(c+=b)}break;case 3:if(p==l&&g==u&&0<c.b){for(v=l,p=m=u+(d-f)*m-(w=(d-f)*B(c.Ea,c.b)),g=l,y=u,b=[],w=(x=w)-1;0<=w;--w)b[w]=g[y+w];for(w=x-1;0<=w;--w)v[p+w]=b[w];rN(c,f,d,l,m,l,u)}else rN(c,f,d,p,g,l,u)}u=o,l=i}l!=i&&n(o,i,u,l,s)}function t_(t,r){var n=t.V,i=t.Ba+t.c*t.C,o=r-t.C;if(e(r<=t.l.o),e(16>=o),0<o){var a=t.l,s=t.Ta,h=t.Ua,u=a.width;if(tx(t,o,n,i),o=h=[h],e((n=t.C)<(i=r)),e(a.v<a.va),i>a.o&&(i=a.o),n<a.j){var l=a.j-n;n=a.j,o[0]+=l*u}if(n>=i?n=0:(o[0]+=4*a.v,a.ka=n-a.j,a.U=a.va-a.v,a.T=i-n,n=1),n){if(h=h[0],11>(n=t.ca).S){var c=n.f.RGBA,f=(i=n.S,o=a.U,a=a.T,l=c.eb,c.A),d=a;for(c=c.fb+t.Ma*c.A;0<d--;){var p=h,g=o,m=l,b=c;switch(i){case nn:rE(s,p,g,m,b);break;case ni:rF(s,p,g,m,b);break;case nl:rF(s,p,g,m,b),r1(m,b,0,g,1,0);break;case no:rM(s,p,g,m,b);break;case na:te(s,p,g,m,b,1);break;case nc:te(s,p,g,m,b,1),r1(m,b,0,g,1,0);break;case ns:te(s,p,g,m,b,0);break;case nf:te(s,p,g,m,b,0),r1(m,b,1,g,1,0);break;case nh:rO(s,p,g,m,b);break;case nd:rO(s,p,g,m,b),r2(m,b,g,1,0);break;case nu:rT(s,p,g,m,b);break;default:e(0)}h+=u,c+=f}t.Ma+=a}else alert("todo:EmitRescaledRowsYUVA");e(t.Ma<=n.height)}}t.C=r,e(t.C<=t.i)}function tA(t){var e;if(0<t.ua)return 0;for(e=0;e<t.Wb;++e){var r=t.Ya[e].G,n=t.Ya[e].H;if(0<r[1][n[1]+0].g||0<r[2][n[2]+0].g||0<r[3][n[3]+0].g)return 0}return 1}function tL(t,r,n,i,o,a){if(0!=t.Z){var s=t.qd,h=t.rd;for(e(null!=nB[t.Z]);r<n;++r)nB[t.Z](s,h,i,o,i,o,a),s=i,h=o,o+=a;t.qd=s,t.rd=h}}function tN(t,r){var n=t.l.ma,i=0==n.Z||1==n.Z?t.l.j:t.C;if(i=t.C<i?i:t.C,e(r<=t.l.o),r>i){var o=t.l.width,a=n.ca,s=n.tb+o*i,h=t.V,u=t.Ba+t.c*i,l=t.gc;e(1==t.ab),e(3==l[0].hc),rP(l[0],i,r,h,u,a,s),tL(n,i,r,a,s,o)}t.C=t.Ma=r}function tS(t,r,n,i,o,a,s){var h=t.$/i,u=t.$%i,l=t.m,c=t.s,f=n+t.$,d=f;o=n+i*o;var p=n+i*a,g=280+c.ua,m=t.Pb?h:0x1000000,b=0<c.ua?c.Wa:null,v=c.wc,y=f<p?tw(c,u,h):null;e(t.C<a),e(p<=o);var w=!1;t:for(;;){for(;w||f<p;){var x=0;if(h>=m){var S=f-n;e((m=t).Pb),m.wd=m.m,m.xd=S,0<m.s.ua&&T(m.s.Wa,m.s.vb),m=h+nx}if(u&v||(y=tw(c,u,h)),e(null!=y),y.Qb&&(r[f]=y.qb,w=!0),!w)if(N(l),y.jc){x=l,S=r;var P=f,k=y.pd[_(x)&rm-1];e(y.jc),256>k.g?(L(x,x.u+k.g),S[P]=k.value,x=0):(L(x,x.u+k.g-256),e(256<=k.value),x=k.value),0==x&&(w=!0)}else x=tv(y.G[0],y.H[0],l);if(l.h)break;if(w||256>x){if(!w)if(y.nd)r[f]=(y.qb|x<<8)>>>0;else{if(N(l),w=tv(y.G[1],y.H[1],l),N(l),S=tv(y.G[2],y.H[2],l),P=tv(y.G[3],y.H[3],l),l.h)break;r[f]=(P<<24|w<<16|x<<8|S)>>>0}if(w=!1,++f,++u>=i&&(u=0,++h,null!=s&&h<=a&&!(h%16)&&s(t,h),null!=b))for(;d<f;)x=r[d++],b.X[(0x1e35a7bd*x|0)>>>b.Mb]=x}else if(280>x){if(x=tm(x-256,l),S=tv(y.G[4],y.H[4],l),N(l),S=tb(i,S=tm(S,l)),l.h)break;if(f-n<S||o-f<x)break t;for(P=0;P<x;++P)r[f+P]=r[f+P-S];for(f+=x,u+=x;u>=i;)u-=i,++h,null!=s&&h<=a&&!(h%16)&&s(t,h);if(e(f<=o),u&v&&(y=tw(c,u,h)),null!=b)for(;d<f;)x=r[d++],b.X[(0x1e35a7bd*x|0)>>>b.Mb]=x}else{if(!(x<g))break t;for(w=x-280,e(null!=b);d<f;)x=r[d++],b.X[(0x1e35a7bd*x|0)>>>b.Mb]=x;x=f,e(!(w>>>(S=b).Xa)),r[x]=S.X[w],w=!0}w||e(l.h==A(l))}if(t.Pb&&l.h&&f<o)e(t.m.h),t.a=5,t.m=t.wd,t.$=t.xd,0<t.s.ua&&T(t.s.vb,t.s.Wa);else{if(l.h)break;null!=s&&s(t,h>a?a:h),t.a=0,t.$=f-n}return 1}return t.a=3,0}function tP(t){e(null!=t),t.vc=null,t.yc=null,t.Ya=null;var r=t.Wa;null!=r&&(r.X=null),t.vb=null,e(null!=t)}function tk(){var e=new eK;return null==e?null:(e.a=0,e.xb=nj,tr("Predictor","VP8LPredictors"),tr("Predictor","VP8LPredictors_C"),tr("PredictorAdd","VP8LPredictorsAdd"),tr("PredictorAdd","VP8LPredictorsAdd_C"),rL=G,rC=J,rE=X,rF=Z,rO=$,rT=Q,rM=tt,t.VP8LMapColor32b=rS,t.VP8LMapColor8b=rk,e)}function tI(t,r,n,s,h){for(var u=1,f=[t],p=[r],g=s.m,m=s.s,b=null,v=0;;){if(n)for(;u&&y(g,1);){var w=f,x=p,A=1,S=s.m,P=s.gc[s.ab],k=y(S,2);if(s.Oc&1<<k)u=0;else{switch(s.Oc|=1<<k,P.hc=k,P.Ea=w[0],P.nc=x[0],P.K=[null],++s.ab,e(4>=s.ab),k){case 0:case 1:P.b=y(S,3)+2,A=tI(B(P.Ea,P.b),B(P.nc,P.b),0,s,P.K),P.K=P.K[0];break;case 3:var I,C=y(S,8)+1,E=16<C?0:4<C?1:2<C?2:3;if(w[0]=B(P.Ea,E),P.b=E,I=A=tI(C,1,0,s,P.K)){var F,T=1<<(8>>P.b),M=o(T);if(null==M)I=0;else{var j=P.K[0],D=P.w;for(M[0]=P.K[0][0],F=1;F<+C;++F)M[F]=R(j[D+F],M[F-1]);for(;F<4*T;++F)M[F]=0;P.K[0]=null,P.K[0]=M,I=1}}A=I;break;case 2:break;default:e(0)}u=A}}if(f=f[0],p=p[0],u&&y(g,1)&&!(u=1<=(v=y(g,4))&&11>=v)){s.a=3;break}if(q=u)e:{var q,U,z,H,W=f,V=p,G=v,Y=s.m,J=s.s,K=[null],X=1,Z=0,$=nw[G];r:for(;;){if(n&&y(Y,1)){var Q=y(Y,3)+2,tt=B(W,Q),te=B(V,Q),tr=tt*te;if(!tI(tt,te,0,s,K))break;for(K=K[0],J.xc=Q,U=0;U<tr;++U){var tn=K[U]>>8&65535;K[U]=tn,tn>=X&&(X=tn+1)}}if(Y.h)break;for(z=0;5>z;++z){var ti=nm[z];!z&&0<G&&(ti+=1<<G),Z<ti&&(Z=ti)}var to=a(X*$,c),ta=X,ts=a(ta,d);if(null==ts)var th=null;else e(65536>=ta),th=ts;var tu=o(Z);if(null==th||null==tu||null==to){s.a=1;break}for(U=H=0;U<X;++U){var tl,tc=th[U],tf=tc.G,td=tc.H,tp=0,tg=1,tm=0;for(z=0;5>z;++z){ti=nm[z],tf[z]=to,td[z]=H,!z&&0<G&&(ti+=1<<G);n:{var tb,tv=ti,tw=H,tx=0,t_=s.m,tA=y(t_,1);if(i(tu,0,0,tv),tA){var tL=y(t_,1)+1,tN=y(t_,1),tk=y(t_,0==tN?1:8);tu[tk]=1,2==tL&&(tu[tk=y(t_,8)]=1);var tC=1}else{var tE=o(19),tF=y(t_,4)+4;if(19<tF){s.a=3;var tO=0;break n}for(tb=0;tb<tF;++tb)tE[nv[tb]]=y(t_,3);var tT=void 0,tM=void 0,tj=0,tB=s.m,tR=8,tD=a(128,c);i:for(;l(tD,0,7,tE,19);){if(y(tB,1)){var tq=2+2*y(tB,3);if((tT=2+y(tB,tq))>tv)break}else tT=tv;for(tM=0;tM<tv&&tT--;){N(tB);var tU=tD[0+(127&_(tB))];L(tB,tB.u+tU.g);var tz=tU.value;if(16>tz)tu[tM++]=tz,0!=tz&&(tR=tz);else{var tH=16==tz,tW=tz-16,tV=ng[tW],tG=y(tB,np[tW])+tV;if(tM+tG>tv)break i;for(var tY=tH?tR:0;0<tG--;)tu[tM++]=tY}}tj=1;break}tj||(s.a=3),tC=tj}(tC=tC&&!t_.h)&&(tx=l(to,tw,8,tu,tv)),tC&&0!=tx?tO=tx:(s.a=3,tO=0)}if(0==tO)break r;if(tg&&1==nb[z]&&(tg=0==to[H].g),tp+=to[H].g,H+=tO,3>=z){var tJ,tK=tu[0];for(tJ=1;tJ<ti;++tJ)tu[tJ]>tK&&(tK=tu[tJ]);tm+=tK}}if(tc.nd=tg,tc.Qb=0,tg&&(tc.qb=(tf[3][td[3]+0].value<<24|tf[1][td[1]+0].value<<16|tf[2][td[2]+0].value)>>>0,0==tp&&256>tf[0][td[0]+0].value&&(tc.Qb=1,tc.qb+=tf[0][td[0]+0].value<<8)),tc.jc=!tc.Qb&&6>tm,tc.jc)for(tl=0;tl<rm;++tl){var tX=tl,tZ=tc.pd[tX],t$=tc.G[0][tc.H[0]+tX];256<=t$.value?(tZ.g=t$.g+256,tZ.value=t$.value):(tZ.g=0,tZ.value=0,tX>>=ty(t$,8,tZ),tX>>=ty(tc.G[1][tc.H[1]+tX],16,tZ),tX>>=ty(tc.G[2][tc.H[2]+tX],0,tZ),ty(tc.G[3][tc.H[3]+tX],24,tZ))}}J.vc=K,J.Wb=X,J.Ya=th,J.yc=to,q=1;break e}q=0}if(!(u=q)){s.a=3;break}if(0<v){if(m.ua=1<<v,!O(m.Wa,v)){s.a=1,u=0;break}}else m.ua=0;var tQ=f,t1=p,t2=s.s,t0=t2.xc;if(s.c=tQ,s.i=t1,t2.md=B(tQ,t0),t2.wc=0==t0?-1:(1<<t0)-1,n){s.xb=nM;break}if(null==(b=o(f*p))){s.a=1,u=0;break}u=(u=tS(s,b,0,f,p,p,null))&&!g.h;break}return u?(null!=h?h[0]=b:(e(null==b),e(n)),s.$=0,n||tP(m)):tP(m),u}function tC(t,r){var n=t.c*t.i;return e(t.c<=r),t.V=o(n+r+16*r),null==t.V?(t.Ta=null,t.Ua=0,t.a=1,0):(t.Ta=t.V,t.Ua=t.Ba+n+r,1)}function tE(t,r){var n=t.C,i=r-n,o=t.V,a=t.Ba+t.c*n;for(e(r<=t.l.o);0<i;){var s=16<i?16:i,h=t.l.ma,u=t.l.width,l=u*s,c=h.ca,f=h.tb+u*n,d=t.Ta,p=t.Ua;tx(t,s,o,a),r5(d,p,c,f,l),tL(h,n,n+s,c,f,u),i-=s,o+=s*t.c,n+=s}e(n==r),t.C=t.Ma=r}function tF(){this.ub=this.yd=this.td=this.Rb=0}function tO(){this.Kd=this.Ld=this.Ud=this.Td=this.i=this.c=0}function tT(){this.Fb=this.Bb=this.Cb=0,this.Zb=o(4),this.Lb=o(4)}function tM(){var t;this.Yb=(function t(e,r,n){for(var i=n[r],o=0;o<i&&(e.push(n.length>r+1?[]:0),!(n.length<r+1));o++)t(e[o],r+1,n)}(t=[],0,[3,11]),t)}function tj(){this.jb=o(3),this.Wc=s([4,8],tM),this.Xc=s([4,17],tM)}function tB(){this.Pc=this.wb=this.Tb=this.zd=0,this.vd=new o(4),this.od=new o(4)}function tR(){this.ld=this.La=this.dd=this.tc=0}function tD(){this.Na=this.la=0}function tq(){this.Sc=[0,0],this.Eb=[0,0],this.Qc=[0,0],this.ia=this.lc=0}function tU(){this.ad=o(384),this.Za=0,this.Ob=o(16),this.$b=this.Ad=this.ia=this.Gc=this.Hc=this.Dd=0}function tz(){this.uc=this.M=this.Nb=0,this.wa=Array(new tR),this.Y=0,this.ya=Array(new tU),this.aa=0,this.l=new tG}function tH(){this.y=o(16),this.f=o(8),this.ea=o(8)}function tW(){this.cb=this.a=0,this.sc="",this.m=new w,this.Od=new tF,this.Kc=new tO,this.ed=new tB,this.Qa=new tT,this.Ic=this.$c=this.Aa=0,this.D=new tz,this.Xb=this.Va=this.Hb=this.zb=this.yb=this.Ub=this.za=0,this.Jc=a(8,w),this.ia=0,this.pb=a(4,tq),this.Pa=new tj,this.Bd=this.kc=0,this.Ac=[],this.Bc=0,this.zc=[0,0,0,0],this.Gd=Array(new tH),this.Hd=0,this.rb=Array(new tD),this.sb=0,this.wa=Array(new tR),this.Y=0,this.oc=[],this.pc=0,this.sa=[],this.ta=0,this.qa=[],this.ra=0,this.Ha=[],this.B=this.R=this.Ia=0,this.Ec=[],this.M=this.ja=this.Vb=this.Fc=0,this.ya=Array(new tU),this.L=this.aa=0,this.gd=s([4,2],tR),this.ga=null,this.Fa=[],this.Cc=this.qc=this.P=0,this.Gb=[],this.Uc=0,this.mb=[],this.nb=0,this.rc=[],this.Ga=this.Vc=0}function tV(t,e){return 0>t?0:t>e?e:t}function tG(){this.T=this.U=this.ka=this.height=this.width=0,this.y=[],this.f=[],this.ea=[],this.Rc=this.fa=this.W=this.N=this.O=0,this.ma="void",this.put="VP8IoPutHook",this.ac="VP8IoSetupHook",this.bc="VP8IoTeardownHook",this.ha=this.Kb=0,this.data=[],this.hb=this.ib=this.da=this.o=this.j=this.va=this.v=this.Da=this.ob=this.w=0,this.F=[],this.J=0}function tY(){var t=new tW;return null!=t&&(t.a=0,t.sc="OK",t.cb=0,t.Xb=0,nL||(nL=tZ)),t}function tJ(t,e,r){return 0==t.a&&(t.a=e,t.sc=r,t.cb=0),0}function tK(t,e,r){return 3<=r&&157==t[e+0]&&1==t[e+1]&&42==t[e+2]}function tX(t,r){if(null==t)return 0;if(t.a=0,t.sc="OK",null==r)return tJ(t,2,"null VP8Io passed to VP8GetHeaders()");var n=r.data,o=r.w,a=r.ha;if(4>a)return tJ(t,7,"Truncated header.");var s=n[o+0]|n[o+1]<<8|n[o+2]<<16,h=t.Od;if(h.Rb=!(1&s),h.td=s>>1&7,h.yd=s>>4&1,h.ub=s>>5,3<h.td)return tJ(t,3,"Incorrect keyframe parameters.");if(!h.yd)return tJ(t,4,"Frame not displayable.");o+=3,a-=3;var u=t.Kc;if(h.Rb){if(7>a)return tJ(t,7,"cannot parse picture header");if(!tK(n,o,a))return tJ(t,3,"Bad code word");u.c=16383&(n[o+4]<<8|n[o+3]),u.Td=n[o+4]>>6,u.i=16383&(n[o+6]<<8|n[o+5]),u.Ud=n[o+6]>>6,o+=7,a-=7,t.za=u.c+15>>4,t.Ub=u.i+15>>4,r.width=u.c,r.height=u.i,r.Da=0,r.j=0,r.v=0,r.va=r.width,r.o=r.height,r.da=0,r.ib=r.width,r.hb=r.height,r.U=r.width,r.T=r.height,i((s=t.Pa).jb,0,255,s.jb.length),e(null!=(s=t.Qa)),s.Cb=0,s.Bb=0,s.Fb=1,i(s.Zb,0,0,s.Zb.length),i(s.Lb,0,0,s.Lb)}if(h.ub>a)return tJ(t,7,"bad partition length");p(s=t.m,n,o,h.ub),o+=h.ub,a-=h.ub,h.Rb&&(u.Ld=P(s),u.Kd=P(s)),u=t.Qa;var l,c=t.Pa;if(e(null!=s),e(null!=u),u.Cb=P(s),u.Cb){if(u.Bb=P(s),P(s)){for(u.Fb=P(s),l=0;4>l;++l)u.Zb[l]=P(s)?m(s,7):0;for(l=0;4>l;++l)u.Lb[l]=P(s)?m(s,6):0}if(u.Bb)for(l=0;3>l;++l)c.jb[l]=P(s)?g(s,8):255}else u.Bb=0;if(s.Ka)return tJ(t,3,"cannot parse segment header");if((u=t.ed).zd=P(s),u.Tb=g(s,6),u.wb=g(s,3),u.Pc=P(s),u.Pc&&P(s)){for(c=0;4>c;++c)P(s)&&(u.vd[c]=m(s,6));for(c=0;4>c;++c)P(s)&&(u.od[c]=m(s,6))}if(t.L=0==u.Tb?0:u.zd?1:2,s.Ka)return tJ(t,3,"cannot parse filter header");var f=a;if(a=l=o,o=l+f,u=f,t.Xb=(1<<g(t.m,2))-1,f<3*(c=t.Xb))n=7;else{for(l+=3*c,u-=3*c,f=0;f<c;++f){var d=n[a+0]|n[a+1]<<8|n[a+2]<<16;d>u&&(d=u),p(t.Jc[+f],n,l,d),l+=d,u-=d,a+=3}p(t.Jc[+c],n,l,u),n=l<o?0:5}if(0!=n)return tJ(t,n,"cannot parse partitions");for(n=g(l=t.m,7),a=P(l)?m(l,4):0,o=P(l)?m(l,4):0,u=P(l)?m(l,4):0,c=P(l)?m(l,4):0,l=P(l)?m(l,4):0,f=t.Qa,d=0;4>d;++d){if(f.Cb){var b=f.Zb[d];f.Fb||(b+=n)}else{if(0<d){t.pb[d]=t.pb[0];continue}b=n}var v=t.pb[d];v.Sc[0]=n_[tV(b+a,127)],v.Sc[1]=nA[tV(b+0,127)],v.Eb[0]=2*n_[tV(b+o,127)],v.Eb[1]=101581*nA[tV(b+u,127)]>>16,8>v.Eb[1]&&(v.Eb[1]=8),v.Qc[0]=n_[tV(b+c,117)],v.Qc[1]=nA[tV(b+l,127)],v.lc=b+l}if(!h.Rb)return tJ(t,4,"Not a key frame.");for(P(s),h=t.Pa,n=0;4>n;++n){for(a=0;8>a;++a)for(o=0;3>o;++o)for(u=0;11>u;++u)c=k(s,nC[n][a][o][u])?g(s,8):nk[n][a][o][u],h.Wc[n][a].Yb[o][u]=c;for(a=0;17>a;++a)h.Xc[n][a]=h.Wc[n][nE[a]]}return t.kc=P(s),t.kc&&(t.Bd=g(s,8)),t.cb=1}function tZ(t,e,r,n,i,o,a){var s=e[i].Yb[r];for(r=0;16>i;++i){if(!k(t,s[r+0]))return i;for(;!k(t,s[r+1]);)if(s=e[++i].Yb[0],r=0,16==i)return 16;var h=e[i+1].Yb;if(k(t,s[r+2])){var u=t,l=0;if(k(u,(f=s)[(c=r)+3]))if(k(u,f[c+6])){for(s=0,c=2*(l=k(u,f[c+8]))+(f=k(u,f[c+9+l])),l=0,f=nN[c];f[s];++s)l+=l+k(u,f[s]);l+=3+(8<<c)}else l=k(u,f[c+7])?7+2*k(u,165)+k(u,145):5+k(u,159);else l=k(u,f[c+4])?3+k(u,f[c+5]):2;s=h[2]}else l=1,s=h[1];h=a+nS[i],0>(u=t).b&&S(u);var c,f=u.b,d=(c=u.Ca>>1)-(u.I>>f)>>31;--u.b,u.Ca+=d,u.Ca|=1,u.I-=(c+1&d)<<f,o[h]=((l^d)-d)*n[(0<i)+0]}return 16}function t$(t){var e=t.rb[t.sb-1];e.la=0,e.Na=0,i(t.zc,0,0,t.zc.length),t.ja=0}function tQ(t,e,r,n,i){i=t[e+r+32*n]+(i>>3),t[e+r+32*n]=-256&i?0>i?0:255:i}function t1(t,e,r,n,i,o){tQ(t,e,0,r,n+i),tQ(t,e,1,r,n+o),tQ(t,e,2,r,n-o),tQ(t,e,3,r,n-i)}function t2(t){return(20091*t>>16)+t}function t0(t,e,r,n){var i,a=0,s=o(16);for(i=0;4>i;++i){var h=t[e+0]+t[e+8],u=t[e+0]-t[e+8],l=(35468*t[e+4]>>16)-t2(t[e+12]),c=t2(t[e+4])+(35468*t[e+12]>>16);s[a+0]=h+c,s[a+1]=u+l,s[a+2]=u-l,s[a+3]=h-c,a+=4,e++}for(i=a=0;4>i;++i)h=(t=s[a+0]+4)+s[a+8],u=t-s[a+8],l=(35468*s[a+4]>>16)-t2(s[a+12]),tQ(r,n,0,0,h+(c=t2(s[a+4])+(35468*s[a+12]>>16))),tQ(r,n,1,0,u+l),tQ(r,n,2,0,u-l),tQ(r,n,3,0,h-c),a++,n+=32}function t5(t,e,r,n){var i=t[e+0]+4,o=35468*t[e+4]>>16,a=t2(t[e+4]),s=35468*t[e+1]>>16;t1(r,n,0,i+a,t=t2(t[e+1]),s),t1(r,n,1,i+o,t,s),t1(r,n,2,i-o,t,s),t1(r,n,3,i-a,t,s)}function t3(t,e,r,n,i){t0(t,e,r,n),i&&t0(t,e+16,r,n+4)}function t4(t,e,r,n){rB(t,e+0,r,n,1),rB(t,e+32,r,n+128,1)}function t6(t,e,r,n){var i;for(t=t[e+0]+4,i=0;4>i;++i)for(e=0;4>e;++e)tQ(r,n,e,i,t)}function t8(t,e,r,n){t[e+0]&&rq(t,e+0,r,n),t[e+16]&&rq(t,e+16,r,n+4),t[e+32]&&rq(t,e+32,r,n+128),t[e+48]&&rq(t,e+48,r,n+128+4)}function t7(t,e,r,n){var i,a=o(16);for(i=0;4>i;++i){var s=t[e+0+i]+t[e+12+i],h=t[e+4+i]+t[e+8+i],u=t[e+4+i]-t[e+8+i],l=t[e+0+i]-t[e+12+i];a[0+i]=s+h,a[8+i]=s-h,a[4+i]=l+u,a[12+i]=l-u}for(i=0;4>i;++i)s=(t=a[0+4*i]+3)+a[3+4*i],h=a[1+4*i]+a[2+4*i],u=a[1+4*i]-a[2+4*i],l=t-a[3+4*i],r[n+0]=s+h>>3,r[n+16]=l+u>>3,r[n+32]=s-h>>3,r[n+48]=l-u>>3,n+=64}function t9(t,e,r){var n,i=e-32,o=255-t[i-1];for(n=0;n<r;++n){var a,s=o+t[e-1];for(a=0;a<r;++a)t[e+a]=ne[s+t[i+a]];e+=32}}function et(t,e){t9(t,e,4)}function ee(t,e){t9(t,e,8)}function er(t,e){t9(t,e,16)}function en(t,e){var r;for(r=0;16>r;++r)n(t,e+32*r,t,e-32,16)}function ei(t,e){var r;for(r=16;0<r;--r)i(t,e,t[e-1],16),e+=32}function eo(t,e,r){var n;for(n=0;16>n;++n)i(e,r+32*n,t,16)}function ea(t,e){var r,n=16;for(r=0;16>r;++r)n+=t[e-1+32*r]+t[e+r-32];eo(n>>5,t,e)}function es(t,e){var r,n=8;for(r=0;16>r;++r)n+=t[e-1+32*r];eo(n>>4,t,e)}function eh(t,e){var r,n=8;for(r=0;16>r;++r)n+=t[e+r-32];eo(n>>4,t,e)}function eu(t,e){eo(128,t,e)}function el(t,e,r){return t+2*e+r+2>>2}function ec(t,e){var r,i=e-32;for(r=0,i=new Uint8Array([el(t[i-1],t[i+0],t[i+1]),el(t[i+0],t[i+1],t[i+2]),el(t[i+1],t[i+2],t[i+3]),el(t[i+2],t[i+3],t[i+4])]);4>r;++r)n(t,e+32*r,i,0,i.length)}function ef(t,e){var r=t[e-1],n=t[e-1+32],i=t[e-1+64],o=t[e-1+96];I(t,e+0,0x1010101*el(t[e-1-32],r,n)),I(t,e+32,0x1010101*el(r,n,i)),I(t,e+64,0x1010101*el(n,i,o)),I(t,e+96,0x1010101*el(i,o,o))}function ed(t,e){var r,n=4;for(r=0;4>r;++r)n+=t[e+r-32]+t[e-1+32*r];for(n>>=3,r=0;4>r;++r)i(t,e+32*r,n,4)}function ep(t,e){var r=t[e-1+0],n=t[e-1+32],i=t[e-1+64],o=t[e-1-32],a=t[e+0-32],s=t[e+1-32],h=t[e+2-32],u=t[e+3-32];t[e+0+96]=el(n,i,t[e-1+96]),t[e+1+96]=t[e+0+64]=el(r,n,i),t[e+2+96]=t[e+1+64]=t[e+0+32]=el(o,r,n),t[e+3+96]=t[e+2+64]=t[e+1+32]=t[e+0+0]=el(a,o,r),t[e+3+64]=t[e+2+32]=t[e+1+0]=el(s,a,o),t[e+3+32]=t[e+2+0]=el(h,s,a),t[e+3+0]=el(u,h,s)}function eg(t,e){var r=t[e+1-32],n=t[e+2-32],i=t[e+3-32],o=t[e+4-32],a=t[e+5-32],s=t[e+6-32],h=t[e+7-32];t[e+0+0]=el(t[e+0-32],r,n),t[e+1+0]=t[e+0+32]=el(r,n,i),t[e+2+0]=t[e+1+32]=t[e+0+64]=el(n,i,o),t[e+3+0]=t[e+2+32]=t[e+1+64]=t[e+0+96]=el(i,o,a),t[e+3+32]=t[e+2+64]=t[e+1+96]=el(o,a,s),t[e+3+64]=t[e+2+96]=el(a,s,h),t[e+3+96]=el(s,h,h)}function em(t,e){var r=t[e-1+0],n=t[e-1+32],i=t[e-1+64],o=t[e-1-32],a=t[e+0-32],s=t[e+1-32],h=t[e+2-32],u=t[e+3-32];t[e+0+0]=t[e+1+64]=o+a+1>>1,t[e+1+0]=t[e+2+64]=a+s+1>>1,t[e+2+0]=t[e+3+64]=s+h+1>>1,t[e+3+0]=h+u+1>>1,t[e+0+96]=el(i,n,r),t[e+0+64]=el(n,r,o),t[e+0+32]=t[e+1+96]=el(r,o,a),t[e+1+32]=t[e+2+96]=el(o,a,s),t[e+2+32]=t[e+3+96]=el(a,s,h),t[e+3+32]=el(s,h,u)}function eb(t,e){var r=t[e+0-32],n=t[e+1-32],i=t[e+2-32],o=t[e+3-32],a=t[e+4-32],s=t[e+5-32],h=t[e+6-32],u=t[e+7-32];t[e+0+0]=r+n+1>>1,t[e+1+0]=t[e+0+64]=n+i+1>>1,t[e+2+0]=t[e+1+64]=i+o+1>>1,t[e+3+0]=t[e+2+64]=o+a+1>>1,t[e+0+32]=el(r,n,i),t[e+1+32]=t[e+0+96]=el(n,i,o),t[e+2+32]=t[e+1+96]=el(i,o,a),t[e+3+32]=t[e+2+96]=el(o,a,s),t[e+3+64]=el(a,s,h),t[e+3+96]=el(s,h,u)}function ev(t,e){var r=t[e-1+0],n=t[e-1+32],i=t[e-1+64],o=t[e-1+96];t[e+0+0]=r+n+1>>1,t[e+2+0]=t[e+0+32]=n+i+1>>1,t[e+2+32]=t[e+0+64]=i+o+1>>1,t[e+1+0]=el(r,n,i),t[e+3+0]=t[e+1+32]=el(n,i,o),t[e+3+32]=t[e+1+64]=el(i,o,o),t[e+3+64]=t[e+2+64]=t[e+0+96]=t[e+1+96]=t[e+2+96]=t[e+3+96]=o}function ey(t,e){var r=t[e-1+0],n=t[e-1+32],i=t[e-1+64],o=t[e-1+96],a=t[e-1-32],s=t[e+0-32],h=t[e+1-32],u=t[e+2-32];t[e+0+0]=t[e+2+32]=r+a+1>>1,t[e+0+32]=t[e+2+64]=n+r+1>>1,t[e+0+64]=t[e+2+96]=i+n+1>>1,t[e+0+96]=o+i+1>>1,t[e+3+0]=el(s,h,u),t[e+2+0]=el(a,s,h),t[e+1+0]=t[e+3+32]=el(r,a,s),t[e+1+32]=t[e+3+64]=el(n,r,a),t[e+1+64]=t[e+3+96]=el(i,n,r),t[e+1+96]=el(o,i,n)}function ew(t,e){var r;for(r=0;8>r;++r)n(t,e+32*r,t,e-32,8)}function ex(t,e){var r;for(r=0;8>r;++r)i(t,e,t[e-1],8),e+=32}function e_(t,e,r){var n;for(n=0;8>n;++n)i(e,r+32*n,t,8)}function eA(t,e){var r,n=8;for(r=0;8>r;++r)n+=t[e+r-32]+t[e-1+32*r];e_(n>>4,t,e)}function eL(t,e){var r,n=4;for(r=0;8>r;++r)n+=t[e+r-32];e_(n>>3,t,e)}function eN(t,e){var r,n=4;for(r=0;8>r;++r)n+=t[e-1+32*r];e_(n>>3,t,e)}function eS(t,e){e_(128,t,e)}function eP(t,e,r){var n=t[e-r],i=t[e+0],o=3*(i-n)+r9[1020+t[e-2*r]-t[e+r]],a=nt[112+(o+4>>3)];t[e-r]=ne[255+n+nt[112+(o+3>>3)]],t[e+0]=ne[255+i-a]}function ek(t,e,r,n){var i=t[e+0],o=t[e+r];return nr[255+t[e-2*r]-t[e-r]]>n||nr[255+o-i]>n}function eI(t,e,r,n){return 4*nr[255+t[e-r]-t[e+0]]+nr[255+t[e-2*r]-t[e+r]]<=n}function eC(t,e,r,n,i){var o=t[e-3*r],a=t[e-2*r],s=t[e-r],h=t[e+0],u=t[e+r],l=t[e+2*r],c=t[e+3*r];return 4*nr[255+s-h]+nr[255+a-u]>n?0:nr[255+t[e-4*r]-o]<=i&&nr[255+o-a]<=i&&nr[255+a-s]<=i&&nr[255+c-l]<=i&&nr[255+l-u]<=i&&nr[255+u-h]<=i}function eE(t,e,r,n){var i=2*n+1;for(n=0;16>n;++n)eI(t,e+n,r,i)&&eP(t,e+n,r)}function eF(t,e,r,n){var i=2*n+1;for(n=0;16>n;++n)eI(t,e+n*r,1,i)&&eP(t,e+n*r,1)}function eO(t,e,r,n){var i;for(i=3;0<i;--i)eE(t,e+=4*r,r,n)}function eT(t,e,r,n){var i;for(i=3;0<i;--i)eF(t,e+=4,r,n)}function eM(t,e,r,n,i,o,a,s){for(o=2*o+1;0<i--;){if(eC(t,e,r,o,a))if(ek(t,e,r,s))eP(t,e,r);else{var h=e,u=t[h-2*r],l=t[h-r],c=t[h+0],f=t[h+r],d=t[h+2*r],p=27*(m=r9[1020+3*(c-l)+r9[1020+u-f]])+63>>7,g=18*m+63>>7,m=9*m+63>>7;t[h-3*r]=ne[255+t[h-3*r]+m],t[h-2*r]=ne[255+u+g],t[h-r]=ne[255+l+p],t[h+0]=ne[255+c-p],t[h+r]=ne[255+f-g],t[h+2*r]=ne[255+d-m]}e+=n}}function ej(t,e,r,n,i,o,a,s){for(o=2*o+1;0<i--;){if(eC(t,e,r,o,a))if(ek(t,e,r,s))eP(t,e,r);else{var h=e,u=t[h-r],l=t[h+0],c=t[h+r],f=nt[112+((d=3*(l-u))+4>>3)],d=nt[112+(d+3>>3)],p=f+1>>1;t[h-2*r]=ne[255+t[h-2*r]+p],t[h-r]=ne[255+u+d],t[h+0]=ne[255+l-f],t[h+r]=ne[255+c-p]}e+=n}}function eB(t,e,r,n,i,o){eM(t,e,r,1,16,n,i,o)}function eR(t,e,r,n,i,o){eM(t,e,1,r,16,n,i,o)}function eD(t,e,r,n,i,o){var a;for(a=3;0<a;--a)ej(t,e+=4*r,r,1,16,n,i,o)}function eq(t,e,r,n,i,o){var a;for(a=3;0<a;--a)ej(t,e+=4,1,r,16,n,i,o)}function eU(t,e,r,n,i,o,a,s){eM(t,e,i,1,8,o,a,s),eM(r,n,i,1,8,o,a,s)}function ez(t,e,r,n,i,o,a,s){eM(t,e,1,i,8,o,a,s),eM(r,n,1,i,8,o,a,s)}function eH(t,e,r,n,i,o,a,s){ej(t,e+4*i,i,1,8,o,a,s),ej(r,n+4*i,i,1,8,o,a,s)}function eW(t,e,r,n,i,o,a,s){ej(t,e+4,1,i,8,o,a,s),ej(r,n+4,1,i,8,o,a,s)}function eV(){this.ba=new ta,this.ec=[],this.cc=[],this.Mc=[],this.Dc=this.Nc=this.dc=this.fc=0,this.Oa=new th,this.memory=0,this.Ib="OutputFunc",this.Jb="OutputAlphaFunc",this.Nd="OutputRowFunc"}function eG(){this.data=[],this.offset=this.kd=this.ha=this.w=0,this.na=[],this.xa=this.gb=this.Ja=this.Sa=this.P=0}function eY(){this.nc=this.Ea=this.b=this.hc=0,this.K=[],this.w=0}function eJ(){this.ua=0,this.Wa=new M,this.vb=new M,this.md=this.xc=this.wc=0,this.vc=[],this.Wb=0,this.Ya=new d,this.yc=new c}function eK(){this.xb=this.a=0,this.l=new tG,this.ca=new ta,this.V=[],this.Ba=0,this.Ta=[],this.Ua=0,this.m=new x,this.Pb=0,this.wd=new x,this.Ma=this.$=this.C=this.i=this.c=this.xd=0,this.s=new eJ,this.ab=0,this.gc=a(4,eY),this.Oc=0}function eX(){this.Lc=this.Z=this.$a=this.i=this.c=0,this.l=new tG,this.ic=0,this.ca=[],this.tb=0,this.qd=null,this.rd=0}function eZ(t,e,r,n,i,o,a){for(t=null==t?0:t[e+0],e=0;e<a;++e)i[o+e]=t+r[n+e]&255,t=i[o+e]}function e$(t,e,r,n,i,o,a){var s;if(null==t)eZ(null,null,r,n,i,o,a);else for(s=0;s<a;++s)i[o+s]=t[e+s]+r[n+s]&255}function eQ(t,e,r,n,i,o,a){if(null==t)eZ(null,null,r,n,i,o,a);else{var s,h=t[e+0],u=h,l=h;for(s=0;s<a;++s)u=l+(h=t[e+s])-u,l=r[n+s]+(-256&u?0>u?0:255:u)&255,u=h,i[o+s]=l}}function e1(t,e,r,n,i,o){for(;0<i--;){var a,s=e+ +!!r,h=e+3*!r;for(a=0;a<n;++a){var u=t[h+4*a];255!=u&&(u*=32897,t[s+4*a+0]=t[s+4*a+0]*u>>23,t[s+4*a+1]=t[s+4*a+1]*u>>23,t[s+4*a+2]=t[s+4*a+2]*u>>23)}e+=o}}function e2(t,e,r,n,i){for(;0<n--;){var o;for(o=0;o<r;++o){var a=t[e+2*o+0],s=15&(u=t[e+2*o+1]),h=4369*s,u=(240&u|u>>4)*h>>16;t[e+2*o+0]=(240&a|a>>4)*h>>16&240|(15&a|a<<4)*h>>16>>4&15,t[e+2*o+1]=240&u|s}e+=i}}function e0(t,e,r,n,i,o,a,s){var h,u,l=255;for(u=0;u<i;++u){for(h=0;h<n;++h){var c=t[e+h];o[a+4*h]=c,l&=c}e+=r,a+=s}return 255!=l}function e5(t,e,r,n,i){var o;for(o=0;o<i;++o)r[n+o]=t[e+o]>>8}function e3(){r1=e1,r2=e2,r0=e0,r5=e5}function e4(r,n,i){t[r]=function(t,r,o,a,s,h,u,l,c,f,d,p,g,m,b,v,y){var w,x=y-1>>1,_=s[h+0]|u[l+0]<<16,A=c[f+0]|d[p+0]<<16;e(null!=t);var L=3*_+A+131074>>2;for(n(t[r+0],255&L,L>>16,g,m),null!=o&&(L=3*A+_+131074>>2,n(o[a+0],255&L,L>>16,b,v)),w=1;w<=x;++w){var N=s[h+w]|u[l+w]<<16,S=c[f+w]|d[p+w]<<16,P=_+N+A+S+524296,k=P+2*(N+A)>>3;L=k+_>>1,_=(P=P+2*(_+S)>>3)+N>>1,n(t[r+2*w-1],255&L,L>>16,g,m+(2*w-1)*i),n(t[r+2*w-0],255&_,_>>16,g,m+(2*w-0)*i),null!=o&&(L=P+A>>1,_=k+S>>1,n(o[a+2*w-1],255&L,L>>16,b,v+(2*w-1)*i),n(o[a+2*w+0],255&_,_>>16,b,v+(2*w+0)*i)),_=N,A=S}1&y||(L=3*_+A+131074>>2,n(t[r+y-1],255&L,L>>16,g,m+(y-1)*i),null!=o&&(L=3*A+_+131074>>2,n(o[a+y-1],255&L,L>>16,b,v+(y-1)*i)))}}function e6(){nR[nn]=nD,nR[ni]=nU,nR[no]=nq,nR[na]=nz,nR[ns]=nH,nR[nh]=nW,nR[nu]=nV,nR[nl]=nU,nR[nc]=nz,nR[nf]=nH,nR[nd]=nW}function e8(t){return t&~nZ?0>t?0:255:t>>nX}function e7(t,e){return e8((19077*t>>8)+(26149*e>>8)-14234)}function e9(t,e,r){return e8((19077*t>>8)-(6419*e>>8)-(13320*r>>8)+8708)}function rt(t,e){return e8((19077*t>>8)+(33050*e>>8)-17685)}function re(t,e,r,n,i){n[i+0]=e7(t,r),n[i+1]=e9(t,e,r),n[i+2]=rt(t,e)}function rr(t,e,r,n,i){n[i+0]=rt(t,e),n[i+1]=e9(t,e,r),n[i+2]=e7(t,r)}function rn(t,e,r,n,i){var o=e9(t,e,r);e=o<<3&224|rt(t,e)>>3,n[i+0]=248&e7(t,r)|o>>5,n[i+1]=e}function ri(t,e,r,n,i){var o=240&rt(t,e)|15;n[i+0]=240&e7(t,r)|e9(t,e,r)>>4,n[i+1]=o}function ro(t,e,r,n,i){n[i+0]=255,re(t,e,r,n,i+1)}function ra(t,e,r,n,i){rr(t,e,r,n,i),n[i+3]=255}function rs(t,e,r,n,i){re(t,e,r,n,i),n[i+3]=255}function tV(t,e){return 0>t?0:t>e?e:t}function rh(e,r,n){t[e]=function(t,e,i,o,a,s,h,u,l){for(var c=u+(-2&l)*n;u!=c;)r(t[e+0],i[o+0],a[s+0],h,u),r(t[e+1],i[o+0],a[s+0],h,u+n),e+=2,++o,++s,u+=2*n;1&l&&r(t[e+0],i[o+0],a[s+0],h,u)}}function ru(t,e,r){return 0==r?0==t?0==e?6:5:4*(0==e):r}function rl(t,e,r,n,i){switch(t>>>30){case 3:rB(e,r,n,i,0);break;case 2:rR(e,r,n,i);break;case 1:rq(e,r,n,i)}}function rc(t,e){var r,o,a=e.M,s=e.Nb,h=t.oc,u=t.pc+40,l=t.oc,c=t.pc+584,f=t.oc,d=t.pc+600;for(r=0;16>r;++r)h[u+32*r-1]=129;for(r=0;8>r;++r)l[c+32*r-1]=129,f[d+32*r-1]=129;for(0<a?h[u-1-32]=l[c-1-32]=f[d-1-32]=129:(i(h,u-32-1,127,21),i(l,c-32-1,127,9),i(f,d-32-1,127,9)),o=0;o<t.za;++o){var p=e.ya[e.aa+o];if(0<o){for(r=-1;16>r;++r)n(h,u+32*r-4,h,u+32*r+12,4);for(r=-1;8>r;++r)n(l,c+32*r-4,l,c+32*r+4,4),n(f,d+32*r-4,f,d+32*r+4,4)}var g=t.Gd,m=t.Hd+o,b=p.ad,v=p.Hc;if(0<a&&(n(h,u-32,g[m].y,0,16),n(l,c-32,g[m].f,0,8),n(f,d-32,g[m].ea,0,8)),p.Za){var y=h,w=u-32+16;for(0<a&&(o>=t.za-1?i(y,w,g[m].y[15],4):n(y,w,g[m+1].y,0,4)),r=0;4>r;r++)y[w+128+r]=y[w+256+r]=y[w+384+r]=y[w+0+r];for(r=0;16>r;++r,v<<=2)y=h,w=u+n4[r],nO[p.Ob[r]](y,w),rl(v,b,16*r,y,w)}else if(nF[y=ru(o,a,p.Ob[0])](h,u),0!=v)for(r=0;16>r;++r,v<<=2)rl(v,b,16*r,h,u+n4[r]);for(r=p.Gc,nT[y=ru(o,a,p.Dd)](l,c),nT[y](f,d),v=b,y=l,w=c,255&(p=0|r)&&(170&p?rD(v,256,y,w):rU(v,256,y,w)),p=f,v=d,255&(r>>=8)&&(170&r?rD(b,320,p,v):rU(b,320,p,v)),a<t.Ub-1&&(n(g[m].y,0,h,u+480,16),n(g[m].f,0,l,c+224,8),n(g[m].ea,0,f,d+224,8)),r=8*s*t.B,g=t.sa,m=t.ta+16*o+16*s*t.R,b=t.qa,p=t.ra+8*o+r,v=t.Ha,y=t.Ia+8*o+r,r=0;16>r;++r)n(g,m+r*t.R,h,u+32*r,16);for(r=0;8>r;++r)n(b,p+r*t.B,l,c+32*r,8),n(v,y+r*t.B,f,d+32*r,8)}}function rf(t,n,i,o,a,s,h,u,l){var c=[0],f=[0],d=0,p=null!=l?l.kd:0,g=null!=l?l:new eG;if(null==t||12>i)return 7;g.data=t,g.w=n,g.ha=i,n=[n],i=[i],g.gb=[g.gb];t:{var m=n,v=i,y=g.gb;if(e(null!=t),e(null!=v),e(null!=y),y[0]=0,12<=v[0]&&!r(t,m[0],"RIFF")){if(r(t,m[0]+8,"WEBP")){y=3;break t}var w=F(t,m[0]+4);if(12>w||0xfffffff6<w){y=3;break t}if(p&&w>v[0]-8){y=7;break t}y[0]=w,m[0]+=12,v[0]-=12}y=0}if(0!=y)return y;for(w=0<g.gb[0],i=i[0];;){t:{var _=t;v=n,y=i;var A=c,L=f,N=m=[0];if((k=d=[d])[0]=0,8>y[0])y=7;else{if(!r(_,v[0],"VP8X")){if(10!=F(_,v[0]+4)){y=3;break t}if(18>y[0]){y=7;break t}var S=F(_,v[0]+8),P=1+E(_,v[0]+12);if(0x80000000<=P*(_=1+E(_,v[0]+15))){y=3;break t}null!=N&&(N[0]=S),null!=A&&(A[0]=P),null!=L&&(L[0]=_),v[0]+=18,y[0]-=18,k[0]=1}y=0}}if(d=d[0],m=m[0],0!=y)return y;if(v=!!(2&m),!w&&d)return 3;if(null!=s&&(s[0]=!!(16&m)),null!=h&&(h[0]=v),null!=u&&(u[0]=0),h=c[0],m=f[0],d&&v&&null==l){y=0;break}if(4>i){y=7;break}if(w&&d||!w&&!d&&!r(t,n[0],"ALPH")){i=[i],g.na=[g.na],g.P=[g.P],g.Sa=[g.Sa];t:{S=t,y=n,w=i;var k=g.gb;A=g.na,L=g.P,N=g.Sa,P=22,e(null!=S),e(null!=w),_=y[0];var I=w[0];for(e(null!=A),e(null!=N),A[0]=null,L[0]=null,N[0]=0;;){if(y[0]=_,w[0]=I,8>I){y=7;break t}var C=F(S,_+4);if(0xfffffff6<C){y=3;break t}var O=8+C+1&-2;if(P+=O,0<k&&P>k){y=3;break t}if(!r(S,_,"VP8 ")||!r(S,_,"VP8L")){y=0;break t}if(I[0]<O){y=7;break t}r(S,_,"ALPH")||(A[0]=S,L[0]=_+8,N[0]=C),_+=O,I-=O}}if(i=i[0],g.na=g.na[0],g.P=g.P[0],g.Sa=g.Sa[0],0!=y)break}i=[i],g.Ja=[g.Ja],g.xa=[g.xa];t:if(k=t,y=n,w=i,A=g.gb[0],L=g.Ja,N=g.xa,_=!r(k,S=y[0],"VP8 "),P=!r(k,S,"VP8L"),e(null!=k),e(null!=w),e(null!=L),e(null!=N),8>w[0])y=7;else{if(_||P){if(k=F(k,S+4),12<=A&&k>A-12){y=3;break t}if(p&&k>w[0]-8){y=7;break t}L[0]=k,y[0]+=8,w[0]-=8,N[0]=P}else N[0]=5<=w[0]&&47==k[S+0]&&!(k[S+4]>>5),L[0]=w[0];y=0}if(i=i[0],g.Ja=g.Ja[0],g.xa=g.xa[0],n=n[0],0!=y)break;if(0xfffffff6<g.Ja)return 3;if(null==u||v||(u[0]=g.xa?2:1),h=[h],m=[m],g.xa){if(5>i){y=7;break}u=h,p=m,v=s,null==t||5>i?t=0:5<=i&&47==t[n+0]&&!(t[n+4]>>5)?(w=[0],k=[0],A=[0],b(L=new x,t,n,i),tg(L,w,k,A)?(null!=u&&(u[0]=w[0]),null!=p&&(p[0]=k[0]),null!=v&&(v[0]=A[0]),t=1):t=0):t=0}else{if(10>i){y=7;break}u=m,null==t||10>i||!tK(t,n+3,i-3)?t=0:(p=t[n+0]|t[n+1]<<8|t[n+2]<<16,v=16383&(t[n+7]<<8|t[n+6]),t=16383&(t[n+9]<<8|t[n+8]),1&p||3<(p>>1&7)||!(p>>4&1)||p>>5>=g.Ja||!v||!t?t=0:(h&&(h[0]=v),u&&(u[0]=t),t=1))}if(!t||(h=h[0],m=m[0],d&&(c[0]!=h||f[0]!=m)))return 3;null!=l&&(l[0]=g,l.offset=n-l.w,e(0xfffffff6>n-l.w),e(l.offset==l.ha-i));break}return 0==y||7==y&&d&&null==l?(null!=s&&(s[0]|=null!=g.na&&0<g.na.length),null!=o&&(o[0]=h),null!=a&&(a[0]=m),0):y}function rd(t,e,r){var n=e.width,i=e.height,o=0,a=0,s=n,h=i;if(e.Da=null!=t&&0<t.Da,e.Da&&(s=t.cd,h=t.bd,o=t.v,a=t.j,11>r||(o&=-2,a&=-2),0>o||0>a||0>=s||0>=h||o+s>n||a+h>i))return 0;if(e.v=o,e.j=a,e.va=o+s,e.o=a+h,e.U=s,e.T=h,e.da=null!=t&&0<t.da,e.da){if(!j(s,h,r=[t.ib],o=[t.hb]))return 0;e.ib=r[0],e.hb=o[0]}return e.ob=null!=t&&t.ob,e.Kb=null==t||!t.Sd,e.da&&(e.ob=e.ib<3*n/4&&e.hb<3*i/4,e.Kb=0),1}function rp(t){if(null==t)return 2;if(11>t.S){var e=t.f.RGBA;e.fb+=(t.height-1)*e.A,e.A=-e.A}else e=t.f.kb,t=t.height,e.O+=(t-1)*e.fa,e.fa=-e.fa,e.N+=(t-1>>1)*e.Ab,e.Ab=-e.Ab,e.W+=(t-1>>1)*e.Db,e.Db=-e.Db,null!=e.F&&(e.J+=(t-1)*e.lb,e.lb=-e.lb);return 0}function rg(t,e,r,n){if(null==n||0>=t||0>=e)return 2;if(null!=r){if(r.Da){var i=r.cd,a=r.bd,s=-2&r.v,h=-2&r.j;if(0>s||0>h||0>=i||0>=a||s+i>t||h+a>e)return 2;t=i,e=a}if(r.da){if(!j(t,e,i=[r.ib],a=[r.hb]))return 2;t=i[0],e=a[0]}}n.width=t,n.height=e;t:{var u=n.width,l=n.height;if(t=n.S,0>=u||0>=l||!(t>=nn&&13>t))t=2;else{if(0>=n.Rd&&null==n.sd){s=a=i=e=0;var c=(h=u*n7[t])*l;if(11>t||(a=(l+1)/2*(e=(u+1)/2),12==t&&(s=(i=u)*l)),null==(l=o(c+2*a+s))){t=1;break t}n.sd=l,11>t?((u=n.f.RGBA).eb=l,u.fb=0,u.A=h,u.size=c):((u=n.f.kb).y=l,u.O=0,u.fa=h,u.Fd=c,u.f=l,u.N=0+c,u.Ab=e,u.Cd=a,u.ea=l,u.W=0+c+a,u.Db=e,u.Ed=a,12==t&&(u.F=l,u.J=0+c+2*a),u.Tc=s,u.lb=i)}if(e=1,i=n.S,a=n.width,s=n.height,i>=nn&&13>i)if(11>i)e&=(h=Math.abs((t=n.f.RGBA).A))*(s-1)+a<=t.size,e&=h>=a*n7[i],e&=null!=t.eb;else{t=n.f.kb,h=(a+1)/2,c=(s+1)/2,u=Math.abs(t.fa),l=Math.abs(t.Ab);var f=Math.abs(t.Db),d=Math.abs(t.lb),p=d*(s-1)+a;e&=u*(s-1)+a<=t.Fd,e&=l*(c-1)+h<=t.Cd,e=(e&=f*(c-1)+h<=t.Ed)&u>=a&l>=h&f>=h&null!=t.y&null!=t.f&null!=t.ea,12==i&&(e&=d>=a,e&=p<=t.Tc,e&=null!=t.F)}else e=0;t=2*!e}}return 0!=t||null!=r&&r.fd&&(t=rp(n)),t}var rm=64,rb=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535,131071,262143,524287,1048575,2097151,4194303,8388607,0xffffff],rv=24,ry=32,rw=8,rx=[0,0,1,1,2,2,2,2,3,3,3,3,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7];D("Predictor0","PredictorAdd0"),t.Predictor0=function(){return 0xff000000},t.Predictor1=function(t){return t},t.Predictor2=function(t,e,r){return e[r+0]},t.Predictor3=function(t,e,r){return e[r+1]},t.Predictor4=function(t,e,r){return e[r-1]},t.Predictor5=function(t,e,r){return U(U(t,e[r+1]),e[r+0])},t.Predictor6=function(t,e,r){return U(t,e[r-1])},t.Predictor7=function(t,e,r){return U(t,e[r+0])},t.Predictor8=function(t,e,r){return U(e[r-1],e[r+0])},t.Predictor9=function(t,e,r){return U(e[r+0],e[r+1])},t.Predictor10=function(t,e,r){return U(U(t,e[r-1]),U(e[r+0],e[r+1]))},t.Predictor11=function(t,e,r){var n=e[r+0];return 0>=W(n>>24&255,t>>24&255,(e=e[r-1])>>24&255)+W(n>>16&255,t>>16&255,e>>16&255)+W(n>>8&255,t>>8&255,e>>8&255)+W(255&n,255&t,255&e)?n:t},t.Predictor12=function(t,e,r){var n=e[r+0];return(z((t>>24&255)+(n>>24&255)-((e=e[r-1])>>24&255))<<24|z((t>>16&255)+(n>>16&255)-(e>>16&255))<<16|z((t>>8&255)+(n>>8&255)-(e>>8&255))<<8|z((255&t)+(255&n)-(255&e)))>>>0},t.Predictor13=function(t,e,r){var n=e[r-1];return(H((t=U(t,e[r+0]))>>24&255,n>>24&255)<<24|H(t>>16&255,n>>16&255)<<16|H(t>>8&255,n>>8&255)<<8|H((0|t)&255,(0|n)&255))>>>0};var r_=t.PredictorAdd0;t.PredictorAdd1=V,D("Predictor2","PredictorAdd2"),D("Predictor3","PredictorAdd3"),D("Predictor4","PredictorAdd4"),D("Predictor5","PredictorAdd5"),D("Predictor6","PredictorAdd6"),D("Predictor7","PredictorAdd7"),D("Predictor8","PredictorAdd8"),D("Predictor9","PredictorAdd9"),D("Predictor10","PredictorAdd10"),D("Predictor11","PredictorAdd11"),D("Predictor12","PredictorAdd12"),D("Predictor13","PredictorAdd13");var rA=t.PredictorAdd2;K("ColorIndexInverseTransform","MapARGB","32b",function(t){return t>>8&255},function(t){return t}),K("VP8LColorIndexInverseTransformAlpha","MapAlpha","8b",function(t){return t},function(t){return t>>8&255});var rL,rN=t.ColorIndexInverseTransform,rS=t.MapARGB,rP=t.VP8LColorIndexInverseTransformAlpha,rk=t.MapAlpha,rI=t.VP8LPredictorsAdd=[];rI.length=16,(t.VP8LPredictors=[]).length=16,(t.VP8LPredictorsAdd_C=[]).length=16,(t.VP8LPredictors_C=[]).length=16;var rC,rE,rF,rO,rT,rM,rj,rB,rR,rD,rq,rU,rz,rH,rW,rV,rG,rY,rJ,rK,rX,rZ,r$,rQ,r1,r2,r0,r5,r3=o(511),r4=o(2041),r6=o(225),r8=o(767),r7=0,r9=r4,nt=r6,ne=r8,nr=r3,nn=0,ni=1,no=2,na=3,ns=4,nh=5,nu=6,nl=7,nc=8,nf=9,nd=10,np=[2,3,7],ng=[3,3,11],nm=[280,256,256,256,40],nb=[0,1,1,1,0],nv=[17,18,0,1,2,3,4,5,16,6,7,8,9,10,11,12,13,14,15],ny=[24,7,23,25,40,6,39,41,22,26,38,42,56,5,55,57,21,27,54,58,37,43,72,4,71,73,20,28,53,59,70,74,36,44,88,69,75,52,60,3,87,89,19,29,86,90,35,45,68,76,85,91,51,61,104,2,103,105,18,30,102,106,34,46,84,92,67,77,101,107,50,62,120,1,119,121,83,93,17,31,100,108,66,78,118,122,33,47,117,123,49,63,99,109,82,94,0,116,124,65,79,16,32,98,110,48,115,125,81,95,64,114,126,97,111,80,113,127,96,112],nw=[2954,2956,2958,2962,2970,2986,3018,3082,3212,3468,3980,5004],nx=8,n_=[4,5,6,7,8,9,10,10,11,12,13,14,15,16,17,17,18,19,20,20,21,21,22,22,23,23,24,25,25,26,27,28,29,30,31,32,33,34,35,36,37,37,38,39,40,41,42,43,44,45,46,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,76,77,78,79,80,81,82,83,84,85,86,87,88,89,91,93,95,96,98,100,101,102,104,106,108,110,112,114,116,118,122,124,126,128,130,132,134,136,138,140,143,145,148,151,154,157],nA=[4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,60,62,64,66,68,70,72,74,76,78,80,82,84,86,88,90,92,94,96,98,100,102,104,106,108,110,112,114,116,119,122,125,128,131,134,137,140,143,146,149,152,155,158,161,164,167,170,173,177,181,185,189,193,197,201,205,209,213,217,221,225,229,234,239,245,249,254,259,264,269,274,279,284],nL=null,nN=[[173,148,140,0],[176,155,140,135,0],[180,157,141,134,130,0],[254,254,243,230,196,177,153,140,133,130,129,0]],nS=[0,1,4,8,5,2,3,6,9,12,13,10,7,11,14,15],nP=[-0,1,-1,2,-2,3,4,6,-3,5,-4,-5,-6,7,-7,8,-8,-9],nk=[[[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]],[[253,136,254,255,228,219,128,128,128,128,128],[189,129,242,255,227,213,255,219,128,128,128],[106,126,227,252,214,209,255,255,128,128,128]],[[1,98,248,255,236,226,255,255,128,128,128],[181,133,238,254,221,234,255,154,128,128,128],[78,134,202,247,198,180,255,219,128,128,128]],[[1,185,249,255,243,255,128,128,128,128,128],[184,150,247,255,236,224,128,128,128,128,128],[77,110,216,255,236,230,128,128,128,128,128]],[[1,101,251,255,241,255,128,128,128,128,128],[170,139,241,252,236,209,255,255,128,128,128],[37,116,196,243,228,255,255,255,128,128,128]],[[1,204,254,255,245,255,128,128,128,128,128],[207,160,250,255,238,128,128,128,128,128,128],[102,103,231,255,211,171,128,128,128,128,128]],[[1,152,252,255,240,255,128,128,128,128,128],[177,135,243,255,234,225,128,128,128,128,128],[80,129,211,255,194,224,128,128,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[246,1,255,128,128,128,128,128,128,128,128],[255,128,128,128,128,128,128,128,128,128,128]]],[[[198,35,237,223,193,187,162,160,145,155,62],[131,45,198,221,172,176,220,157,252,221,1],[68,47,146,208,149,167,221,162,255,223,128]],[[1,149,241,255,221,224,255,255,128,128,128],[184,141,234,253,222,220,255,199,128,128,128],[81,99,181,242,176,190,249,202,255,255,128]],[[1,129,232,253,214,197,242,196,255,255,128],[99,121,210,250,201,198,255,202,128,128,128],[23,91,163,242,170,187,247,210,255,255,128]],[[1,200,246,255,234,255,128,128,128,128,128],[109,178,241,255,231,245,255,255,128,128,128],[44,130,201,253,205,192,255,255,128,128,128]],[[1,132,239,251,219,209,255,165,128,128,128],[94,136,225,251,218,190,255,255,128,128,128],[22,100,174,245,186,161,255,199,128,128,128]],[[1,182,249,255,232,235,128,128,128,128,128],[124,143,241,255,227,234,128,128,128,128,128],[35,77,181,251,193,211,255,205,128,128,128]],[[1,157,247,255,236,231,255,255,128,128,128],[121,141,235,255,225,227,255,255,128,128,128],[45,99,188,251,195,217,255,224,128,128,128]],[[1,1,251,255,213,255,128,128,128,128,128],[203,1,248,255,255,128,128,128,128,128,128],[137,1,177,255,224,255,128,128,128,128,128]]],[[[253,9,248,251,207,208,255,192,128,128,128],[175,13,224,243,193,185,249,198,255,255,128],[73,17,171,221,161,179,236,167,255,234,128]],[[1,95,247,253,212,183,255,255,128,128,128],[239,90,244,250,211,209,255,255,128,128,128],[155,77,195,248,188,195,255,255,128,128,128]],[[1,24,239,251,218,219,255,205,128,128,128],[201,51,219,255,196,186,128,128,128,128,128],[69,46,190,239,201,218,255,228,128,128,128]],[[1,191,251,255,255,128,128,128,128,128,128],[223,165,249,255,213,255,128,128,128,128,128],[141,124,248,255,255,128,128,128,128,128,128]],[[1,16,248,255,255,128,128,128,128,128,128],[190,36,230,255,236,255,128,128,128,128,128],[149,1,255,128,128,128,128,128,128,128,128]],[[1,226,255,128,128,128,128,128,128,128,128],[247,192,255,128,128,128,128,128,128,128,128],[240,128,255,128,128,128,128,128,128,128,128]],[[1,134,252,255,255,128,128,128,128,128,128],[213,62,250,255,255,128,128,128,128,128,128],[55,93,255,128,128,128,128,128,128,128,128]],[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]]],[[[202,24,213,235,186,191,220,160,240,175,255],[126,38,182,232,169,184,228,174,255,187,128],[61,46,138,219,151,178,240,170,255,216,128]],[[1,112,230,250,199,191,247,159,255,255,128],[166,109,228,252,211,215,255,174,128,128,128],[39,77,162,232,172,180,245,178,255,255,128]],[[1,52,220,246,198,199,249,220,255,255,128],[124,74,191,243,183,193,250,221,255,255,128],[24,71,130,219,154,170,243,182,255,255,128]],[[1,182,225,249,219,240,255,224,128,128,128],[149,150,226,252,216,205,255,171,128,128,128],[28,108,170,242,183,194,254,223,255,255,128]],[[1,81,230,252,204,203,255,192,128,128,128],[123,102,209,247,188,196,255,233,128,128,128],[20,95,153,243,164,173,255,203,128,128,128]],[[1,222,248,255,216,213,128,128,128,128,128],[168,175,246,252,235,205,255,255,128,128,128],[47,116,215,255,211,212,255,255,128,128,128]],[[1,121,236,253,212,214,255,255,128,128,128],[141,84,213,252,201,202,255,219,128,128,128],[42,80,160,240,162,185,255,205,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[244,1,255,128,128,128,128,128,128,128,128],[238,1,255,128,128,128,128,128,128,128,128]]]],nI=[[[231,120,48,89,115,113,120,152,112],[152,179,64,126,170,118,46,70,95],[175,69,143,80,85,82,72,155,103],[56,58,10,171,218,189,17,13,152],[114,26,17,163,44,195,21,10,173],[121,24,80,195,26,62,44,64,85],[144,71,10,38,171,213,144,34,26],[170,46,55,19,136,160,33,206,71],[63,20,8,114,114,208,12,9,226],[81,40,11,96,182,84,29,16,36]],[[134,183,89,137,98,101,106,165,148],[72,187,100,130,157,111,32,75,80],[66,102,167,99,74,62,40,234,128],[41,53,9,178,241,141,26,8,107],[74,43,26,146,73,166,49,23,157],[65,38,105,160,51,52,31,115,128],[104,79,12,27,217,255,87,17,7],[87,68,71,44,114,51,15,186,23],[47,41,14,110,182,183,21,17,194],[66,45,25,102,197,189,23,18,22]],[[88,88,147,150,42,46,45,196,205],[43,97,183,117,85,38,35,179,61],[39,53,200,87,26,21,43,232,171],[56,34,51,104,114,102,29,93,77],[39,28,85,171,58,165,90,98,64],[34,22,116,206,23,34,43,166,73],[107,54,32,26,51,1,81,43,31],[68,25,106,22,64,171,36,225,114],[34,19,21,102,132,188,16,76,124],[62,18,78,95,85,57,50,48,51]],[[193,101,35,159,215,111,89,46,111],[60,148,31,172,219,228,21,18,111],[112,113,77,85,179,255,38,120,114],[40,42,1,196,245,209,10,25,109],[88,43,29,140,166,213,37,43,154],[61,63,30,155,67,45,68,1,209],[100,80,8,43,154,1,51,26,71],[142,78,78,16,255,128,34,197,171],[41,40,5,102,211,183,4,1,221],[51,50,17,168,209,192,23,25,82]],[[138,31,36,171,27,166,38,44,229],[67,87,58,169,82,115,26,59,179],[63,59,90,180,59,166,93,73,154],[40,40,21,116,143,209,34,39,175],[47,15,16,183,34,223,49,45,183],[46,17,33,183,6,98,15,32,183],[57,46,22,24,128,1,54,17,37],[65,32,73,115,28,128,23,128,205],[40,3,9,115,51,192,18,6,223],[87,37,9,115,59,77,64,21,47]],[[104,55,44,218,9,54,53,130,226],[64,90,70,205,40,41,23,26,57],[54,57,112,184,5,41,38,166,213],[30,34,26,133,152,116,10,32,134],[39,19,53,221,26,114,32,73,255],[31,9,65,234,2,15,1,118,73],[75,32,12,51,192,255,160,43,51],[88,31,35,67,102,85,55,186,85],[56,21,23,111,59,205,45,37,192],[55,38,70,124,73,102,1,34,98]],[[125,98,42,88,104,85,117,175,82],[95,84,53,89,128,100,113,101,45],[75,79,123,47,51,128,81,171,1],[57,17,5,71,102,57,53,41,49],[38,33,13,121,57,73,26,1,85],[41,10,67,138,77,110,90,47,114],[115,21,2,10,102,255,166,23,6],[101,29,16,10,85,128,101,196,26],[57,18,10,102,102,213,34,20,43],[117,20,15,36,163,128,68,1,26]],[[102,61,71,37,34,53,31,243,192],[69,60,71,38,73,119,28,222,37],[68,45,128,34,1,47,11,245,171],[62,17,19,70,146,85,55,62,70],[37,43,37,154,100,163,85,160,1],[63,9,92,136,28,64,32,201,85],[75,15,9,9,64,255,184,119,16],[86,6,28,5,64,255,25,248,1],[56,8,17,132,137,255,55,116,128],[58,15,20,82,135,57,26,121,40]],[[164,50,31,137,154,133,25,35,218],[51,103,44,131,131,123,31,6,158],[86,40,64,135,148,224,45,183,128],[22,26,17,131,240,154,14,1,209],[45,16,21,91,64,222,7,1,197],[56,21,39,155,60,138,23,102,213],[83,12,13,54,192,255,68,47,28],[85,26,85,85,128,128,32,146,171],[18,11,7,63,144,171,4,4,246],[35,27,10,146,174,171,12,26,128]],[[190,80,35,99,180,80,126,54,45],[85,126,47,87,176,51,41,20,32],[101,75,128,139,118,146,116,128,85],[56,41,15,176,236,85,37,9,62],[71,30,17,119,118,255,17,18,138],[101,38,60,138,55,70,43,26,142],[146,36,19,30,171,255,97,27,20],[138,45,61,62,219,1,81,188,64],[32,41,20,117,151,142,20,21,163],[112,19,12,61,195,128,48,4,24]]],nC=[[[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[176,246,255,255,255,255,255,255,255,255,255],[223,241,252,255,255,255,255,255,255,255,255],[249,253,253,255,255,255,255,255,255,255,255]],[[255,244,252,255,255,255,255,255,255,255,255],[234,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255]],[[255,246,254,255,255,255,255,255,255,255,255],[239,253,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[251,255,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[251,254,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,254,253,255,254,255,255,255,255,255,255],[250,255,254,255,254,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[217,255,255,255,255,255,255,255,255,255,255],[225,252,241,253,255,255,254,255,255,255,255],[234,250,241,250,253,255,253,254,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[223,254,254,255,255,255,255,255,255,255,255],[238,253,254,254,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[249,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,255,255,255,255,255,255,255,255,255],[247,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[252,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[186,251,250,255,255,255,255,255,255,255,255],[234,251,244,254,255,255,255,255,255,255,255],[251,251,243,253,254,255,254,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[236,253,254,255,255,255,255,255,255,255,255],[251,253,253,254,254,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[254,254,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[254,254,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[248,255,255,255,255,255,255,255,255,255,255],[250,254,252,254,255,255,255,255,255,255,255],[248,254,249,253,255,255,255,255,255,255,255]],[[255,253,253,255,255,255,255,255,255,255,255],[246,253,253,255,255,255,255,255,255,255,255],[252,254,251,254,254,255,255,255,255,255,255]],[[255,254,252,255,255,255,255,255,255,255,255],[248,254,253,255,255,255,255,255,255,255,255],[253,255,254,254,255,255,255,255,255,255,255]],[[255,251,254,255,255,255,255,255,255,255,255],[245,251,254,255,255,255,255,255,255,255,255],[253,253,254,255,255,255,255,255,255,255,255]],[[255,251,253,255,255,255,255,255,255,255,255],[252,253,254,255,255,255,255,255,255,255,255],[255,254,255,255,255,255,255,255,255,255,255]],[[255,252,255,255,255,255,255,255,255,255,255],[249,255,254,255,255,255,255,255,255,255,255],[255,255,254,255,255,255,255,255,255,255,255]],[[255,255,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]]],nE=[0,1,2,3,6,4,5,6,6,6,6,6,6,6,6,7,0],nF=[],nO=[],nT=[],nM=1,nj=2,nB=[],nR=[];e4("UpsampleRgbLinePair",re,3),e4("UpsampleBgrLinePair",rr,3),e4("UpsampleRgbaLinePair",rs,4),e4("UpsampleBgraLinePair",ra,4),e4("UpsampleArgbLinePair",ro,4),e4("UpsampleRgba4444LinePair",ri,2),e4("UpsampleRgb565LinePair",rn,2);var nD=t.UpsampleRgbLinePair,nq=t.UpsampleBgrLinePair,nU=t.UpsampleRgbaLinePair,nz=t.UpsampleBgraLinePair,nH=t.UpsampleArgbLinePair,nW=t.UpsampleRgba4444LinePair,nV=t.UpsampleRgb565LinePair,nG=16,nY=32768,nJ=-227,nK=482,nX=6,nZ=16383,n$=0,nQ=o(256),n1=o(256),n2=o(256),n0=o(256),n5=o(nK-nJ),n3=o(nK-nJ);rh("YuvToRgbRow",re,3),rh("YuvToBgrRow",rr,3),rh("YuvToRgbaRow",rs,4),rh("YuvToBgraRow",ra,4),rh("YuvToArgbRow",ro,4),rh("YuvToRgba4444Row",ri,2),rh("YuvToRgb565Row",rn,2);var n4=[0,4,8,12,128,132,136,140,256,260,264,268,384,388,392,396],n6=[0,2,8],n8=[8,7,6,4,4,2,2,2,1,1,1,1];this.WebPDecodeRGBA=function(t,r,s,h,u){var l=ni,c=new eV,f=new ta;c.ba=f,f.S=l,f.width=[f.width],f.height=[f.height];var d=f.width,p=f.height,g=new ts;if(null==g||null==t)var m=2;else e(null!=g),m=rf(t,r,s,g.width,g.height,g.Pd,g.Qd,g.format,null);if(0!=m?d=0:(null!=d&&(d[0]=g.width[0]),null!=p&&(p[0]=g.height[0]),d=1),d){f.width=f.width[0],f.height=f.height[0],null!=h&&(h[0]=f.width),null!=u&&(u[0]=f.height);t:{if(h=new tG,(u=new eG).data=t,u.w=r,u.ha=s,u.kd=1,r=[0],e(null!=u),(0==(t=rf(u.data,u.w,u.ha,null,null,null,r,null,u))||7==t)&&r[0]&&(t=4),0==(r=t)){if(e(null!=c),h.data=u.data,h.w=u.w+u.offset,h.ha=u.ha-u.offset,h.put=td,h.ac=tf,h.bc=tp,h.ma=c,u.xa){if(null==(t=tk())){c=1;break t}if(function(t,r){for(var n=[0],i=[0],o=[0];;){if(null==t)return 0;if(null==r)return t.a=2,0;if(t.l=r,t.a=0,b(t.m,r.data,r.w,r.ha),!tg(t.m,n,i,o)){t.a=3;break}if(t.xb=nj,r.width=n[0],r.height=i[0],!tI(n[0],i[0],1,t,null))break;return 1}return e(0!=t.a),0}(t,h)){if(h=0==(r=rg(h.width,h.height,c.Oa,c.ba))){e:{for(h=t;;){if(null==h){h=0;break e}if(e(null!=h.s.yc),e(null!=h.s.Ya),e(0<h.s.Wb),e(null!=(s=h.l)),e(null!=(u=s.ma)),0!=h.xb){if(h.ca=u.ba,h.tb=u.tb,e(null!=h.ca),!rd(u.Oa,s,na)){h.a=2;break}if(!tC(h,s.width)||s.da)break;if((s.da||tn(h.ca.S))&&e3(),11>h.ca.S||(alert("todo:WebPInitConvertARGBToYUV"),null!=h.ca.f.kb.F&&e3()),h.Pb&&0<h.s.ua&&null==h.s.vb.X&&!O(h.s.vb,h.s.Wa.Xa)){h.a=1;break}h.xb=0}if(!tS(h,h.V,h.Ba,h.c,h.i,s.o,t_))break;u.Dc=h.Ma,h=1;break e}e(0!=h.a),h=0}h=!h}h&&(r=t.a)}else r=t.a}else if((t=new tY).Fa=u.na,t.P=u.P,t.qc=u.Sa,tX(t,h)){if(0==(r=rg(h.width,h.height,c.Oa,c.ba))){if(t.Aa=0,s=c.Oa,e(null!=(u=t)),null!=s){if(0<(d=0>(d=s.Md)?0:100<d?255:255*d/100)){for(p=g=0;4>p;++p)12>(m=u.pb[p]).lc&&(m.ia=d*n8[0>m.lc?0:m.lc]>>3),g|=m.ia;g&&(alert("todo:VP8InitRandom"),u.ia=1)}u.Ga=s.Id,100<u.Ga?u.Ga=100:0>u.Ga&&(u.Ga=0)}(function(t,r){if(null==t)return 0;if(null==r)return tJ(t,2,"NULL VP8Io parameter in VP8Decode().");if(!t.cb&&!tX(t,r))return 0;if(e(t.cb),null==r.ac||r.ac(r)){r.ob&&(t.L=0);var s=n6[t.L];if(2==t.L?(t.yb=0,t.zb=0):(t.yb=r.v-s>>4,t.zb=r.j-s>>4,0>t.yb&&(t.yb=0),0>t.zb&&(t.zb=0)),t.Va=r.o+15+s>>4,t.Hb=r.va+15+s>>4,t.Hb>t.za&&(t.Hb=t.za),t.Va>t.Ub&&(t.Va=t.Ub),0<t.L){var h,u=t.ed;for(s=0;4>s;++s){if(t.Qa.Cb){var l=t.Qa.Lb[s];t.Qa.Fb||(l+=u.Tb)}else l=u.Tb;for(h=0;1>=h;++h){var c=t.gd[s][h],f=l;if(u.Pc&&(f+=u.vd[0],h&&(f+=u.od[0])),0<(f=0>f?0:63<f?63:f)){var d=f;0<u.wb&&(d=4<u.wb?d>>2:d>>1)>9-u.wb&&(d=9-u.wb),1>d&&(d=1),c.dd=d,c.tc=2*f+d,c.ld=40<=f?2:+(15<=f)}else c.tc=0;c.La=h}}}s=0}else tJ(t,6,"Frame setup failed"),s=t.a;if(s=0==s){if(s){t.$c=0,0<t.Aa||(t.Ic=1);t:{s=t.Ic,u=4*(d=t.za);var p=32*d,g=d+1,m=0<t.L?d*(0<t.Aa?2:1):0,v=(2==t.Aa?2:1)*d;if((c=u+832+(h=3*(16*s+n6[t.L])/2*p)+(l=null!=t.Fa&&0<t.Fa.length?t.Kc.c*t.Kc.i:0))!=c)s=0;else{if(c>t.Vb){if(t.Vb=0,t.Ec=o(c),t.Fc=0,null==t.Ec){s=tJ(t,1,"no memory during frame initialization.");break t}t.Vb=c}c=t.Ec,f=t.Fc,t.Ac=c,t.Bc=f,f+=u,t.Gd=a(p,tH),t.Hd=0,t.rb=a(g+1,tD),t.sb=1,t.wa=m?a(m,tR):null,t.Y=0,t.D.Nb=0,t.D.wa=t.wa,t.D.Y=t.Y,0<t.Aa&&(t.D.Y+=d),e(!0),t.oc=c,t.pc=f,f+=832,t.ya=a(v,tU),t.aa=0,t.D.ya=t.ya,t.D.aa=t.aa,2==t.Aa&&(t.D.aa+=d),t.R=16*d,t.B=8*d,d=(p=n6[t.L])*t.R,p=p/2*t.B,t.sa=c,t.ta=f+d,t.qa=t.sa,t.ra=t.ta+16*s*t.R+p,t.Ha=t.qa,t.Ia=t.ra+8*s*t.B+p,t.$c=0,f+=h,t.mb=l?c:null,t.nb=l?f:null,e(f+l<=t.Fc+t.Vb),t$(t),i(t.Ac,t.Bc,0,u),s=1}}if(s){if(r.ka=0,r.y=t.sa,r.O=t.ta,r.f=t.qa,r.N=t.ra,r.ea=t.Ha,r.Vd=t.Ia,r.fa=t.R,r.Rc=t.B,r.F=null,r.J=0,!r7){for(s=-255;255>=s;++s)r3[255+s]=0>s?-s:s;for(s=-1020;1020>=s;++s)r4[1020+s]=-128>s?-128:127<s?127:s;for(s=-112;112>=s;++s)r6[112+s]=-16>s?-16:15<s?15:s;for(s=-255;510>=s;++s)r8[255+s]=0>s?0:255<s?255:s;r7=1}rj=t7,rB=t3,rD=t4,rq=t6,rU=t8,rR=t5,rz=eB,rH=eR,rW=eU,rV=ez,rG=eD,rY=eq,rJ=eH,rK=eW,rX=eE,rZ=eF,r$=eO,rQ=eT,nO[0]=ed,nO[1]=et,nO[2]=ec,nO[3]=ef,nO[4]=ep,nO[5]=em,nO[6]=eg,nO[7]=eb,nO[8]=ey,nO[9]=ev,nF[0]=ea,nF[1]=er,nF[2]=en,nF[3]=ei,nF[4]=es,nF[5]=eh,nF[6]=eu,nT[0]=eA,nT[1]=ee,nT[2]=ew,nT[3]=ex,nT[4]=eN,nT[5]=eL,nT[6]=eS,s=1}else s=0}s&&(s=function(t,r){for(t.M=0;t.M<t.Va;++t.M){var a,s=t.Jc[t.M&t.Xb],h=t.m,u=t;for(a=0;a<u.za;++a){var l=h,c=u,f=c.Ac,d=c.Bc+4*a,p=c.zc,g=c.ya[c.aa+a];if(c.Qa.Bb?g.$b=k(l,c.Pa.jb[0])?2+k(l,c.Pa.jb[2]):k(l,c.Pa.jb[1]):g.$b=0,c.kc&&(g.Ad=k(l,c.Bd)),g.Za=!k(l,145)+0,g.Za){var m=g.Ob,v=0;for(c=0;4>c;++c){var y,w=p[0+c];for(y=0;4>y;++y){w=nI[f[d+y]][w];for(var x=nP[k(l,w[0])];0<x;)x=nP[2*x+k(l,w[x])];w=-x,f[d+y]=w}n(m,v,f,d,4),v+=4,p[0+c]=w}}else w=k(l,156)?k(l,128)?1:3:2*!!k(l,163),g.Ob[0]=w,i(f,d,w,4),i(p,0,w,4);g.Dd=k(l,142)?k(l,114)?k(l,183)?1:3:2:0}if(u.m.Ka)return tJ(t,7,"Premature end-of-partition0 encountered.");for(;t.ja<t.za;++t.ja){if(u=s,l=(h=t).rb[h.sb-1],f=h.rb[h.sb+h.ja],a=h.ya[h.aa+h.ja],d=h.kc?a.Ad:0)l.la=f.la=0,a.Za||(l.Na=f.Na=0),a.Hc=0,a.Gc=0,a.ia=0;else{if(l=f,f=u,d=h.Pa.Xc,p=h.ya[h.aa+h.ja],g=h.pb[p.$b],c=p.ad,m=0,v=h.rb[h.sb-1],w=y=0,i(c,m,0,384),p.Za)var _,L,S=0,P=d[3];else{x=o(16);var I=l.Na+v.Na;if(I=nL(f,d[1],I,g.Eb,0,x,0),l.Na=v.Na=(0<I)+0,1<I)rj(x,0,c,m);else{var C=x[0]+3>>3;for(x=0;256>x;x+=16)c[m+x]=C}S=1,P=d[0]}var E=15&l.la,F=15&v.la;for(x=0;4>x;++x){var O=1&F;for(C=L=0;4>C;++C)E=E>>1|(O=(I=nL(f,P,I=O+(1&E),g.Sc,S,c,m))>S)<<7,L=L<<2|(3<I?3:1<I?2:0!=c[m+0]),m+=16;E>>=4,F=F>>1|O<<7,y=(y<<8|L)>>>0}for(P=E,S=F>>4,_=0;4>_;_+=2){for(L=0,E=l.la>>4+_,F=v.la>>4+_,x=0;2>x;++x){for(O=1&F,C=0;2>C;++C)I=O+(1&E),E=E>>1|(O=0<(I=nL(f,d[2],I,g.Qc,0,c,m)))<<3,L=L<<2|(3<I?3:1<I?2:0!=c[m+0]),m+=16;E>>=2,F=F>>1|O<<5}w|=L<<4*_,P|=E<<4<<_,S|=(240&F)<<_}l.la=P,v.la=S,p.Hc=y,p.Gc=w,p.ia=43690&w?0:g.ia,d=!(y|w)}if(0<h.L&&(h.wa[h.Y+h.ja]=h.gd[a.$b][a.Za],h.wa[h.Y+h.ja].La|=!d),u.Ka)return tJ(t,7,"Premature end-of-file encountered.")}if(t$(t),h=r,u=1,a=(s=t).D,l=0<s.L&&s.M>=s.zb&&s.M<=s.Va,0==s.Aa)t:{if(a.M=s.M,a.uc=l,rc(s,a),u=1,a=(L=s.D).Nb,l=(w=n6[s.L])*s.R,f=w/2*s.B,x=16*a*s.R,C=8*a*s.B,d=s.sa,p=s.ta-l+x,g=s.qa,c=s.ra-f+C,m=s.Ha,v=s.Ia-f+C,F=0==(E=L.M),y=E>=s.Va-1,2==s.Aa&&rc(s,L),L.uc)for(O=(I=s).D.M,e(I.D.uc),L=I.yb;L<I.Hb;++L){S=L,P=O;var T=(M=(W=I).D).Nb;_=W.R;var M=M.wa[M.Y+S],j=W.sa,B=W.ta+16*T*_+16*S,R=M.dd,D=M.tc;if(0!=D)if(e(3<=D),1==W.L)0<S&&rZ(j,B,_,D+4),M.La&&rQ(j,B,_,D),0<P&&rX(j,B,_,D+4),M.La&&r$(j,B,_,D);else{var q=W.B,U=W.qa,z=W.ra+8*T*q+8*S,H=W.Ha,W=W.Ia+8*T*q+8*S;T=M.ld,0<S&&(rH(j,B,_,D+4,R,T),rV(U,z,H,W,q,D+4,R,T)),M.La&&(rY(j,B,_,D,R,T),rK(U,z,H,W,q,D,R,T)),0<P&&(rz(j,B,_,D+4,R,T),rW(U,z,H,W,q,D+4,R,T)),M.La&&(rG(j,B,_,D,R,T),rJ(U,z,H,W,q,D,R,T))}}if(s.ia&&alert("todo:DitherRow"),null!=h.put){if(L=16*E,E=16*(E+1),F?(h.y=s.sa,h.O=s.ta+x,h.f=s.qa,h.N=s.ra+C,h.ea=s.Ha,h.W=s.Ia+C):(L-=w,h.y=d,h.O=p,h.f=g,h.N=c,h.ea=m,h.W=v),y||(E-=w),E>h.o&&(E=h.o),h.F=null,h.J=null,null!=s.Fa&&0<s.Fa.length&&L<E&&(h.J=function(t,r,i,a){var s=r.width,h=r.o;if(e(null!=t&&null!=r),0>i||0>=a||i+a>h)return null;if(!t.Cc){if(null==t.ga){if(t.ga=new eX,(S=null==t.ga)||(S=r.width*r.o,e(0==t.Gb.length),t.Gb=o(S),t.Uc=0,null==t.Gb?S=0:(t.mb=t.Gb,t.nb=t.Uc,t.rc=null,S=1),S=!S),!S){S=t.ga;var u=t.Fa,l=t.P,c=t.qc,f=t.mb,d=t.nb,p=l+1,g=c-1,m=S.l;if(e(null!=u&&null!=f&&null!=r),nB[0]=null,nB[1]=eZ,nB[2]=e$,nB[3]=eQ,S.ca=f,S.tb=d,S.c=r.width,S.i=r.height,e(0<S.c&&0<S.i),1>=c)r=0;else if(S.$a=(0|u[l+0])&3,S.Z=u[l+0]>>2&3,S.Lc=u[l+0]>>4&3,l=u[l+0]>>6&3,0>S.$a||1<S.$a||4<=S.Z||1<S.Lc||l)r=0;else if(m.put=td,m.ac=tf,m.bc=tp,m.ma=S,m.width=r.width,m.height=r.height,m.Da=r.Da,m.v=r.v,m.va=r.va,m.j=r.j,m.o=r.o,S.$a)t:{for(e(1==S.$a),r=tk();;){if(null==r){r=0;break t}if(e(null!=S),S.mc=r,r.c=S.c,r.i=S.i,r.l=S.l,r.l.ma=S,r.l.width=S.c,r.l.height=S.i,r.a=0,b(r.m,u,p,g),!tI(S.c,S.i,1,r,null)||(1==r.ab&&3==r.gc[0].hc&&tA(r.s)?(S.ic=1,u=r.c*r.i,r.Ta=null,r.Ua=0,r.V=o(u),r.Ba=0,null==r.V?(r.a=1,r=0):r=1):(S.ic=0,r=tC(r,S.c)),!r))break;r=1;break t}S.mc=null,r=0}else r=g>=S.c*S.i;S=!r}if(S)return null;1!=t.ga.Lc?t.Ga=0:a=h-i}e(null!=t.ga),e(i+a<=h);t:{if(r=(u=t.ga).c,h=u.l.o,0==u.$a){if(p=t.rc,g=t.Vc,m=t.Fa,l=t.P+1+i*r,c=t.mb,f=t.nb+i*r,e(l<=t.P+t.qc),0!=u.Z)for(e(null!=nB[u.Z]),S=0;S<a;++S)nB[u.Z](p,g,m,l,c,f,r),p=c,g=f,f+=r,l+=r;else for(S=0;S<a;++S)n(c,f,m,l,r),p=c,g=f,f+=r,l+=r;t.rc=p,t.Vc=g}else{if(e(null!=u.mc),r=i+a,e(null!=(S=u.mc)),e(r<=S.i),S.C>=r)r=1;else if(u.ic||e3(),u.ic){u=S.V,p=S.Ba,g=S.c;var v=S.i,y=(m=1,l=S.$/g,c=S.$%g,f=S.m,d=S.s,S.$),w=g*v,x=g*r,_=d.wc,L=y<x?tw(d,c,l):null;e(y<=w),e(r<=v),e(tA(d));e:for(;;){for(;!f.h&&y<x;){if(c&_||(L=tw(d,c,l)),e(null!=L),N(f),256>(v=tv(L.G[0],L.H[0],f)))u[p+y]=v,++y,++c>=g&&(c=0,++l<=r&&!(l%16)&&tN(S,l));else{if(!(280>v)){m=0;break e}v=tm(v-256,f);var S,P,k=tv(L.G[4],L.H[4],f);if(N(f),!(y>=(k=tb(g,k=tm(k,f)))&&w-y>=v)){m=0;break e}for(P=0;P<v;++P)u[p+y+P]=u[p+y+P-k];for(y+=v,c+=v;c>=g;)c-=g,++l<=r&&!(l%16)&&tN(S,l);y<x&&c&_&&(L=tw(d,c,l))}e(f.h==A(f))}tN(S,l>r?r:l);break}!m||f.h&&y<w?(m=0,S.a=f.h?5:3):S.$=y,r=m}else r=tS(S,S.V,S.Ba,S.c,S.i,r,tE);if(!r){a=0;break t}}i+a>=h&&(t.Cc=1),a=1}if(!a)return null;if(t.Cc&&(null!=(a=t.ga)&&(a.mc=null),t.ga=null,0<t.Ga))return alert("todo:WebPDequantizeLevels"),null}return t.nb+i*s}(s,h,L,E-L),h.F=s.mb,null==h.F&&0==h.F.length)){u=tJ(s,3,"Could not decode alpha data.");break t}L<h.j&&(w=h.j-L,L=h.j,e(!(1&w)),h.O+=s.R*w,h.N+=s.B*(w>>1),h.W+=s.B*(w>>1),null!=h.F&&(h.J+=h.width*w)),L<E&&(h.O+=h.v,h.N+=h.v>>1,h.W+=h.v>>1,null!=h.F&&(h.J+=h.v),h.ka=L-h.j,h.U=h.va-h.v,h.T=E-L,u=h.put(h))}a+1!=s.Ic||y||(n(s.sa,s.ta-l,d,p+16*s.R,l),n(s.qa,s.ra-f,g,c+8*s.B,f),n(s.Ha,s.Ia-f,m,v+8*s.B,f))}if(!u)return tJ(t,6,"Output aborted.")}return 1}(t,r)),null!=r.bc&&r.bc(r),s&=1}return s?(t.cb=0,s):0})(t,h)||(r=t.a)}}else r=t.a;0==r&&null!=c.Oa&&c.Oa.fd&&(r=rp(c.ba))}c=r}l=0!=c?null:11>l?f.f.RGBA.eb:f.f.kb.y}else l=null;return l};var n7=[3,4,3,4,4,2,2,4,4,4,2,1,1]};function u(t,e){return(0|t[e+0]|t[e+1]<<8|t[e+2]<<16)>>>0}function l(t,e){return(0|t[e+0]|t[e+1]<<8|t[e+2]<<16|t[e+3]<<24)>>>0}new h;var c=[0],f=[0],d=[],p=new h,g=function(t,e){var r={},n=0,i=!1,o=0,a=0;if(r.frames=[],!function(t,e,r,n){for(var i=0;i<4;i++)if(t[e+i]!=r.charCodeAt(i))return!0;return!1}(t,e,"RIFF",0)){for(l(t,e+=4),e+=8;e<t.length;){var s,h,c,f=function(t,e){for(var r="",n=0;n<4;n++)r+=String.fromCharCode(t[e++]);return r}(t,e),d=l(t,e+=4);e+=4;var p=d+(1&d);switch(f){case"VP8 ":case"VP8L":void 0===r.frames[n]&&(r.frames[n]={}),(c=r.frames[n]).src_off=i?a:e-8,c.src_size=o+d+8,n++,i&&(i=!1,o=0,a=0);break;case"VP8X":(c=r.header={}).feature_flags=t[e];var g=e+4;c.canvas_width=1+u(t,g),g+=3,c.canvas_height=1+u(t,g),g+=3;break;case"ALPH":i=!0,o=p+8,a=e-8;break;case"ANIM":(c=r.header).bgcolor=l(t,e),g=e+4,c.loop_count=0|t[(s=g)+0]|t[s+1]<<8,g+=2;break;case"ANMF":(c=r.frames[n]={}).offset_x=2*u(t,e),e+=3,c.offset_y=2*u(t,e),e+=3,c.width=1+u(t,e),e+=3,c.height=1+u(t,e),e+=3,c.duration=u(t,e),e+=3,h=t[e++],c.dispose=1&h,c.blend=h>>1&1}"ANMF"!=f&&(e+=p)}return r}}(t,0);g.response=t,g.rgbaoutput=!0,g.dataurl=!1;var m=g.header?g.header:null,b=g.frames?g.frames:null;if(m){m.loop_counter=m.loop_count,c=[m.canvas_height],f=[m.canvas_width];for(var v=0;v<b.length&&0!=b[v].blend;v++);}var y=b[0],w=p.WebPDecodeRGBA(t,y.src_off,y.src_size,f,c);y.rgba=w,y.imgwidth=f[0],y.imgheight=c[0];for(var x=0;x<f[0]*c[0]*4;x++)d[x]=w[x];return this.width=f,this.height=c,this.data=d,this}!function(t){var e=function(e,n,h,u){var l=4,c=o;switch(u){case t.image_compression.FAST:l=1,c=i;break;case t.image_compression.MEDIUM:l=6,c=a;break;case t.image_compression.SLOW:l=9,c=s}var f=tb(e=r(e,n,h,c),{level:l});return t.__addimage__.arrayBufferToBinaryString(f)},r=function(t,e,r,n){for(var i,o,a,s=t.length/e,h=new Uint8Array(t.length+s),c=u(),f=0;f<s;f+=1){if(a=f*e,i=t.subarray(a,a+e),n)h.set(n(i,r,o),a+f);else{for(var d,p=c.length,g=[];d<p;d+=1)g[d]=c[d](i,r,o);var m=l(g.concat());h.set(g[m],a+f)}o=i}return h},n=function(t){var e=Array.apply([],t);return e.unshift(0),e},i=function(t,e){var r,n=[],i=t.length;n[0]=1;for(var o=0;o<i;o+=1)r=t[o-e]||0,n[o+1]=t[o]-r+256&255;return n},o=function(t,e,r){var n,i=[],o=t.length;i[0]=2;for(var a=0;a<o;a+=1)n=r&&r[a]||0,i[a+1]=t[a]-n+256&255;return i},a=function(t,e,r){var n,i,o=[],a=t.length;o[0]=3;for(var s=0;s<a;s+=1)n=t[s-e]||0,i=r&&r[s]||0,o[s+1]=t[s]+256-(n+i>>>1)&255;return o},s=function(t,e,r){var n,i,o=[],a=t.length;o[0]=4;for(var s=0;s<a;s+=1)n=t[s-e]||0,i=h(n,r&&r[s]||0,r&&r[s-e]||0),o[s+1]=t[s]-i+256&255;return o},h=function(t,e,r){if(t===e&&e===r)return t;var n=Math.abs(e-r),i=Math.abs(t-r),o=Math.abs(t+e-r-r);return n<=i&&n<=o?t:i<=o?e:r},u=function(){return[n,i,o,a,s]},l=function(t){var e=t.map(function(t){return t.reduce(function(t,e){return t+Math.abs(e)},0)});return e.indexOf(Math.min.apply(null,e))};t.processPNG=function(r,n,i,o){var a,s,h,u,l,c,f,d,p,g,m,b,v,y,w,x=this.decode.FLATE_DECODE,_="";if(this.__addimage__.isArrayBuffer(r)&&(r=new Uint8Array(r)),this.__addimage__.isArrayBufferView(r)){if(r=(h=new e1(r)).imgData,s=h.bits,a=h.colorSpace,l=h.colors,-1!==[4,6].indexOf(h.colorType)){if(8===h.bits){p=(d=32==h.pixelBitlength?new Uint32Array(h.decodePixels().buffer):16==h.pixelBitlength?new Uint16Array(h.decodePixels().buffer):new Uint8Array(h.decodePixels().buffer)).length,m=new Uint8Array(p*h.colors),g=new Uint8Array(p);var A,L=h.pixelBitlength-h.bits;for(y=0,w=0;y<p;y++){for(v=d[y],A=0;A<L;)m[w++]=v>>>A&255,A+=h.bits;g[y]=v>>>A&255}}if(16===h.bits){m=new Uint8Array((p=(d=new Uint32Array(h.decodePixels().buffer)).length)*(32/h.pixelBitlength)*h.colors),g=new Uint8Array(p*(32/h.pixelBitlength)),b=h.colors>1,y=0,w=0;for(var N=0;y<p;)v=d[y++],m[w++]=v>>>0&255,b&&(m[w++]=v>>>16&255,v=d[y++],m[w++]=v>>>0&255),g[N++]=v>>>16&255;s=8}o!==t.image_compression.NONE?(r=e(m,h.width*h.colors,h.colors,o),f=e(g,h.width,1,o)):(r=m,f=g,x=void 0)}if(3===h.colorType&&(a=this.color_spaces.INDEXED,c=h.palette,h.transparency.indexed)){var S=h.transparency.indexed,P=0;for(y=0,p=S.length;y<p;++y)P+=S[y];if((P/=255)==p-1&&-1!==S.indexOf(0))u=[S.indexOf(0)];else if(P!==p){for(g=new Uint8Array((d=h.decodePixels()).length),y=0,p=d.length;y<p;y++)g[y]=S[d[y]];f=e(g,h.width,1)}}var k=function(e){var r;switch(e){case t.image_compression.FAST:r=11;break;case t.image_compression.MEDIUM:r=13;break;case t.image_compression.SLOW:r=14;break;default:r=12}return r}(o);return x===this.decode.FLATE_DECODE&&(_="/Predictor "+k+" "),_+="/Colors "+l+" /BitsPerComponent "+s+" /Columns "+h.width,(this.__addimage__.isArrayBuffer(r)||this.__addimage__.isArrayBufferView(r))&&(r=this.__addimage__.arrayBufferToBinaryString(r)),(f&&this.__addimage__.isArrayBuffer(f)||this.__addimage__.isArrayBufferView(f))&&(f=this.__addimage__.arrayBufferToBinaryString(f)),{alias:i,data:r,index:n,filter:x,decodeParameters:_,transparency:u,palette:c,sMask:f,predictor:k,width:h.width,height:h.height,bitsPerComponent:s,colorSpace:a}}}}(t1.API),function(t){t.processGIF89A=function(e,r,n,i){var o=new e2(e),a=o.width,s=o.height,h=[];o.decodeAndBlitFrameRGBA(0,h);var u=new e5(100).encode({data:h,width:a,height:s},100);return t.processJPEG.call(this,u,r,n,i)},t.processGIF87A=t.processGIF89A}(t1.API),e3.prototype.parseHeader=function(){if(this.fileSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.reserved=this.datav.getUint32(this.pos,!0),this.pos+=4,this.offset=this.datav.getUint32(this.pos,!0),this.pos+=4,this.headerSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.width=this.datav.getUint32(this.pos,!0),this.pos+=4,this.height=this.datav.getInt32(this.pos,!0),this.pos+=4,this.planes=this.datav.getUint16(this.pos,!0),this.pos+=2,this.bitPP=this.datav.getUint16(this.pos,!0),this.pos+=2,this.compress=this.datav.getUint32(this.pos,!0),this.pos+=4,this.rawSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.hr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.vr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.colors=this.datav.getUint32(this.pos,!0),this.pos+=4,this.importantColors=this.datav.getUint32(this.pos,!0),this.pos+=4,16===this.bitPP&&this.is_with_alpha&&(this.bitPP=15),this.bitPP<15){var t=0===this.colors?1<<this.bitPP:this.colors;this.palette=Array(t);for(var e=0;e<t;e++){var r=this.datav.getUint8(this.pos++,!0),n=this.datav.getUint8(this.pos++,!0),i=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0);this.palette[e]={red:i,green:n,blue:r,quad:o}}}this.height<0&&(this.height*=-1,this.bottom_up=!1)},e3.prototype.parseBGR=function(){this.pos=this.offset;try{var t="bit"+this.bitPP,e=this.width*this.height*4;this.data=new Uint8Array(e),this[t]()}catch(t){tN.log("bit decode error:"+t)}},e3.prototype.bit1=function(){var t,e=Math.ceil(this.width/8),r=e%4;for(t=this.height-1;t>=0;t--){for(var n=this.bottom_up?t:this.height-1-t,i=0;i<e;i++)for(var o=this.datav.getUint8(this.pos++,!0),a=n*this.width*4+8*i*4,s=0;s<8&&8*i+s<this.width;s++){var h=this.palette[o>>7-s&1];this.data[a+4*s]=h.blue,this.data[a+4*s+1]=h.green,this.data[a+4*s+2]=h.red,this.data[a+4*s+3]=255}0!==r&&(this.pos+=4-r)}},e3.prototype.bit4=function(){for(var t=Math.ceil(this.width/2),e=t%4,r=this.height-1;r>=0;r--){for(var n=this.bottom_up?r:this.height-1-r,i=0;i<t;i++){var o=this.datav.getUint8(this.pos++,!0),a=n*this.width*4+2*i*4,s=o>>4,h=15&o,u=this.palette[s];if(this.data[a]=u.blue,this.data[a+1]=u.green,this.data[a+2]=u.red,this.data[a+3]=255,2*i+1>=this.width)break;u=this.palette[h],this.data[a+4]=u.blue,this.data[a+4+1]=u.green,this.data[a+4+2]=u.red,this.data[a+4+3]=255}0!==e&&(this.pos+=4-e)}},e3.prototype.bit8=function(){for(var t=this.width%4,e=this.height-1;e>=0;e--){for(var r=this.bottom_up?e:this.height-1-e,n=0;n<this.width;n++){var i=this.datav.getUint8(this.pos++,!0),o=r*this.width*4+4*n;if(i<this.palette.length){var a=this.palette[i];this.data[o]=a.red,this.data[o+1]=a.green,this.data[o+2]=a.blue,this.data[o+3]=255}else this.data[o]=255,this.data[o+1]=255,this.data[o+2]=255,this.data[o+3]=255}0!==t&&(this.pos+=4-t)}},e3.prototype.bit15=function(){for(var t=this.width%3,e=parseInt("11111",2),r=this.height-1;r>=0;r--){for(var n=this.bottom_up?r:this.height-1-r,i=0;i<this.width;i++){var o=this.datav.getUint16(this.pos,!0);this.pos+=2;var a=(o&e)/e*255|0,s=(o>>5&e)/e*255|0,h=(o>>10&e)/e*255|0,u=o>>15?255:0,l=n*this.width*4+4*i;this.data[l]=h,this.data[l+1]=s,this.data[l+2]=a,this.data[l+3]=u}this.pos+=t}},e3.prototype.bit16=function(){for(var t=this.width%3,e=parseInt("11111",2),r=parseInt("111111",2),n=this.height-1;n>=0;n--){for(var i=this.bottom_up?n:this.height-1-n,o=0;o<this.width;o++){var a=this.datav.getUint16(this.pos,!0);this.pos+=2;var s=(a&e)/e*255|0,h=(a>>5&r)/r*255|0,u=(a>>11)/e*255|0,l=i*this.width*4+4*o;this.data[l]=u,this.data[l+1]=h,this.data[l+2]=s,this.data[l+3]=255}this.pos+=t}},e3.prototype.bit24=function(){for(var t=this.height-1;t>=0;t--){for(var e=this.bottom_up?t:this.height-1-t,r=0;r<this.width;r++){var n=this.datav.getUint8(this.pos++,!0),i=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0),a=e*this.width*4+4*r;this.data[a]=o,this.data[a+1]=i,this.data[a+2]=n,this.data[a+3]=255}this.pos+=this.width%4}},e3.prototype.bit32=function(){for(var t=this.height-1;t>=0;t--)for(var e=this.bottom_up?t:this.height-1-t,r=0;r<this.width;r++){var n=this.datav.getUint8(this.pos++,!0),i=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0),a=this.datav.getUint8(this.pos++,!0),s=e*this.width*4+4*r;this.data[s]=o,this.data[s+1]=i,this.data[s+2]=n,this.data[s+3]=a}},e3.prototype.getData=function(){return this.data},function(t){t.processBMP=function(e,r,n,i){var o=new e3(e,!1),a=o.width,s=o.height,h={data:o.getData(),width:a,height:s},u=new e5(100).encode(h,100);return t.processJPEG.call(this,u,r,n,i)}}(t1.API),e4.prototype.getData=function(){return this.data},function(t){t.processWEBP=function(e,r,n,i){var o=new e4(e),a=o.width,s=o.height,h={data:o.getData(),width:a,height:s},u=new e5(100).encode(h,100);return t.processJPEG.call(this,u,r,n,i)}}(t1.API),t1.API.processRGBA=function(t,e,r){for(var n=t.data,i=n.length,o=new Uint8Array(i/4*3),a=new Uint8Array(i/4),s=0,h=0,u=0;u<i;u+=4){var l=n[u],c=n[u+1],f=n[u+2],d=n[u+3];o[s++]=l,o[s++]=c,o[s++]=f,a[h++]=d}var p=this.__addimage__.arrayBufferToBinaryString(o);return{alpha:this.__addimage__.arrayBufferToBinaryString(a),data:p,index:e,alias:r,colorSpace:"DeviceRGB",bitsPerComponent:8,width:t.width,height:t.height}},t1.API.setLanguage=function(t){return void 0===this.internal.languageSettings&&(this.internal.languageSettings={},this.internal.languageSettings.isSubscribed=!1),void 0!==({af:"Afrikaans",sq:"Albanian",ar:"Arabic (Standard)","ar-DZ":"Arabic (Algeria)","ar-BH":"Arabic (Bahrain)","ar-EG":"Arabic (Egypt)","ar-IQ":"Arabic (Iraq)","ar-JO":"Arabic (Jordan)","ar-KW":"Arabic (Kuwait)","ar-LB":"Arabic (Lebanon)","ar-LY":"Arabic (Libya)","ar-MA":"Arabic (Morocco)","ar-OM":"Arabic (Oman)","ar-QA":"Arabic (Qatar)","ar-SA":"Arabic (Saudi Arabia)","ar-SY":"Arabic (Syria)","ar-TN":"Arabic (Tunisia)","ar-AE":"Arabic (U.A.E.)","ar-YE":"Arabic (Yemen)",an:"Aragonese",hy:"Armenian",as:"Assamese",ast:"Asturian",az:"Azerbaijani",eu:"Basque",be:"Belarusian",bn:"Bengali",bs:"Bosnian",br:"Breton",bg:"Bulgarian",my:"Burmese",ca:"Catalan",ch:"Chamorro",ce:"Chechen",zh:"Chinese","zh-HK":"Chinese (Hong Kong)","zh-CN":"Chinese (PRC)","zh-SG":"Chinese (Singapore)","zh-TW":"Chinese (Taiwan)",cv:"Chuvash",co:"Corsican",cr:"Cree",hr:"Croatian",cs:"Czech",da:"Danish",nl:"Dutch (Standard)","nl-BE":"Dutch (Belgian)",en:"English","en-AU":"English (Australia)","en-BZ":"English (Belize)","en-CA":"English (Canada)","en-IE":"English (Ireland)","en-JM":"English (Jamaica)","en-NZ":"English (New Zealand)","en-PH":"English (Philippines)","en-ZA":"English (South Africa)","en-TT":"English (Trinidad & Tobago)","en-GB":"English (United Kingdom)","en-US":"English (United States)","en-ZW":"English (Zimbabwe)",eo:"Esperanto",et:"Estonian",fo:"Faeroese",fj:"Fijian",fi:"Finnish",fr:"French (Standard)","fr-BE":"French (Belgium)","fr-CA":"French (Canada)","fr-FR":"French (France)","fr-LU":"French (Luxembourg)","fr-MC":"French (Monaco)","fr-CH":"French (Switzerland)",fy:"Frisian",fur:"Friulian",gd:"Gaelic (Scots)","gd-IE":"Gaelic (Irish)",gl:"Galacian",ka:"Georgian",de:"German (Standard)","de-AT":"German (Austria)","de-DE":"German (Germany)","de-LI":"German (Liechtenstein)","de-LU":"German (Luxembourg)","de-CH":"German (Switzerland)",el:"Greek",gu:"Gujurati",ht:"Haitian",he:"Hebrew",hi:"Hindi",hu:"Hungarian",is:"Icelandic",id:"Indonesian",iu:"Inuktitut",ga:"Irish",it:"Italian (Standard)","it-CH":"Italian (Switzerland)",ja:"Japanese",kn:"Kannada",ks:"Kashmiri",kk:"Kazakh",km:"Khmer",ky:"Kirghiz",tlh:"Klingon",ko:"Korean","ko-KP":"Korean (North Korea)","ko-KR":"Korean (South Korea)",la:"Latin",lv:"Latvian",lt:"Lithuanian",lb:"Luxembourgish",mk:"North Macedonia",ms:"Malay",ml:"Malayalam",mt:"Maltese",mi:"Maori",mr:"Marathi",mo:"Moldavian",nv:"Navajo",ng:"Ndonga",ne:"Nepali",no:"Norwegian",nb:"Norwegian (Bokmal)",nn:"Norwegian (Nynorsk)",oc:"Occitan",or:"Oriya",om:"Oromo",fa:"Persian","fa-IR":"Persian/Iran",pl:"Polish",pt:"Portuguese","pt-BR":"Portuguese (Brazil)",pa:"Punjabi","pa-IN":"Punjabi (India)","pa-PK":"Punjabi (Pakistan)",qu:"Quechua",rm:"Rhaeto-Romanic",ro:"Romanian","ro-MO":"Romanian (Moldavia)",ru:"Russian","ru-MO":"Russian (Moldavia)",sz:"Sami (Lappish)",sg:"Sango",sa:"Sanskrit",sc:"Sardinian",sd:"Sindhi",si:"Singhalese",sr:"Serbian",sk:"Slovak",sl:"Slovenian",so:"Somani",sb:"Sorbian",es:"Spanish","es-AR":"Spanish (Argentina)","es-BO":"Spanish (Bolivia)","es-CL":"Spanish (Chile)","es-CO":"Spanish (Colombia)","es-CR":"Spanish (Costa Rica)","es-DO":"Spanish (Dominican Republic)","es-EC":"Spanish (Ecuador)","es-SV":"Spanish (El Salvador)","es-GT":"Spanish (Guatemala)","es-HN":"Spanish (Honduras)","es-MX":"Spanish (Mexico)","es-NI":"Spanish (Nicaragua)","es-PA":"Spanish (Panama)","es-PY":"Spanish (Paraguay)","es-PE":"Spanish (Peru)","es-PR":"Spanish (Puerto Rico)","es-ES":"Spanish (Spain)","es-UY":"Spanish (Uruguay)","es-VE":"Spanish (Venezuela)",sx:"Sutu",sw:"Swahili",sv:"Swedish","sv-FI":"Swedish (Finland)","sv-SV":"Swedish (Sweden)",ta:"Tamil",tt:"Tatar",te:"Teluga",th:"Thai",tig:"Tigre",ts:"Tsonga",tn:"Tswana",tr:"Turkish",tk:"Turkmen",uk:"Ukrainian",hsb:"Upper Sorbian",ur:"Urdu",ve:"Venda",vi:"Vietnamese",vo:"Volapuk",wa:"Walloon",cy:"Welsh",xh:"Xhosa",ji:"Yiddish",zu:"Zulu"})[t]&&(this.internal.languageSettings.languageCode=t,!1===this.internal.languageSettings.isSubscribed&&(this.internal.events.subscribe("putCatalog",function(){this.internal.write("/Lang ("+this.internal.languageSettings.languageCode+")")}),this.internal.languageSettings.isSubscribed=!0)),this},eX=(eK=t1.API).getCharWidthsArray=function(t,e){var r,n,o=(e=e||{}).font||this.internal.getFont(),a=e.fontSize||this.internal.getFontSize(),s=e.charSpace||this.internal.getCharSpace(),h=e.widths?e.widths:o.metadata.Unicode.widths,u=h.fof?h.fof:1,l=e.kerning?e.kerning:o.metadata.Unicode.kerning,c=l.fof?l.fof:1,f=!1!==e.doKerning,d=0,p=t.length,g=0,m=h[0]||u,b=[];for(r=0;r<p;r++)n=t.charCodeAt(r),"function"==typeof o.metadata.widthOfString?b.push((o.metadata.widthOfGlyph(o.metadata.characterToGlyph(n))+1e3/a*s||0)/1e3):(d=f&&"object"===i()(l[n])&&!isNaN(parseInt(l[n][g],10))?l[n][g]/c:0,b.push((h[n]||m)/u+d)),g=n;return b},eZ=eK.getStringUnitWidth=function(t,e){var r=(e=e||{}).fontSize||this.internal.getFontSize(),n=e.font||this.internal.getFont(),i=e.charSpace||this.internal.getCharSpace();return eK.processArabic&&(t=eK.processArabic(t)),"function"==typeof n.metadata.widthOfString?n.metadata.widthOfString(t,r,i)/r:eX.apply(this,arguments).reduce(function(t,e){return t+e},0)},e$=function(t,e,r,n){for(var i=[],o=0,a=t.length,s=0;o!==a&&s+e[o]<r;)s+=e[o],o++;i.push(t.slice(0,o));var h=o;for(s=0;o!==a;)s+e[o]>n&&(i.push(t.slice(h,o)),s=0,h=o),s+=e[o],o++;return h!==o&&i.push(t.slice(h,o)),i},eQ=function(t,e,r){r||(r={});var n,i,o,a,s,h,u,l=[],c=[l],f=r.textIndent||0,d=0,p=0,g=t.split(" "),m=eX.apply(this,[" ",r])[0];if(h=-1===r.lineIndent?g[0].length+2:r.lineIndent||0){var b=Array(h).join(" "),v=[];g.map(function(t){(t=t.split(/\s*\n/)).length>1?v=v.concat(t.map(function(t,e){return(e&&t.length?"\n":"")+t})):v.push(t[0])}),g=v,h=eZ.apply(this,[b,r])}for(o=0,a=g.length;o<a;o++){var y=0;if(n=g[o],h&&"\n"==n[0]&&(n=n.substr(1),y=1),f+d+(p=(i=eX.apply(this,[n,r])).reduce(function(t,e){return t+e},0))>e||y){if(p>e){for(s=e$.apply(this,[n,i,e-(f+d),e]),l.push(s.shift()),l=[s.pop()];s.length;)c.push([s.shift()]);p=i.slice(n.length-(l[0]?l[0].length:0)).reduce(function(t,e){return t+e},0)}else l=[n];c.push(l),f=p+h,d=m}else l.push(n),f+=d+p,d=m}return u=h?function(t,e){return(e?b:"")+t.join(" ")}:function(t){return t.join(" ")},c.map(u)},eK.splitTextToSize=function(t,e,r){var n,i=(r=r||{}).fontSize||this.internal.getFontSize(),o=(function(t){if(t.widths&&t.kerning)return{widths:t.widths,kerning:t.kerning};var e=this.internal.getFont(t.fontName,t.fontStyle);return e.metadata.Unicode?{widths:e.metadata.Unicode.widths||{0:1},kerning:e.metadata.Unicode.kerning||{}}:{font:e.metadata,fontSize:this.internal.getFontSize(),charSpace:this.internal.getCharSpace()}}).call(this,r);n=Array.isArray(t)?t:String(t).split(/\r?\n/);var a=this.internal.scaleFactor*e/i;o.textIndent=r.textIndent?r.textIndent*this.internal.scaleFactor/i:0,o.lineIndent=r.lineIndent;var s,h,u=[];for(s=0,h=n.length;s<h;s++)u=u.concat(eQ.apply(this,[n[s],a,o]));return u},function(t){t.__fontmetrics__=t.__fontmetrics__||{};for(var e="klmnopqrstuvwxyz",r={},n={},o=0;o<e.length;o++)r[e[o]]="0123456789abcdef"[o],n["0123456789abcdef"[o]]=e[o];var a=function(t){return"0x"+parseInt(t,10).toString(16)},s=t.__fontmetrics__.compress=function(t){var e,r,o,h,u=["{"];for(var l in t){if(e=t[l],r=isNaN(parseInt(l,10))?"'"+l+"'":(r=a(l=parseInt(l,10)).slice(2)).slice(0,-1)+n[r.slice(-1)],"number"==typeof e)e<0?(o=a(e).slice(3),h="-"):(o=a(e).slice(2),h=""),o=h+o.slice(0,-1)+n[o.slice(-1)];else{if("object"!==i()(e))throw Error("Don't know what to do with value type "+i()(e)+".");o=s(e)}u.push(r+o)}return u.push("}"),u.join("")},h=t.__fontmetrics__.uncompress=function(t){if("string"!=typeof t)throw Error("Invalid argument passed to uncompress.");for(var e,n,i,o,a={},s=1,h=a,u=[],l="",c="",f=t.length-1,d=1;d<f;d+=1)"'"==(o=t[d])?e?(i=e.join(""),e=void 0):e=[]:e?e.push(o):"{"==o?(u.push([h,i]),h={},i=void 0):"}"==o?((n=u.pop())[0][n[1]]=h,i=void 0,h=n[0]):"-"==o?s=-1:void 0===i?r.hasOwnProperty(o)?(l+=r[o],i=parseInt(l,16)*s,s=1,l=""):l+=o:r.hasOwnProperty(o)?(c+=r[o],h[i]=parseInt(c,16)*s,s=1,i=void 0,c=""):c+=o;return a},u={codePages:["WinAnsiEncoding"],WinAnsiEncoding:h("{19m8n201n9q201o9r201s9l201t9m201u8m201w9n201x9o201y8o202k8q202l8r202m9p202q8p20aw8k203k8t203t8v203u9v2cq8s212m9t15m8w15n9w2dw9s16k8u16l9u17s9z17x8y17y9y}")},l={Courier:u,"Courier-Bold":u,"Courier-BoldOblique":u,"Courier-Oblique":u,Helvetica:u,"Helvetica-Bold":u,"Helvetica-BoldOblique":u,"Helvetica-Oblique":u,"Times-Roman":u,"Times-Bold":u,"Times-BoldItalic":u,"Times-Italic":u},c={Unicode:{"Courier-Oblique":h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-BoldItalic":h("{'widths'{k3o2q4ycx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2r202m2n2n3m2o3m2p5n202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5n4l4m4m4m4n4m4o4s4p4m4q4m4r4s4s4y4t2r4u3m4v4m4w3x4x5t4y4s4z4s5k3x5l4s5m4m5n3r5o3x5p4s5q4m5r5t5s4m5t3x5u3x5v2l5w1w5x2l5y3t5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q2l6r3m6s3r6t1w6u1w6v3m6w1w6x4y6y3r6z3m7k3m7l3m7m2r7n2r7o1w7p3r7q2w7r4m7s3m7t2w7u2r7v2n7w1q7x2n7y3t202l3mcl4mal2ram3man3mao3map3mar3mas2lat4uau1uav3maw3way4uaz2lbk2sbl3t'fof'6obo2lbp3tbq3mbr1tbs2lbu1ybv3mbz3mck4m202k3mcm4mcn4mco4mcp4mcq5ycr4mcs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz2w203k6o212m6o2dw2l2cq2l3t3m3u2l17s3x19m3m}'kerning'{cl{4qu5kt5qt5rs17ss5ts}201s{201ss}201t{cks4lscmscnscoscpscls2wu2yu201ts}201x{2wu2yu}2k{201ts}2w{4qx5kx5ou5qx5rs17su5tu}2x{17su5tu5ou}2y{4qx5kx5ou5qx5rs17ss5ts}'fof'-6ofn{17sw5tw5ou5qw5rs}7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qs}3v{17su5tu5os5qs}7p{17su5tu}ck{4qu5kt5qt5rs17ss5ts}4l{4qu5kt5qt5rs17ss5ts}cm{4qu5kt5qt5rs17ss5ts}cn{4qu5kt5qt5rs17ss5ts}co{4qu5kt5qt5rs17ss5ts}cp{4qu5kt5qt5rs17ss5ts}6l{4qu5ou5qw5rt17su5tu}5q{ckuclucmucnucoucpu4lu}5r{ckuclucmucnucoucpu4lu}7q{cksclscmscnscoscps4ls}6p{4qu5ou5qw5rt17sw5tw}ek{4qu5ou5qw5rt17su5tu}el{4qu5ou5qw5rt17su5tu}em{4qu5ou5qw5rt17su5tu}en{4qu5ou5qw5rt17su5tu}eo{4qu5ou5qw5rt17su5tu}ep{4qu5ou5qw5rt17su5tu}es{17ss5ts5qs4qu}et{4qu5ou5qw5rt17sw5tw}eu{4qu5ou5qw5rt17ss5ts}ev{17ss5ts5qs4qu}6z{17sw5tw5ou5qw5rs}fm{17sw5tw5ou5qw5rs}7n{201ts}fo{17sw5tw5ou5qw5rs}fp{17sw5tw5ou5qw5rs}fq{17sw5tw5ou5qw5rs}7r{cksclscmscnscoscps4ls}fs{17sw5tw5ou5qw5rs}ft{17su5tu}fu{17su5tu}fv{17su5tu}fw{17su5tu}fz{cksclscmscnscoscps4ls}}}"),"Helvetica-Bold":h("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),Courier:h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-BoldOblique":h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Bold":h("{'widths'{k3q2q5ncx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2l202m2n2n3m2o3m2p6o202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5x4l4s4m4m4n4s4o4s4p4m4q3x4r4y4s4y4t2r4u3m4v4y4w4m4x5y4y4s4z4y5k3x5l4y5m4s5n3r5o4m5p4s5q4s5r6o5s4s5t4s5u4m5v2l5w1w5x2l5y3u5z3m6k2l6l3m6m3r6n2w6o3r6p2w6q2l6r3m6s3r6t1w6u2l6v3r6w1w6x5n6y3r6z3m7k3r7l3r7m2w7n2r7o2l7p3r7q3m7r4s7s3m7t3m7u2w7v2r7w1q7x2r7y3o202l3mcl4sal2lam3man3mao3map3mar3mas2lat4uau1yav3maw3tay4uaz2lbk2sbl3t'fof'6obo2lbp3rbr1tbs2lbu2lbv3mbz3mck4s202k3mcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3rek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3m3u2l17s4s19m3m}'kerning'{cl{4qt5ks5ot5qy5rw17sv5tv}201t{cks4lscmscnscoscpscls4wv}2k{201ts}2w{4qu5ku7mu5os5qx5ru17su5tu}2x{17su5tu5ou5qs}2y{4qv5kv7mu5ot5qz5ru17su5tu}'fof'-6o7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qu}3v{17su5tu5os5qu}fu{17su5tu5ou5qu}7p{17su5tu5ou5qu}ck{4qt5ks5ot5qy5rw17sv5tv}4l{4qt5ks5ot5qy5rw17sv5tv}cm{4qt5ks5ot5qy5rw17sv5tv}cn{4qt5ks5ot5qy5rw17sv5tv}co{4qt5ks5ot5qy5rw17sv5tv}cp{4qt5ks5ot5qy5rw17sv5tv}6l{17st5tt5ou5qu}17s{ckuclucmucnucoucpu4lu4wu}5o{ckuclucmucnucoucpu4lu4wu}5q{ckzclzcmzcnzcozcpz4lz4wu}5r{ckxclxcmxcnxcoxcpx4lx4wu}5t{ckuclucmucnucoucpu4lu4wu}7q{ckuclucmucnucoucpu4lu}6p{17sw5tw5ou5qu}ek{17st5tt5qu}el{17st5tt5ou5qu}em{17st5tt5qu}en{17st5tt5qu}eo{17st5tt5qu}ep{17st5tt5ou5qu}es{17ss5ts5qu}et{17sw5tw5ou5qu}eu{17sw5tw5ou5qu}ev{17ss5ts5qu}6z{17sw5tw5ou5qu5rs}fm{17sw5tw5ou5qu5rs}fn{17sw5tw5ou5qu5rs}fo{17sw5tw5ou5qu5rs}fp{17sw5tw5ou5qu5rs}fq{17sw5tw5ou5qu5rs}7r{cktcltcmtcntcotcpt4lt5os}fs{17sw5tw5ou5qu5rs}ft{17su5tu5ou5qu}7m{5os}fv{17su5tu5ou5qu}fw{17su5tu5ou5qu}fz{cksclscmscnscoscps4ls}}}"),Symbol:h("{'widths'{k3uaw4r19m3m2k1t2l2l202m2y2n3m2p5n202q6o3k3m2s2l2t2l2v3r2w1t3m3m2y1t2z1wbk2sbl3r'fof'6o3n3m3o3m3p3m3q3m3r3m3s3m3t3m3u1w3v1w3w3r3x3r3y3r3z2wbp3t3l3m5v2l5x2l5z3m2q4yfr3r7v3k7w1o7x3k}'kerning'{'fof'-6o}}"),Helvetica:h("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}"),"Helvetica-BoldOblique":h("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),ZapfDingbats:h("{'widths'{k4u2k1w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-Bold":h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Italic":h("{'widths'{k3n2q4ycx2l201n3m201o5t201s2l201t2l201u2l201w3r201x3r201y3r2k1t2l2l202m2n2n3m2o3m2p5n202q5t2r1p2s2l2t2l2u3m2v4n2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w4n3x4n3y4n3z3m4k5w4l3x4m3x4n4m4o4s4p3x4q3x4r4s4s4s4t2l4u2w4v4m4w3r4x5n4y4m4z4s5k3x5l4s5m3x5n3m5o3r5p4s5q3x5r5n5s3x5t3r5u3r5v2r5w1w5x2r5y2u5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q1w6r3m6s3m6t1w6u1w6v2w6w1w6x4s6y3m6z3m7k3m7l3m7m2r7n2r7o1w7p3m7q2w7r4m7s2w7t2w7u2r7v2s7w1v7x2s7y3q202l3mcl3xal2ram3man3mao3map3mar3mas2lat4wau1vav3maw4nay4waz2lbk2sbl4n'fof'6obo2lbp3mbq3obr1tbs2lbu1zbv3mbz3mck3x202k3mcm3xcn3xco3xcp3xcq5tcr4mcs3xct3xcu3xcv3xcw2l2m2ucy2lcz2ldl4mdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr4nfs3mft3mfu3mfv3mfw3mfz2w203k6o212m6m2dw2l2cq2l3t3m3u2l17s3r19m3m}'kerning'{cl{5kt4qw}201s{201sw}201t{201tw2wy2yy6q-t}201x{2wy2yy}2k{201tw}2w{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}2x{17ss5ts5os}2y{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}'fof'-6o6t{17ss5ts5qs}7t{5os}3v{5qs}7p{17su5tu5qs}ck{5kt4qw}4l{5kt4qw}cm{5kt4qw}cn{5kt4qw}co{5kt4qw}cp{5kt4qw}6l{4qs5ks5ou5qw5ru17su5tu}17s{2ks}5q{ckvclvcmvcnvcovcpv4lv}5r{ckuclucmucnucoucpu4lu}5t{2ks}6p{4qs5ks5ou5qw5ru17su5tu}ek{4qs5ks5ou5qw5ru17su5tu}el{4qs5ks5ou5qw5ru17su5tu}em{4qs5ks5ou5qw5ru17su5tu}en{4qs5ks5ou5qw5ru17su5tu}eo{4qs5ks5ou5qw5ru17su5tu}ep{4qs5ks5ou5qw5ru17su5tu}es{5ks5qs4qs}et{4qs5ks5ou5qw5ru17su5tu}eu{4qs5ks5qw5ru17su5tu}ev{5ks5qs4qs}ex{17ss5ts5qs}6z{4qv5ks5ou5qw5ru17su5tu}fm{4qv5ks5ou5qw5ru17su5tu}fn{4qv5ks5ou5qw5ru17su5tu}fo{4qv5ks5ou5qw5ru17su5tu}fp{4qv5ks5ou5qw5ru17su5tu}fq{4qv5ks5ou5qw5ru17su5tu}7r{5os}fs{4qv5ks5ou5qw5ru17su5tu}ft{17su5tu5qs}fu{17su5tu5qs}fv{17su5tu5qs}fw{17su5tu5qs}}}"),"Times-Roman":h("{'widths'{k3n2q4ycx2l201n3m201o6o201s2l201t2l201u2l201w2w201x2w201y2w2k1t2l2l202m2n2n3m2o3m2p5n202q6o2r1m2s2l2t2l2u3m2v3s2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v1w3w3s3x3s3y3s3z2w4k5w4l4s4m4m4n4m4o4s4p3x4q3r4r4s4s4s4t2l4u2r4v4s4w3x4x5t4y4s4z4s5k3r5l4s5m4m5n3r5o3x5p4s5q4s5r5y5s4s5t4s5u3x5v2l5w1w5x2l5y2z5z3m6k2l6l2w6m3m6n2w6o3m6p2w6q2l6r3m6s3m6t1w6u1w6v3m6w1w6x4y6y3m6z3m7k3m7l3m7m2l7n2r7o1w7p3m7q3m7r4s7s3m7t3m7u2w7v3k7w1o7x3k7y3q202l3mcl4sal2lam3man3mao3map3mar3mas2lat4wau1vav3maw3say4waz2lbk2sbl3s'fof'6obo2lbp3mbq2xbr1tbs2lbu1zbv3mbz2wck4s202k3mcm4scn4sco4scp4scq5tcr4mcs3xct3xcu3xcv3xcw2l2m2tcy2lcz2ldl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek2wel2wem2wen2weo2wep2weq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr3sfs3mft3mfu3mfv3mfw3mfz3m203k6o212m6m2dw2l2cq2l3t3m3u1w17s4s19m3m}'kerning'{cl{4qs5ku17sw5ou5qy5rw201ss5tw201ws}201s{201ss}201t{ckw4lwcmwcnwcowcpwclw4wu201ts}2k{201ts}2w{4qs5kw5os5qx5ru17sx5tx}2x{17sw5tw5ou5qu}2y{4qs5kw5os5qx5ru17sx5tx}'fof'-6o7t{ckuclucmucnucoucpu4lu5os5rs}3u{17su5tu5qs}3v{17su5tu5qs}7p{17sw5tw5qs}ck{4qs5ku17sw5ou5qy5rw201ss5tw201ws}4l{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cm{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cn{4qs5ku17sw5ou5qy5rw201ss5tw201ws}co{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cp{4qs5ku17sw5ou5qy5rw201ss5tw201ws}6l{17su5tu5os5qw5rs}17s{2ktclvcmvcnvcovcpv4lv4wuckv}5o{ckwclwcmwcnwcowcpw4lw4wu}5q{ckyclycmycnycoycpy4ly4wu5ms}5r{cktcltcmtcntcotcpt4lt4ws}5t{2ktclvcmvcnvcovcpv4lv4wuckv}7q{cksclscmscnscoscps4ls}6p{17su5tu5qw5rs}ek{5qs5rs}el{17su5tu5os5qw5rs}em{17su5tu5os5qs5rs}en{17su5qs5rs}eo{5qs5rs}ep{17su5tu5os5qw5rs}es{5qs}et{17su5tu5qw5rs}eu{17su5tu5qs5rs}ev{5qs}6z{17sv5tv5os5qx5rs}fm{5os5qt5rs}fn{17sv5tv5os5qx5rs}fo{17sv5tv5os5qx5rs}fp{5os5qt5rs}fq{5os5qt5rs}7r{ckuclucmucnucoucpu4lu5os}fs{17sv5tv5os5qx5rs}ft{17ss5ts5qs}fu{17sw5tw5qs}fv{17sw5tw5qs}fw{17ss5ts5qs}fz{ckuclucmucnucoucpu4lu5os5rs}}}"),"Helvetica-Oblique":h("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}")}};t.events.push(["addFont",function(t){var e=t.font,r=c.Unicode[e.postScriptName];r&&(e.metadata.Unicode={},e.metadata.Unicode.widths=r.widths,e.metadata.Unicode.kerning=r.kerning);var n=l[e.postScriptName];n&&(e.metadata.Unicode.encoding=n,e.encoding=n.codePages[0])}])}(t1.API),function(t){var e=function(t){for(var e=t.length,r=new Uint8Array(e),n=0;n<e;n++)r[n]=t.charCodeAt(n);return r};t.API.events.push(["addFont",function(r){var n,i=void 0,o=r.font,a=r.instance;if(!o.isStandardFont){if(void 0===a)throw Error("Font does not exist in vFS, import fonts or remove declaration doc.addFont('"+o.postScriptName+"').");if("string"!=typeof(i=!1===a.existsFileInVFS(o.postScriptName)?a.loadFile(o.postScriptName):a.getFileFromVFS(o.postScriptName)))throw Error("Font is not stored as string-data in vFS, import fonts or remove declaration doc.addFont('"+o.postScriptName+"').");n=i,n=/^\x00\x01\x00\x00/.test(n)?e(n):e(tI(n)),o.metadata=t.API.TTFFont.open(n),o.metadata.Unicode=o.metadata.Unicode||{encoding:{},kerning:{},widths:[]},o.metadata.glyIdsUsed=[0]}}])}(t1),t1.API.addSvgAsImage=function(t,e,n,i,o,a,s,h){if(isNaN(e)||isNaN(n))throw tN.error("jsPDF.addSvgAsImage: Invalid coordinates",arguments),Error("Invalid coordinates passed to jsPDF.addSvgAsImage");if(isNaN(i)||isNaN(o))throw tN.error("jsPDF.addSvgAsImage: Invalid measurements",arguments),Error("Invalid measurements (width and/or height) passed to jsPDF.addSvgAsImage");var u=document.createElement("canvas");u.width=i,u.height=o;var l=u.getContext("2d");l.fillStyle="#fff",l.fillRect(0,0,u.width,u.height);var c={ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0},f=this;return(tA.canvg?Promise.resolve(tA.canvg):r.e(8495).then(r.bind(r,98495))).catch(function(t){return Promise.reject(Error("Could not load canvg: "+t))}).then(function(t){return t.default?t.default:t}).then(function(e){return e.fromString(l,t,c)},function(){return Promise.reject(Error("Could not load canvg."))}).then(function(t){return t.render(c)}).then(function(){f.addImage(u.toDataURL("image/jpeg",1),e,n,i,o,s,h)})},t1.API.putTotalPages=function(t){var e,r=0;15>parseInt(this.internal.getFont().id.substr(1),10)?(e=RegExp(t,"g"),r=this.internal.getNumberOfPages()):(e=RegExp(this.pdfEscape16(t,this.internal.getFont()),"g"),r=this.pdfEscape16(this.internal.getNumberOfPages()+"",this.internal.getFont()));for(var n=1;n<=this.internal.getNumberOfPages();n++)for(var i=0;i<this.internal.pages[n].length;i++)this.internal.pages[n][i]=this.internal.pages[n][i].replace(e,r);return this},t1.API.viewerPreferences=function(t,e){t=t||{},e=e||!1;var r,n,o,a,s={HideToolbar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideMenubar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideWindowUI:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},FitWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},CenterWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},DisplayDocTitle:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.4},NonFullScreenPageMode:{defaultValue:"UseNone",value:"UseNone",type:"name",explicitSet:!1,valueSet:["UseNone","UseOutlines","UseThumbs","UseOC"],pdfVersion:1.3},Direction:{defaultValue:"L2R",value:"L2R",type:"name",explicitSet:!1,valueSet:["L2R","R2L"],pdfVersion:1.3},ViewArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},ViewClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintScaling:{defaultValue:"AppDefault",value:"AppDefault",type:"name",explicitSet:!1,valueSet:["AppDefault","None"],pdfVersion:1.6},Duplex:{defaultValue:"",value:"none",type:"name",explicitSet:!1,valueSet:["Simplex","DuplexFlipShortEdge","DuplexFlipLongEdge","none"],pdfVersion:1.7},PickTrayByPDFSize:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.7},PrintPageRange:{defaultValue:"",value:"",type:"array",explicitSet:!1,valueSet:null,pdfVersion:1.7},NumCopies:{defaultValue:1,value:1,type:"integer",explicitSet:!1,valueSet:null,pdfVersion:1.7}},h=Object.keys(s),u=[],l=0,c=0,f=0;function d(t,e){var r,n=!1;for(r=0;r<t.length;r+=1)t[r]===e&&(n=!0);return n}if(void 0===this.internal.viewerpreferences&&(this.internal.viewerpreferences={},this.internal.viewerpreferences.configuration=JSON.parse(JSON.stringify(s)),this.internal.viewerpreferences.isSubscribed=!1),r=this.internal.viewerpreferences.configuration,"reset"===t||!0===e){var p=h.length;for(f=0;f<p;f+=1)r[h[f]].value=r[h[f]].defaultValue,r[h[f]].explicitSet=!1}if("object"===i()(t)){for(o in t)if(a=t[o],d(h,o)&&void 0!==a){if("boolean"===r[o].type&&"boolean"==typeof a)r[o].value=a;else if("name"===r[o].type&&d(r[o].valueSet,a))r[o].value=a;else if("integer"===r[o].type&&Number.isInteger(a))r[o].value=a;else if("array"===r[o].type){for(l=0;l<a.length;l+=1)if(n=!0,1===a[l].length&&"number"==typeof a[l][0])u.push(String(a[l]-1));else if(a[l].length>1){for(c=0;c<a[l].length;c+=1)"number"!=typeof a[l][c]&&(n=!1);!0===n&&u.push([a[l][0]-1,a[l][1]-1].join(" "))}r[o].value="["+u.join(" ")+"]"}else r[o].value=r[o].defaultValue;r[o].explicitSet=!0}}return!1===this.internal.viewerpreferences.isSubscribed&&(this.internal.events.subscribe("putCatalog",function(){var t,e=[];for(t in r)!0===r[t].explicitSet&&("name"===r[t].type?e.push("/"+t+" /"+r[t].value):e.push("/"+t+" "+r[t].value));0!==e.length&&this.internal.write("/ViewerPreferences\n<<\n"+e.join("\n")+"\n>>")}),this.internal.viewerpreferences.isSubscribed=!0),this.internal.viewerpreferences.configuration=r,this},function(t){var e=function(){var t='<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"><rdf:Description rdf:about="" xmlns:jspdf="'+this.internal.__metadata__.namespaceuri+'"><jspdf:metadata>',e=unescape(encodeURIComponent('<x:xmpmeta xmlns:x="adobe:ns:meta/">')),r=unescape(encodeURIComponent(t)),n=unescape(encodeURIComponent(this.internal.__metadata__.metadata)),i=unescape(encodeURIComponent("</jspdf:metadata></rdf:Description></rdf:RDF>")),o=unescape(encodeURIComponent("</x:xmpmeta>")),a=r.length+n.length+i.length+e.length+o.length;this.internal.__metadata__.metadata_object_number=this.internal.newObject(),this.internal.write("<< /Type /Metadata /Subtype /XML /Length "+a+" >>"),this.internal.write("stream"),this.internal.write(e+r+n+i+o),this.internal.write("endstream"),this.internal.write("endobj")},r=function(){this.internal.__metadata__.metadata_object_number&&this.internal.write("/Metadata "+this.internal.__metadata__.metadata_object_number+" 0 R")};t.addMetadata=function(t,n){return void 0===this.internal.__metadata__&&(this.internal.__metadata__={metadata:t,namespaceuri:n||"http://jspdf.default.namespaceuri/"},this.internal.events.subscribe("putCatalog",r),this.internal.events.subscribe("postPutResources",e)),this}}(t1.API),function(t){var e=t.API,r=e.pdfEscape16=function(t,e){for(var r,n=e.metadata.Unicode.widths,i=["","0","00","000","0000"],o=[""],a=0,s=t.length;a<s&&(r=e.metadata.characterToGlyph(t.charCodeAt(a)),e.metadata.glyIdsUsed.push(r),e.metadata.toUnicode[r]=t.charCodeAt(a),-1==n.indexOf(r)&&(n.push(r),n.push([parseInt(e.metadata.widthOfGlyph(r),10)])),"0"!=r);++a)r=r.toString(16),o.push(i[4-r.length],r);return o.join("")},n=function(t){var e,r,n,i,o,a,s;for(o="/CIDInit /ProcSet findresource begin\n12 dict begin\nbegincmap\n/CIDSystemInfo <<\n  /Registry (Adobe)\n  /Ordering (UCS)\n  /Supplement 0\n>> def\n/CMapName /Adobe-Identity-UCS def\n/CMapType 2 def\n1 begincodespacerange\n<0000><ffff>\nendcodespacerange",n=[],a=0,s=(r=Object.keys(t).sort(function(t,e){return t-e})).length;a<s;a++)e=r[a],n.length>=100&&(o+="\n"+n.length+" beginbfchar\n"+n.join("\n")+"\nendbfchar",n=[]),void 0!==t[e]&&null!==t[e]&&"function"==typeof t[e].toString&&(i=("0000"+t[e].toString(16)).slice(-4),e=("0000"+(+e).toString(16)).slice(-4),n.push("<"+e+"><"+i+">"));return n.length&&(o+="\n"+n.length+" beginbfchar\n"+n.join("\n")+"\nendbfchar\n"),o+="endcmap\nCMapName currentdict /CMap defineresource pop\nend\nend"};e.events.push(["putFont",function(e){!function(e){var r=e.font,i=e.out,o=e.newObject,a=e.putStream;if(r.metadata instanceof t.API.TTFFont&&"Identity-H"===r.encoding){for(var s=r.metadata.Unicode.widths,h=r.metadata.subset.encode(r.metadata.glyIdsUsed,1),u="",l=0;l<h.length;l++)u+=String.fromCharCode(h[l]);var c=o();a({data:u,addLength1:!0,objectId:c}),i("endobj");var f=o();a({data:n(r.metadata.toUnicode),addLength1:!0,objectId:f}),i("endobj");var d=o();i("<<"),i("/Type /FontDescriptor"),i("/FontName /"+tJ(r.fontName)),i("/FontFile2 "+c+" 0 R"),i("/FontBBox "+t.API.PDFObject.convert(r.metadata.bbox)),i("/Flags "+r.metadata.flags),i("/StemV "+r.metadata.stemV),i("/ItalicAngle "+r.metadata.italicAngle),i("/Ascent "+r.metadata.ascender),i("/Descent "+r.metadata.decender),i("/CapHeight "+r.metadata.capHeight),i(">>"),i("endobj");var p=o();i("<<"),i("/Type /Font"),i("/BaseFont /"+tJ(r.fontName)),i("/FontDescriptor "+d+" 0 R"),i("/W "+t.API.PDFObject.convert(s)),i("/CIDToGIDMap /Identity"),i("/DW 1000"),i("/Subtype /CIDFontType2"),i("/CIDSystemInfo"),i("<<"),i("/Supplement 0"),i("/Registry (Adobe)"),i("/Ordering ("+r.encoding+")"),i(">>"),i(">>"),i("endobj"),r.objectNumber=o(),i("<<"),i("/Type /Font"),i("/Subtype /Type0"),i("/ToUnicode "+f+" 0 R"),i("/BaseFont /"+tJ(r.fontName)),i("/Encoding /"+r.encoding),i("/DescendantFonts ["+p+" 0 R]"),i(">>"),i("endobj"),r.isAlreadyPutted=!0}}(e)}]),e.events.push(["putFont",function(e){!function(e){var r=e.font,i=e.out,o=e.newObject,a=e.putStream;if(r.metadata instanceof t.API.TTFFont&&"WinAnsiEncoding"===r.encoding){for(var s=r.metadata.rawData,h="",u=0;u<s.length;u++)h+=String.fromCharCode(s[u]);var l=o();a({data:h,addLength1:!0,objectId:l}),i("endobj");var c=o();a({data:n(r.metadata.toUnicode),addLength1:!0,objectId:c}),i("endobj");var f=o();i("<<"),i("/Descent "+r.metadata.decender),i("/CapHeight "+r.metadata.capHeight),i("/StemV "+r.metadata.stemV),i("/Type /FontDescriptor"),i("/FontFile2 "+l+" 0 R"),i("/Flags 96"),i("/FontBBox "+t.API.PDFObject.convert(r.metadata.bbox)),i("/FontName /"+tJ(r.fontName)),i("/ItalicAngle "+r.metadata.italicAngle),i("/Ascent "+r.metadata.ascender),i(">>"),i("endobj"),r.objectNumber=o();for(var d=0;d<r.metadata.hmtx.widths.length;d++)r.metadata.hmtx.widths[d]=parseInt(r.metadata.hmtx.widths[d]*(1e3/r.metadata.head.unitsPerEm));i("<</Subtype/TrueType/Type/Font/ToUnicode "+c+" 0 R/BaseFont/"+tJ(r.fontName)+"/FontDescriptor "+f+" 0 R/Encoding/"+r.encoding+" /FirstChar 29 /LastChar 255 /Widths "+t.API.PDFObject.convert(r.metadata.hmtx.widths)+">>"),i("endobj"),r.isAlreadyPutted=!0}}(e)}]);var i=function(t){var e,n=t.text||"",i=t.x,o=t.y,a=t.options||{},s=t.mutex||{},h=s.pdfEscape,u=s.activeFontKey,l=s.fonts,c=u,f="",d=0,p="",g=l[c].encoding;if("Identity-H"!==l[c].encoding)return{text:n,x:i,y:o,options:a,mutex:s};for(p=n,c=u,Array.isArray(n)&&(p=n[0]),d=0;d<p.length;d+=1)l[c].metadata.hasOwnProperty("cmap")&&(e=l[c].metadata.cmap.unicode.codeMap[p[d].charCodeAt(0)]),e||256>p[d].charCodeAt(0)&&l[c].metadata.hasOwnProperty("Unicode")?f+=p[d]:f+="";var m="";return 14>parseInt(c.slice(1))||"WinAnsiEncoding"===g?m=h(f,c).split("").map(function(t){return t.charCodeAt(0).toString(16)}).join(""):"Identity-H"===g&&(m=r(f,l[c])),s.isHex=!0,{text:m,x:i,y:o,options:a,mutex:s}};e.events.push(["postProcessText",function(t){var e=t.text||"",r=[],n={text:e,x:t.x,y:t.y,options:t.options,mutex:t.mutex};if(Array.isArray(e)){var o=0;for(o=0;o<e.length;o+=1)Array.isArray(e[o])&&3===e[o].length?r.push([i(Object.assign({},n,{text:e[o][0]})).text,e[o][1],e[o][2]]):r.push(i(Object.assign({},n,{text:e[o]})).text);t.text=r}else t.text=i(Object.assign({},n,{text:e})).text}])}(t1),function(t){var e=function(){return void 0===this.internal.vFS&&(this.internal.vFS={}),!0};t.existsFileInVFS=function(t){return e.call(this),void 0!==this.internal.vFS[t]},t.addFileToVFS=function(t,r){return e.call(this),this.internal.vFS[t]=r,this},t.getFileFromVFS=function(t){return e.call(this),void 0!==this.internal.vFS[t]?this.internal.vFS[t]:null}}(t1.API),function(t){t.__bidiEngine__=t.prototype.__bidiEngine__=function(t){var r,n,i,o,a,s,h,u=[[0,3,0,1,0,0,0],[0,3,0,1,2,2,0],[0,3,0,17,2,0,1],[0,3,5,5,4,1,0],[0,3,21,21,4,0,1],[0,3,5,5,4,2,0]],l=[[2,0,1,1,0,1,0],[2,0,1,1,0,2,0],[2,0,2,1,3,2,0],[2,0,2,33,3,1,1]],c={L:0,R:1,EN:2,AN:3,N:4,B:5,S:6},f={0:0,5:1,6:2,7:3,32:4,251:5,254:6,255:7},d=["(",")","(","<",">","<","[","]","[","{","}","{","\xab","\xbb","\xab","‹","›","‹","⁅","⁆","⁅","⁽","⁾","⁽","₍","₎","₍","≤","≥","≤","〈","〉","〈","﹙","﹚","﹙","﹛","﹜","﹛","﹝","﹞","﹝","﹤","﹥","﹤"],p=new RegExp(/^([1-4|9]|1[0-9]|2[0-9]|3[0168]|4[04589]|5[012]|7[78]|159|16[0-9]|17[0-2]|21[569]|22[03489]|250)$/),g=!1,m=0;this.__bidiEngine__={};var b=function(t){var r=t.charCodeAt(),n=r>>8,i=f[n];return void 0!==i?e[256*i+(255&r)]:252===n||253===n?"AL":p.test(n)?"L":8===n?"R":"N"},v=function(t){for(var e,r=0;r<t.length&&"L"!==(e=b(t.charAt(r)));r++)if("R"===e)return!0;return!1},y=function(t,e,a,s){var h,u,l,c,f=e[s];switch(f){case"L":case"R":case"LRE":case"RLE":case"LRO":case"RLO":case"PDF":g=!1;break;case"N":case"AN":break;case"EN":g&&(f="AN");break;case"AL":g=!0,f="R";break;case"WS":case"BN":f="N";break;case"CS":s<1||s+1>=e.length||"EN"!==(h=a[s-1])&&"AN"!==h||"EN"!==(u=e[s+1])&&"AN"!==u?f="N":g&&(u="AN"),f=u===h?u:"N";break;case"ES":f="EN"===(h=s>0?a[s-1]:"B")&&s+1<e.length&&"EN"===e[s+1]?"EN":"N";break;case"ET":if(s>0&&"EN"===a[s-1]){f="EN";break}if(g){f="N";break}for(l=s+1,c=e.length;l<c&&"ET"===e[l];)l++;f=l<c&&"EN"===e[l]?"EN":"N";break;case"NSM":if(i&&!o){for(c=e.length,l=s+1;l<c&&"NSM"===e[l];)l++;if(l<c){var d=t[s];if(h=e[l],(d>=1425&&d<=2303||64286===d)&&("R"===h||"AL"===h)){f="R";break}}}f=s<1||"B"===(h=e[s-1])?"N":a[s-1];break;case"B":g=!1,r=!0,f=m;break;case"S":n=!0,f="N"}return f},w=function(t,e,r){var n=t.split("");return r&&x(n,r,{hiLevel:m}),n.reverse(),e&&e.reverse(),n.join("")},x=function(t,e,i){var o,a,s,h,f,d=-1,p=t.length,v=0,w=[],x=m?l:u,_=[];for(g=!1,r=!1,n=!1,a=0;a<p;a++)_[a]=b(t[a]);for(s=0;s<p;s++){if(f=v,w[s]=y(t,_,w,s),o=240&(v=x[f][c[w[s]]]),v&=15,e[s]=h=x[v][5],o>0)if(16===o){for(a=d;a<s;a++)e[a]=1;d=-1}else d=-1;if(x[v][6])-1===d&&(d=s);else if(d>-1){for(a=d;a<s;a++)e[a]=h;d=-1}"B"===_[s]&&(e[s]=0),i.hiLevel|=h}n&&function(t,e,r){for(var n=0;n<r;n++)if("S"===t[n]){e[n]=m;for(var i=n-1;i>=0&&"WS"===t[i];i--)e[i]=m}}(_,e,p)},_=function(t,e,n,i,o){if(!(o.hiLevel<t)){if(1===t&&1===m&&!r)return e.reverse(),void(n&&n.reverse());for(var a,s,h,u,l=e.length,c=0;c<l;){if(i[c]>=t){for(h=c+1;h<l&&i[h]>=t;)h++;for(u=c,s=h-1;u<s;u++,s--)a=e[u],e[u]=e[s],e[s]=a,n&&(a=n[u],n[u]=n[s],n[s]=a);c=h}c++}}},A=function(t,e,r){var n=t.split(""),i={hiLevel:m};return r||(r=[]),x(n,r,i),function(t,e,r){if(0!==r.hiLevel&&h)for(var n,i=0;i<t.length;i++)1===e[i]&&(n=d.indexOf(t[i]))>=0&&(t[i]=d[n+1])}(n,r,i),_(2,n,e,r,i),_(1,n,e,r,i),n.join("")};return this.__bidiEngine__.doBidiReorder=function(t,e,r){if(function(t,e){if(e)for(var r=0;r<t.length;r++)e[r]=r;void 0===o&&(o=v(t)),void 0===s&&(s=v(t))}(t,e),i||!a||s)if(i&&a&&o^s)m=+!!o,t=w(t,e,r);else if(!i&&a&&s)m=+!!o,t=w(t=A(t,e,r),e);else if(!i||o||a||s){if(i&&!a&&o^s)t=w(t,e),o?(m=0,t=A(t,e,r)):(m=1,t=w(t=A(t,e,r),e));else if(i&&o&&!a&&s)m=1,t=w(t=A(t,e,r),e);else if(!i&&!a&&o^s){var n=h;o?(m=1,t=A(t,e,r),m=0,h=!1,t=A(t,e,r),h=n):(m=0,t=w(t=A(t,e,r),e),m=1,h=!1,t=A(t,e,r),h=n,t=w(t,e))}}else m=0,t=A(t,e,r);else m=+!!o,t=A(t,e,r);return t},this.__bidiEngine__.setOptions=function(t){t&&(i=t.isInputVisual,a=t.isOutputVisual,o=t.isInputRtl,s=t.isOutputRtl,h=t.isSymmetricSwapping)},this.__bidiEngine__.setOptions(t),this.__bidiEngine__};var e=["BN","BN","BN","BN","BN","BN","BN","BN","BN","S","B","S","WS","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","B","B","B","S","WS","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","BN","BN","BN","BN","BN","BN","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","CS","N","ET","ET","ET","ET","N","N","N","N","L","N","N","BN","N","N","ET","ET","EN","EN","N","L","N","N","N","EN","L","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","N","N","N","N","N","ET","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","NSM","R","NSM","NSM","R","NSM","NSM","R","NSM","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","N","N","N","N","N","R","R","R","R","R","N","N","N","N","N","N","N","N","N","N","N","AN","AN","AN","AN","AN","AN","N","N","AL","ET","ET","AL","CS","AL","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","AN","AN","AN","AN","AN","AN","AN","AN","AN","ET","AN","AN","AL","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","N","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","NSM","NSM","N","NSM","NSM","NSM","NSM","AL","AL","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","R","N","N","N","N","R","N","N","N","N","N","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","BN","BN","BN","L","R","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","B","LRE","RLE","PDF","LRO","RLO","CS","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","BN","BN","BN","BN","BN","N","LRI","RLI","FSI","PDI","BN","BN","BN","BN","BN","BN","EN","L","N","N","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","L","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","N","N","N","N","N","R","NSM","R","R","R","R","R","R","R","R","R","R","ES","R","R","R","R","R","R","R","R","R","R","R","R","R","N","R","R","R","R","R","N","R","N","R","R","N","R","R","N","R","R","R","R","R","R","R","R","R","R","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","CS","N","N","CS","N","N","N","N","N","N","N","N","N","ET","N","N","ES","ES","N","N","N","N","N","ET","ET","N","N","N","N","N","AL","AL","AL","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","BN","N","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","N","N","N","ET","ET","N","N","N","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N"],r=new t.__bidiEngine__({isInputVisual:!0});t.API.events.push(["postProcessText",function(t){var e=t.text;t.x,t.y;var n=t.options||{};t.mutex,n.lang;var i=[];if(n.isInputVisual="boolean"!=typeof n.isInputVisual||n.isInputVisual,r.setOptions(n),"[object Array]"===Object.prototype.toString.call(e)){var o=0;for(i=[],o=0;o<e.length;o+=1)"[object Array]"===Object.prototype.toString.call(e[o])?i.push([r.doBidiReorder(e[o][0]),e[o][1],e[o][2]]):i.push([r.doBidiReorder(e[o])]);t.text=i}else t.text=r.doBidiReorder(e);r.setOptions({isInputVisual:!0})}])}(t1),t1.API.TTFFont=function(){function t(t){var e;if(this.rawData=t,e=this.contents=new e8(t),this.contents.pos=4,"ttcf"===e.readString(4))throw Error("TTCF not supported.");e.pos=0,this.parse(),this.subset=new rm(this),this.registerTTF()}return t.open=function(e){return new t(e)},t.prototype.parse=function(){return this.directory=new e7(this.contents),this.head=new re(this),this.name=new rh(this),this.cmap=new rn(this),this.toUnicode={},this.hhea=new ri(this),this.maxp=new ru(this),this.hmtx=new rl(this),this.post=new ra(this),this.os2=new ro(this),this.loca=new rg(this),this.glyf=new rf(this),this.ascender=this.os2.exists&&this.os2.ascender||this.hhea.ascender,this.decender=this.os2.exists&&this.os2.decender||this.hhea.decender,this.lineGap=this.os2.exists&&this.os2.lineGap||this.hhea.lineGap,this.bbox=[this.head.xMin,this.head.yMin,this.head.xMax,this.head.yMax]},t.prototype.registerTTF=function(){var t,e,r,n,i;if(this.scaleFactor=1e3/this.head.unitsPerEm,this.bbox=(function(){var e,r,n,i;for(i=[],e=0,r=(n=this.bbox).length;e<r;e++)t=n[e],i.push(Math.round(t*this.scaleFactor));return i}).call(this),this.stemV=0,this.post.exists?(r=255&(n=this.post.italic_angle),0!=(32768&(e=n>>16))&&(e=-(1+(65535^e))),this.italicAngle=+(e+"."+r)):this.italicAngle=0,this.ascender=Math.round(this.ascender*this.scaleFactor),this.decender=Math.round(this.decender*this.scaleFactor),this.lineGap=Math.round(this.lineGap*this.scaleFactor),this.capHeight=this.os2.exists&&this.os2.capHeight||this.ascender,this.xHeight=this.os2.exists&&this.os2.xHeight||0,this.familyClass=(this.os2.exists&&this.os2.familyClass||0)>>8,this.isSerif=1===(i=this.familyClass)||2===i||3===i||4===i||5===i||7===i,this.isScript=10===this.familyClass,this.flags=0,this.post.isFixedPitch&&(this.flags|=1),this.isSerif&&(this.flags|=2),this.isScript&&(this.flags|=8),0!==this.italicAngle&&(this.flags|=64),this.flags|=32,!this.cmap.unicode)throw Error("No unicode cmap for font")},t.prototype.characterToGlyph=function(t){var e;return(null!=(e=this.cmap.unicode)?e.codeMap[t]:void 0)||0},t.prototype.widthOfGlyph=function(t){var e;return e=1e3/this.head.unitsPerEm,this.hmtx.forGlyph(t).advance*e},t.prototype.widthOfString=function(t,e,r){var n,i,o,a;for(o=0,i=0,a=(t=""+t).length;0<=a?i<a:i>a;i=0<=a?++i:--i)n=t.charCodeAt(i),o+=this.widthOfGlyph(this.characterToGlyph(n))+1e3/e*r||0;return e/1e3*o},t.prototype.lineHeight=function(t,e){var r;return null==e&&(e=!1),r=e?this.lineGap:0,(this.ascender+r-this.decender)/1e3*t},t}();var e6,e8=function(){function t(t){this.data=null!=t?t:[],this.pos=0,this.length=this.data.length}return t.prototype.readByte=function(){return this.data[this.pos++]},t.prototype.writeByte=function(t){return this.data[this.pos++]=t},t.prototype.readUInt32=function(){return 0x1000000*this.readByte()+(this.readByte()<<16)+(this.readByte()<<8)+this.readByte()},t.prototype.writeUInt32=function(t){return this.writeByte(t>>>24&255),this.writeByte(t>>16&255),this.writeByte(t>>8&255),this.writeByte(255&t)},t.prototype.readInt32=function(){var t;return(t=this.readUInt32())>=0x80000000?t-0x100000000:t},t.prototype.writeInt32=function(t){return t<0&&(t+=0x100000000),this.writeUInt32(t)},t.prototype.readUInt16=function(){return this.readByte()<<8|this.readByte()},t.prototype.writeUInt16=function(t){return this.writeByte(t>>8&255),this.writeByte(255&t)},t.prototype.readInt16=function(){var t;return(t=this.readUInt16())>=32768?t-65536:t},t.prototype.writeInt16=function(t){return t<0&&(t+=65536),this.writeUInt16(t)},t.prototype.readString=function(t){var e,r;for(r=[],e=0;0<=t?e<t:e>t;e=0<=t?++e:--e)r[e]=String.fromCharCode(this.readByte());return r.join("")},t.prototype.writeString=function(t){var e,r,n;for(n=[],e=0,r=t.length;0<=r?e<r:e>r;e=0<=r?++e:--e)n.push(this.writeByte(t.charCodeAt(e)));return n},t.prototype.readShort=function(){return this.readInt16()},t.prototype.writeShort=function(t){return this.writeInt16(t)},t.prototype.readLongLong=function(){var t,e,r,n,i,o,a,s;return t=this.readByte(),e=this.readByte(),r=this.readByte(),n=this.readByte(),i=this.readByte(),o=this.readByte(),a=this.readByte(),s=this.readByte(),128&t?-1*(0x100000000000000*(255^t)+0x1000000000000*(255^e)+0x10000000000*(255^r)+0x100000000*(255^n)+0x1000000*(255^i)+65536*(255^o)+256*(255^a)+(255^s)+1):0x100000000000000*t+0x1000000000000*e+0x10000000000*r+0x100000000*n+0x1000000*i+65536*o+256*a+s},t.prototype.writeLongLong=function(t){var e,r;return e=Math.floor(t/0x100000000),r=0|t,this.writeByte(e>>24&255),this.writeByte(e>>16&255),this.writeByte(e>>8&255),this.writeByte(255&e),this.writeByte(r>>24&255),this.writeByte(r>>16&255),this.writeByte(r>>8&255),this.writeByte(255&r)},t.prototype.readInt=function(){return this.readInt32()},t.prototype.writeInt=function(t){return this.writeInt32(t)},t.prototype.read=function(t){var e,r;for(e=[],r=0;0<=t?r<t:r>t;r=0<=t?++r:--r)e.push(this.readByte());return e},t.prototype.write=function(t){var e,r,n,i;for(i=[],r=0,n=t.length;r<n;r++)e=t[r],i.push(this.writeByte(e));return i},t}(),e7=function(){var t;function e(t){var e,r,n;for(this.scalarType=t.readInt(),this.tableCount=t.readShort(),this.searchRange=t.readShort(),this.entrySelector=t.readShort(),this.rangeShift=t.readShort(),this.tables={},r=0,n=this.tableCount;0<=n?r<n:r>n;r=0<=n?++r:--r)e={tag:t.readString(4),checksum:t.readInt(),offset:t.readInt(),length:t.readInt()},this.tables[e.tag]=e}return e.prototype.encode=function(e){var r,n,i,o,a,s,h,u,l,c,f,d,p;for(p in o=Math.floor((l=16*Math.floor(Math.log(f=Object.keys(e).length)/(s=Math.log(2))))/s),u=16*f-l,(n=new e8).writeInt(this.scalarType),n.writeShort(f),n.writeShort(l),n.writeShort(o),n.writeShort(u),i=16*f,h=n.pos+i,a=null,d=[],e)for(c=e[p],n.writeString(p),n.writeInt(t(c)),n.writeInt(h),n.writeInt(c.length),d=d.concat(c),"head"===p&&(a=h),h+=c.length;h%4;)d.push(0),h++;return n.write(d),r=0xb1b0afba-t(n.data),n.pos=a+8,n.writeUInt32(r),n.data},t=function(t){var e,r,n,i;for(t=rc.call(t);t.length%4;)t.push(0);for(n=new e8(t),r=0,e=0,i=t.length;e<i;e=e+=4)r+=n.readUInt32();return 0|r},e}(),e9={}.hasOwnProperty,rt=function(t,e){for(var r in e)e9.call(e,r)&&(t[r]=e[r]);function n(){this.constructor=t}return n.prototype=e.prototype,t.prototype=new n,t.__super__=e.prototype,t};e6=function(){function t(t){var e;this.file=t,e=this.file.directory.tables[this.tag],this.exists=!!e,e&&(this.offset=e.offset,this.length=e.length,this.parse(this.file.contents))}return t.prototype.parse=function(){},t.prototype.encode=function(){},t.prototype.raw=function(){return this.exists?(this.file.contents.pos=this.offset,this.file.contents.read(this.length)):null},t}();var re=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return rt(e,e6),e.prototype.tag="head",e.prototype.parse=function(t){return t.pos=this.offset,this.version=t.readInt(),this.revision=t.readInt(),this.checkSumAdjustment=t.readInt(),this.magicNumber=t.readInt(),this.flags=t.readShort(),this.unitsPerEm=t.readShort(),this.created=t.readLongLong(),this.modified=t.readLongLong(),this.xMin=t.readShort(),this.yMin=t.readShort(),this.xMax=t.readShort(),this.yMax=t.readShort(),this.macStyle=t.readShort(),this.lowestRecPPEM=t.readShort(),this.fontDirectionHint=t.readShort(),this.indexToLocFormat=t.readShort(),this.glyphDataFormat=t.readShort()},e.prototype.encode=function(t){var e;return(e=new e8).writeInt(this.version),e.writeInt(this.revision),e.writeInt(this.checkSumAdjustment),e.writeInt(this.magicNumber),e.writeShort(this.flags),e.writeShort(this.unitsPerEm),e.writeLongLong(this.created),e.writeLongLong(this.modified),e.writeShort(this.xMin),e.writeShort(this.yMin),e.writeShort(this.xMax),e.writeShort(this.yMax),e.writeShort(this.macStyle),e.writeShort(this.lowestRecPPEM),e.writeShort(this.fontDirectionHint),e.writeShort(t),e.writeShort(this.glyphDataFormat),e.data},e}(),rr=function(){function t(t,e){var r,n,i,o,a,s,h,u,l,c,f,d,p,g,m,b;switch(this.platformID=t.readUInt16(),this.encodingID=t.readShort(),this.offset=e+t.readInt(),l=t.pos,t.pos=this.offset,this.format=t.readUInt16(),this.length=t.readUInt16(),this.language=t.readUInt16(),this.isUnicode=3===this.platformID&&1===this.encodingID&&4===this.format||0===this.platformID&&4===this.format,this.codeMap={},this.format){case 0:for(s=0;s<256;++s)this.codeMap[s]=t.readByte();break;case 4:for(c=t.readUInt16()/2,t.pos+=6,i=function(){var e,r;for(r=[],s=e=0;0<=c?e<c:e>c;s=0<=c?++e:--e)r.push(t.readUInt16());return r}(),t.pos+=2,d=function(){var e,r;for(r=[],s=e=0;0<=c?e<c:e>c;s=0<=c?++e:--e)r.push(t.readUInt16());return r}(),h=function(){var e,r;for(r=[],s=e=0;0<=c?e<c:e>c;s=0<=c?++e:--e)r.push(t.readUInt16());return r}(),u=function(){var e,r;for(r=[],s=e=0;0<=c?e<c:e>c;s=0<=c?++e:--e)r.push(t.readUInt16());return r}(),n=(this.length-t.pos+this.offset)/2,a=function(){var e,r;for(r=[],s=e=0;0<=n?e<n:e>n;s=0<=n?++e:--e)r.push(t.readUInt16());return r}(),s=g=0,b=i.length;g<b;s=++g)for(p=i[s],r=m=f=d[s];f<=p?m<=p:m>=p;r=f<=p?++m:--m)0===u[s]?o=r+h[s]:0!==(o=a[u[s]/2+(r-f)-(c-s)]||0)&&(o+=h[s]),this.codeMap[r]=65535&o}t.pos=l}return t.encode=function(t,e){var r,n,i,o,a,s,h,u,l,c,f,d,p,g,m,b,v,y,w,x,_,A,L,N,S,P,k,I,C,E,F,O,T,M,j,B,R,D,q,U,z,H,W,V,G,Y;switch(I=new e8,o=Object.keys(t).sort(function(t,e){return t-e}),e){case"macroman":for(p=0,g=function(){var t=[];for(d=0;d<256;++d)t.push(0);return t}(),b={0:0},i={},C=0,T=o.length;C<T;C++)null==b[W=t[n=o[C]]]&&(b[W]=++p),i[n]={old:t[n],new:b[t[n]]},g[n]=b[t[n]];return I.writeUInt16(1),I.writeUInt16(0),I.writeUInt32(12),I.writeUInt16(0),I.writeUInt16(262),I.writeUInt16(0),I.write(g),{charMap:i,subtable:I.data,maxGlyphID:p+1};case"unicode":for(P=[],l=[],v=0,b={},r={},m=h=null,E=0,M=o.length;E<M;E++)null==b[w=t[n=o[E]]]&&(b[w]=++v),r[n]={old:w,new:b[w]},a=b[w]-n,null!=m&&a===h||(m&&l.push(m),P.push(n),h=a),m=n;for(m&&l.push(m),l.push(65535),P.push(65535),N=2*(L=P.length),c=Math.log((A=2*Math.pow(Math.log(L)/Math.LN2,2))/2)/Math.LN2,_=2*L-A,s=[],x=[],f=[],d=F=0,j=P.length;F<j;d=++F){if(S=P[d],u=l[d],65535===S){s.push(0),x.push(0);break}if(S-(k=r[S].new)>=32768)for(s.push(0),x.push(2*(f.length+L-d)),n=O=S;S<=u?O<=u:O>=u;n=S<=u?++O:--O)f.push(r[n].new);else s.push(k-S),x.push(0)}for(I.writeUInt16(3),I.writeUInt16(1),I.writeUInt32(12),I.writeUInt16(4),I.writeUInt16(16+8*L+2*f.length),I.writeUInt16(0),I.writeUInt16(N),I.writeUInt16(A),I.writeUInt16(c),I.writeUInt16(_),z=0,B=l.length;z<B;z++)n=l[z],I.writeUInt16(n);for(I.writeUInt16(0),H=0,R=P.length;H<R;H++)n=P[H],I.writeUInt16(n);for(V=0,D=s.length;V<D;V++)a=s[V],I.writeUInt16(a);for(G=0,q=x.length;G<q;G++)y=x[G],I.writeUInt16(y);for(Y=0,U=f.length;Y<U;Y++)p=f[Y],I.writeUInt16(p);return{charMap:r,subtable:I.data,maxGlyphID:v+1}}},t}(),rn=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return rt(e,e6),e.prototype.tag="cmap",e.prototype.parse=function(t){var e,r,n;for(t.pos=this.offset,this.version=t.readUInt16(),n=t.readUInt16(),this.tables=[],this.unicode=null,r=0;0<=n?r<n:r>n;r=0<=n?++r:--r)e=new rr(t,this.offset),this.tables.push(e),e.isUnicode&&null==this.unicode&&(this.unicode=e);return!0},e.encode=function(t,e){var r,n;return null==e&&(e="macroman"),r=rr.encode(t,e),(n=new e8).writeUInt16(0),n.writeUInt16(1),r.table=n.data.concat(r.subtable),r},e}(),ri=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return rt(e,e6),e.prototype.tag="hhea",e.prototype.parse=function(t){return t.pos=this.offset,this.version=t.readInt(),this.ascender=t.readShort(),this.decender=t.readShort(),this.lineGap=t.readShort(),this.advanceWidthMax=t.readShort(),this.minLeftSideBearing=t.readShort(),this.minRightSideBearing=t.readShort(),this.xMaxExtent=t.readShort(),this.caretSlopeRise=t.readShort(),this.caretSlopeRun=t.readShort(),this.caretOffset=t.readShort(),t.pos+=8,this.metricDataFormat=t.readShort(),this.numberOfMetrics=t.readUInt16()},e}(),ro=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return rt(e,e6),e.prototype.tag="OS/2",e.prototype.parse=function(t){if(t.pos=this.offset,this.version=t.readUInt16(),this.averageCharWidth=t.readShort(),this.weightClass=t.readUInt16(),this.widthClass=t.readUInt16(),this.type=t.readShort(),this.ySubscriptXSize=t.readShort(),this.ySubscriptYSize=t.readShort(),this.ySubscriptXOffset=t.readShort(),this.ySubscriptYOffset=t.readShort(),this.ySuperscriptXSize=t.readShort(),this.ySuperscriptYSize=t.readShort(),this.ySuperscriptXOffset=t.readShort(),this.ySuperscriptYOffset=t.readShort(),this.yStrikeoutSize=t.readShort(),this.yStrikeoutPosition=t.readShort(),this.familyClass=t.readShort(),this.panose=function(){var e,r;for(r=[],e=0;e<10;++e)r.push(t.readByte());return r}(),this.charRange=function(){var e,r;for(r=[],e=0;e<4;++e)r.push(t.readInt());return r}(),this.vendorID=t.readString(4),this.selection=t.readShort(),this.firstCharIndex=t.readShort(),this.lastCharIndex=t.readShort(),this.version>0&&(this.ascent=t.readShort(),this.descent=t.readShort(),this.lineGap=t.readShort(),this.winAscent=t.readShort(),this.winDescent=t.readShort(),this.codePageRange=function(){var e,r;for(r=[],e=0;e<2;e=++e)r.push(t.readInt());return r}(),this.version>1))return this.xHeight=t.readShort(),this.capHeight=t.readShort(),this.defaultChar=t.readShort(),this.breakChar=t.readShort(),this.maxContext=t.readShort()},e}(),ra=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return rt(e,e6),e.prototype.tag="post",e.prototype.parse=function(t){var e,r,n,i;switch(t.pos=this.offset,this.format=t.readInt(),this.italicAngle=t.readInt(),this.underlinePosition=t.readShort(),this.underlineThickness=t.readShort(),this.isFixedPitch=t.readInt(),this.minMemType42=t.readInt(),this.maxMemType42=t.readInt(),this.minMemType1=t.readInt(),this.maxMemType1=t.readInt(),this.format){case 65536:case 196608:break;case 131072:for(r=t.readUInt16(),this.glyphNameIndex=[],i=0;0<=r?i<r:i>r;i=0<=r?++i:--i)this.glyphNameIndex.push(t.readUInt16());for(this.names=[],n=[];t.pos<this.offset+this.length;)e=t.readByte(),n.push(this.names.push(t.readString(e)));return n;case 151552:return r=t.readUInt16(),this.offsets=t.read(r);case 262144:return this.map=(function(){var e,r,n;for(n=[],i=e=0,r=this.file.maxp.numGlyphs;0<=r?e<r:e>r;i=0<=r?++e:--e)n.push(t.readUInt32());return n}).call(this)}},e}(),rs=function(t,e){this.raw=t,this.length=t.length,this.platformID=e.platformID,this.encodingID=e.encodingID,this.languageID=e.languageID},rh=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return rt(e,e6),e.prototype.tag="name",e.prototype.parse=function(t){var e,r,n,i,o,a,s,h,u,l;for(t.pos=this.offset,t.readShort(),e=t.readShort(),a=t.readShort(),r=[],i=0;0<=e?i<e:i>e;i=0<=e?++i:--i)r.push({platformID:t.readShort(),encodingID:t.readShort(),languageID:t.readShort(),nameID:t.readShort(),length:t.readShort(),offset:this.offset+a+t.readShort()});for(s={},i=h=0,u=r.length;h<u;i=++h)t.pos=(n=r[i]).offset,o=new rs(t.readString(n.length),n),null==s[l=n.nameID]&&(s[l]=[]),s[n.nameID].push(o);this.strings=s,this.copyright=s[0],this.fontFamily=s[1],this.fontSubfamily=s[2],this.uniqueSubfamily=s[3],this.fontName=s[4],this.version=s[5];try{this.postscriptName=s[6][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}catch(t){this.postscriptName=s[4][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}return this.trademark=s[7],this.manufacturer=s[8],this.designer=s[9],this.description=s[10],this.vendorUrl=s[11],this.designerUrl=s[12],this.license=s[13],this.licenseUrl=s[14],this.preferredFamily=s[15],this.preferredSubfamily=s[17],this.compatibleFull=s[18],this.sampleText=s[19]},e}(),ru=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return rt(e,e6),e.prototype.tag="maxp",e.prototype.parse=function(t){return t.pos=this.offset,this.version=t.readInt(),this.numGlyphs=t.readUInt16(),this.maxPoints=t.readUInt16(),this.maxContours=t.readUInt16(),this.maxCompositePoints=t.readUInt16(),this.maxComponentContours=t.readUInt16(),this.maxZones=t.readUInt16(),this.maxTwilightPoints=t.readUInt16(),this.maxStorage=t.readUInt16(),this.maxFunctionDefs=t.readUInt16(),this.maxInstructionDefs=t.readUInt16(),this.maxStackElements=t.readUInt16(),this.maxSizeOfInstructions=t.readUInt16(),this.maxComponentElements=t.readUInt16(),this.maxComponentDepth=t.readUInt16()},e}(),rl=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return rt(e,e6),e.prototype.tag="hmtx",e.prototype.parse=function(t){var e,r,n,i,o,a,s;for(t.pos=this.offset,this.metrics=[],e=0,a=this.file.hhea.numberOfMetrics;0<=a?e<a:e>a;e=0<=a?++e:--e)this.metrics.push({advance:t.readUInt16(),lsb:t.readInt16()});for(n=this.file.maxp.numGlyphs-this.file.hhea.numberOfMetrics,this.leftSideBearings=function(){var r,i;for(i=[],e=r=0;0<=n?r<n:r>n;e=0<=n?++r:--r)i.push(t.readInt16());return i}(),this.widths=(function(){var t,e,r,n;for(n=[],t=0,e=(r=this.metrics).length;t<e;t++)i=r[t],n.push(i.advance);return n}).call(this),r=this.widths[this.widths.length-1],s=[],e=o=0;0<=n?o<n:o>n;e=0<=n?++o:--o)s.push(this.widths.push(r));return s},e.prototype.forGlyph=function(t){return t in this.metrics?this.metrics[t]:{advance:this.metrics[this.metrics.length-1].advance,lsb:this.leftSideBearings[t-this.metrics.length]}},e}(),rc=[].slice,rf=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return rt(e,e6),e.prototype.tag="glyf",e.prototype.parse=function(){return this.cache={}},e.prototype.glyphFor=function(t){var e,r,n,i,o,a,s,h,u,l;return t in this.cache?this.cache[t]:(i=this.file.loca,e=this.file.contents,r=i.indexOf(t),0===(n=i.lengthOf(t))?this.cache[t]=null:(e.pos=this.offset+r,o=(a=new e8(e.read(n))).readShort(),h=a.readShort(),l=a.readShort(),s=a.readShort(),u=a.readShort(),this.cache[t]=-1===o?new rp(a,h,l,s,u):new rd(a,o,h,l,s,u),this.cache[t]))},e.prototype.encode=function(t,e,r){var n,i,o,a,s;for(o=[],i=[],a=0,s=e.length;a<s;a++)n=t[e[a]],i.push(o.length),n&&(o=o.concat(n.encode(r)));return i.push(o.length),{table:o,offsets:i}},e}(),rd=function(){function t(t,e,r,n,i,o){this.raw=t,this.numberOfContours=e,this.xMin=r,this.yMin=n,this.xMax=i,this.yMax=o,this.compound=!1}return t.prototype.encode=function(){return this.raw.data},t}(),rp=function(){function t(t,e,r,n,i){var o,a;for(this.raw=t,this.xMin=e,this.yMin=r,this.xMax=n,this.yMax=i,this.compound=!0,this.glyphIDs=[],this.glyphOffsets=[],o=this.raw;a=o.readShort(),this.glyphOffsets.push(o.pos),this.glyphIDs.push(o.readUInt16()),32&a;)o.pos+=1&a?4:2,128&a?o.pos+=8:64&a?o.pos+=4:8&a&&(o.pos+=2)}return t.prototype.encode=function(){var t,e,r;for(e=new e8(rc.call(this.raw.data)),t=0,r=this.glyphIDs.length;t<r;++t)e.pos=this.glyphOffsets[t];return e.data},t}(),rg=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return rt(e,e6),e.prototype.tag="loca",e.prototype.parse=function(t){var e,r;return t.pos=this.offset,e=this.file.head.indexToLocFormat,this.offsets=0===e?(function(){var e,n;for(n=[],r=0,e=this.length;r<e;r+=2)n.push(2*t.readUInt16());return n}).call(this):(function(){var e,n;for(n=[],r=0,e=this.length;r<e;r+=4)n.push(t.readUInt32());return n}).call(this)},e.prototype.indexOf=function(t){return this.offsets[t]},e.prototype.lengthOf=function(t){return this.offsets[t+1]-this.offsets[t]},e.prototype.encode=function(t,e){for(var r=new Uint32Array(this.offsets.length),n=0,i=0,o=0;o<r.length;++o)if(r[o]=n,i<e.length&&e[i]==o){++i,r[o]=n;var a=this.offsets[o],s=this.offsets[o+1]-a;s>0&&(n+=s)}for(var h=Array(4*r.length),u=0;u<r.length;++u)h[4*u+3]=255&r[u],h[4*u+2]=(65280&r[u])>>8,h[4*u+1]=(0xff0000&r[u])>>16,h[4*u]=(0xff000000&r[u])>>24;return h},e}(),rm=function(){function t(t){this.font=t,this.subset={},this.unicodes={},this.next=33}return t.prototype.generateCmap=function(){var t,e,r,n,i;for(e in n=this.font.cmap.tables[0].codeMap,t={},i=this.subset)r=i[e],t[e]=n[r];return t},t.prototype.glyphsFor=function(t){var e,r,n,i,o,a,s;for(n={},o=0,a=t.length;o<a;o++)n[i=t[o]]=this.font.glyf.glyphFor(i);for(i in e=[],n)(null!=(r=n[i])?r.compound:void 0)&&e.push.apply(e,r.glyphIDs);if(e.length>0)for(i in s=this.glyphsFor(e))r=s[i],n[i]=r;return n},t.prototype.encode=function(t,e){var r,n,i,o,a,s,h,u,l,c,f,d,p,g,m;for(n in r=rn.encode(this.generateCmap(),"unicode"),o=this.glyphsFor(t),f={0:0},m=r.charMap)f[(s=m[n]).old]=s.new;for(d in c=r.maxGlyphID,o)d in f||(f[d]=c++);return l=Object.keys(u=function(t){var e,r;for(e in r={},t)r[t[e]]=e;return r}(f)).sort(function(t,e){return t-e}),p=function(){var t,e,r;for(r=[],t=0,e=l.length;t<e;t++)a=l[t],r.push(u[a]);return r}(),i=this.font.glyf.encode(o,p,f),h=this.font.loca.encode(i.offsets,p),g={cmap:this.font.cmap.raw(),glyf:i.table,loca:h,hmtx:this.font.hmtx.raw(),hhea:this.font.hhea.raw(),maxp:this.font.maxp.raw(),post:this.font.post.raw(),name:this.font.name.raw(),head:this.font.head.encode(e)},this.font.os2.exists&&(g["OS/2"]=this.font.os2.raw()),this.font.directory.encode(g)},t}();t1.API.PDFObject=function(){var t;function e(){}return t=function(t,e){return(Array(e+1).join("0")+t).slice(-e)},e.convert=function(r){var n,i,o,a;if(Array.isArray(r))return"["+(function(){var t,i,o;for(o=[],t=0,i=r.length;t<i;t++)n=r[t],o.push(e.convert(n));return o})().join(" ")+"]";if("string"==typeof r)return"/"+r;if(null!=r?r.isString:void 0)return"("+r+")";if(r instanceof Date)return"(D:"+t(r.getUTCFullYear(),4)+t(r.getUTCMonth(),2)+t(r.getUTCDate(),2)+t(r.getUTCHours(),2)+t(r.getUTCMinutes(),2)+t(r.getUTCSeconds(),2)+"Z)";if("[object Object]"===({}).toString.call(r)){for(i in o=["<<"],r)a=r[i],o.push("/"+i+" "+e.convert(a));return o.push(">>"),o.join("\n")}return""+r},e}()},82031:(t,e,r)=>{let n=r(65440),i=r(93032),o=r(24071),a=r(72461),s=r(71094),h=r(27782);function u(t,e,r){if(void 0===t)throw Error("String required as first argument");if(void 0===r&&(r=e,e={}),"function"!=typeof r)if(n())e=r||{},r=null;else throw Error("Callback required as last argument");return{opts:e,cb:r}}function l(t){switch(t){case"svg":return h;case"txt":case"utf8":return a;default:return o}}function c(t,e,r){if(!r.cb)return new Promise(function(n,o){try{let a=i.create(e,r.opts);return t(a,r.opts,function(t,e){return t?o(t):n(e)})}catch(t){o(t)}});try{let n=i.create(e,r.opts);return t(n,r.opts,r.cb)}catch(t){r.cb(t)}}e.create=i.create,e.toCanvas=r(22306).toCanvas,e.toString=function(t,e,r){let n=u(t,e,r);return c(function(t){switch(t){case"svg":return h;case"terminal":return s;default:return a}}(n.opts?n.opts.type:void 0).render,t,n)},e.toDataURL=function(t,e,r){let n=u(t,e,r);return c(l(n.opts.type).renderToDataURL,t,n)},e.toBuffer=function(t,e,r){let n=u(t,e,r);return c(l(n.opts.type).renderToBuffer,t,n)},e.toFile=function(t,e,r,i){if("string"!=typeof t||"string"!=typeof e&&"object"!=typeof e)throw Error("Invalid argument");if(arguments.length<3&&!n())throw Error("Too few arguments provided");let o=u(e,r,i);return c(l(o.opts.type||t.slice((t.lastIndexOf(".")-1>>>0)+2).toLowerCase()).renderToFile.bind(null,t),e,o)},e.toFileStream=function(t,e,r){if(arguments.length<2)throw Error("Too few arguments provided");let n=u(e,r,t.emit.bind(t,"error"));c(l("png").renderToFileStream.bind(null,t),e,n)}},83182:(t,e,r)=>{"use strict";let n=r(98985),i=[function(){},function(t,e,r,n){if(n===e.length)throw Error("Ran out of data");let i=e[n];t[r]=i,t[r+1]=i,t[r+2]=i,t[r+3]=255},function(t,e,r,n){if(n+1>=e.length)throw Error("Ran out of data");let i=e[n];t[r]=i,t[r+1]=i,t[r+2]=i,t[r+3]=e[n+1]},function(t,e,r,n){if(n+2>=e.length)throw Error("Ran out of data");t[r]=e[n],t[r+1]=e[n+1],t[r+2]=e[n+2],t[r+3]=255},function(t,e,r,n){if(n+3>=e.length)throw Error("Ran out of data");t[r]=e[n],t[r+1]=e[n+1],t[r+2]=e[n+2],t[r+3]=e[n+3]}],o=[function(){},function(t,e,r,n){let i=e[0];t[r]=i,t[r+1]=i,t[r+2]=i,t[r+3]=n},function(t,e,r){let n=e[0];t[r]=n,t[r+1]=n,t[r+2]=n,t[r+3]=e[1]},function(t,e,r,n){t[r]=e[0],t[r+1]=e[1],t[r+2]=e[2],t[r+3]=n},function(t,e,r){t[r]=e[0],t[r+1]=e[1],t[r+2]=e[2],t[r+3]=e[3]}];e.dataToBitMap=function(t,e){let r,a,s,h,u,l,c=e.width,f=e.height,d=e.depth,p=e.bpp,g=e.interlace;8!==d&&(r=[],a=0,s={get:function(e){for(;r.length<e;)!function(){let e,n,i,o;if(a===t.length)throw Error("Ran out of data");let s=t[a];switch(a++,d){default:throw Error("unrecognised depth");case 16:i=t[a],a++,r.push((s<<8)+i);break;case 4:i=15&s,o=s>>4,r.push(o,i);break;case 2:e=3&s,n=s>>2&3,i=s>>4&3,o=s>>6&3,r.push(o,i,n,e);break;case 1:e=s>>4&1,n=s>>5&1,i=s>>6&1,o=s>>7&1,r.push(o,i,n,e,s>>3&1,s>>2&1,s>>1&1,1&s)}}();let n=r.slice(0,e);return r=r.slice(e),n},resetAfterLine:function(){r.length=0},end:function(){if(a!==t.length)throw Error("extra data found")}}),h=d<=8?Buffer.alloc(c*f*4):new Uint16Array(c*f*4);let m=Math.pow(2,d)-1,b=0;if(g)u=n.getImagePasses(c,f),l=n.getInterlaceIterator(c,f);else{let t=0;l=function(){let e=t;return t+=4,e},u=[{width:c,height:f}]}for(let e=0;e<u.length;e++)8===d?b=function(t,e,r,n,o,a){let s=t.width,h=t.height,u=t.index;for(let t=0;t<h;t++)for(let h=0;h<s;h++){let s=r(h,t,u);i[n](e,o,s,a),a+=n}return a}(u[e],h,l,p,t,b):function(t,e,r,n,i,a){let s=t.width,h=t.height,u=t.index;for(let t=0;t<h;t++){for(let h=0;h<s;h++){let s=i.get(n),l=r(h,t,u);o[n](e,s,l,a)}i.resetAfterLine()}}(u[e],h,l,p,s,m);if(8===d){if(b!==t.length)throw Error("extra data found")}else s.end();return h}},85177:(t,e,r)=>{"use strict";let n=r(28354),i=r(27910),o=t.exports=function(){i.call(this),this._buffers=[],this._buffered=0,this._reads=[],this._paused=!1,this._encoding="utf8",this.writable=!0};n.inherits(o,i),o.prototype.read=function(t,e){this._reads.push({length:Math.abs(t),allowLess:t<0,func:e}),process.nextTick((function(){this._process(),this._paused&&this._reads&&this._reads.length>0&&(this._paused=!1,this.emit("drain"))}).bind(this))},o.prototype.write=function(t,e){let r;return this.writable?(r=Buffer.isBuffer(t)?t:Buffer.from(t,e||this._encoding),this._buffers.push(r),this._buffered+=r.length,this._process(),this._reads&&0===this._reads.length&&(this._paused=!0),this.writable&&!this._paused):(this.emit("error",Error("Stream not writable")),!1)},o.prototype.end=function(t,e){t&&this.write(t,e),this.writable=!1,this._buffers&&(0===this._buffers.length?this._end():(this._buffers.push(null),this._process()))},o.prototype.destroySoon=o.prototype.end,o.prototype._end=function(){this._reads.length>0&&this.emit("error",Error("Unexpected end of input")),this.destroy()},o.prototype.destroy=function(){this._buffers&&(this.writable=!1,this._reads=null,this._buffers=null,this.emit("close"))},o.prototype._processReadAllowingLess=function(t){this._reads.shift();let e=this._buffers[0];e.length>t.length?(this._buffered-=t.length,this._buffers[0]=e.slice(t.length),t.func.call(this,e.slice(0,t.length))):(this._buffered-=e.length,this._buffers.shift(),t.func.call(this,e))},o.prototype._processRead=function(t){this._reads.shift();let e=0,r=0,n=Buffer.alloc(t.length);for(;e<t.length;){let i=this._buffers[r++],o=Math.min(i.length,t.length-e);i.copy(n,e,0,o),e+=o,o!==i.length&&(this._buffers[--r]=i.slice(o))}r>0&&this._buffers.splice(0,r),this._buffered-=t.length,t.func.call(this,n)},o.prototype._process=function(){try{for(;this._buffered>0&&this._reads&&this._reads.length>0;){let t=this._reads[0];if(t.allowLess)this._processReadAllowingLess(t);else if(this._buffered>=t.length)this._processRead(t);else break}this._buffers&&!this.writable&&this._end()}catch(t){this.emit("error",t)}}},85585:(t,e,r)=>{let n=r(38125),i=r(66631);e.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},e.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},e.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},e.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},e.MIXED={bit:-1},e.getCharCountIndicator=function(t,e){if(!t.ccBits)throw Error("Invalid mode: "+t);if(!n.isValid(e))throw Error("Invalid version: "+e);return e>=1&&e<10?t.ccBits[0]:e<27?t.ccBits[1]:t.ccBits[2]},e.getBestModeForData=function(t){return i.testNumeric(t)?e.NUMERIC:i.testAlphanumeric(t)?e.ALPHANUMERIC:i.testKanji(t)?e.KANJI:e.BYTE},e.toString=function(t){if(t&&t.id)return t.id;throw Error("Invalid mode")},e.isValid=function(t){return t&&t.bit&&t.ccBits},e.from=function(t,r){if(e.isValid(t))return t;try{if("string"!=typeof t)throw Error("Param is not a string");switch(t.toLowerCase()){case"numeric":return e.NUMERIC;case"alphanumeric":return e.ALPHANUMERIC;case"kanji":return e.KANJI;case"byte":return e.BYTE;default:throw Error("Unknown mode: "+t)}}catch(t){return r}}},88120:(t,e,r)=>{"use strict";let n=r(12831),i={0:function(t,e,r,n,i){for(let o=0;o<r;o++)n[i+o]=t[e+o]},1:function(t,e,r,n,i,o){for(let a=0;a<r;a++){let r=a>=o?t[e+a-o]:0,s=t[e+a]-r;n[i+a]=s}},2:function(t,e,r,n,i){for(let o=0;o<r;o++){let a=e>0?t[e+o-r]:0,s=t[e+o]-a;n[i+o]=s}},3:function(t,e,r,n,i,o){for(let a=0;a<r;a++){let s=a>=o?t[e+a-o]:0,h=e>0?t[e+a-r]:0,u=t[e+a]-(s+h>>1);n[i+a]=u}},4:function(t,e,r,i,o,a){for(let s=0;s<r;s++){let h=s>=a?t[e+s-a]:0,u=e>0?t[e+s-r]:0,l=e>0&&s>=a?t[e+s-(r+a)]:0,c=t[e+s]-n(h,u,l);i[o+s]=c}}},o={0:function(t,e,r){let n=0,i=e+r;for(let r=e;r<i;r++)n+=Math.abs(t[r]);return n},1:function(t,e,r,n){let i=0;for(let o=0;o<r;o++){let r=o>=n?t[e+o-n]:0;i+=Math.abs(t[e+o]-r)}return i},2:function(t,e,r){let n=0,i=e+r;for(let o=e;o<i;o++){let i=e>0?t[o-r]:0;n+=Math.abs(t[o]-i)}return n},3:function(t,e,r,n){let i=0;for(let o=0;o<r;o++){let a=o>=n?t[e+o-n]:0,s=e>0?t[e+o-r]:0;i+=Math.abs(t[e+o]-(a+s>>1))}return i},4:function(t,e,r,i){let o=0;for(let a=0;a<r;a++){let s=a>=i?t[e+a-i]:0,h=e>0?t[e+a-r]:0,u=e>0&&a>=i?t[e+a-(r+i)]:0;o+=Math.abs(t[e+a]-n(s,h,u))}return o}};t.exports=function(t,e,r,n,a){let s;if("filterType"in n&&-1!==n.filterType)if("number"==typeof n.filterType)s=[n.filterType];else throw Error("unrecognised filter types");else s=[0,1,2,3,4];16===n.bitDepth&&(a*=2);let h=e*a,u=0,l=0,c=Buffer.alloc((h+1)*r),f=s[0];for(let e=0;e<r;e++){if(s.length>1){let e=1/0;for(let r=0;r<s.length;r++){let n=o[s[r]](t,l,h,a);n<e&&(f=s[r],e=n)}}c[u]=f,u++,i[f](t,l,h,c,u,a),u+=h,l+=h}return c}},91257:(t,e,r)=>{let n=r(62564),i=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],o=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];e.getBlocksCount=function(t,e){switch(e){case n.L:return i[(t-1)*4+0];case n.M:return i[(t-1)*4+1];case n.Q:return i[(t-1)*4+2];case n.H:return i[(t-1)*4+3];default:return}},e.getTotalCodewordsCount=function(t,e){switch(e){case n.L:return o[(t-1)*4+0];case n.M:return o[(t-1)*4+1];case n.Q:return o[(t-1)*4+2];case n.H:return o[(t-1)*4+3];default:return}}},92081:(t,e)=>{function r(t){if("number"==typeof t&&(t=t.toString()),"string"!=typeof t)throw Error("Color should be defined as hex string");let e=t.slice().replace("#","").split("");if(e.length<3||5===e.length||e.length>8)throw Error("Invalid hex color: "+t);(3===e.length||4===e.length)&&(e=Array.prototype.concat.apply([],e.map(function(t){return[t,t]}))),6===e.length&&e.push("F","F");let r=parseInt(e.join(""),16);return{r:r>>24&255,g:r>>16&255,b:r>>8&255,a:255&r,hex:"#"+e.slice(0,6).join("")}}e.getOptions=function(t){t||(t={}),t.color||(t.color={});let e=void 0===t.margin||null===t.margin||t.margin<0?4:t.margin,n=t.width&&t.width>=21?t.width:void 0,i=t.scale||4;return{width:n,scale:n?4:i,margin:e,color:{dark:r(t.color.dark||"#000000ff"),light:r(t.color.light||"#ffffffff")},type:t.type,rendererOpts:t.rendererOpts||{}}},e.getScale=function(t,e){return e.width&&e.width>=t+2*e.margin?e.width/(t+2*e.margin):e.scale},e.getImageWidth=function(t,r){let n=e.getScale(t,r);return Math.floor((t+2*r.margin)*n)},e.qrToImageData=function(t,r,n){let i=r.modules.size,o=r.modules.data,a=e.getScale(i,n),s=Math.floor((i+2*n.margin)*a),h=n.margin*a,u=[n.color.light,n.color.dark];for(let e=0;e<s;e++)for(let r=0;r<s;r++){let l=(e*s+r)*4,c=n.color.light;e>=h&&r>=h&&e<s-h&&r<s-h&&(c=u[+!!o[Math.floor((e-h)/a)*i+Math.floor((r-h)/a)]]),t[l++]=c.r,t[l++]=c.g,t[l++]=c.b,t[l]=c.a}}},93032:(t,e,r)=>{let n=r(53117),i=r(62564),o=r(35958),a=r(46073),s=r(73854),h=r(11753),u=r(38205),l=r(91257),c=r(77949),f=r(61320),d=r(41382),p=r(85585),g=r(99024);function m(t,e,r){let n,i,o=t.size,a=d.getEncodedBits(e,r);for(n=0;n<15;n++)i=(a>>n&1)==1,n<6?t.set(n,8,i,!0):n<8?t.set(n+1,8,i,!0):t.set(o-15+n,8,i,!0),n<8?t.set(8,o-n-1,i,!0):n<9?t.set(8,15-n-1+1,i,!0):t.set(8,15-n-1,i,!0);t.set(o-8,8,1,!0)}e.create=function(t,e){let r,d;if(void 0===t||""===t)throw Error("No input text");let b=i.M;return void 0!==e&&(b=i.from(e.errorCorrectionLevel,i.M),r=f.from(e.version),d=u.from(e.maskPattern),e.toSJISFunc&&n.setToSJISFunction(e.toSJISFunc)),function(t,e,r,i){let d;if(Array.isArray(t))d=g.fromArray(t);else if("string"==typeof t){let n=e;if(!n){let e=g.rawSplit(t);n=f.getBestVersionForData(e,r)}d=g.fromString(t,n||40)}else throw Error("Invalid data");let b=f.getBestVersionForData(d,r);if(!b)throw Error("The amount of data is too big to be stored in a QR Code");if(e){if(e<b)throw Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+b+".\n")}else e=b;let v=function(t,e,r){let i=new o;r.forEach(function(e){i.put(e.mode.bit,4),i.put(e.getLength(),p.getCharCountIndicator(e.mode,t)),e.write(i)});let a=(n.getSymbolTotalCodewords(t)-l.getTotalCodewordsCount(t,e))*8;for(i.getLengthInBits()+4<=a&&i.put(0,4);i.getLengthInBits()%8!=0;)i.putBit(0);let s=(a-i.getLengthInBits())/8;for(let t=0;t<s;t++)i.put(t%2?17:236,8);return function(t,e,r){let i,o,a=n.getSymbolTotalCodewords(e),s=a-l.getTotalCodewordsCount(e,r),h=l.getBlocksCount(e,r),u=a%h,f=h-u,d=Math.floor(a/h),p=Math.floor(s/h),g=p+1,m=d-p,b=new c(m),v=0,y=Array(h),w=Array(h),x=0,_=new Uint8Array(t.buffer);for(let t=0;t<h;t++){let e=t<f?p:g;y[t]=_.slice(v,v+e),w[t]=b.encode(y[t]),v+=e,x=Math.max(x,e)}let A=new Uint8Array(a),L=0;for(i=0;i<x;i++)for(o=0;o<h;o++)i<y[o].length&&(A[L++]=y[o][i]);for(i=0;i<m;i++)for(o=0;o<h;o++)A[L++]=w[o][i];return A}(i,t,e)}(e,r,d),y=new a(n.getSymbolSize(e));!function(t,e){let r=t.size,n=h.getPositions(e);for(let e=0;e<n.length;e++){let i=n[e][0],o=n[e][1];for(let e=-1;e<=7;e++)if(!(i+e<=-1)&&!(r<=i+e))for(let n=-1;n<=7;n++)o+n<=-1||r<=o+n||(e>=0&&e<=6&&(0===n||6===n)||n>=0&&n<=6&&(0===e||6===e)||e>=2&&e<=4&&n>=2&&n<=4?t.set(i+e,o+n,!0,!0):t.set(i+e,o+n,!1,!0))}}(y,e);let w=y.size;for(let t=8;t<w-8;t++){let e=t%2==0;y.set(t,6,e,!0),y.set(6,t,e,!0)}return!function(t,e){let r=s.getPositions(e);for(let e=0;e<r.length;e++){let n=r[e][0],i=r[e][1];for(let e=-2;e<=2;e++)for(let r=-2;r<=2;r++)-2===e||2===e||-2===r||2===r||0===e&&0===r?t.set(n+e,i+r,!0,!0):t.set(n+e,i+r,!1,!0)}}(y,e),m(y,r,0),e>=7&&function(t,e){let r,n,i,o=t.size,a=f.getEncodedBits(e);for(let e=0;e<18;e++)r=Math.floor(e/3),n=e%3+o-8-3,i=(a>>e&1)==1,t.set(r,n,i,!0),t.set(n,r,i,!0)}(y,e),!function(t,e){let r=t.size,n=-1,i=r-1,o=7,a=0;for(let s=r-1;s>0;s-=2)for(6===s&&s--;;){for(let r=0;r<2;r++)if(!t.isReserved(i,s-r)){let n=!1;a<e.length&&(n=(e[a]>>>o&1)==1),t.set(i,s-r,n),-1==--o&&(a++,o=7)}if((i+=n)<0||r<=i){i-=n,n=-n;break}}}(y,v),isNaN(i)&&(i=u.getBestMask(y,m.bind(null,y,r))),u.applyMask(i,y),m(y,r,i),{modules:y,version:e,errorCorrectionLevel:r,maskPattern:i,segments:d}}(t,r,b,d)}},96559:(t,e,r)=>{"use strict";t.exports=r(44870)},97359:(t,e,r)=>{let n=r(92081);function i(t,e){let r=t.a/255,n=e+'="'+t.hex+'"';return r<1?n+" "+e+'-opacity="'+r.toFixed(2).slice(1)+'"':n}function o(t,e,r){let n=t+e;return void 0!==r&&(n+=" "+r),n}e.render=function(t,e,r){let a=n.getOptions(e),s=t.modules.size,h=t.modules.data,u=s+2*a.margin,l=a.color.light.a?"<path "+i(a.color.light,"fill")+' d="M0 0h'+u+"v"+u+'H0z"/>':"",c="<path "+i(a.color.dark,"stroke")+' d="'+function(t,e,r){let n="",i=0,a=!1,s=0;for(let h=0;h<t.length;h++){let u=Math.floor(h%e),l=Math.floor(h/e);u||a||(a=!0),t[h]?(s++,h>0&&u>0&&t[h-1]||(n+=a?o("M",u+r,.5+l+r):o("m",i,0),i=0,a=!1),u+1<e&&t[h+1]||(n+=o("h",s),s=0)):i++}return n}(h,s,a.margin)+'"/>',f='<svg xmlns="http://www.w3.org/2000/svg" '+(a.width?'width="'+a.width+'" height="'+a.width+'" ':"")+('viewBox="0 0 '+u+" ")+u+'" shape-rendering="crispEdges">'+l+c+"</svg>\n";return"function"==typeof r&&r(null,f),f}},97651:(t,e,r)=>{"use strict";let n=r(25),i=r(45686);e.read=function(t,e){return n(t,e||{})},e.write=function(t,e){return i(t,e)}},98985:(t,e)=>{"use strict";let r=[{x:[0],y:[0]},{x:[4],y:[0]},{x:[0,4],y:[4]},{x:[2,6],y:[0,4]},{x:[0,2,4,6],y:[2,6]},{x:[1,3,5,7],y:[0,2,4,6]},{x:[0,1,2,3,4,5,6,7],y:[1,3,5,7]}];e.getImagePasses=function(t,e){let n=[],i=t%8,o=e%8,a=(t-i)/8,s=(e-o)/8;for(let t=0;t<r.length;t++){let e=r[t],h=a*e.x.length,u=s*e.y.length;for(let t=0;t<e.x.length;t++)if(e.x[t]<i)h++;else break;for(let t=0;t<e.y.length;t++)if(e.y[t]<o)u++;else break;h>0&&u>0&&n.push({width:h,height:u,index:t})}return n},e.getInterlaceIterator=function(t){return function(e,n,i){let o=e%r[i].x.length,a=(e-o)/r[i].x.length*8+r[i].x[o],s=n%r[i].y.length;return 4*a+((n-s)/r[i].y.length*8+r[i].y[s])*t*4}}},99024:(t,e,r)=>{let n=r(85585),i=r(23320),o=r(51514),a=r(37781),s=r(27108),h=r(66631),u=r(53117),l=r(59995);function c(t){return unescape(encodeURIComponent(t)).length}function f(t,e,r){let n,i=[];for(;null!==(n=t.exec(r));)i.push({data:n[0],index:n.index,mode:e,length:n[0].length});return i}function d(t){let e,r,i=f(h.NUMERIC,n.NUMERIC,t),o=f(h.ALPHANUMERIC,n.ALPHANUMERIC,t);return u.isKanjiModeEnabled()?(e=f(h.BYTE,n.BYTE,t),r=f(h.KANJI,n.KANJI,t)):(e=f(h.BYTE_KANJI,n.BYTE,t),r=[]),i.concat(o,e,r).sort(function(t,e){return t.index-e.index}).map(function(t){return{data:t.data,mode:t.mode,length:t.length}})}function p(t,e){switch(e){case n.NUMERIC:return i.getBitsLength(t);case n.ALPHANUMERIC:return o.getBitsLength(t);case n.KANJI:return s.getBitsLength(t);case n.BYTE:return a.getBitsLength(t)}}function g(t,e){let r,h=n.getBestModeForData(t);if((r=n.from(e,h))!==n.BYTE&&r.bit<h.bit)throw Error('"'+t+'" cannot be encoded with mode '+n.toString(r)+".\n Suggested mode is: "+n.toString(h));switch(r===n.KANJI&&!u.isKanjiModeEnabled()&&(r=n.BYTE),r){case n.NUMERIC:return new i(t);case n.ALPHANUMERIC:return new o(t);case n.KANJI:return new s(t);case n.BYTE:return new a(t)}}e.fromArray=function(t){return t.reduce(function(t,e){return"string"==typeof e?t.push(g(e,null)):e.data&&t.push(g(e.data,e.mode)),t},[])},e.fromString=function(t,r){let i=function(t,e){let r={},i={start:{}},o=["start"];for(let a=0;a<t.length;a++){let s=t[a],h=[];for(let t=0;t<s.length;t++){let u=s[t],l=""+a+t;h.push(l),r[l]={node:u,lastCount:0},i[l]={};for(let t=0;t<o.length;t++){let a=o[t];r[a]&&r[a].node.mode===u.mode?(i[a][l]=p(r[a].lastCount+u.length,u.mode)-p(r[a].lastCount,u.mode),r[a].lastCount+=u.length):(r[a]&&(r[a].lastCount=u.length),i[a][l]=p(u.length,u.mode)+4+n.getCharCountIndicator(u.mode,e))}}o=h}for(let t=0;t<o.length;t++)i[o[t]].end=0;return{map:i,table:r}}(function(t){let e=[];for(let r=0;r<t.length;r++){let i=t[r];switch(i.mode){case n.NUMERIC:e.push([i,{data:i.data,mode:n.ALPHANUMERIC,length:i.length},{data:i.data,mode:n.BYTE,length:i.length}]);break;case n.ALPHANUMERIC:e.push([i,{data:i.data,mode:n.BYTE,length:i.length}]);break;case n.KANJI:e.push([i,{data:i.data,mode:n.BYTE,length:c(i.data)}]);break;case n.BYTE:e.push([{data:i.data,mode:n.BYTE,length:c(i.data)}])}}return e}(d(t,u.isKanjiModeEnabled())),r),o=l.find_path(i.map,"start","end"),a=[];for(let t=1;t<o.length-1;t++)a.push(i.table[o[t]].node);return e.fromArray(a.reduce(function(t,e){let r=t.length-1>=0?t[t.length-1]:null;return r&&r.mode===e.mode?t[t.length-1].data+=e.data:t.push(e),t},[]))},e.rawSplit=function(t){return e.fromArray(d(t,u.isKanjiModeEnabled()))}}};