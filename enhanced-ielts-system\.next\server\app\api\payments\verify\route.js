"use strict";(()=>{var e={};e.id=737,e.ids=[737],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65473:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>q,routeModule:()=>g,serverHooks:()=>v,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>j});var a={};r.r(a),r.d(a,{GET:()=>f,POST:()=>x});var s=r(96559),n=r(48088),i=r(37719),o=r(32190),d=r(26326),c=r(71682),p=r(32767),u=r(94634),l=r(45697),y=r(18758);let m=l.z.object({transactionId:l.z.string().min(1)});async function x(e){try{let t=await (0,d.j2)();if(!t?.user?.organizationId)return o.NextResponse.json({error:"Unauthorized"},{status:401});let r=await e.json(),{transactionId:a}=m.parse(r),s=await c.db.select({transaction:p.paymentTransactions,candidate:p.candidates}).from(p.paymentTransactions).leftJoin(p.candidates,(0,u.eq)(p.paymentTransactions.candidateId,p.candidates.id)).where((0,u.Uo)((0,u.eq)(p.paymentTransactions.id,a),(0,u.eq)(p.candidates.organizationId,t.user.organizationId))).limit(1);if(0===s.length)return o.NextResponse.json({error:"Transaction not found"},{status:404});let n=s[0],i=await (0,y.nz)(n.transaction.candidateId,n.transaction.featureType,n.transaction.resultId||void 0);return o.NextResponse.json({transactionId:n.transaction.id,status:n.transaction.status,amount:parseFloat(n.transaction.amount),currency:n.transaction.currency,gateway:n.transaction.gateway,featureType:n.transaction.featureType,resultId:n.transaction.resultId,gatewayTransactionId:n.transaction.gatewayTransactionId,createdAt:n.transaction.createdAt,completedAt:n.transaction.completedAt,hasAccess:i.hasAccess,accessType:i.accessType,expiresAt:i.expiresAt,daysRemaining:i.daysRemaining,candidate:{id:n.candidate?.id,fullName:n.candidate?.fullName,email:n.candidate?.email}})}catch(e){if(console.error("Payment verification error:",e),e instanceof l.z.ZodError)return o.NextResponse.json({error:"Invalid data",details:e.errors},{status:400});return o.NextResponse.json({error:"Payment verification failed"},{status:500})}}async function f(e){try{let t=await (0,d.j2)();if(!t?.user?.organizationId)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),a=r.get("candidateId"),s=r.get("featureType"),n=r.get("resultId");if(!a||!s)return o.NextResponse.json({error:"Missing required parameters"},{status:400});let i=await c.db.select().from(p.candidates).where((0,u.Uo)((0,u.eq)(p.candidates.id,a),(0,u.eq)(p.candidates.organizationId,t.user.organizationId))).limit(1);if(0===i.length)return o.NextResponse.json({error:"Candidate not found"},{status:404});let l=await (0,y.nz)(a,s,n||void 0);return o.NextResponse.json({candidateId:a,featureType:s,resultId:n,hasAccess:l.hasAccess,accessType:l.accessType,expiresAt:l.expiresAt,daysRemaining:l.daysRemaining})}catch(e){return console.error("Access check error:",e),o.NextResponse.json({error:"Access check failed"},{status:500})}}let g=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/payments/verify/route",pathname:"/api/payments/verify",filename:"route",bundlePath:"app/api/payments/verify/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\payments\\verify\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:h,workUnitAsyncStorage:j,serverHooks:v}=g;function q(){return(0,i.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:j})}},74998:e=>{e.exports=require("perf_hooks")},77598:e=>{e.exports=require("node:crypto")},91645:e=>{e.exports=require("net")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,5552,2190,1057,1595,6326,7346],()=>r(65473));module.exports=a})();