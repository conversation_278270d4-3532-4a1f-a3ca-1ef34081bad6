(()=>{var e={};e.id=2812,e.ids=[2812],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73620:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>z,routeModule:()=>I,serverHooks:()=>h,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>b});var a={};r.r(a),r.d(a,{GET:()=>x,POST:()=>g});var n=r(96559),s=r(48088),i=r(37719),o=r(32190),u=r(26326),d=r(71682),c=r(32767),p=r(94634),m=r(45697),l=r(52175),y=r(97450);let f=m.z.object({candidateId:m.z.string().min(1),featureType:m.z.enum(["feedback","certificate","progress"]),resultId:m.z.string().optional(),amount:m.z.number().positive(),currency:m.z.string().min(1),paymentMethod:m.z.enum(["bank_transfer","cash"]),referenceNumber:m.z.string().min(1),paymentDate:m.z.string().min(1),notes:m.z.string().optional()});async function g(e){try{let t=await (0,u.j2)();if(!t?.user?.organizationId)return o.NextResponse.json({error:"Unauthorized"},{status:401});let r=await e.json(),a=f.parse(r),n=await d.db.select().from(c.candidates).where((0,p.Uo)((0,p.eq)(c.candidates.id,a.candidateId),(0,p.eq)(c.candidates.organizationId,t.user.organizationId))).limit(1);if(0===n.length)return o.NextResponse.json({error:"Candidate not found or does not belong to your organization"},{status:404});let s=y.kp[a.featureType];if(a.amount!==s.amount||a.currency!==s.currency)return o.NextResponse.json({error:"Invalid amount or currency for this feature"},{status:400});let i=(0,l.sX)(),[m]=await d.db.insert(c.paymentTransactions).values({id:i,candidateId:a.candidateId,organizationId:t.user.organizationId,amount:a.amount.toString(),currency:a.currency,gateway:"manual",status:"pending",featureType:a.featureType,resultId:a.resultId,metadata:{paymentMethod:a.paymentMethod,referenceNumber:a.referenceNumber,paymentDate:a.paymentDate,notes:a.notes,submittedBy:t.user.id,submittedAt:new Date().toISOString()}}).returning();return o.NextResponse.json({transactionId:m.id,status:"pending",message:"Manual payment submitted successfully. It will be reviewed by an admin.",estimatedProcessingTime:"1-3 business days"},{status:201})}catch(e){if(console.error("Manual payment submission error:",e),e instanceof m.z.ZodError)return o.NextResponse.json({error:"Invalid data",details:e.errors},{status:400});return o.NextResponse.json({error:"Failed to submit manual payment"},{status:500})}}async function x(e){try{let t=await (0,u.j2)();if(!t?.user?.organizationId)return o.NextResponse.json({error:"Unauthorized"},{status:401});if("admin"!==t.user.role&&!t.user.masterAdmin)return o.NextResponse.json({error:"Insufficient permissions"},{status:403});let{searchParams:r}=new URL(e.url),a=r.get("status"),n=d.db.select({transaction:c.paymentTransactions,candidate:c.candidates}).from(c.paymentTransactions).leftJoin(c.candidates,(0,p.eq)(c.paymentTransactions.candidateId,c.candidates.id)).where((0,p.Uo)((0,p.eq)(c.paymentTransactions.gateway,"manual"),(0,p.eq)(c.candidates.organizationId,t.user.organizationId)));a&&(n=n.where((0,p.Uo)((0,p.eq)(c.paymentTransactions.gateway,"manual"),(0,p.eq)(c.candidates.organizationId,t.user.organizationId),(0,p.eq)(c.paymentTransactions.status,a))));let s=await n;return o.NextResponse.json(s.map(e=>({id:e.transaction.id,candidate:{id:e.candidate?.id,fullName:e.candidate?.fullName,email:e.candidate?.email},amount:parseFloat(e.transaction.amount),currency:e.transaction.currency,featureType:e.transaction.featureType,resultId:e.transaction.resultId,status:e.transaction.status,metadata:e.transaction.metadata,createdAt:e.transaction.createdAt,completedAt:e.transaction.completedAt})))}catch(e){return console.error("Manual payments fetch error:",e),o.NextResponse.json({error:"Failed to fetch manual payments"},{status:500})}}let I=new n.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/payments/manual/route",pathname:"/api/payments/manual",filename:"route",bundlePath:"app/api/payments/manual/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\payments\\manual\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:w,workUnitAsyncStorage:b,serverHooks:h}=I;function z(){return(0,i.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:b})}},74998:e=>{"use strict";e.exports=require("perf_hooks")},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},91645:e=>{"use strict";e.exports=require("net")},96487:()=>{},97450:(e,t,r)=>{"use strict";r.d(t,{kp:()=>a,lO:()=>n,s_:()=>s});let a={feedback:{amount:5e4,currency:"UZS",description:"AI-powered personalized feedback and study recommendations"},certificate:{amount:3e4,currency:"UZS",description:"Official IELTS certificate with verification"},progress:{amount:25e3,currency:"UZS",description:"Detailed progress tracking and analytics"}},n={minAmount:1e3,maxAmount:1e7,allowedCurrencies:["UZS","USD"],transactionTimeout:18e5,maxRetries:3},s={defaultExpiry:{feedback:null,certificate:15552e6,progress:null},gracePeriod:6048e5,maxConcurrentAccess:{feedback:1,certificate:1,progress:1}}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,5552,2190,1057,1595,6326],()=>r(73620));module.exports=a})();