"use strict";exports.id=892,exports.ids=[892],exports.modules={16189:(e,t,s)=>{var r=s(65773);s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},99208:(e,t,s)=>{s.d(t,{Jv:()=>q,CI:()=>G}),s(60687);var r=s(43210);class n extends Error{constructor(e,t){e instanceof Error?super(void 0,{cause:{err:e,...e.cause,...t}}):"string"==typeof e?(t instanceof Error&&(t={err:t,...t.cause}),super(e,t)):super(void 0,e),this.name=this.constructor.name,this.type=this.constructor.type??"AuthError",this.kind=this.constructor.kind??"error",Error.captureStackTrace?.(this,this.constructor);let s=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${s}`}}class a extends n{}a.kind="signIn";class o extends n{}o.type="AdapterError";class i extends n{}i.type="AccessDenied";class c extends n{}c.type="CallbackRouteError";class l extends n{}l.type="ErrorPageLoop";class d extends n{}d.type="EventError";class u extends n{}u.type="InvalidCallbackUrl";class p extends a{constructor(){super(...arguments),this.code="credentials"}}p.type="CredentialsSignin";class h extends n{}h.type="InvalidEndpoints";class y extends n{}y.type="InvalidCheck";class w extends n{}w.type="JWTSessionError";class x extends n{}x.type="MissingAdapter";class g extends n{}g.type="MissingAdapterMethods";class f extends n{}f.type="MissingAuthorize";class v extends n{}v.type="MissingSecret";class E extends a{}E.type="OAuthAccountNotLinked";class U extends a{}U.type="OAuthCallbackError";class m extends n{}m.type="OAuthProfileParseError";class R extends n{}R.type="SessionTokenError";class S extends a{}S.type="OAuthSignInError";class b extends a{}b.type="EmailSignInError";class A extends n{}A.type="SignOutError";class L extends n{}L.type="UnknownAction";class k extends n{}k.type="UnsupportedStrategy";class T extends n{}T.type="InvalidProvider";class P extends n{}P.type="UntrustedHost";class $ extends n{}$.type="Verification";class C extends a{}C.type="MissingCSRF";class _ extends n{}_.type="DuplicateConditionalUI";class N extends n{}N.type="MissingWebAuthnAutocomplete";class I extends n{}I.type="WebAuthnVerificationError";class M extends a{}M.type="AccountNotLinked";class X extends n{}X.type="ExperimentalFeatureNotEnabled";class H extends n{}async function O(e,t,s,r={}){let n=`${j(t)}/${e}`;try{let e={headers:{"Content-Type":"application/json",...r?.headers?.cookie?{cookie:r.headers.cookie}:{}}};r?.body&&(e.body=JSON.stringify(r.body),e.method="POST");let t=await fetch(n,e),s=await t.json();if(!t.ok)throw s;return s}catch(e){return s.error(new H(e.message,e)),null}}function j(e){return`${e.baseUrlServer}${e.basePathServer}`}function W(e){let t=new URL("http://localhost:3000/api/auth");e&&!e.startsWith("http")&&(e=`https://${e}`);let s=new URL(e||t),r=("/"===s.pathname?t.pathname:s.pathname).replace(/\/$/,""),n=`${s.origin}${r}`;return{origin:s.origin,host:s.host,path:r,base:n,toString:()=>n}}let V={baseUrl:W(process.env.NEXTAUTH_URL??process.env.VERCEL_URL).origin,basePath:W(process.env.NEXTAUTH_URL).path,baseUrlServer:W(process.env.NEXTAUTH_URL_INTERNAL??process.env.NEXTAUTH_URL??process.env.VERCEL_URL).origin,basePathServer:W(process.env.NEXTAUTH_URL_INTERNAL??process.env.NEXTAUTH_URL).path,_lastSync:0,_session:void 0,_getSession:()=>{}},J=null;function B(){return new BroadcastChannel("next-auth")}let D={debug:console.debug,error:console.error,warn:console.warn};r.createContext?.(void 0);async function F(){let e=await O("csrf",V,D);return e?.csrfToken??""}async function z(){return O("providers",V,D)}async function q(e,t,s){let{callbackUrl:r,...n}=t??{},{redirect:a=!0,redirectTo:o=r??window.location.href,...i}=n,c=j(V),l=await z();if(!l){let e=`${c}/error`;window.location.href=e;return}if(!e||!l[e]){let e=`${c}/signin?${new URLSearchParams({callbackUrl:o})}`;window.location.href=e;return}let d=l[e].type;if("webauthn"===d)throw TypeError(`Provider id "${e}" refers to a WebAuthn provider.
Please use \`import { signIn } from "next-auth/webauthn"\` instead.`);let u=`${c}/${"credentials"===d?"callback":"signin"}/${e}`,p=await F(),h=await fetch(`${u}?${new URLSearchParams(s)}`,{method:"post",headers:{"Content-Type":"application/x-www-form-urlencoded","X-Auth-Return-Redirect":"1"},body:new URLSearchParams({...i,csrfToken:p,callbackUrl:o})}),y=await h.json();if(a){let e=y.url??o;window.location.href=e,e.includes("#")&&window.location.reload();return}let w=new URL(y.url).searchParams.get("error")??void 0,x=new URL(y.url).searchParams.get("code")??void 0;return h.ok&&await V._getSession({event:"storage"}),{error:w,code:x,status:h.status,ok:h.ok,url:w?null:y.url}}async function G(e){let{redirect:t=!0,redirectTo:s=e?.callbackUrl??window.location.href}=e??{},r=j(V),n=await F(),a=await fetch(`${r}/signout`,{method:"post",headers:{"Content-Type":"application/x-www-form-urlencoded","X-Auth-Return-Redirect":"1"},body:new URLSearchParams({csrfToken:n,callbackUrl:s})}),o=await a.json();if(("undefined"==typeof BroadcastChannel?{postMessage:()=>{},addEventListener:()=>{},removeEventListener:()=>{}}:(null===J&&(J=B()),J)).postMessage({event:"session",data:{trigger:"signout"}}),t){let e=o.url??s;window.location.href=e,e.includes("#")&&window.location.reload();return}return await V._getSession({event:"storage"}),o}}};