(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[72],{285:(e,r,t)=>{"use strict";t.d(r,{$:()=>l});var s=t(5155),a=t(2115),n=t(6486);let l=a.forwardRef((e,r)=>{let{className:t,variant:a="default",size:l="default",...i}=e;return(0,s.jsx)("button",{className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{"bg-blue-600 text-white hover:bg-blue-700":"default"===a,"bg-red-600 text-white hover:bg-red-700":"destructive"===a,"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50":"outline"===a,"bg-gray-100 text-gray-900 hover:bg-gray-200":"secondary"===a,"hover:bg-gray-100 hover:text-gray-900":"ghost"===a,"text-blue-600 underline-offset-4 hover:underline":"link"===a},{"h-10 px-4 py-2":"default"===l,"h-9 rounded-md px-3":"sm"===l,"h-11 rounded-md px-8":"lg"===l,"h-10 w-10":"icon"===l},t),ref:r,...i})});l.displayName="Button"},2523:(e,r,t)=>{"use strict";t.d(r,{p:()=>l});var s=t(5155),a=t(2115),n=t(6486);let l=a.forwardRef((e,r)=>{let{className:t,type:a,error:l,...i}=e;return(0,s.jsxs)("div",{className:"w-full",children:[(0,s.jsx)("input",{type:a,className:(0,n.cn)("flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:cursor-not-allowed disabled:opacity-50",l&&"border-red-500 focus:ring-red-500 focus:border-red-500",t),ref:r,...i}),l&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-500",children:l})]})});l.displayName="Input"},6486:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var s=t(2596),a=t(9688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}},8459:(e,r,t)=>{Promise.resolve().then(t.bind(t,9146))},9146:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var s=t(5155),a=t(2115),n=t(5493),l=t(5695),i=t(285),d=t(2523);function o(){let[e,r]=(0,a.useState)(""),[t,o]=(0,a.useState)(""),[c,u]=(0,a.useState)(!1),[m,x]=(0,a.useState)(""),f=(0,l.useRouter)(),g=(0,l.useSearchParams)().get("callbackUrl")||"/admin",h=async r=>{r.preventDefault(),u(!0),x("");try{let r=await (0,n.Jv)("credentials",{email:e,password:t,redirect:!1});(null==r?void 0:r.error)?x("Invalid email or password"):(null==r?void 0:r.ok)&&(f.push(g),f.refresh())}catch(e){console.error("Login error:",e),x("An error occurred during login")}finally{u(!1)}};return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Enhanced IELTS System"}),(0,s.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Sign in to your account"})]}),(0,s.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:h,children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(d.p,{type:"email",placeholder:"Email address",value:e,onChange:e=>r(e.target.value),required:!0,autoComplete:"email"}),(0,s.jsx)(d.p,{type:"password",placeholder:"Password",value:t,onChange:e=>o(e.target.value),required:!0,autoComplete:"current-password"})]}),m&&(0,s.jsx)("div",{className:"text-red-600 text-sm text-center",children:m}),(0,s.jsx)(i.$,{type:"submit",disabled:c,className:"w-full",children:c?"Signing in...":"Sign In"})]})]})})}function c(){return(0,s.jsx)(a.Suspense,{fallback:(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}),children:(0,s.jsx)(o,{})})}}},e=>{var r=r=>e(e.s=r);e.O(0,[4277,7215,8441,1684,7358],()=>r(8459)),_N_E=e.O()}]);