"use strict";(()=>{var e={};e.id=3904,e.ids=[3904],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6450:(e,t,a)=>{a.r(t),a.d(t,{patchFetch:()=>g,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>_,workUnitAsyncStorage:()=>f});var r={};a.r(r),a.d(r,{GET:()=>c,POST:()=>u});var s=a(96559),n=a(48088),i=a(37719),d=a(32190),o=a(38955),l=a(18758);async function u(e){try{let t=await e.json();console.log("Payme webhook received:",t);let a=await (0,o.d5)(t);if(!a.success)return console.error("Payme webhook processing failed:",a.error),d.NextResponse.json({jsonrpc:"2.0",id:t.id||null,error:a.error});return"PerformTransaction"===t.method&&t.params?.id?await (0,l.ee)(t.params.account?.order_id||t.params.id,"completed",t.params.id,{gatewayResponse:t,performTime:a.result?.perform_time}):"CancelTransaction"===t.method&&t.params?.id&&await (0,l.ee)(t.params.account?.order_id||t.params.id,"cancelled",t.params.id,{gatewayResponse:t,cancelTime:a.result?.cancel_time,cancelReason:t.params.reason}),d.NextResponse.json({jsonrpc:"2.0",id:t.id||null,result:a.result})}catch(e){return console.error("Payme webhook error:",e),d.NextResponse.json({jsonrpc:"2.0",id:null,error:{code:-32700,message:"Parse error"}},{status:500})}}async function c(e){return d.NextResponse.json({status:"Payme webhook endpoint is active",timestamp:new Date().toISOString()})}let p=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/payments/webhook/payme/route",pathname:"/api/payments/webhook/payme",filename:"route",bundlePath:"app/api/payments/webhook/payme/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\payments\\webhook\\payme\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:_,workUnitAsyncStorage:f,serverHooks:m}=p;function g(){return(0,i.patchFetch)({workAsyncStorage:_,workUnitAsyncStorage:f})}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32767:(e,t,a)=>{a.r(t),a.d(t,{accessPermissions:()=>N,accessPermissionsRelations:()=>h,aiFeedback:()=>I,aiFeedbackRelations:()=>S,candidates:()=>g,candidatesRelations:()=>b,certificateLifecycle:()=>Q,certificateLifecycleRelations:()=>z,organizations:()=>f,organizationsRelations:()=>w,paymentTransactions:()=>q,paymentTransactionsRelations:()=>A,promotionalRules:()=>v,promotionalRulesRelations:()=>E,testRegistrations:()=>y,testRegistrationsRelations:()=>k,testResults:()=>x,testResultsRelations:()=>R,users:()=>m,usersRelations:()=>P});var r=a(92768),s=a(29334),n=a(34359),i=a(9253),d=a(89697),o=a(54693),l=a(63431),u=a(91036),c=a(72170),p=a(52175),_=a(3884);let f=(0,r.cJ)("organizations",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,p.sX)()),name:(0,s.Qq)("name").notNull(),slug:(0,s.Qq)("slug").unique().notNull(),settings:(0,n.Pq)("settings").$type().default({}),features:(0,n.Pq)("features").$type().default([]),billingPlan:(0,s.Qq)("billing_plan",{enum:["basic","premium","enterprise"]}).default("basic"),status:(0,s.Qq)("status",{enum:["active","suspended","disabled"]}).default("active"),createdAt:(0,i.vE)("created_at").defaultNow().notNull(),updatedAt:(0,i.vE)("updated_at").defaultNow().notNull()},e=>({slugIdx:(0,d.Pe)("org_slug_idx").on(e.slug),statusIdx:(0,d.Pe)("org_status_idx").on(e.status)})),m=(0,r.cJ)("users",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,p.sX)()),organizationId:(0,s.Qq)("organization_id").references(()=>f.id,{onDelete:"cascade"}),email:(0,s.Qq)("email").unique().notNull(),password:(0,s.Qq)("password").notNull(),name:(0,s.Qq)("name").notNull(),role:(0,s.Qq)("role",{enum:["admin","checker"]}).notNull(),masterAdmin:(0,o.zM)("master_admin").default(!1),status:(0,s.Qq)("status",{enum:["active","inactive","suspended"]}).default("active"),lastLoginAt:(0,i.vE)("last_login_at"),createdAt:(0,i.vE)("created_at").defaultNow().notNull(),updatedAt:(0,i.vE)("updated_at").defaultNow().notNull()},e=>({emailIdx:(0,d.Pe)("user_email_idx").on(e.email),orgIdx:(0,d.Pe)("user_org_idx").on(e.organizationId),roleIdx:(0,d.Pe)("user_role_idx").on(e.role)})),g=(0,r.cJ)("candidates",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,p.sX)()),organizationId:(0,s.Qq)("organization_id").references(()=>f.id,{onDelete:"cascade"}).notNull(),fullName:(0,s.Qq)("full_name").notNull(),email:(0,s.Qq)("email"),phoneNumber:(0,s.Qq)("phone_number"),dateOfBirth:(0,i.vE)("date_of_birth"),nationality:(0,s.Qq)("nationality"),passportNumber:(0,s.Qq)("passport_number").notNull(),photoData:(0,s.Qq)("photo_data"),studentStatus:(0,o.zM)("student_status").default(!1),totalTests:(0,l.nd)("total_tests").default(0),createdAt:(0,i.vE)("created_at").defaultNow().notNull(),updatedAt:(0,i.vE)("updated_at").defaultNow().notNull()},e=>({passportIdx:(0,d.Pe)("candidate_passport_idx").on(e.passportNumber),orgIdx:(0,d.Pe)("candidate_org_idx").on(e.organizationId),nameIdx:(0,d.Pe)("candidate_name_idx").on(e.fullName),uniquePassport:(0,u.Am)("unique_passport_per_org").on(e.organizationId,e.passportNumber)})),y=(0,r.cJ)("test_registrations",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,p.sX)()),candidateId:(0,s.Qq)("candidate_id").references(()=>g.id,{onDelete:"cascade"}).notNull(),candidateNumber:(0,s.Qq)("candidate_number").notNull(),testDate:(0,i.vE)("test_date").notNull(),testCenter:(0,s.Qq)("test_center").notNull(),status:(0,s.Qq)("status",{enum:["registered","completed","cancelled"]}).default("registered"),createdAt:(0,i.vE)("created_at").defaultNow().notNull(),updatedAt:(0,i.vE)("updated_at").defaultNow().notNull()},e=>({candidateIdx:(0,d.Pe)("test_reg_candidate_idx").on(e.candidateId),dateIdx:(0,d.Pe)("test_reg_date_idx").on(e.testDate),statusIdx:(0,d.Pe)("test_reg_status_idx").on(e.status)})),x=(0,r.cJ)("test_results",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,p.sX)()),testRegistrationId:(0,s.Qq)("test_registration_id").references(()=>y.id,{onDelete:"cascade"}).notNull(),listeningScore:(0,l.nd)("listening_score"),listeningBandScore:(0,c._)("listening_band_score",{precision:2,scale:1}),readingScore:(0,l.nd)("reading_score"),readingBandScore:(0,c._)("reading_band_score",{precision:2,scale:1}),writingTask1Score:(0,c._)("writing_task1_score",{precision:2,scale:1}),writingTask2Score:(0,c._)("writing_task2_score",{precision:2,scale:1}),writingBandScore:(0,c._)("writing_band_score",{precision:2,scale:1}),speakingFluencyScore:(0,c._)("speaking_fluency_score",{precision:2,scale:1}),speakingLexicalScore:(0,c._)("speaking_lexical_score",{precision:2,scale:1}),speakingGrammarScore:(0,c._)("speaking_grammar_score",{precision:2,scale:1}),speakingPronunciationScore:(0,c._)("speaking_pronunciation_score",{precision:2,scale:1}),speakingBandScore:(0,c._)("speaking_band_score",{precision:2,scale:1}),overallBandScore:(0,c._)("overall_band_score",{precision:2,scale:1}),status:(0,s.Qq)("status",{enum:["draft","completed","verified"]}).default("draft"),enteredBy:(0,s.Qq)("entered_by").references(()=>m.id),verifiedBy:(0,s.Qq)("verified_by").references(()=>m.id),createdAt:(0,i.vE)("created_at").defaultNow().notNull(),updatedAt:(0,i.vE)("updated_at").defaultNow().notNull()},e=>({testRegIdx:(0,d.Pe)("test_result_reg_idx").on(e.testRegistrationId),statusIdx:(0,d.Pe)("test_result_status_idx").on(e.status),overallScoreIdx:(0,d.Pe)("test_result_overall_idx").on(e.overallBandScore)})),q=(0,r.cJ)("payment_transactions",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,p.sX)()),candidateId:(0,s.Qq)("candidate_id").references(()=>g.id,{onDelete:"cascade"}).notNull(),organizationId:(0,s.Qq)("organization_id").references(()=>f.id,{onDelete:"cascade"}).notNull(),amount:(0,c._)("amount",{precision:10,scale:2}).notNull(),currency:(0,s.Qq)("currency").default("UZS").notNull(),gateway:(0,s.Qq)("gateway",{enum:["click","payme","manual"]}).notNull(),gatewayTransactionId:(0,s.Qq)("gateway_transaction_id"),status:(0,s.Qq)("status",{enum:["pending","completed","failed","cancelled","refunded"]}).default("pending"),featureType:(0,s.Qq)("feature_type",{enum:["feedback","certificate","progress"]}).notNull(),resultId:(0,s.Qq)("result_id").references(()=>x.id),metadata:(0,n.Pq)("metadata").$type().default({}),createdAt:(0,i.vE)("created_at").defaultNow().notNull(),completedAt:(0,i.vE)("completed_at")},e=>({candidateIdx:(0,d.Pe)("payment_candidate_idx").on(e.candidateId),statusIdx:(0,d.Pe)("payment_status_idx").on(e.status),gatewayIdx:(0,d.Pe)("payment_gateway_idx").on(e.gateway),featureIdx:(0,d.Pe)("payment_feature_idx").on(e.featureType)})),N=(0,r.cJ)("access_permissions",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,p.sX)()),candidateId:(0,s.Qq)("candidate_id").references(()=>g.id,{onDelete:"cascade"}).notNull(),resultId:(0,s.Qq)("result_id").references(()=>x.id,{onDelete:"cascade"}),featureType:(0,s.Qq)("feature_type",{enum:["feedback","certificate","progress"]}).notNull(),accessType:(0,s.Qq)("access_type",{enum:["paid","promotional","manual"]}).notNull(),grantedBy:(0,s.Qq)("granted_by").references(()=>m.id),grantedAt:(0,i.vE)("granted_at").defaultNow().notNull(),expiresAt:(0,i.vE)("expires_at"),metadata:(0,n.Pq)("metadata").$type().default({})},e=>({candidateIdx:(0,d.Pe)("access_candidate_idx").on(e.candidateId),resultIdx:(0,d.Pe)("access_result_idx").on(e.resultId),featureIdx:(0,d.Pe)("access_feature_idx").on(e.featureType),expiryIdx:(0,d.Pe)("access_expiry_idx").on(e.expiresAt)})),v=(0,r.cJ)("promotional_rules",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,p.sX)()),organizationId:(0,s.Qq)("organization_id").references(()=>f.id,{onDelete:"cascade"}).notNull(),name:(0,s.Qq)("name").notNull(),type:(0,s.Qq)("type",{enum:["student_discount","loyalty_reward","time_based","custom"]}).notNull(),featureType:(0,s.Qq)("feature_type",{enum:["feedback","certificate","progress","all"]}).notNull(),criteria:(0,n.Pq)("criteria").$type().notNull(),benefits:(0,n.Pq)("benefits").$type().notNull(),status:(0,s.Qq)("status",{enum:["active","inactive","expired"]}).default("active"),validFrom:(0,i.vE)("valid_from").notNull(),validUntil:(0,i.vE)("valid_until"),usageLimit:(0,l.nd)("usage_limit"),usageCount:(0,l.nd)("usage_count").default(0),createdAt:(0,i.vE)("created_at").defaultNow().notNull(),updatedAt:(0,i.vE)("updated_at").defaultNow().notNull()},e=>({orgIdx:(0,d.Pe)("promo_org_idx").on(e.organizationId),statusIdx:(0,d.Pe)("promo_status_idx").on(e.status),typeIdx:(0,d.Pe)("promo_type_idx").on(e.type),validityIdx:(0,d.Pe)("promo_validity_idx").on(e.validFrom,e.validUntil)})),I=(0,r.cJ)("ai_feedback",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,p.sX)()),testResultId:(0,s.Qq)("test_result_id").references(()=>x.id,{onDelete:"cascade"}).notNull().unique(),listeningFeedback:(0,s.Qq)("listening_feedback"),readingFeedback:(0,s.Qq)("reading_feedback"),writingFeedback:(0,s.Qq)("writing_feedback"),speakingFeedback:(0,s.Qq)("speaking_feedback"),overallFeedback:(0,s.Qq)("overall_feedback"),studyRecommendations:(0,s.Qq)("study_recommendations"),strengths:(0,n.Pq)("strengths").$type().default([]),weaknesses:(0,n.Pq)("weaknesses").$type().default([]),studyPlan:(0,n.Pq)("study_plan").$type().default({}),generatedAt:(0,i.vE)("generated_at").defaultNow().notNull()},e=>({resultIdx:(0,d.Pe)("ai_feedback_result_idx").on(e.testResultId)})),Q=(0,r.cJ)("certificate_lifecycle",{id:(0,s.Qq)("id").primaryKey().$defaultFn(()=>(0,p.sX)()),resultId:(0,s.Qq)("result_id").references(()=>x.id,{onDelete:"cascade"}).notNull().unique(),serialNumber:(0,s.Qq)("serial_number").unique().notNull(),generatedAt:(0,i.vE)("generated_at").defaultNow().notNull(),expiresAt:(0,i.vE)("expires_at").notNull(),status:(0,s.Qq)("status",{enum:["active","expired","deleted"]}).default("active"),deletionScheduledAt:(0,i.vE)("deletion_scheduled_at"),metadata:(0,n.Pq)("metadata").$type().default({}),createdAt:(0,i.vE)("created_at").defaultNow().notNull(),updatedAt:(0,i.vE)("updated_at").defaultNow().notNull()},e=>({resultIdx:(0,d.Pe)("cert_result_idx").on(e.resultId),serialIdx:(0,d.Pe)("cert_serial_idx").on(e.serialNumber),statusIdx:(0,d.Pe)("cert_status_idx").on(e.status),expiryIdx:(0,d.Pe)("cert_expiry_idx").on(e.expiresAt)})),w=(0,_.K1)(f,({many:e})=>({users:e(m),candidates:e(g),paymentTransactions:e(q),promotionalRules:e(v)})),P=(0,_.K1)(m,({one:e,many:t})=>({organization:e(f,{fields:[m.organizationId],references:[f.id]}),enteredResults:t(x,{relationName:"enteredBy"}),verifiedResults:t(x,{relationName:"verifiedBy"}),grantedPermissions:t(N)})),b=(0,_.K1)(g,({one:e,many:t})=>({organization:e(f,{fields:[g.organizationId],references:[f.id]}),testRegistrations:t(y),paymentTransactions:t(q),accessPermissions:t(N)})),k=(0,_.K1)(y,({one:e,many:t})=>({candidate:e(g,{fields:[y.candidateId],references:[g.id]}),testResults:t(x)})),R=(0,_.K1)(x,({one:e})=>({testRegistration:e(y,{fields:[x.testRegistrationId],references:[y.id]}),enteredByUser:e(m,{fields:[x.enteredBy],references:[m.id],relationName:"enteredBy"}),verifiedByUser:e(m,{fields:[x.verifiedBy],references:[m.id],relationName:"verifiedBy"}),aiFeedback:e(I,{fields:[x.id],references:[I.testResultId]}),certificate:e(Q,{fields:[x.id],references:[Q.resultId]})})),A=(0,_.K1)(q,({one:e})=>({candidate:e(g,{fields:[q.candidateId],references:[g.id]}),organization:e(f,{fields:[q.organizationId],references:[f.id]}),result:e(x,{fields:[q.resultId],references:[x.id]})})),h=(0,_.K1)(N,({one:e})=>({candidate:e(g,{fields:[N.candidateId],references:[g.id]}),result:e(x,{fields:[N.resultId],references:[x.id]}),grantedByUser:e(m,{fields:[N.grantedBy],references:[m.id]})})),E=(0,_.K1)(v,({one:e})=>({organization:e(f,{fields:[v.organizationId],references:[f.id]})})),S=(0,_.K1)(I,({one:e})=>({testResult:e(x,{fields:[I.testResultId],references:[x.id]})})),z=(0,_.K1)(Q,({one:e})=>({result:e(x,{fields:[Q.resultId],references:[x.id]})}))},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71682:(e,t,a)=>{a.d(t,{db:()=>i});var r=a(30686),s=a(43971),n=a(32767);let i=function(){if(!process.env.DATABASE_URL)throw Error("DATABASE_URL environment variable is required");let e=process.env.DATABASE_URL,t=(0,s.A)(e,{prepare:!1});return(0,r.f)(t,{schema:n})}()},74998:e=>{e.exports=require("perf_hooks")},77598:e=>{e.exports=require("node:crypto")},91645:e=>{e.exports=require("net")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[4447,5552,2190,1595,7346],()=>a(6450));module.exports=r})();