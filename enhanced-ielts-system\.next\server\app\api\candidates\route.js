(()=>{var e={};e.id=3048,e.ids=[3048],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23913:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>j,routeModule:()=>f,serverHooks:()=>z,workAsyncStorage:()=>q,workUnitAsyncStorage:()=>y});var s={};r.r(s),r.d(s,{GET:()=>h,POST:()=>x});var a=r(96559),n=r(48088),i=r(37719),o=r(32190),u=r(26326),d=r(71682),p=r(32767),l=r(94634),c=r(52175),m=r(45697);let g=m.z.object({fullName:m.z.string().min(2),email:m.z.string().email().optional().nullable(),phoneNumber:m.z.string().optional().nullable(),dateOfBirth:m.z.string().optional().nullable(),nationality:m.z.string().min(2),passportNumber:m.z.string().min(5),studentStatus:m.z.boolean().default(!1)});async function x(e){try{let t=await (0,u.j2)();if(!t?.user?.organizationId)return o.NextResponse.json({error:"Unauthorized"},{status:401});if("admin"!==t.user.role&&!t.user.masterAdmin)return o.NextResponse.json({error:"Insufficient permissions"},{status:403});let r=await e.json(),s=g.parse(r);if((await d.db.select().from(p.candidates).where((0,l.Uo)((0,l.eq)(p.candidates.organizationId,t.user.organizationId),(0,l.eq)(p.candidates.passportNumber,s.passportNumber))).limit(1)).length>0)return o.NextResponse.json({error:"A candidate with this passport number already exists in your organization"},{status:409});let[a]=await d.db.insert(p.candidates).values({id:(0,c.sX)(),organizationId:t.user.organizationId,fullName:s.fullName,email:s.email||null,phoneNumber:s.phoneNumber||null,dateOfBirth:s.dateOfBirth?new Date(s.dateOfBirth):null,nationality:s.nationality,passportNumber:s.passportNumber,studentStatus:s.studentStatus,totalTests:0}).returning();return o.NextResponse.json(a,{status:201})}catch(e){if(console.error("Error creating candidate:",e),e instanceof m.z.ZodError)return o.NextResponse.json({error:"Invalid input data",details:e.errors},{status:400});return o.NextResponse.json({error:"Internal server error"},{status:500})}}async function h(e){try{let t=await (0,u.j2)();if(!t?.user?.organizationId)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url);r.get("search");let s=r.get("studentStatus"),a=r.get("nationality"),n=parseInt(r.get("page")||"1"),i=parseInt(r.get("limit")||"20");d.db.select().from(p.candidates).where((0,l.eq)(p.candidates.organizationId,t.user.organizationId));let c=[(0,l.eq)(p.candidates.organizationId,t.user.organizationId)];"student"===s?c.push((0,l.eq)(p.candidates.studentStatus,!0)):"regular"===s&&c.push((0,l.eq)(p.candidates.studentStatus,!1)),a&&"other"!==a&&c.push((0,l.eq)(p.candidates.nationality,a));let m=await d.db.select().from(p.candidates).where((0,l.Uo)(...c)).limit(i).offset((n-1)*i).orderBy(p.candidates.createdAt);return o.NextResponse.json({candidates:m,pagination:{page:n,limit:i,total:m.length}})}catch(e){return console.error("Error fetching candidates:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}let f=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/candidates/route",pathname:"/api/candidates",filename:"route",bundlePath:"app/api/candidates/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\candidates\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:q,workUnitAsyncStorage:y,serverHooks:z}=f;function j(){return(0,i.patchFetch)({workAsyncStorage:q,workUnitAsyncStorage:y})}},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74998:e=>{"use strict";e.exports=require("perf_hooks")},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},91645:e=>{"use strict";e.exports=require("net")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,5552,2190,1057,1595,6326],()=>r(23913));module.exports=s})();