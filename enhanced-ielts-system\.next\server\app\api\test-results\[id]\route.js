(()=>{var e={};e.id=7451,e.ids=[7451],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30882:(e,t,r)=>{"use strict";r.d(t,{kK:()=>i});function s(e){return e>=0&&e<=9&&2*e%1==0}function i(e){let t=[];return void 0!==e.listeningScore&&(e.listeningScore<0||e.listeningScore>40)&&t.push("Listening score must be between 0 and 40"),void 0!==e.readingScore&&(e.readingScore<0||e.readingScore>40)&&t.push("Reading score must be between 0 and 40"),void 0===e.writingTask1Score||s(e.writingTask1Score)||t.push("Writing Task 1 score must be a valid band score (0-9 in 0.5 increments)"),void 0===e.writingTask2Score||s(e.writingTask2Score)||t.push("Writing Task 2 score must be a valid band score (0-9 in 0.5 increments)"),[{score:e.speakingFluencyScore,name:"Speaking Fluency"},{score:e.speakingLexicalScore,name:"Speaking Lexical Resource"},{score:e.speakingGrammarScore,name:"Speaking Grammar"},{score:e.speakingPronunciationScore,name:"Speaking Pronunciation"}].forEach(({score:e,name:r})=>{void 0===e||s(e)||t.push(`${r} score must be a valid band score (0-9 in 0.5 increments)`)}),{isValid:0===t.length,errors:t}}},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},44878:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>b,routeModule:()=>R,serverHooks:()=>v,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{DELETE:()=>x,GET:()=>k,PUT:()=>m});var i=r(96559),n=r(48088),o=r(37719),a=r(32190),d=r(26326),c=r(71682),u=r(32767),l=r(94634),g=r(45697),p=r(30882);let S=g.z.object({listeningScore:g.z.number().min(0).max(40).optional(),listeningBandScore:g.z.number().min(0).max(9).optional(),readingScore:g.z.number().min(0).max(40).optional(),readingBandScore:g.z.number().min(0).max(9).optional(),writingTask1Score:g.z.number().min(0).max(9).optional(),writingTask2Score:g.z.number().min(0).max(9).optional(),writingBandScore:g.z.number().min(0).max(9).optional(),speakingFluencyScore:g.z.number().min(0).max(9).optional(),speakingLexicalScore:g.z.number().min(0).max(9).optional(),speakingGrammarScore:g.z.number().min(0).max(9).optional(),speakingPronunciationScore:g.z.number().min(0).max(9).optional(),speakingBandScore:g.z.number().min(0).max(9).optional(),overallBandScore:g.z.number().min(0).max(9).optional(),status:g.z.enum(["draft","completed","verified"]).optional()});async function m(e,{params:t}){try{let r=await (0,d.j2)();if(!r?.user?.organizationId)return a.NextResponse.json({error:"Unauthorized"},{status:401});if("admin"!==r.user.role&&"checker"!==r.user.role&&!r.user.masterAdmin)return a.NextResponse.json({error:"Insufficient permissions"},{status:403});let s=await e.json(),i=S.parse(s),n=await c.db.select({result:u.testResults,registration:u.testRegistrations,candidate:u.candidates}).from(u.testResults).leftJoin(u.testRegistrations,(0,l.eq)(u.testResults.testRegistrationId,u.testRegistrations.id)).leftJoin(u.candidates,(0,l.eq)(u.testRegistrations.candidateId,u.candidates.id)).where((0,l.Uo)((0,l.eq)(u.testResults.id,t.id),(0,l.eq)(u.candidates.organizationId,r.user.organizationId))).limit(1);if(0===n.length)return a.NextResponse.json({error:"Test result not found or does not belong to your organization"},{status:404});if(Object.keys(i).some(e=>e.includes("Score"))){let e=(0,p.kK)({listeningScore:i.listeningScore,readingScore:i.readingScore,writingTask1Score:i.writingTask1Score,writingTask2Score:i.writingTask2Score,speakingFluencyScore:i.speakingFluencyScore,speakingLexicalScore:i.speakingLexicalScore,speakingGrammarScore:i.speakingGrammarScore,speakingPronunciationScore:i.speakingPronunciationScore});if(!e.isValid)return a.NextResponse.json({error:"Invalid test result data",details:e.errors},{status:400})}let o={updatedAt:new Date};void 0!==i.listeningScore&&(o.listeningScore=i.listeningScore),void 0!==i.listeningBandScore&&(o.listeningBandScore=i.listeningBandScore.toString()),void 0!==i.readingScore&&(o.readingScore=i.readingScore),void 0!==i.readingBandScore&&(o.readingBandScore=i.readingBandScore.toString()),void 0!==i.writingTask1Score&&(o.writingTask1Score=i.writingTask1Score.toString()),void 0!==i.writingTask2Score&&(o.writingTask2Score=i.writingTask2Score.toString()),void 0!==i.writingBandScore&&(o.writingBandScore=i.writingBandScore.toString()),void 0!==i.speakingFluencyScore&&(o.speakingFluencyScore=i.speakingFluencyScore.toString()),void 0!==i.speakingLexicalScore&&(o.speakingLexicalScore=i.speakingLexicalScore.toString()),void 0!==i.speakingGrammarScore&&(o.speakingGrammarScore=i.speakingGrammarScore.toString()),void 0!==i.speakingPronunciationScore&&(o.speakingPronunciationScore=i.speakingPronunciationScore.toString()),void 0!==i.speakingBandScore&&(o.speakingBandScore=i.speakingBandScore.toString()),void 0!==i.overallBandScore&&(o.overallBandScore=i.overallBandScore.toString()),void 0!==i.status&&(o.status=i.status);let[g]=await c.db.update(u.testResults).set(o).where((0,l.eq)(u.testResults.id,t.id)).returning();return a.NextResponse.json(g)}catch(e){if(console.error("Error updating test result:",e),e instanceof g.z.ZodError)return a.NextResponse.json({error:"Invalid data",details:e.errors},{status:400});return a.NextResponse.json({error:"Failed to update test result"},{status:500})}}async function k(e,{params:t}){try{let e=await (0,d.j2)();if(!e?.user?.organizationId)return a.NextResponse.json({error:"Unauthorized"},{status:401});let r=await c.db.select({result:u.testResults,registration:u.testRegistrations,candidate:u.candidates}).from(u.testResults).leftJoin(u.testRegistrations,(0,l.eq)(u.testResults.testRegistrationId,u.testRegistrations.id)).leftJoin(u.candidates,(0,l.eq)(u.testRegistrations.candidateId,u.candidates.id)).where((0,l.Uo)((0,l.eq)(u.testResults.id,t.id),(0,l.eq)(u.candidates.organizationId,e.user.organizationId))).limit(1);if(0===r.length)return a.NextResponse.json({error:"Test result not found or does not belong to your organization"},{status:404});return a.NextResponse.json(r[0])}catch(e){return console.error("Error fetching test result:",e),a.NextResponse.json({error:"Failed to fetch test result"},{status:500})}}async function x(e,{params:t}){try{let e=await (0,d.j2)();if(!e?.user?.organizationId)return a.NextResponse.json({error:"Unauthorized"},{status:401});if("admin"!==e.user.role&&!e.user.masterAdmin)return a.NextResponse.json({error:"Insufficient permissions"},{status:403});let r=await c.db.select({result:u.testResults,registration:u.testRegistrations,candidate:u.candidates}).from(u.testResults).leftJoin(u.testRegistrations,(0,l.eq)(u.testResults.testRegistrationId,u.testRegistrations.id)).leftJoin(u.candidates,(0,l.eq)(u.testRegistrations.candidateId,u.candidates.id)).where((0,l.Uo)((0,l.eq)(u.testResults.id,t.id),(0,l.eq)(u.candidates.organizationId,e.user.organizationId))).limit(1);if(0===r.length)return a.NextResponse.json({error:"Test result not found or does not belong to your organization"},{status:404});return await c.db.delete(u.testResults).where((0,l.eq)(u.testResults.id,t.id)),await c.db.update(u.testRegistrations).set({status:"registered",updatedAt:new Date}).where((0,l.eq)(u.testRegistrations.id,r[0].registration.id)),a.NextResponse.json({message:"Test result deleted successfully"})}catch(e){return console.error("Error deleting test result:",e),a.NextResponse.json({error:"Failed to delete test result"},{status:500})}}let R=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/test-results/[id]/route",pathname:"/api/test-results/[id]",filename:"route",bundlePath:"app/api/test-results/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\test-results\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:w,workUnitAsyncStorage:f,serverHooks:v}=R;function b(){return(0,o.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:f})}},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74998:e=>{"use strict";e.exports=require("perf_hooks")},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},91645:e=>{"use strict";e.exports=require("net")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,5552,2190,1057,1595,6326],()=>r(44878));module.exports=s})();