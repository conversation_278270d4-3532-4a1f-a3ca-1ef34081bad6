(()=>{var e={};e.id=5466,e.ids=[5466],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74998:e=>{"use strict";e.exports=require("perf_hooks")},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},91487:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>q,routeModule:()=>x,serverHooks:()=>m,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{GET:()=>l});var i=r(96559),o=r(48088),a=r(37719),n=r(32190),u=r(26326),c=r(71682),p=r(32767),d=r(94634);async function l(e){let t=await (0,u.j2)();if(!t?.user?.organizationId)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),s=r.get("resultId");if(!s)return n.NextResponse.json({error:"Result ID required"},{status:400});try{let e=await c.db.select().from(p.certificateLifecycle).where((0,d.eq)(p.certificateLifecycle.resultId,s)).limit(1);if(0===e.length)return n.NextResponse.json({error:"Certificate not found"},{status:404});let t=e[0];return n.NextResponse.json({id:t.id,serialNumber:t.serialNumber,generatedAt:t.generatedAt.toISOString(),expiresAt:t.expiresAt.toISOString(),status:t.status,downloadUrl:`/api/certificates/download/${t.id}`})}catch(e){return console.error("Error fetching certificate:",e),n.NextResponse.json({error:"Failed to fetch certificate"},{status:500})}}let x=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/certificates/route",pathname:"/api/certificates",filename:"route",bundlePath:"app/api/certificates/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\certificates\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:g,serverHooks:m}=x;function q(){return(0,a.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:g})}},91645:e=>{"use strict";e.exports=require("net")},96487:()=>{},96559:(e,t,r)=>{"use strict";e.exports=r(44870)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,5552,2190,1057,6326],()=>r(91487));module.exports=s})();