(()=>{var e={};e.id=447,e.ids=[447],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30882:(e,t,r)=>{"use strict";r.d(t,{kK:()=>n});function s(e){return e>=0&&e<=9&&2*e%1==0}function n(e){let t=[];return void 0!==e.listeningScore&&(e.listeningScore<0||e.listeningScore>40)&&t.push("Listening score must be between 0 and 40"),void 0!==e.readingScore&&(e.readingScore<0||e.readingScore>40)&&t.push("Reading score must be between 0 and 40"),void 0===e.writingTask1Score||s(e.writingTask1Score)||t.push("Writing Task 1 score must be a valid band score (0-9 in 0.5 increments)"),void 0===e.writingTask2Score||s(e.writingTask2Score)||t.push("Writing Task 2 score must be a valid band score (0-9 in 0.5 increments)"),[{score:e.speakingFluencyScore,name:"Speaking Fluency"},{score:e.speakingLexicalScore,name:"Speaking Lexical Resource"},{score:e.speakingGrammarScore,name:"Speaking Grammar"},{score:e.speakingPronunciationScore,name:"Speaking Pronunciation"}].forEach(({score:e,name:r})=>{void 0===e||s(e)||t.push(`${r} score must be a valid band score (0-9 in 0.5 increments)`)}),{isValid:0===t.length,errors:t}}},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74998:e=>{"use strict";e.exports=require("perf_hooks")},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},79349:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>k,serverHooks:()=>b,workAsyncStorage:()=>R,workUnitAsyncStorage:()=>w});var s={};r.r(s),r.d(s,{GET:()=>x,POST:()=>S});var n=r(96559),i=r(48088),a=r(37719),o=r(32190),c=r(26326),d=r(71682),u=r(32767),g=r(94634),l=r(45697),p=r(30882);let m=l.z.object({testRegistrationId:l.z.string().min(1),listeningScore:l.z.number().min(0).max(40),listeningBandScore:l.z.number().min(0).max(9),readingScore:l.z.number().min(0).max(40),readingBandScore:l.z.number().min(0).max(9),writingTask1Score:l.z.number().min(0).max(9),writingTask2Score:l.z.number().min(0).max(9),writingBandScore:l.z.number().min(0).max(9),speakingFluencyScore:l.z.number().min(0).max(9),speakingLexicalScore:l.z.number().min(0).max(9),speakingGrammarScore:l.z.number().min(0).max(9),speakingPronunciationScore:l.z.number().min(0).max(9),speakingBandScore:l.z.number().min(0).max(9),overallBandScore:l.z.number().min(0).max(9)});async function S(e){try{let t=await (0,c.j2)();if(!t?.user?.organizationId)return o.NextResponse.json({error:"Unauthorized"},{status:401});if("admin"!==t.user.role&&"checker"!==t.user.role&&!t.user.masterAdmin)return o.NextResponse.json({error:"Insufficient permissions"},{status:403});let r=await e.json(),s=m.parse(r),n=(0,p.kK)({listeningScore:s.listeningScore,readingScore:s.readingScore,writingTask1Score:s.writingTask1Score,writingTask2Score:s.writingTask2Score,speakingFluencyScore:s.speakingFluencyScore,speakingLexicalScore:s.speakingLexicalScore,speakingGrammarScore:s.speakingGrammarScore,speakingPronunciationScore:s.speakingPronunciationScore});if(!n.isValid)return o.NextResponse.json({error:"Invalid test result data",details:n.errors},{status:400});let i=await d.db.select({registration:u.testRegistrations,candidate:u.candidates}).from(u.testRegistrations).leftJoin(u.candidates,(0,g.eq)(u.testRegistrations.candidateId,u.candidates.id)).where((0,g.Uo)((0,g.eq)(u.testRegistrations.id,s.testRegistrationId),(0,g.eq)(u.candidates.organizationId,t.user.organizationId))).limit(1);if(0===i.length)return o.NextResponse.json({error:"Test registration not found or does not belong to your organization"},{status:404});if((await d.db.select().from(u.testResults).where((0,g.eq)(u.testResults.testRegistrationId,s.testRegistrationId)).limit(1)).length>0)return o.NextResponse.json({error:"Test result already exists for this registration"},{status:409});let[a]=await d.db.insert(u.testResults).values({testRegistrationId:s.testRegistrationId,listeningScore:s.listeningScore,listeningBandScore:s.listeningBandScore.toString(),readingScore:s.readingScore,readingBandScore:s.readingBandScore.toString(),writingTask1Score:s.writingTask1Score.toString(),writingTask2Score:s.writingTask2Score.toString(),writingBandScore:s.writingBandScore.toString(),speakingFluencyScore:s.speakingFluencyScore.toString(),speakingLexicalScore:s.speakingLexicalScore.toString(),speakingGrammarScore:s.speakingGrammarScore.toString(),speakingPronunciationScore:s.speakingPronunciationScore.toString(),speakingBandScore:s.speakingBandScore.toString(),overallBandScore:s.overallBandScore.toString(),status:"completed",enteredBy:t.user.id}).returning();return await d.db.update(u.testRegistrations).set({status:"completed",updatedAt:new Date}).where((0,g.eq)(u.testRegistrations.id,s.testRegistrationId)),o.NextResponse.json(a,{status:201})}catch(e){if(console.error("Error creating test result:",e),e instanceof l.z.ZodError)return o.NextResponse.json({error:"Invalid data",details:e.errors},{status:400});return o.NextResponse.json({error:"Failed to create test result"},{status:500})}}async function x(e){try{let t=await (0,c.j2)();if(!t?.user?.organizationId)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),s=r.get("candidateId"),n=r.get("registrationId"),i=d.db.select({result:u.testResults,registration:u.testRegistrations,candidate:u.candidates}).from(u.testResults).leftJoin(u.testRegistrations,(0,g.eq)(u.testResults.testRegistrationId,u.testRegistrations.id)).leftJoin(u.candidates,(0,g.eq)(u.testRegistrations.candidateId,u.candidates.id)).where((0,g.eq)(u.candidates.organizationId,t.user.organizationId));s&&(i=i.where((0,g.Uo)((0,g.eq)(u.candidates.organizationId,t.user.organizationId),(0,g.eq)(u.candidates.id,s)))),n&&(i=i.where((0,g.Uo)((0,g.eq)(u.candidates.organizationId,t.user.organizationId),(0,g.eq)(u.testRegistrations.id,n))));let a=await i;return o.NextResponse.json(a)}catch(e){return console.error("Error fetching test results:",e),o.NextResponse.json({error:"Failed to fetch test results"},{status:500})}}let k=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/test-results/route",pathname:"/api/test-results",filename:"route",bundlePath:"app/api/test-results/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\test-results\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:R,workUnitAsyncStorage:w,serverHooks:b}=k;function f(){return(0,a.patchFetch)({workAsyncStorage:R,workUnitAsyncStorage:w})}},91645:e=>{"use strict";e.exports=require("net")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,5552,2190,1057,1595,6326],()=>r(79349));module.exports=s})();