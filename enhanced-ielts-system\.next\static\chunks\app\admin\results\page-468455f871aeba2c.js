(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3967],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var s=r(5155),a=r(2115),i=r(6486);let l=a.forwardRef((e,t)=>{let{className:r,variant:a="default",size:l="default",...n}=e;return(0,s.jsx)("button",{className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{"bg-blue-600 text-white hover:bg-blue-700":"default"===a,"bg-red-600 text-white hover:bg-red-700":"destructive"===a,"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50":"outline"===a,"bg-gray-100 text-gray-900 hover:bg-gray-200":"secondary"===a,"hover:bg-gray-100 hover:text-gray-900":"ghost"===a,"text-blue-600 underline-offset-4 hover:underline":"link"===a},{"h-10 px-4 py-2":"default"===l,"h-9 rounded-md px-3":"sm"===l,"h-11 rounded-md px-8":"lg"===l,"h-10 w-10":"icon"===l},r),ref:t,...n})});l.displayName="Button"},1007:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1154:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},2523:(e,t,r)=>{"use strict";r.d(t,{p:()=>l});var s=r(5155),a=r(2115),i=r(6486);let l=a.forwardRef((e,t)=>{let{className:r,type:a,error:l,...n}=e;return(0,s.jsxs)("div",{className:"w-full",children:[(0,s.jsx)("input",{type:a,className:(0,i.cn)("flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:cursor-not-allowed disabled:opacity-50",l&&"border-red-500 focus:ring-red-500 focus:border-red-500",r),ref:t,...n}),l&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-500",children:l})]})});l.displayName="Input"},2657:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3352:(e,t,r)=>{"use strict";r.d(t,{a:()=>c});var s=r(5155),a=r(2115),i=r(5939),l=r(280),n=r(4416),d=r(6486);function c(e){let{isOpen:t,onClose:r,title:c,children:o,size:m="md"}=e;return(0,s.jsx)(i.e,{appear:!0,show:t,as:a.Fragment,children:(0,s.jsxs)(l.lG,{as:"div",className:"relative z-50",onClose:r,children:[(0,s.jsx)(i.e.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,s.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,s.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,s.jsx)(i.e.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,s.jsxs)(l.lG.Panel,{className:(0,d.cn)("w-full transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl"}[m]),children:[c&&(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)(l.lG.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:c}),(0,s.jsx)("button",{type:"button",className:"rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500",onClick:r,children:(0,s.jsx)(n.A,{className:"h-5 w-5"})})]}),o]})})})})]})})}},3873:(e,t,r)=>{Promise.resolve().then(r.bind(r,8638)),Promise.resolve().then(r.bind(r,9543))},4516:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},6126:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var s=r(5155);r(2115);var a=r(6486);function i(e){let{className:t,variant:r="default",...i}=e;return(0,s.jsx)("div",{className:(0,a.cn)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{"border-transparent bg-primary text-primary-foreground hover:bg-primary/80":"default"===r,"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80":"secondary"===r,"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80":"destructive"===r,"text-foreground":"outline"===r,"border-transparent bg-blue-100 text-blue-800":"blue"===r,"border-transparent bg-green-100 text-green-800":"green"===r,"border-transparent bg-yellow-100 text-yellow-800":"yellow"===r,"border-transparent bg-red-100 text-red-800":"red"===r,"border-transparent bg-purple-100 text-purple-800":"purple"===r,"border-transparent bg-orange-100 text-orange-800":"orange"===r,"border-transparent bg-gray-100 text-gray-800":"gray"===r},t),...i})}},6486:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var s=r(2596),a=r(9688);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}},7434:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},7870:(e,t,r)=>{"use strict";r.d(t,{AI:()=>l,EH:()=>d,Fd:()=>c,dN:()=>n});let s={39:9,38:9,37:8.5,36:8.5,35:8,34:8,33:7.5,32:7.5,31:7,30:7,29:6.5,28:6.5,27:6,26:6,25:5.5,24:5.5,23:5,22:5,21:4.5,20:4.5,19:4,18:4,17:3.5,16:3.5,15:3,14:3,13:2.5,12:2.5,11:2,10:2,9:1.5,8:1.5,7:1,6:1,5:.5,4:.5,3:0,2:0,1:0,0:0},a={39:9,38:9,37:8.5,36:8.5,35:8,34:8,33:7.5,32:7.5,31:7,30:7,29:6.5,28:6.5,27:6,26:6,25:5.5,24:5.5,23:5,22:5,21:4.5,20:4.5,19:4,18:4,17:3.5,16:3.5,15:3,14:3,13:2.5,12:2.5,11:2,10:2,9:1.5,8:1.5,7:1,6:1,5:.5,4:.5,3:0,2:0,1:0,0:0},i={40:9,39:8.5,38:8.5,37:8,36:8,35:7.5,34:7.5,33:7,32:7,31:6.5,30:6.5,29:6,28:6,27:5.5,26:5.5,25:5,24:5,23:4.5,22:4.5,21:4,20:4,19:3.5,18:3.5,17:3,16:3,15:2.5,14:2.5,13:2,12:2,11:1.5,10:1.5,9:1,8:1,7:.5,6:.5,5:0,4:0,3:0,2:0,1:0,0:0};function l(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"academic",l=Math.max(0,Math.min(40,Math.floor(t)));return"listening"===e?s[l]||0:"reading"===e&&("academic"===r?a:i)[l]||0}function n(e,t,r,s){return Math.round((e+t+r+s)/4*2)/2}function d(e){return({9:"Expert User",8.5:"Very Good User",8:"Very Good User",7.5:"Good User",7:"Good User",6.5:"Competent User",6:"Competent User",5.5:"Modest User",5:"Modest User",4.5:"Limited User",4:"Limited User",3.5:"Extremely Limited User",3:"Extremely Limited User",2.5:"Intermittent User",2:"Intermittent User",1.5:"Non User",1:"Non User",.5:"Did not attempt the test",0:"Did not attempt the test"})[e]||"Invalid Score"}function c(e){return e>=8.5?{level:"Excellent",color:"green",description:"Very high proficiency level"}:e>=7?{level:"Good",color:"blue",description:"Good proficiency level"}:e>=6?{level:"Competent",color:"yellow",description:"Competent proficiency level"}:e>=5?{level:"Modest",color:"orange",description:"Modest proficiency level"}:{level:"Limited",color:"red",description:"Limited proficiency level"}}},8638:(e,t,r)=>{"use strict";r.d(t,{TestRegistrationModal:()=>g});var s=r(5155),a=r(2115),i=r(2177),l=r(221),n=r(1153),d=r(3352),c=r(285),o=r(2523),m=r(9074),x=r(1154);let u=n.z.object({candidateId:n.z.string().min(1,"Please select a candidate"),testDate:n.z.string().min(1,"Test date is required"),testCenter:n.z.string().min(1,"Test center is required"),candidateNumber:n.z.string().min(1,"Candidate number is required")});function g(e){var t,r,n;let{children:g,candidates:p=[]}=e,[h,b]=(0,a.useState)(!1),[y,v]=(0,a.useState)(!1),{register:f,handleSubmit:j,reset:N,formState:{errors:k}}=(0,i.mN)({resolver:(0,l.u)(u)}),w=async e=>{v(!0);try{if(!(await fetch("/api/test-registrations",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).ok)throw Error("Failed to register test");N(),b(!1),window.location.reload()}catch(e){console.error("Error registering test:",e),alert("Failed to register test. Please try again.")}finally{v(!1)}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{onClick:()=>b(!0),children:g}),(0,s.jsx)(d.a,{isOpen:h,onClose:()=>b(!1),children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)(m.A,{className:"h-6 w-6 text-blue-600 mr-2"}),(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Register New Test"})]}),(0,s.jsxs)("form",{onSubmit:j(w),className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Candidate"}),(0,s.jsxs)("select",{...f("candidateId"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"",children:"Select a candidate"}),p.map(e=>(0,s.jsxs)("option",{value:e.id,children:[e.fullName," (",e.passportNumber,")"]},e.id))]}),k.candidateId&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-1",children:k.candidateId.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Test Date"}),(0,s.jsx)(o.p,{type:"datetime-local",...f("testDate"),error:null==(t=k.testDate)?void 0:t.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Test Center"}),(0,s.jsx)(o.p,{placeholder:"Enter test center location",...f("testCenter"),error:null==(r=k.testCenter)?void 0:r.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Candidate Number"}),(0,s.jsx)(o.p,{placeholder:"Enter candidate test number",...f("candidateNumber"),error:null==(n=k.candidateNumber)?void 0:n.message})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,s.jsx)(c.$,{type:"button",variant:"outline",onClick:()=>b(!1),disabled:y,children:"Cancel"}),(0,s.jsx)(c.$,{type:"submit",disabled:y,children:y?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(x.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Registering..."]}):"Register Test"})]})]})]})})]})}},9074:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9543:(e,t,r)=>{"use strict";r.d(t,{TestResultsTable:()=>w});var s=r(5155),a=r(2115),i=r(6126),l=r(285),n=r(2177),d=r(221),c=r(1153),o=r(3352),m=r(2523),x=r(7870),u=r(7434),g=r(9946);let p=(0,g.A)("calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]);var h=r(1154);let b=c.z.object({listeningScore:c.z.number().min(0).max(40),readingScore:c.z.number().min(0).max(40),writingTask1Score:c.z.number().min(0).max(9).step(.5),writingTask2Score:c.z.number().min(0).max(9).step(.5),speakingFluencyScore:c.z.number().min(0).max(9).step(.5),speakingLexicalScore:c.z.number().min(0).max(9).step(.5),speakingGrammarScore:c.z.number().min(0).max(9).step(.5),speakingPronunciationScore:c.z.number().min(0).max(9).step(.5)});function y(e){var t,r,i,c,g,y,v,f,j;let{isOpen:N,onClose:k,registration:w}=e,[S,A]=(0,a.useState)(!1),[C,T]=(0,a.useState)({listeningBand:0,readingBand:0,writingBand:0,speakingBand:0,overallBand:0}),{register:F,handleSubmit:M,watch:B,reset:z,setValue:E,formState:{errors:R}}=(0,n.mN)({resolver:(0,d.u)(b),defaultValues:w.result?{listeningScore:w.result.listeningScore||0,readingScore:w.result.readingScore||0,writingTask1Score:parseFloat(w.result.writingTask1Score||"0"),writingTask2Score:parseFloat(w.result.writingTask2Score||"0"),speakingFluencyScore:parseFloat(w.result.speakingFluencyScore||"0"),speakingLexicalScore:parseFloat(w.result.speakingLexicalScore||"0"),speakingGrammarScore:parseFloat(w.result.speakingGrammarScore||"0"),speakingPronunciationScore:parseFloat(w.result.speakingPronunciationScore||"0")}:void 0}),U=B();(0,a.useEffect)(()=>{let e=(0,x.AI)("listening",U.listeningScore||0),t=(0,x.AI)("reading",U.readingScore||0),r=((U.writingTask1Score||0)+(U.writingTask2Score||0))/2,s=((U.speakingFluencyScore||0)+(U.speakingLexicalScore||0)+(U.speakingGrammarScore||0)+(U.speakingPronunciationScore||0))/4,a=(0,x.dN)(e,t,r,s);T({listeningBand:e,readingBand:t,writingBand:r,speakingBand:s,overallBand:a})},[U]);let P=async e=>{A(!0);try{let t={testRegistrationId:w.registration.id,...e,listeningBandScore:C.listeningBand,readingBandScore:C.readingBand,writingBandScore:C.writingBand,speakingBandScore:C.speakingBand,overallBandScore:C.overallBand},r=w.result?"/api/test-results/".concat(w.result.id):"/api/test-results",s=w.result?"PUT":"POST";if(!(await fetch(r,{method:s,headers:{"Content-Type":"application/json"},body:JSON.stringify(t)})).ok)throw Error("Failed to save test result");z(),k(),window.location.reload()}catch(e){console.error("Error saving test result:",e),alert("Failed to save test result. Please try again.")}finally{A(!1)}};return(0,s.jsx)(o.a,{isOpen:N,onClose:k,size:"large",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex items-center mb-6",children:[(0,s.jsx)(u.A,{className:"h-6 w-6 text-blue-600 mr-2"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:w.result?"Edit Test Result":"Enter Test Result"}),(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:[null==(t=w.candidate)?void 0:t.fullName," - ",w.registration.candidateNumber]})]})]}),(0,s.jsxs)("form",{onSubmit:M(P),className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-3",children:"Listening"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Raw Score (0-40)"}),(0,s.jsx)(m.p,{type:"number",min:"0",max:"40",...F("listeningScore",{valueAsNumber:!0}),error:null==(r=R.listeningScore)?void 0:r.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Band Score (Calculated)"}),(0,s.jsx)("div",{className:"px-3 py-2 bg-white border border-gray-300 rounded-md text-lg font-bold text-blue-600",children:C.listeningBand.toFixed(1)})]})]})]}),(0,s.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-3",children:"Reading"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Raw Score (0-40)"}),(0,s.jsx)(m.p,{type:"number",min:"0",max:"40",...F("readingScore",{valueAsNumber:!0}),error:null==(i=R.readingScore)?void 0:i.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Band Score (Calculated)"}),(0,s.jsx)("div",{className:"px-3 py-2 bg-white border border-gray-300 rounded-md text-lg font-bold text-blue-600",children:C.readingBand.toFixed(1)})]})]})]}),(0,s.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-3",children:"Writing"}),(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Task 1 Score (0-9)"}),(0,s.jsx)(m.p,{type:"number",min:"0",max:"9",step:"0.5",...F("writingTask1Score",{valueAsNumber:!0}),error:null==(c=R.writingTask1Score)?void 0:c.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Task 2 Score (0-9)"}),(0,s.jsx)(m.p,{type:"number",min:"0",max:"9",step:"0.5",...F("writingTask2Score",{valueAsNumber:!0}),error:null==(g=R.writingTask2Score)?void 0:g.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Band Score (Average)"}),(0,s.jsx)("div",{className:"px-3 py-2 bg-white border border-gray-300 rounded-md text-lg font-bold text-blue-600",children:C.writingBand.toFixed(1)})]})]})]}),(0,s.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-3",children:"Speaking"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Fluency & Coherence (0-9)"}),(0,s.jsx)(m.p,{type:"number",min:"0",max:"9",step:"0.5",...F("speakingFluencyScore",{valueAsNumber:!0}),error:null==(y=R.speakingFluencyScore)?void 0:y.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Lexical Resource (0-9)"}),(0,s.jsx)(m.p,{type:"number",min:"0",max:"9",step:"0.5",...F("speakingLexicalScore",{valueAsNumber:!0}),error:null==(v=R.speakingLexicalScore)?void 0:v.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Grammatical Range (0-9)"}),(0,s.jsx)(m.p,{type:"number",min:"0",max:"9",step:"0.5",...F("speakingGrammarScore",{valueAsNumber:!0}),error:null==(f=R.speakingGrammarScore)?void 0:f.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Pronunciation (0-9)"}),(0,s.jsx)(m.p,{type:"number",min:"0",max:"9",step:"0.5",...F("speakingPronunciationScore",{valueAsNumber:!0}),error:null==(j=R.speakingPronunciationScore)?void 0:j.message})]})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Speaking Band Score (Average)"}),(0,s.jsx)("div",{className:"inline-block px-4 py-2 bg-white border border-gray-300 rounded-md text-lg font-bold text-blue-600",children:C.speakingBand.toFixed(1)})]})]}),(0,s.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg text-center",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center mb-2",children:[(0,s.jsx)(p,{className:"h-5 w-5 text-blue-600 mr-2"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Overall Band Score"})]}),(0,s.jsx)("div",{className:"text-3xl font-bold text-blue-600",children:C.overallBand.toFixed(1)})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,s.jsx)(l.$,{type:"button",variant:"outline",onClick:k,disabled:S,children:"Cancel"}),(0,s.jsx)(l.$,{type:"submit",disabled:S,children:S?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(h.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Saving..."]}):w.result?"Update Result":"Save Result"})]})]})]})})}var v=r(1007),f=r(9074),j=r(4516);let N=(0,g.A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]);var k=r(2657);function w(e){let{registrations:t}=e,[r,n]=(0,a.useState)(null),[d,c]=(0,a.useState)(!1),o=e=>{let t={registered:{color:"blue",label:"Registered"},completed:{color:"green",label:"Completed"},cancelled:{color:"red",label:"Cancelled"}}[e]||{color:"gray",label:e};return(0,s.jsx)(i.E,{variant:t.color,children:t.label})},m=e=>{if(!e)return(0,s.jsx)(i.E,{variant:"gray",children:"No Result"});let t={draft:{color:"yellow",label:"Draft"},completed:{color:"green",label:"Completed"},verified:{color:"blue",label:"Verified"}}[e.status]||{color:"gray",label:e.status};return(0,s.jsx)(i.E,{variant:t.color,children:t.label})},x=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),g=e=>{n(e),c(!0)};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,s.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Test Registrations & Results"})}),(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Candidate"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Test Details"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Result"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Band Score"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:t.map(e=>{var t,r,a;return(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(v.A,{className:"h-8 w-8 text-gray-400 mr-3"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:(null==(t=e.candidate)?void 0:t.fullName)||"Unknown"}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:null==(r=e.candidate)?void 0:r.passportNumber})]})]})}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,s.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,s.jsxs)("div",{className:"flex items-center mb-1",children:[(0,s.jsx)(f.A,{className:"h-4 w-4 text-gray-400 mr-1"}),x(e.registration.testDate)]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(j.A,{className:"h-4 w-4 text-gray-400 mr-1"}),e.registration.testCenter]})]}),(0,s.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["#",e.registration.candidateNumber]})]}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:o(e.registration.status)}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:m(e.result)}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:(null==(a=e.result)?void 0:a.overallBandScore)?(0,s.jsx)("span",{className:"text-lg font-bold text-blue-600",children:e.result.overallBandScore}):(0,s.jsx)("span",{className:"text-gray-400",children:"-"})})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,s.jsxs)("div",{className:"flex space-x-2",children:[!e.result&&"completed"===e.registration.status&&(0,s.jsxs)(l.$,{size:"sm",onClick:()=>g(e),children:[(0,s.jsx)(u.A,{className:"h-4 w-4 mr-1"}),"Enter Result"]}),e.result&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(l.$,{size:"sm",variant:"outline",onClick:()=>g(e),children:[(0,s.jsx)(N,{className:"h-4 w-4 mr-1"}),"Edit"]}),(0,s.jsxs)(l.$,{size:"sm",variant:"outline",children:[(0,s.jsx)(k.A,{className:"h-4 w-4 mr-1"}),"View"]})]})]})})]},e.registration.id)})})]})}),0===t.length&&(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)(u.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,s.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No registrations found"}),(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"No test registrations match your current filters."})]})]}),r&&(0,s.jsx)(y,{isOpen:d,onClose:()=>{c(!1),n(null)},registration:r})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,4343,8441,1684,7358],()=>t(3873)),_N_E=e.O()}]);