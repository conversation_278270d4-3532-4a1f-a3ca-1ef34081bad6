import { db } from '@/lib/db';
import { certificateLifecycle } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { headers } from 'next/headers';
import { CertificateVerificationDisplay } from '@/components/specialized/certificate-verification';

interface VerifyPageProps {
  params: { serial: string };
}

export default async function VerifyPage({ params }: VerifyPageProps) {
  const headersList = headers();
  const userAgent = headersList.get('user-agent') || '';
  const forwardedFor = headersList.get('x-forwarded-for');
  const realIp = headersList.get('x-real-ip');
  const ip = forwardedFor?.split(',')[0] || realIp || 'unknown';

  try {
    // Find certificate by serial number
    const certificate = await db
      .select()
      .from(certificateLifecycle)
      .where(eq(certificateLifecycle.serialNumber, params.serial))
      .limit(1);

    if (certificate.length === 0) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-red-600 mb-4">Certificate Not Found</h1>
            <p className="text-gray-600">The certificate with serial number {params.serial} could not be found.</p>
          </div>
        </div>
      );
    }

    const cert = certificate[0];

    // Check certificate status
    const isExpired = new Date() > cert.expiresAt;
    const isValid = cert.status === 'active' && !isExpired;

    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4">
          <CertificateVerificationDisplay
            certificate={cert}
            isValid={isValid}
            isExpired={isExpired}
          />
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error verifying certificate:', error);
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Verification Error</h1>
          <p className="text-gray-600">An error occurred while verifying the certificate.</p>
        </div>
      </div>
    );
  }
}
