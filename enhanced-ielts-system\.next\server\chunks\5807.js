exports.id=5807,exports.ids=[5807],exports.modules={6727:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("clipboard-list",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]])},7766:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var s=r(49384),a=r(82348);function i(...e){return(0,a.QP)((0,s.$)(e))}},10590:(e,t,r)=>{"use strict";r.d(t,{Header:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\layout\\header.tsx","Header")},19352:(e,t,r)=>{"use strict";r.d(t,{a:()=>d});var s=r(60687),a=r(43210),i=r(13516),n=r(18908),l=r(11860),o=r(7766);function d({isOpen:e,onClose:t,title:r,children:d,size:c="md"}){return(0,s.jsx)(i.e,{appear:!0,show:e,as:a.Fragment,children:(0,s.jsxs)(n.lG,{as:"div",className:"relative z-50",onClose:t,children:[(0,s.jsx)(i.e.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,s.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,s.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,s.jsx)(i.e.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,s.jsxs)(n.lG.Panel,{className:(0,o.cn)("w-full transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl"}[c]),children:[r&&(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)(n.lG.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:r}),(0,s.jsx)("button",{type:"button",className:"rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500",onClick:t,children:(0,s.jsx)(l.A,{className:"h-5 w-5"})})]}),d]})})})})]})})}},23469:(e,t,r)=>{"use strict";r.d(t,{$:()=>n});var s=r(37413),a=r(61120),i=r(72984);let n=a.forwardRef(({className:e,variant:t="default",size:r="default",...a},n)=>(0,s.jsx)("button",{className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{"bg-blue-600 text-white hover:bg-blue-700":"default"===t,"bg-red-600 text-white hover:bg-red-700":"destructive"===t,"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50":"outline"===t,"bg-gray-100 text-gray-900 hover:bg-gray-200":"secondary"===t,"hover:bg-gray-100 hover:text-gray-900":"ghost"===t,"text-blue-600 underline-offset-4 hover:underline":"link"===t},{"h-10 px-4 py-2":"default"===r,"h-9 rounded-md px-3":"sm"===r,"h-11 rounded-md px-8":"lg"===r,"h-10 w-10":"icon"===r},e),ref:n,...a}));n.displayName="Button"},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>n});var s=r(60687),a=r(43210),i=r(7766);let n=a.forwardRef(({className:e,variant:t="default",size:r="default",...a},n)=>(0,s.jsx)("button",{className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{"bg-blue-600 text-white hover:bg-blue-700":"default"===t,"bg-red-600 text-white hover:bg-red-700":"destructive"===t,"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50":"outline"===t,"bg-gray-100 text-gray-900 hover:bg-gray-200":"secondary"===t,"hover:bg-gray-100 hover:text-gray-900":"ghost"===t,"text-blue-600 underline-offset-4 hover:underline":"link"===t},{"h-10 px-4 py-2":"default"===r,"h-9 rounded-md px-3":"sm"===r,"h-11 rounded-md px-8":"lg"===r,"h-10 w-10":"icon"===r},e),ref:n,...a}));n.displayName="Button"},30599:(e,t,r)=>{"use strict";r.d(t,{Sidebar:()=>f});var s=r(60687),a=r(85814),i=r.n(a),n=r(16189),l=r(7766),o=r(49625),d=r(17313),c=r(53411),h=r(41312),m=r(10022),u=r(85778),x=r(80428),b=r(6727);function f({userRole:e,isMasterAdmin:t}){let r=(0,n.usePathname)(),a=[{href:"/master/dashboard",label:"Master Dashboard",icon:o.A},{href:"/master/organizations",label:"Organizations",icon:d.A},{href:"/master/analytics",label:"Analytics",icon:c.A}],f=[{href:"/admin/dashboard",label:"Dashboard",icon:o.A},{href:"/admin/candidates",label:"Candidates",icon:h.A},{href:"/admin/results",label:"Test Results",icon:m.A},{href:"/admin/payments",label:"Payments",icon:u.A},{href:"/admin/promotions",label:"Promotions",icon:x.A}],y=[{href:"/checker/dashboard",label:"Dashboard",icon:o.A},{href:"/checker/entry",label:"Result Entry",icon:b.A},{href:"/checker/results",label:"My Results",icon:m.A}],g=t?[...a,...f]:"admin"===e?f:y;return(0,s.jsx)("aside",{className:"w-64 bg-white shadow-sm border-r border-gray-200 min-h-screen",children:(0,s.jsx)("nav",{className:"mt-8 px-4",children:(0,s.jsx)("ul",{className:"space-y-2",children:g.map(e=>{let t=e.icon,a=r===e.href||r.startsWith(e.href+"/");return(0,s.jsx)("li",{children:(0,s.jsxs)(i(),{href:e.href,className:(0,l.cn)("flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors",a?"bg-blue-50 text-blue-700 border-r-2 border-blue-700":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"),children:[(0,s.jsx)(t,{className:"mr-3 h-5 w-5"}),e.label]})},e.href)})})})})}},37882:()=>{},40083:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},41312:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},49625:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},50259:(e,t,r)=>{Promise.resolve().then(r.bind(r,74456)),Promise.resolve().then(r.bind(r,30599))},59105:(e,t,r)=>{"use strict";r.d(t,{Sidebar:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\layout\\sidebar.tsx","Sidebar")},61135:()=>{},67973:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},72984:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var s=r(75986),a=r(8974);function i(...e){return(0,a.QP)((0,s.$)(e))}},74456:(e,t,r)=>{"use strict";r.d(t,{Header:()=>d});var s=r(60687),a=r(99208),i=r(29523),n=r(58869),l=r(84027),o=r(40083);function d({user:e}){return(0,s.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"Enhanced IELTS System"}),e.masterAdmin&&(0,s.jsx)("span",{className:"ml-3 px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full",children:"Master Admin"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(n.A,{className:"h-4 w-4 text-gray-500"}),(0,s.jsxs)("div",{className:"text-sm",children:[(0,s.jsx)("div",{className:"font-medium text-gray-900",children:e.name}),(0,s.jsx)("div",{className:"text-gray-500 capitalize",children:e.role})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(i.$,{variant:"ghost",size:"sm",children:(0,s.jsx)(l.A,{className:"h-4 w-4"})}),(0,s.jsxs)(i.$,{variant:"ghost",size:"sm",onClick:()=>{(0,a.CI)({callbackUrl:"/login"})},children:[(0,s.jsx)(o.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"ml-1",children:"Sign Out"})]})]})]})]})})})}},78245:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},78593:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var s=r(37413),a=r(61120),i=r(72984);let n=a.forwardRef(({className:e,type:t,error:r,...a},n)=>(0,s.jsxs)("div",{className:"w-full",children:[(0,s.jsx)("input",{type:t,className:(0,i.cn)("flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:cursor-not-allowed disabled:opacity-50",r&&"border-red-500 focus:ring-red-500 focus:border-red-500",e),ref:n,...a}),r&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-500",children:r})]}));n.displayName="Input"},80428:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]])},84027:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},91026:()=>{},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>o});var s=r(37413),a=r(22376),i=r.n(a),n=r(68726),l=r.n(n);r(61135);let o={title:"Create Next App",description:"Generated by create next app"};function d({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${i().variable} ${l().variable} antialiased`,children:e})})}},97115:(e,t,r)=>{Promise.resolve().then(r.bind(r,10590)),Promise.resolve().then(r.bind(r,59105))}};