(()=>{var e={};e.id=8997,e.ids=[8997],e.modules={1463:(e,t,s)=>{"use strict";s.d(t,{U9:()=>a});var r=s(96657);function a(e){return(0,r.ll)`count(${e||r.ll.raw("*")})`.mapWith(Number)}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4536:(e,t,s)=>{let{createProxy:r}=s(39844);e.exports=r("C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\node_modules\\next\\dist\\client\\app-dir\\link.js")},5148:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(26373).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},7244:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4536,23)),Promise.resolve().then(s.bind(s,13316))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11663:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),d=s(30893),l={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);s.d(t,l);let o={children:["",{children:["admin",{children:["candidates",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,94288)),"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\admin\\candidates\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,99111)),"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\admin\\candidates\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/candidates/page",pathname:"/admin/candidates",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},13316:(e,t,s)=>{"use strict";s.d(t,{CreateCandidateModal:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call CreateCandidateModal() from the server but CreateCandidateModal is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\specialized\\create-candidate-modal.tsx","CreateCandidateModal")},19001:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(26373).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30084:(e,t,s)=>{"use strict";s.d(t,{E:()=>i});var r=s(37413);s(61120);var a=s(72984);function i({className:e,variant:t="default",...s}){return(0,r.jsx)("div",{className:(0,a.cn)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{"border-transparent bg-primary text-primary-foreground hover:bg-primary/80":"default"===t,"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80":"secondary"===t,"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80":"destructive"===t,"text-foreground":"outline"===t,"border-transparent bg-blue-100 text-blue-800":"blue"===t,"border-transparent bg-green-100 text-green-800":"green"===t,"border-transparent bg-yellow-100 text-yellow-800":"yellow"===t,"border-transparent bg-red-100 text-red-800":"red"===t,"border-transparent bg-purple-100 text-purple-800":"purple"===t,"border-transparent bg-orange-100 text-orange-800":"orange"===t,"border-transparent bg-gray-100 text-gray-800":"gray"===t},e),...s})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36282:(e,t,s)=>{"use strict";s.d(t,{CreateCandidateModal:()=>x});var r=s(60687),a=s(43210),i=s(16189),n=s(27605),d=s(63442),l=s(9275),o=s(29523),c=s(89667),m=s(19352);let u=l.z.object({fullName:l.z.string().min(2,"Full name must be at least 2 characters"),email:l.z.string().email("Invalid email address").optional().or(l.z.literal("")),phoneNumber:l.z.string().optional(),dateOfBirth:l.z.string().optional(),nationality:l.z.string().min(2,"Nationality is required"),passportNumber:l.z.string().min(5,"Passport number must be at least 5 characters"),studentStatus:l.z.boolean().default(!1)});function x({children:e}){let[t,s]=(0,a.useState)(!1),[l,x]=(0,a.useState)(!1),h=(0,i.useRouter)(),p=(0,n.mN)({resolver:(0,d.u)(u),defaultValues:{studentStatus:!1}}),g=async e=>{x(!0);try{let t=await fetch("/api/candidates",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e,email:e.email||null,dateOfBirth:e.dateOfBirth?new Date(e.dateOfBirth).toISOString():null})});if(!t.ok){let e=await t.json();throw Error(e.message||"Failed to create candidate")}await t.json(),s(!1),p.reset(),h.refresh()}catch(e){console.error("Error creating candidate:",e)}finally{x(!1)}};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{onClick:()=>s(!0),children:e}),(0,r.jsx)(m.a,{isOpen:t,onClose:()=>s(!1),title:"Add New Candidate",children:(0,r.jsxs)("form",{onSubmit:p.handleSubmit(g),className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)(c.p,{...p.register("fullName"),placeholder:"Full Name *",error:p.formState.errors.fullName?.message}),(0,r.jsx)(c.p,{...p.register("nationality"),placeholder:"Nationality *",error:p.formState.errors.nationality?.message})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)(c.p,{...p.register("email"),type:"email",placeholder:"Email Address",error:p.formState.errors.email?.message}),(0,r.jsx)(c.p,{...p.register("phoneNumber"),placeholder:"Phone Number",error:p.formState.errors.phoneNumber?.message})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)(c.p,{...p.register("passportNumber"),placeholder:"Passport Number *",error:p.formState.errors.passportNumber?.message}),(0,r.jsx)(c.p,{...p.register("dateOfBirth"),type:"date",placeholder:"Date of Birth",error:p.formState.errors.dateOfBirth?.message})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"checkbox",...p.register("studentStatus"),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,r.jsx)("label",{className:"text-sm text-gray-700",children:"This candidate is a student (eligible for student discounts)"})]}),(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Important Notes:"}),(0,r.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,r.jsx)("li",{children:"• Passport number must be unique within your organization"}),(0,r.jsx)("li",{children:"• Student status affects promotional pricing eligibility"}),(0,r.jsx)("li",{children:"• Email is optional but recommended for notifications"})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,r.jsx)(o.$,{type:"button",variant:"outline",onClick:()=>s(!1),disabled:l,children:"Cancel"}),(0,r.jsx)(o.$,{type:"submit",disabled:l,children:l?"Creating...":"Create Candidate"})]})]})})]})}},55511:e=>{"use strict";e.exports=require("crypto")},61348:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(26373).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74998:e=>{"use strict";e.exports=require("perf_hooks")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},88804:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(26373).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},89667:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var r=s(60687),a=s(43210),i=s(7766);let n=a.forwardRef(({className:e,type:t,error:s,...a},n)=>(0,r.jsxs)("div",{className:"w-full",children:[(0,r.jsx)("input",{type:t,className:(0,i.cn)("flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:cursor-not-allowed disabled:opacity-50",s&&"border-red-500 focus:ring-red-500 focus:border-red-500",e),ref:n,...a}),s&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-500",children:s})]}));n.displayName="Input"},91645:e=>{"use strict";e.exports=require("net")},94092:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,85814,23)),Promise.resolve().then(s.bind(s,36282))},94288:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>k});var r=s(37413),a=s(26326),i=s(71682),n=s(32767),d=s(1463),l=s(94634),o=s(16048),c=s(30084),m=s(23469),u=s(26373);let x=(0,u.A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),h=(0,u.A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]),p=(0,u.A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]);var g=s(5148),j=s(88804),y=s(4536),f=s.n(y);function b({candidate:e,testCount:t}){return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"p-2 bg-blue-50 rounded-lg",children:(0,r.jsx)(x,{className:"h-6 w-6 text-blue-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900",children:e.fullName}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.nationality})]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[e.studentStatus&&(0,r.jsx)(c.E,{className:"bg-green-100 text-green-800",children:"Student"}),(0,r.jsxs)(c.E,{className:"bg-blue-100 text-blue-800",children:[t," tests"]})]})]}),(0,r.jsxs)("div",{className:"space-y-3 mb-4",children:[e.email&&(0,r.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,r.jsx)(h,{className:"h-4 w-4 mr-2"}),(0,r.jsx)("span",{className:"truncate",children:e.email})]}),e.phoneNumber&&(0,r.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,r.jsx)(p,{className:"h-4 w-4 mr-2"}),(0,r.jsx)("span",{children:e.phoneNumber})]}),(0,r.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,r.jsx)(g.A,{className:"h-4 w-4 mr-2"}),(0,r.jsx)("span",{children:e.passportNumber})]}),(0,r.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,r.jsx)(j.A,{className:"h-4 w-4 mr-2"}),(0,r.jsxs)("span",{children:["Registered ",new Date(e.createdAt).toLocaleDateString()]})]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(f(),{href:`/admin/candidates/${e.id}`,className:"flex-1",children:(0,r.jsx)(m.$,{variant:"outline",size:"sm",className:"w-full",children:"View Details"})}),(0,r.jsx)(f(),{href:`/admin/candidates/${e.id}/tests`,className:"flex-1",children:(0,r.jsxs)(m.$,{variant:"outline",size:"sm",className:"w-full",children:[(0,r.jsx)(j.A,{className:"h-4 w-4 mr-1"}),"Tests"]})})]})]})}var N=s(13316),v=s(78593),w=s(61348),A=s(19001),C=s(78768);async function k(){let e=await (0,a.j2)();if(!e?.user?.organizationId)return(0,r.jsx)("div",{children:"Access denied"});let t=await i.db.select({id:n.candidates.id,fullName:n.candidates.fullName,email:n.candidates.email,phoneNumber:n.candidates.phoneNumber,passportNumber:n.candidates.passportNumber,nationality:n.candidates.nationality,studentStatus:n.candidates.studentStatus,totalTests:n.candidates.totalTests,createdAt:n.candidates.createdAt,testCount:(0,d.U9)(n.testRegistrations.id)}).from(n.candidates).leftJoin(n.testRegistrations,(0,l.eq)(n.candidates.id,n.testRegistrations.candidateId)).where((0,l.eq)(n.candidates.organizationId,e.user.organizationId)).groupBy(n.candidates.id).orderBy((0,o.i)(n.candidates.createdAt)),s=t.length,c=t.filter(e=>e.studentStatus).length,u=t.filter(e=>e.testCount>0).length;return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Candidates"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Manage test candidates and their information"})]}),(0,r.jsx)(N.CreateCandidateModal,{children:(0,r.jsxs)(m.$,{children:[(0,r.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Add Candidate"]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(A.A,{className:"h-8 w-8 text-blue-600"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Candidates"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:s})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(A.A,{className:"h-8 w-8 text-green-600"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Students"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:c})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(A.A,{className:"h-8 w-8 text-purple-600"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:u})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(A.A,{className:"h-8 w-8 text-orange-600"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"New This Month"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:t.filter(e=>new Date(e.createdAt).getMonth()===new Date().getMonth()).length})]})]})})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(C.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,r.jsx)(v.p,{placeholder:"Search by name, email, or passport number...",className:"pl-10"})]})}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)("select",{className:"px-3 py-2 border border-gray-300 rounded-md text-sm",children:[(0,r.jsx)("option",{value:"",children:"All Candidates"}),(0,r.jsx)("option",{value:"student",children:"Students Only"}),(0,r.jsx)("option",{value:"regular",children:"Regular Only"})]}),(0,r.jsxs)("select",{className:"px-3 py-2 border border-gray-300 rounded-md text-sm",children:[(0,r.jsx)("option",{value:"",children:"All Nationalities"}),(0,r.jsx)("option",{value:"uzbekistan",children:"Uzbekistan"}),(0,r.jsx)("option",{value:"kazakhstan",children:"Kazakhstan"}),(0,r.jsx)("option",{value:"kyrgyzstan",children:"Kyrgyzstan"}),(0,r.jsx)("option",{value:"tajikistan",children:"Tajikistan"}),(0,r.jsx)("option",{value:"other",children:"Other"})]})]})]})}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:t.map(t=>(0,r.jsx)(b,{candidate:{id:t.id,fullName:t.fullName,email:t.email,phoneNumber:t.phoneNumber,passportNumber:t.passportNumber,nationality:t.nationality,studentStatus:t.studentStatus,totalTests:t.totalTests,createdAt:t.createdAt,organizationId:e.user.organizationId,dateOfBirth:null,photoData:null,updatedAt:t.createdAt},testCount:t.testCount},t.id))}),0===t.length&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(A.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,r.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No candidates"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Get started by adding your first candidate."}),(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsx)(N.CreateCandidateModal,{children:(0,r.jsxs)(m.$,{children:[(0,r.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Add Candidate"]})})})]})]})}},99111:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var r=s(37413),a=s(26326),i=s(39916),n=s(59105),d=s(10590);async function l({children:e}){let t=await (0,a.j2)();return t?.user||(0,i.redirect)("/login"),"admin"===t.user.role||t.user.masterAdmin||(0,i.redirect)("/checker"),(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(d.Header,{user:t.user}),(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)(n.Sidebar,{userRole:t.user.role,isMasterAdmin:t.user.masterAdmin}),(0,r.jsx)("main",{className:"flex-1 p-6",children:e})]})]})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,5552,2190,1057,145,1658,9069,5814,892,4017,367,6326,5807],()=>s(11663));module.exports=r})();