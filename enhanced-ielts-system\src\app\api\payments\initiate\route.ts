import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth/config';
import { z } from 'zod';
import { createPaymentTransaction, getFeaturePricing } from '@/lib/payments/utils';
import { PaymentGateway, FeatureType } from '@/lib/payments/types';

const initiatePaymentSchema = z.object({
  candidateId: z.string().min(1),
  featureType: z.enum(['feedback', 'certificate', 'progress']),
  gateway: z.enum(['click', 'payme', 'manual']),
  resultId: z.string().optional(),
  returnUrl: z.string().url(),
});

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = initiatePaymentSchema.parse(body);

    // Get feature pricing
    const pricing = getFeaturePricing(validatedData.featureType as FeatureType);

    // Create payment transaction
    const paymentResponse = await createPaymentTransaction({
      candidateId: validatedData.candidateId,
      organizationId: session.user.organizationId,
      amount: pricing.amount,
      currency: pricing.currency,
      gateway: validatedData.gateway as PaymentGateway,
      featureType: validatedData.featureType as FeatureType,
      resultId: validatedData.resultId,
      returnUrl: validatedData.returnUrl,
      description: pricing.description,
    });

    if (paymentResponse.status === 'failed') {
      return NextResponse.json(
        { error: paymentResponse.error },
        { status: 400 }
      );
    }

    return NextResponse.json({
      transactionId: paymentResponse.transactionId,
      paymentUrl: paymentResponse.paymentUrl,
      status: paymentResponse.status,
      amount: pricing.amount,
      currency: pricing.currency,
      description: pricing.description,
    });
  } catch (error) {
    console.error('Payment initiation error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Payment initiation failed' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const featureType = searchParams.get('featureType') as FeatureType;

    if (!featureType || !['feedback', 'certificate', 'progress'].includes(featureType)) {
      return NextResponse.json(
        { error: 'Invalid feature type' },
        { status: 400 }
      );
    }

    // Get pricing information
    const pricing = getFeaturePricing(featureType);

    return NextResponse.json({
      featureType,
      amount: pricing.amount,
      currency: pricing.currency,
      description: pricing.description,
      availableGateways: [
        {
          id: 'click',
          name: 'Click',
          description: 'Pay with Click - Fast and secure',
          processingTime: 'Instant',
        },
        {
          id: 'payme',
          name: 'Payme',
          description: 'Pay with Payme - Convenient mobile payments',
          processingTime: 'Instant',
        },
        {
          id: 'manual',
          name: 'Manual Payment',
          description: 'Bank transfer or cash payment with admin approval',
          processingTime: '1-3 business days',
        },
      ],
    });
  } catch (error) {
    console.error('Payment info error:', error);
    return NextResponse.json(
      { error: 'Failed to get payment information' },
      { status: 500 }
    );
  }
}
