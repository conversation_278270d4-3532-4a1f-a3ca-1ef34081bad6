'use client';

import { 
  CreditCard, 
  TrendingUp, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  DollarSign
} from 'lucide-react';
import { formatAmount } from '@/lib/payments/utils';

interface PaymentStatisticsProps {
  totalTransactions: number;
  completedPayments: number;
  pendingPayments: number;
  totalRevenue: number;
  pendingManualPayments: number;
}

export function PaymentStatistics({
  totalTransactions,
  completedPayments,
  pendingPayments,
  totalRevenue,
  pendingManualPayments,
}: PaymentStatisticsProps) {
  const completionRate = totalTransactions > 0 
    ? Math.round((completedPayments / totalTransactions) * 100) 
    : 0;

  const statistics = [
    {
      title: 'Total Transactions',
      value: totalTransactions.toLocaleString(),
      icon: <CreditCard className="h-8 w-8 text-blue-600" />,
      color: 'blue',
      description: 'All payment transactions',
    },
    {
      title: 'Total Revenue',
      value: formatAmount(totalRevenue, 'UZS'),
      icon: <DollarSign className="h-8 w-8 text-green-600" />,
      color: 'green',
      description: 'From completed payments',
    },
    {
      title: 'Completed Payments',
      value: completedPayments.toLocaleString(),
      icon: <CheckCircle className="h-8 w-8 text-green-600" />,
      color: 'green',
      description: `${completionRate}% completion rate`,
    },
    {
      title: 'Pending Payments',
      value: pendingPayments.toLocaleString(),
      icon: <Clock className="h-8 w-8 text-yellow-600" />,
      color: 'yellow',
      description: 'Awaiting completion',
    },
    {
      title: 'Manual Approvals',
      value: pendingManualPayments.toLocaleString(),
      icon: <AlertTriangle className="h-8 w-8 text-orange-600" />,
      color: 'orange',
      description: 'Require admin approval',
    },
    {
      title: 'Success Rate',
      value: `${completionRate}%`,
      icon: <TrendingUp className="h-8 w-8 text-purple-600" />,
      color: 'purple',
      description: 'Payment completion rate',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
      {statistics.map((stat, index) => (
        <div key={index} className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              {stat.icon}
            </div>
            <div className="ml-4 flex-1">
              <p className="text-sm font-medium text-gray-600">{stat.title}</p>
              <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
              <p className="text-xs text-gray-500 mt-1">{stat.description}</p>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
