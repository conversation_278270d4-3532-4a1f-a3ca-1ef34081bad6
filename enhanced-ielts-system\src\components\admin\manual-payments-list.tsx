'use client';

import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ManualPaymentApprovalModal } from './manual-payment-approval-modal';
import { 
  CreditCard, 
  User, 
  Calendar, 
  FileText, 
  CheckCircle, 
  XCircle,
  Clock,
  Eye,
  Building2,
  Smartphone
} from 'lucide-react';
import { formatAmount } from '@/lib/payments/utils';

interface Transaction {
  transaction: {
    id: string;
    candidateId: string;
    amount: string;
    currency: string;
    gateway: string;
    status: string;
    featureType: string;
    resultId: string | null;
    metadata: any;
    createdAt: Date;
    completedAt: Date | null;
  };
  candidate: {
    id: string;
    fullName: string;
    email: string | null;
    passportNumber: string;
  } | null;
}

interface ManualPaymentsListProps {
  transactions: Transaction[];
}

export function ManualPaymentsList({ transactions }: ManualPaymentsListProps) {
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [isApprovalModalOpen, setIsApprovalModalOpen] = useState(false);

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: 'yellow', label: 'Pending', icon: Clock },
      completed: { color: 'green', label: 'Completed', icon: CheckCircle },
      failed: { color: 'red', label: 'Failed', icon: XCircle },
      cancelled: { color: 'gray', label: 'Cancelled', icon: XCircle },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { 
      color: 'gray', 
      label: status, 
      icon: Clock 
    };
    
    const IconComponent = config.icon;
    
    return (
      <Badge variant={config.color as any} className="flex items-center">
        <IconComponent className="h-3 w-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const getGatewayIcon = (gateway: string) => {
    switch (gateway) {
      case 'click':
        return <CreditCard className="h-4 w-4 text-blue-600" />;
      case 'payme':
        return <Smartphone className="h-4 w-4 text-green-600" />;
      case 'manual':
        return <Building2 className="h-4 w-4 text-orange-600" />;
      default:
        return <CreditCard className="h-4 w-4 text-gray-600" />;
    }
  };

  const getFeatureTypeLabel = (featureType: string) => {
    const labels = {
      feedback: 'AI Feedback',
      certificate: 'Certificate',
      progress: 'Progress Analytics',
    };
    return labels[featureType as keyof typeof labels] || featureType;
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handleApprovePayment = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    setIsApprovalModalOpen(true);
  };

  const handleApprovalComplete = () => {
    setIsApprovalModalOpen(false);
    setSelectedTransaction(null);
    // Refresh the page to show updated data
    window.location.reload();
  };

  return (
    <>
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Payment Transactions</h3>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Transaction
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Candidate
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Feature
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Gateway
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {transactions.map((transaction) => (
                <tr key={transaction.transaction.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm">
                      <div className="font-medium text-gray-900">
                        #{transaction.transaction.id.slice(-8)}
                      </div>
                      {transaction.transaction.metadata?.referenceNumber && (
                        <div className="text-gray-500">
                          Ref: {transaction.transaction.metadata.referenceNumber}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <User className="h-8 w-8 text-gray-400 mr-3" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {transaction.candidate?.fullName || 'Unknown'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {transaction.candidate?.email || transaction.candidate?.passportNumber}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {formatAmount(parseFloat(transaction.transaction.amount), transaction.transaction.currency)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {getFeatureTypeLabel(transaction.transaction.featureType)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getGatewayIcon(transaction.transaction.gateway)}
                      <span className="ml-2 text-sm text-gray-900 capitalize">
                        {transaction.transaction.gateway}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(transaction.transaction.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {formatDate(transaction.transaction.createdAt)}
                    </div>
                    {transaction.transaction.completedAt && (
                      <div className="text-xs text-gray-500">
                        Completed: {formatDate(transaction.transaction.completedAt)}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleApprovePayment(transaction)}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                      {transaction.transaction.gateway === 'manual' && 
                       transaction.transaction.status === 'pending' && (
                        <Button
                          size="sm"
                          onClick={() => handleApprovePayment(transaction)}
                        >
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Review
                        </Button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {transactions.length === 0 && (
          <div className="text-center py-12">
            <CreditCard className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No transactions found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Payment transactions will appear here once candidates start purchasing features.
            </p>
          </div>
        )}
      </div>

      {/* Manual Payment Approval Modal */}
      {selectedTransaction && (
        <ManualPaymentApprovalModal
          isOpen={isApprovalModalOpen}
          onClose={() => setIsApprovalModalOpen(false)}
          transaction={selectedTransaction}
          onApprovalComplete={handleApprovalComplete}
        />
      )}
    </>
  );
}
