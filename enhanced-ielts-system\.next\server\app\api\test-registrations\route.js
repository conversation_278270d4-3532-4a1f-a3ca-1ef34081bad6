(()=>{var e={};e.id=3371,e.ids=[3371],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9093:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>R,routeModule:()=>f,serverHooks:()=>h,workAsyncStorage:()=>q,workUnitAsyncStorage:()=>w});var s={};r.r(s),r.d(s,{GET:()=>m,POST:()=>x});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),d=r(26326),u=r(71682),c=r(32767),p=r(94634),g=r(45697);let l=g.z.object({candidateId:g.z.string().min(1),testDate:g.z.string().min(1),testCenter:g.z.string().min(1),candidateNumber:g.z.string().min(1)});async function x(e){try{let t=await (0,d.j2)();if(!t?.user?.organizationId)return o.NextResponse.json({error:"Unauthorized"},{status:401});if("admin"!==t.user.role&&!t.user.masterAdmin)return o.NextResponse.json({error:"Insufficient permissions"},{status:403});let r=await e.json(),s=l.parse(r),a=await u.db.select().from(c.candidates).where((0,p.Uo)((0,p.eq)(c.candidates.id,s.candidateId),(0,p.eq)(c.candidates.organizationId,t.user.organizationId))).limit(1);if(0===a.length)return o.NextResponse.json({error:"Candidate not found or does not belong to your organization"},{status:404});if((await u.db.select().from(c.testRegistrations).where((0,p.Uo)((0,p.eq)(c.testRegistrations.candidateNumber,s.candidateNumber),(0,p.eq)(c.testRegistrations.testDate,new Date(s.testDate)))).limit(1)).length>0)return o.NextResponse.json({error:"Candidate number already exists for this test date"},{status:409});let[i]=await u.db.insert(c.testRegistrations).values({candidateId:s.candidateId,candidateNumber:s.candidateNumber,testDate:new Date(s.testDate),testCenter:s.testCenter,status:"registered"}).returning();return await u.db.update(c.candidates).set({totalTests:a[0].totalTests+1,updatedAt:new Date}).where((0,p.eq)(c.candidates.id,s.candidateId)),o.NextResponse.json(i,{status:201})}catch(e){if(console.error("Error creating test registration:",e),e instanceof g.z.ZodError)return o.NextResponse.json({error:"Invalid data",details:e.errors},{status:400});return o.NextResponse.json({error:"Failed to create test registration"},{status:500})}}async function m(e){try{let t=await (0,d.j2)();if(!t?.user?.organizationId)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),s=r.get("candidateId"),a=r.get("status"),i=u.db.select({registration:c.testRegistrations,candidate:c.candidates}).from(c.testRegistrations).leftJoin(c.candidates,(0,p.eq)(c.testRegistrations.candidateId,c.candidates.id)).where((0,p.eq)(c.candidates.organizationId,t.user.organizationId));s&&(i=i.where((0,p.Uo)((0,p.eq)(c.candidates.organizationId,t.user.organizationId),(0,p.eq)(c.testRegistrations.candidateId,s)))),a&&(i=i.where((0,p.Uo)((0,p.eq)(c.candidates.organizationId,t.user.organizationId),(0,p.eq)(c.testRegistrations.status,a))));let n=await i;return o.NextResponse.json(n)}catch(e){return console.error("Error fetching test registrations:",e),o.NextResponse.json({error:"Failed to fetch test registrations"},{status:500})}}let f=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/test-registrations/route",pathname:"/api/test-registrations",filename:"route",bundlePath:"app/api/test-registrations/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\test-registrations\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:q,workUnitAsyncStorage:w,serverHooks:h}=f;function R(){return(0,n.patchFetch)({workAsyncStorage:q,workUnitAsyncStorage:w})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74998:e=>{"use strict";e.exports=require("perf_hooks")},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},91645:e=>{"use strict";e.exports=require("net")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,5552,2190,1057,1595,6326],()=>r(9093));module.exports=s})();