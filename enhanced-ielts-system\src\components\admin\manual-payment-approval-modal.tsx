'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Modal } from '@/components/ui/modal';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  CheckCircle, 
  XCircle, 
  User, 
  CreditCard, 
  Calendar, 
  FileText,
  Building2,
  Loader2,
  AlertTriangle
} from 'lucide-react';
import { formatAmount } from '@/lib/payments/utils';

const approvalSchema = z.object({
  action: z.enum(['approve', 'reject']),
  notes: z.string().optional(),
});

type ApprovalForm = z.infer<typeof approvalSchema>;

interface Transaction {
  transaction: {
    id: string;
    candidateId: string;
    amount: string;
    currency: string;
    gateway: string;
    status: string;
    featureType: string;
    resultId: string | null;
    metadata: any;
    createdAt: Date;
    completedAt: Date | null;
  };
  candidate: {
    id: string;
    fullName: string;
    email: string | null;
    passportNumber: string;
  } | null;
}

interface ManualPaymentApprovalModalProps {
  isOpen: boolean;
  onClose: () => void;
  transaction: Transaction;
  onApprovalComplete: () => void;
}

export function ManualPaymentApprovalModal({
  isOpen,
  onClose,
  transaction,
  onApprovalComplete,
}: ManualPaymentApprovalModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<ApprovalForm>({
    resolver: zodResolver(approvalSchema),
  });

  const action = watch('action');

  const getFeatureTypeLabel = (featureType: string) => {
    const labels = {
      feedback: 'AI Feedback',
      certificate: 'Certificate',
      progress: 'Progress Analytics',
    };
    return labels[featureType as keyof typeof labels] || featureType;
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const onSubmit = async (data: ApprovalForm) => {
    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/payments/manual/${transaction.transaction.id}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to process approval');
      }

      onApprovalComplete();
    } catch (error) {
      console.error('Approval error:', error);
      alert('Failed to process approval. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="large">
      <div className="p-6">
        <div className="flex items-center mb-6">
          <Building2 className="h-6 w-6 text-orange-600 mr-2" />
          <h2 className="text-xl font-semibold text-gray-900">Manual Payment Review</h2>
          <Badge variant="yellow" className="ml-3">
            Pending Approval
          </Badge>
        </div>

        {/* Transaction Details */}
        <div className="bg-gray-50 rounded-lg p-6 mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Transaction Details</h3>
          
          <div className="grid grid-cols-2 gap-6">
            <div>
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-600">Transaction ID</label>
                  <div className="text-sm text-gray-900 font-mono">
                    #{transaction.transaction.id}
                  </div>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-gray-600">Amount</label>
                  <div className="text-lg font-bold text-gray-900">
                    {formatAmount(parseFloat(transaction.transaction.amount), transaction.transaction.currency)}
                  </div>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-gray-600">Feature Type</label>
                  <div className="text-sm text-gray-900">
                    {getFeatureTypeLabel(transaction.transaction.featureType)}
                  </div>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-gray-600">Submitted Date</label>
                  <div className="text-sm text-gray-900">
                    {formatDate(transaction.transaction.createdAt)}
                  </div>
                </div>
              </div>
            </div>
            
            <div>
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-600">Candidate</label>
                  <div className="flex items-center">
                    <User className="h-4 w-4 text-gray-400 mr-2" />
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {transaction.candidate?.fullName || 'Unknown'}
                      </div>
                      <div className="text-xs text-gray-500">
                        {transaction.candidate?.passportNumber}
                      </div>
                    </div>
                  </div>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-gray-600">Email</label>
                  <div className="text-sm text-gray-900">
                    {transaction.candidate?.email || 'Not provided'}
                  </div>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-gray-600">Payment Method</label>
                  <div className="text-sm text-gray-900 capitalize">
                    {transaction.transaction.metadata?.paymentMethod?.replace('_', ' ') || 'Not specified'}
                  </div>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-gray-600">Reference Number</label>
                  <div className="text-sm text-gray-900 font-mono">
                    {transaction.transaction.metadata?.referenceNumber || 'Not provided'}
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Payment Date */}
          {transaction.transaction.metadata?.paymentDate && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <label className="text-sm font-medium text-gray-600">Payment Date (as reported)</label>
              <div className="text-sm text-gray-900">
                {new Date(transaction.transaction.metadata.paymentDate).toLocaleDateString()}
              </div>
            </div>
          )}
          
          {/* Additional Notes */}
          {transaction.transaction.metadata?.notes && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <label className="text-sm font-medium text-gray-600">Additional Notes</label>
              <div className="text-sm text-gray-900 mt-1 p-3 bg-white rounded border">
                {transaction.transaction.metadata.notes}
              </div>
            </div>
          )}
        </div>

        {/* Approval Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Approval Decision
            </label>
            <div className="grid grid-cols-2 gap-4">
              <label className="relative">
                <input
                  type="radio"
                  value="approve"
                  {...register('action')}
                  className="sr-only"
                />
                <div className={`
                  border-2 rounded-lg p-4 cursor-pointer transition-all
                  ${action === 'approve' 
                    ? 'border-green-500 bg-green-50' 
                    : 'border-gray-200 hover:border-gray-300'
                  }
                `}>
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                    <span className="font-medium text-green-900">Approve Payment</span>
                  </div>
                  <p className="text-sm text-green-700 mt-1">
                    Grant access to the premium feature
                  </p>
                </div>
              </label>

              <label className="relative">
                <input
                  type="radio"
                  value="reject"
                  {...register('action')}
                  className="sr-only"
                />
                <div className={`
                  border-2 rounded-lg p-4 cursor-pointer transition-all
                  ${action === 'reject' 
                    ? 'border-red-500 bg-red-50' 
                    : 'border-gray-200 hover:border-gray-300'
                  }
                `}>
                  <div className="flex items-center">
                    <XCircle className="h-5 w-5 text-red-600 mr-2" />
                    <span className="font-medium text-red-900">Reject Payment</span>
                  </div>
                  <p className="text-sm text-red-700 mt-1">
                    Decline the payment request
                  </p>
                </div>
              </label>
            </div>
            {errors.action && (
              <p className="text-red-500 text-sm mt-1">{errors.action.message}</p>
            )}
          </div>

          {/* Admin Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Admin Notes (Optional)
            </label>
            <textarea
              {...register('notes')}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Add any notes about this approval decision..."
            />
          </div>

          {/* Warning for Rejection */}
          {action === 'reject' && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-start">
                <AlertTriangle className="h-5 w-5 text-red-600 mr-3 mt-0.5" />
                <div className="text-sm text-red-800">
                  <div className="font-medium mb-1">Payment Rejection</div>
                  <div>
                    Rejecting this payment will notify the candidate that their payment was not approved. 
                    They will not gain access to the premium feature.
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={isSubmitting || !action}
              variant={action === 'approve' ? 'default' : 'destructive'}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : action === 'approve' ? (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Approve Payment
                </>
              ) : (
                <>
                  <XCircle className="h-4 w-4 mr-2" />
                  Reject Payment
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
}
