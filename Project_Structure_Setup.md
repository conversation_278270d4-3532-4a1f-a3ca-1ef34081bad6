# Enhanced IELTS System - Project Structure & Initial Setup

## 📁 Complete Project Structure

```
enhanced-ielts-system/
├── 📁 src/
│   ├── 📁 app/                          # Next.js App Router
│   │   ├── 📁 (auth)/                   # Auth route group
│   │   │   ├── 📁 login/
│   │   │   │   └── page.tsx
│   │   │   └── 📁 register/
│   │   │       └── page.tsx
│   │   ├── 📁 (public)/                 # Public routes
│   │   │   ├── 📁 search/
│   │   │   │   └── page.tsx
│   │   │   ├── 📁 results/
│   │   │   │   └── 📁 [id]/
│   │   │   │       └── page.tsx         # Tabbed results interface
│   │   │   └── 📁 verify/
│   │   │       └── 📁 [serial]/
│   │   │           └── page.tsx
│   │   ├── 📁 master/                   # Master admin routes
│   │   │   ├── 📁 dashboard/
│   │   │   ├── 📁 organizations/
│   │   │   ├── 📁 analytics/
│   │   │   └── layout.tsx
│   │   ├── 📁 admin/                    # Organization admin routes
│   │   │   ├── 📁 dashboard/
│   │   │   ├── 📁 candidates/
│   │   │   ├── 📁 results/
│   │   │   ├── 📁 promotions/
│   │   │   ├── 📁 payments/
│   │   │   └── layout.tsx
│   │   ├── 📁 checker/                  # Test checker routes
│   │   │   ├── 📁 dashboard/
│   │   │   ├── 📁 entry/
│   │   │   ├── 📁 results/
│   │   │   └── layout.tsx
│   │   ├── 📁 api/                      # API routes
│   │   │   ├── 📁 auth/
│   │   │   │   └── 📁 [...nextauth]/
│   │   │   │       └── route.ts
│   │   │   ├── 📁 master/
│   │   │   │   ├── 📁 organizations/
│   │   │   │   └── 📁 analytics/
│   │   │   ├── 📁 organizations/
│   │   │   │   └── 📁 [id]/
│   │   │   ├── 📁 candidates/
│   │   │   │   ├── 📁 [id]/
│   │   │   │   └── 📁 progress/
│   │   │   ├── 📁 results/
│   │   │   │   └── 📁 [id]/
│   │   │   ├── 📁 payments/
│   │   │   │   ├── 📁 initiate/
│   │   │   │   ├── 📁 webhook/
│   │   │   │   └── 📁 verify/
│   │   │   ├── 📁 promotions/
│   │   │   │   ├── 📁 rules/
│   │   │   │   └── 📁 eligibility/
│   │   │   ├── 📁 certificates/
│   │   │   │   ├── 📁 generate/
│   │   │   │   └── 📁 download/
│   │   │   ├── 📁 ai/
│   │   │   │   └── 📁 feedback/
│   │   │   └── 📁 public/
│   │   │       ├── 📁 search/
│   │   │       └── 📁 results/
│   │   ├── globals.css
│   │   ├── layout.tsx
│   │   ├── page.tsx
│   │   └── not-found.tsx
│   ├── 📁 components/                   # Reusable components
│   │   ├── 📁 ui/                       # Base UI components
│   │   │   ├── button.tsx
│   │   │   ├── input.tsx
│   │   │   ├── modal.tsx
│   │   │   ├── card.tsx
│   │   │   ├── badge.tsx
│   │   │   ├── table.tsx
│   │   │   ├── tabs.tsx
│   │   │   ├── chart.tsx
│   │   │   └── index.ts
│   │   ├── 📁 forms/                    # Form components
│   │   │   ├── candidate-form.tsx
│   │   │   ├── result-entry-form.tsx
│   │   │   ├── payment-form.tsx
│   │   │   └── promotion-form.tsx
│   │   ├── 📁 paywall/                  # Paywall components
│   │   │   ├── paywall-overlay.tsx
│   │   │   ├── payment-modal.tsx
│   │   │   ├── unlock-button.tsx
│   │   │   └── pricing-card.tsx
│   │   ├── 📁 results/                  # Results display
│   │   │   ├── results-tab.tsx
│   │   │   ├── progress-tab.tsx
│   │   │   ├── feedback-tab.tsx
│   │   │   ├── certificate-tab.tsx
│   │   │   └── score-card.tsx
│   │   ├── 📁 charts/                   # Chart components
│   │   │   ├── progress-chart.tsx
│   │   │   ├── score-comparison.tsx
│   │   │   └── analytics-dashboard.tsx
│   │   ├── 📁 layout/                   # Layout components
│   │   │   ├── header.tsx
│   │   │   ├── sidebar.tsx
│   │   │   ├── footer.tsx
│   │   │   └── breadcrumbs.tsx
│   │   └── 📁 specialized/              # Specialized components
│   │       ├── certificate-preview.tsx
│   │       ├── promotion-banner.tsx
│   │       ├── student-badge.tsx
│   │       └── organization-selector.tsx
│   ├── 📁 lib/                          # Core utilities
│   │   ├── 📁 db/                       # Database layer
│   │   │   ├── index.ts
│   │   │   ├── schema.ts
│   │   │   ├── migrations/
│   │   │   └── seed.ts
│   │   ├── 📁 auth/                     # Authentication
│   │   │   ├── config.ts
│   │   │   ├── providers.ts
│   │   │   └── middleware.ts
│   │   ├── 📁 payments/                 # Payment processing
│   │   │   ├── click.ts
│   │   │   ├── payme.ts
│   │   │   ├── types.ts
│   │   │   └── utils.ts
│   │   ├── 📁 ai/                       # AI integration
│   │   │   ├── claude.ts
│   │   │   ├── feedback-generator.ts
│   │   │   └── prompts.ts
│   │   ├── 📁 certificates/             # Certificate generation
│   │   │   ├── generator.ts
│   │   │   ├── templates.ts
│   │   │   └── lifecycle.ts
│   │   ├── 📁 promotions/               # Promotional system
│   │   │   ├── engine.ts
│   │   │   ├── rules.ts
│   │   │   └── eligibility.ts
│   │   ├── 📁 utils/                    # Utility functions
│   │   │   ├── validation.ts
│   │   │   ├── formatting.ts
│   │   │   ├── encryption.ts
│   │   │   ├── email.ts
│   │   │   └── constants.ts
│   │   └── 📁 hooks/                    # Custom React hooks
│   │       ├── use-auth.ts
│   │       ├── use-payment.ts
│   │       ├── use-promotion.ts
│   │       └── use-organization.ts
│   ├── 📁 types/                        # TypeScript definitions
│   │   ├── auth.ts
│   │   ├── database.ts
│   │   ├── payment.ts
│   │   ├── promotion.ts
│   │   ├── api.ts
│   │   └── global.d.ts
│   └── 📁 styles/                       # Styling
│       ├── globals.css
│       ├── components.css
│       └── paywall.css
├── 📁 public/                           # Static assets
│   ├── 📁 images/
│   │   ├── 📁 logos/
│   │   ├── 📁 icons/
│   │   ├── 📁 certificates/
│   │   └── 📁 paywall/
│   ├── 📁 fonts/
│   └── favicon.ico
├── 📁 scripts/                          # Utility scripts
│   ├── setup-db.ts
│   ├── seed-data.ts
│   ├── create-master-admin.ts
│   ├── migrate.ts
│   └── health-check.ts
├── 📁 docs/                             # Documentation
│   ├── api.md
│   ├── deployment.md
│   ├── database.md
│   └── development.md
├── 📁 tests/                            # Test files
│   ├── 📁 __mocks__/
│   ├── 📁 components/
│   ├── 📁 api/
│   ├── 📁 integration/
│   └── setup.ts
├── .env.local                           # Environment variables
├── .env.example                         # Environment template
├── .gitignore
├── package.json
├── package-lock.json
├── next.config.ts
├── tailwind.config.ts
├── drizzle.config.ts
├── tsconfig.json
├── eslint.config.mjs
├── prettier.config.js
└── README.md
```

---

## 🚀 Initial Setup Commands

### 1. Project Initialization
```bash
# Create new Next.js project
npx create-next-app@latest enhanced-ielts-system --typescript --tailwind --eslint --app --src-dir

# Navigate to project directory
cd enhanced-ielts-system

# Install additional dependencies
npm install @auth/drizzle-adapter @paralleldrive/cuid2 bcryptjs drizzle-orm drizzle-kit postgres next-auth@beta @anthropic-ai/sdk jspdf html2canvas react-hook-form @hookform/resolvers zod lucide-react @radix-ui/react-dialog @radix-ui/react-dropdown-menu @radix-ui/react-select @radix-ui/react-toast @radix-ui/react-tabs recharts zustand @tanstack/react-query

# Install dev dependencies
npm install -D @types/bcryptjs @types/pg prettier prettier-plugin-tailwindcss husky lint-staged
```

### 2. Environment Configuration
```bash
# Create environment files
cp .env.example .env.local

# Edit .env.local with your configuration
```

### 3. Database Setup
```bash
# Generate initial migration
npm run db:generate

# Apply migrations
npm run db:migrate

# Seed initial data
npm run db:seed

# Create master admin
npm run setup:master-admin
```

---

## 📦 Package.json Configuration

```json
{
  "name": "enhanced-ielts-system",
  "version": "1.0.0",
  "private": true,
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "lint:fix": "next lint --fix",
    "type-check": "tsc --noEmit",
    "db:generate": "drizzle-kit generate",
    "db:migrate": "drizzle-kit migrate",
    "db:studio": "drizzle-kit studio",
    "db:seed": "tsx scripts/seed-data.ts",
    "db:setup": "tsx scripts/setup-db.ts",
    "setup:master-admin": "tsx scripts/create-master-admin.ts",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:e2e": "playwright test",
    "format": "prettier --write .",
    "format:check": "prettier --check .",
    "prepare": "husky install"
  },
  "dependencies": {
    "@anthropic-ai/sdk": "^0.52.0",
    "@auth/drizzle-adapter": "^1.9.1",
    "@hookform/resolvers": "^5.0.1",
    "@paralleldrive/cuid2": "^2.2.2",
    "@radix-ui/react-dialog": "^1.1.14",
    "@radix-ui/react-dropdown-menu": "^2.1.15",
    "@radix-ui/react-select": "^2.2.5",
    "@radix-ui/react-tabs": "^1.1.14",
    "@radix-ui/react-toast": "^1.2.14",
    "@tanstack/react-query": "^5.0.0",
    "bcryptjs": "^3.0.2",
    "drizzle-orm": "^0.43.1",
    "html2canvas": "^1.4.1",
    "jspdf": "^3.0.1",
    "lucide-react": "^0.511.0",
    "next": "15.1.8",
    "next-auth": "^5.0.0-beta.28",
    "postgres": "^3.4.7",
    "react": "^19.0.0",
    "react-dom": "^19.0.0",
    "react-hook-form": "^7.56.4",
    "recharts": "^2.12.7",
    "zod": "^3.25.28",
    "zustand": "^5.0.0"
  },
  "devDependencies": {
    "@types/bcryptjs": "^2.4.6",
    "@types/node": "^20",
    "@types/pg": "^8.15.2",
    "@types/react": "^19",
    "@types/react-dom": "^19",
    "drizzle-kit": "^0.31.1",
    "eslint": "^9",
    "eslint-config-next": "15.1.8",
    "husky": "^9.0.0",
    "jest": "^29.7.0",
    "lint-staged": "^15.0.0",
    "playwright": "^1.40.0",
    "prettier": "^3.0.0",
    "prettier-plugin-tailwindcss": "^0.5.0",
    "tsx": "^4.19.0",
    "typescript": "^5"
  }
}
```

---

## ⚙️ Configuration Files

### Next.js Configuration
```typescript
// next.config.ts
import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  serverExternalPackages: ['postgres', 'bcryptjs'],
  images: {
    domains: ['localhost'],
    formats: ['image/webp', 'image/avif'],
  },
  experimental: {
    serverComponentsExternalPackages: ['postgres'],
  },
};

export default nextConfig;
```

### Tailwind Configuration
```typescript
// tailwind.config.ts
import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#0066CC',
          600: '#004499',
          700: '#1d4ed8',
        },
        paywall: {
          gold: '#FFD700',
          premium: '#8B5CF6',
          overlay: 'rgba(0, 0, 0, 0.8)',
        },
      },
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
        heading: ['Poppins', 'sans-serif'],
      },
    },
  },
  plugins: [],
};

export default config;
```

### Drizzle Configuration
```typescript
// drizzle.config.ts
import { defineConfig } from 'drizzle-kit';

export default defineConfig({
  schema: './src/lib/db/schema.ts',
  out: './src/lib/db/migrations',
  dialect: 'postgresql',
  dbCredentials: {
    url: process.env.DATABASE_URL!,
  },
  verbose: true,
  strict: true,
});
```

---

## 🔧 Development Tools Setup

### ESLint Configuration
```javascript
// eslint.config.mjs
import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    rules: {
      "@typescript-eslint/no-unused-vars": "error",
      "@typescript-eslint/no-explicit-any": "warn",
    },
  },
];

export default eslintConfig;
```

### Prettier Configuration
```javascript
// prettier.config.js
module.exports = {
  semi: true,
  trailingComma: 'es5',
  singleQuote: true,
  printWidth: 80,
  tabWidth: 2,
  plugins: ['prettier-plugin-tailwindcss'],
};
```

This complete project structure and setup guide provides everything needed to start building the Enhanced IELTS Certification System from scratch with proper organization and tooling.
