(()=>{var e={};e.id=149,e.ids=[149],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4536:(e,t,s)=>{let{createProxy:r}=s(39844);e.exports=r("C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\node_modules\\next\\dist\\client\\app-dir\\link.js")},7766:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var r=s(49384),a=s(82348);function n(...e){return(0,a.QP)((0,r.$)(e))}},10355:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(37413),a=s(26326),n=s(39916),i=s(59105),l=s(10590);async function d({children:e}){let t=await (0,a.j2)();return t?.user||(0,n.redirect)("/login"),"checker"===t.user.role||"admin"===t.user.role||t.user.masterAdmin||(0,n.redirect)("/login"),(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(l.Header,{user:t.user}),(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)(i.Sidebar,{userRole:t.user.role,isMasterAdmin:t.user.masterAdmin}),(0,r.jsx)("main",{className:"flex-1 p-6",children:e})]})]})}},10590:(e,t,s)=>{"use strict";s.d(t,{Header:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\layout\\header.tsx","Header")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},22739:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>o});var r=s(65239),a=s(48088),n=s(88170),i=s.n(n),l=s(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let o={children:["",{children:["checker",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,60696)),"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\checker\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,10355)),"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\checker\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\checker\\dashboard\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/checker/dashboard/page",pathname:"/checker/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},23469:(e,t,s)=>{"use strict";s.d(t,{$:()=>i});var r=s(37413),a=s(61120),n=s(72984);let i=a.forwardRef(({className:e,variant:t="default",size:s="default",...a},i)=>(0,r.jsx)("button",{className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{"bg-blue-600 text-white hover:bg-blue-700":"default"===t,"bg-red-600 text-white hover:bg-red-700":"destructive"===t,"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50":"outline"===t,"bg-gray-100 text-gray-900 hover:bg-gray-200":"secondary"===t,"hover:bg-gray-100 hover:text-gray-900":"ghost"===t,"text-blue-600 underline-offset-4 hover:underline":"link"===t},{"h-10 px-4 py-2":"default"===s,"h-9 rounded-md px-3":"sm"===s,"h-11 rounded-md px-8":"lg"===s,"h-10 w-10":"icon"===s},e),ref:i,...a}));i.displayName="Button"},27467:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(26373).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,t,s)=>{"use strict";s.d(t,{$:()=>i});var r=s(60687),a=s(43210),n=s(7766);let i=a.forwardRef(({className:e,variant:t="default",size:s="default",...a},i)=>(0,r.jsx)("button",{className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{"bg-blue-600 text-white hover:bg-blue-700":"default"===t,"bg-red-600 text-white hover:bg-red-700":"destructive"===t,"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50":"outline"===t,"bg-gray-100 text-gray-900 hover:bg-gray-200":"secondary"===t,"hover:bg-gray-100 hover:text-gray-900":"ghost"===t,"text-blue-600 underline-offset-4 hover:underline":"link"===t},{"h-10 px-4 py-2":"default"===s,"h-9 rounded-md px-3":"sm"===s,"h-11 rounded-md px-8":"lg"===s,"h-10 w-10":"icon"===s},e),ref:i,...a}));i.displayName="Button"},30084:(e,t,s)=>{"use strict";s.d(t,{E:()=>n});var r=s(37413);s(61120);var a=s(72984);function n({className:e,variant:t="default",...s}){return(0,r.jsx)("div",{className:(0,a.cn)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{"border-transparent bg-primary text-primary-foreground hover:bg-primary/80":"default"===t,"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80":"secondary"===t,"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80":"destructive"===t,"text-foreground":"outline"===t,"border-transparent bg-blue-100 text-blue-800":"blue"===t,"border-transparent bg-green-100 text-green-800":"green"===t,"border-transparent bg-yellow-100 text-yellow-800":"yellow"===t,"border-transparent bg-red-100 text-red-800":"red"===t,"border-transparent bg-purple-100 text-purple-800":"purple"===t,"border-transparent bg-orange-100 text-orange-800":"orange"===t,"border-transparent bg-gray-100 text-gray-800":"gray"===t},e),...s})}},30599:(e,t,s)=>{"use strict";s.d(t,{Sidebar:()=>g});var r=s(60687),a=s(85814),n=s.n(a),i=s(16189),l=s(7766),d=s(49625),o=s(17313),c=s(53411),u=s(41312),m=s(10022),x=s(85778),h=s(80428),p=s(6727);function g({userRole:e,isMasterAdmin:t}){let s=(0,i.usePathname)(),a=[{href:"/master/dashboard",label:"Master Dashboard",icon:d.A},{href:"/master/organizations",label:"Organizations",icon:o.A},{href:"/master/analytics",label:"Analytics",icon:c.A}],g=[{href:"/admin/dashboard",label:"Dashboard",icon:d.A},{href:"/admin/candidates",label:"Candidates",icon:u.A},{href:"/admin/results",label:"Test Results",icon:m.A},{href:"/admin/payments",label:"Payments",icon:x.A},{href:"/admin/promotions",label:"Promotions",icon:h.A}],b=[{href:"/checker/dashboard",label:"Dashboard",icon:d.A},{href:"/checker/entry",label:"Result Entry",icon:p.A},{href:"/checker/results",label:"My Results",icon:m.A}],f=t?[...a,...g]:"admin"===e?g:b;return(0,r.jsx)("aside",{className:"w-64 bg-white shadow-sm border-r border-gray-200 min-h-screen",children:(0,r.jsx)("nav",{className:"mt-8 px-4",children:(0,r.jsx)("ul",{className:"space-y-2",children:f.map(e=>{let t=e.icon,a=s===e.href||s.startsWith(e.href+"/");return(0,r.jsx)("li",{children:(0,r.jsxs)(n(),{href:e.href,className:(0,l.cn)("flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors",a?"bg-blue-50 text-blue-700 border-r-2 border-blue-700":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"),children:[(0,r.jsx)(t,{className:"mr-3 h-5 w-5"}),e.label]})},e.href)})})})})}},32021:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4536,23))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37882:()=>{},43648:(e,t,s)=>{"use strict";s.d(t,{C:()=>a});var r=s(37413);function a({title:e,value:t,icon:s,description:a,trend:n}){return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e}),(0,r.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:t.toLocaleString()})]}),(0,r.jsx)("div",{className:"p-3 bg-blue-50 rounded-full",children:(0,r.jsx)(s,{className:"h-6 w-6 text-blue-600"})})]}),(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:a}),(0,r.jsx)("p",{className:"text-sm text-green-600 font-medium",children:n})]})]})}},50259:(e,t,s)=>{Promise.resolve().then(s.bind(s,74456)),Promise.resolve().then(s.bind(s,30599))},55511:e=>{"use strict";e.exports=require("crypto")},59105:(e,t,s)=>{"use strict";s.d(t,{Sidebar:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\layout\\sidebar.tsx","Sidebar")},60696:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>N});var r=s(37413),a=s(26326),n=s(71682),i=s(32767),l=s(1463),d=s(94634),o=s(16048),c=s(43648),u=s(30084),m=s(35407);function x({results:e}){let t=e=>{switch(e){case"completed":return"bg-green-100 text-green-800";case"verified":return"bg-blue-100 text-blue-800";case"draft":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},s=e=>{if(!e)return"text-gray-500";let t=parseFloat(e);return t>=8?"text-green-600 font-semibold":t>=7?"text-blue-600 font-semibold":t>=6?"text-yellow-600 font-semibold":"text-red-600 font-semibold"};return 0===e.length?(0,r.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No results found. Start by entering your first test result."}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Candidate"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Candidate Number"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Overall Score"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.candidateName})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("div",{className:"text-sm text-gray-900",children:e.candidateNumber})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("div",{className:`text-sm ${s(e.overallBandScore)}`,children:e.overallBandScore?`Band ${e.overallBandScore}`:"Not set"})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)(u.E,{className:t(e.status),children:e.status})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,m.m)(new Date(e.createdAt),{addSuffix:!0})})]},e.id))})]})})}var h=s(88804),p=s(26373);let g=(0,p.A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),b=(0,p.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);var f=s(27467),y=s(23469),v=s(4536),j=s.n(v);async function N(){let e=await (0,a.j2)();if(!e?.user?.organizationId)return(0,r.jsx)("div",{children:"Access denied"});let[t,s,u,m]=await Promise.all([n.db.select({count:(0,l.U9)()}).from(i.testResults).where((0,d.eq)(i.testResults.enteredBy,e.user.id)),n.db.select({count:(0,l.U9)()}).from(i.testResults).where((0,d.Uo)((0,d.eq)(i.testResults.enteredBy,e.user.id),(0,d.eq)(i.testResults.status,"draft"))),n.db.select({count:(0,l.U9)()}).from(i.testResults).where((0,d.Uo)((0,d.eq)(i.testResults.enteredBy,e.user.id),(0,d.eq)(i.testResults.status,"completed"),(0,d.RO)(i.testResults.createdAt,new Date(new Date().getFullYear(),new Date().getMonth(),1)))),n.db.select({id:i.testResults.id,overallBandScore:i.testResults.overallBandScore,status:i.testResults.status,createdAt:i.testResults.createdAt,candidateName:i.candidates.fullName,candidateNumber:i.testRegistrations.candidateNumber}).from(i.testResults).innerJoin(i.testRegistrations,(0,d.eq)(i.testResults.testRegistrationId,i.testRegistrations.id)).innerJoin(i.candidates,(0,d.eq)(i.testRegistrations.candidateId,i.candidates.id)).where((0,d.Uo)((0,d.eq)(i.testResults.enteredBy,e.user.id),(0,d.eq)(i.candidates.organizationId,e.user.organizationId))).orderBy((0,o.i)(i.testResults.createdAt)).limit(10)]),p=[{title:"Total Results",value:t[0]?.count||0,icon:h.A,description:"Results entered by you",trend:"+5% from last month"},{title:"Pending Review",value:s[0]?.count||0,icon:g,description:"Draft results",trend:"Awaiting completion"},{title:"This Month",value:u[0]?.count||0,icon:b,description:"Completed results",trend:"+12% from last month"},{title:"Average Score",value:m.length>0?(m.reduce((e,t)=>e+parseFloat(t.overallBandScore||"0"),0)/m.length).toFixed(1):"0.0",icon:f.A,description:"Recent average",trend:"Last 10 results"}];return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Checker Dashboard"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Welcome back! Here's your test result entry overview."})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:p.map((e,t)=>(0,r.jsx)(c.C,{...e},t))}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quick Actions"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsx)(j(),{href:"/checker/entry",children:(0,r.jsxs)("div",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer",children:[(0,r.jsx)(h.A,{className:"h-8 w-8 text-blue-600 mb-2"}),(0,r.jsx)("h3",{className:"font-medium text-gray-900",children:"Enter New Result"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Add test results for a candidate"})]})}),(0,r.jsx)(j(),{href:"/checker/results",children:(0,r.jsxs)("div",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer",children:[(0,r.jsx)(g,{className:"h-8 w-8 text-yellow-600 mb-2"}),(0,r.jsx)("h3",{className:"font-medium text-gray-900",children:"Review Drafts"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Complete pending result entries"})]})}),(0,r.jsx)(j(),{href:"/checker/results?status=completed",children:(0,r.jsxs)("div",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer",children:[(0,r.jsx)(b,{className:"h-8 w-8 text-green-600 mb-2"}),(0,r.jsx)("h3",{className:"font-medium text-gray-900",children:"View Completed"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"See your completed results"})]})})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Recent Results"}),(0,r.jsx)(j(),{href:"/checker/results",children:(0,r.jsx)(y.$,{variant:"outline",size:"sm",children:"View All"})})]}),(0,r.jsx)(x,{results:m})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Performance Summary"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:m.filter(e=>"completed"===e.status).length}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Completed Today"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:m.filter(e=>parseFloat(e.overallBandScore||"0")>=7).length}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"High Scores (7+)"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-purple-600",children:[Math.round(m.filter(e=>"completed"===e.status).length/Math.max(m.length,1)*100),"%"]}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Completion Rate"})]})]})]})]})}},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67973:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},72984:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var r=s(75986),a=s(8974);function n(...e){return(0,a.QP)((0,r.$)(e))}},73877:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,85814,23))},74456:(e,t,s)=>{"use strict";s.d(t,{Header:()=>o});var r=s(60687),a=s(99208),n=s(29523),i=s(58869),l=s(84027),d=s(40083);function o({user:e}){return(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"Enhanced IELTS System"}),e.masterAdmin&&(0,r.jsx)("span",{className:"ml-3 px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full",children:"Master Admin"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(i.A,{className:"h-4 w-4 text-gray-500"}),(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("div",{className:"font-medium text-gray-900",children:e.name}),(0,r.jsx)("div",{className:"text-gray-500 capitalize",children:e.role})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(n.$,{variant:"ghost",size:"sm",children:(0,r.jsx)(l.A,{className:"h-4 w-4"})}),(0,r.jsxs)(n.$,{variant:"ghost",size:"sm",onClick:()=>{(0,a.CI)({callbackUrl:"/login"})},children:[(0,r.jsx)(d.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"ml-1",children:"Sign Out"})]})]})]})]})})})}},74998:e=>{"use strict";e.exports=require("perf_hooks")},77598:e=>{"use strict";e.exports=require("node:crypto")},78245:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},79551:e=>{"use strict";e.exports=require("url")},91026:()=>{},91645:e=>{"use strict";e.exports=require("net")},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o,metadata:()=>d});var r=s(37413),a=s(22376),n=s.n(a),i=s(68726),l=s.n(i);s(61135);let d={title:"Create Next App",description:"Generated by create next app"};function o({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${n().variable} ${l().variable} antialiased`,children:e})})}},97115:(e,t,s)=>{Promise.resolve().then(s.bind(s,10590)),Promise.resolve().then(s.bind(s,59105))}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,5552,2190,1057,145,1658,9069,5814,892,4017,6590,6326],()=>s(22739));module.exports=r})();