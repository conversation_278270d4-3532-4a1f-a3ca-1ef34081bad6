(()=>{var e={};e.id=9368,e.ids=[9368],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3631:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,4536,23)),Promise.resolve().then(t.bind(t,94154)),Promise.resolve().then(t.bind(t,99129))},4536:(e,s,t)=>{let{createProxy:a}=t(39844);e.exports=a("C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\node_modules\\next\\dist\\client\\app-dir\\link.js")},7959:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>d});var a=t(65239),r=t(48088),i=t(88170),l=t.n(i),n=t(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d={children:["",{children:["(public)",{children:["search",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,26496)),"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\(public)\\search\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,58448)),"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\(public)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\(public)\\search\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(public)/search/page",pathname:"/search",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16189:(e,s,t)=>{"use strict";var a=t(65773);t.o(a,"usePathname")&&t.d(s,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(s,{useSearchParams:function(){return a.useSearchParams}})},17581:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]])},18062:(e,s,t)=>{"use strict";t.d(s,{PublicSearchForm:()=>g});var a=t(60687),r=t(43210),i=t(16189),l=t(27605),n=t(63442),c=t(9275),d=t(29523),o=t(89667),m=t(93613),x=t(41862);let h=(0,t(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]),u=c.z.object({passport:c.z.string().min(5,"Passport/Birth certificate number must be at least 5 characters"),dateOfBirth:c.z.string().min(1,"Date of birth is required")});function g(){let[e,s]=(0,r.useState)(!1),[t,c]=(0,r.useState)(null),g=(0,i.useRouter)(),{register:j,handleSubmit:p,formState:{errors:b}}=(0,l.mN)({resolver:(0,n.u)(u)}),y=async e=>{s(!0),c(null);try{let s=await fetch("/api/public/validate-search",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok){let e=await s.json();throw Error(e.message||"Search validation failed")}let t=new URLSearchParams({passport:e.passport.trim(),dob:e.dateOfBirth});g.push(`/search?${t.toString()}`)}catch(e){console.error("Search error:",e),c(e instanceof Error?e.message:"Search failed. Please try again.")}finally{s(!1)}};return(0,a.jsxs)("form",{onSubmit:p(y),className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Passport or Birth Certificate Number"}),(0,a.jsx)(o.p,{...j("passport"),placeholder:"Enter your passport or birth certificate number",className:"text-lg",error:b.passport?.message,disabled:e}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Enter the exact number used during test registration"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Date of Birth"}),(0,a.jsx)(o.p,{...j("dateOfBirth"),type:"date",className:"text-lg",error:b.dateOfBirth?.message,disabled:e}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Select your date of birth as registered"})]}),t&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(m.A,{className:"h-5 w-5 text-red-600 mr-3 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Search Error"}),(0,a.jsx)("p",{className:"text-sm text-red-700 mt-1",children:t})]})]})}),(0,a.jsx)(d.$,{type:"submit",disabled:e,className:"w-full text-lg py-3",children:e?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.A,{className:"h-5 w-5 mr-2 animate-spin"}),"Searching..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(h,{className:"h-5 w-5 mr-2"}),"Search My Results"]})}),(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"bg-blue-100 p-1 rounded-full mr-3 mt-0.5",children:(0,a.jsx)(m.A,{className:"h-4 w-4 text-blue-600"})}),(0,a.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,a.jsx)("h4",{className:"font-medium mb-1",children:"Privacy & Security"}),(0,a.jsx)("p",{children:"Your personal information is protected and only used to retrieve your test results. We do not store or share your search data."})]})]})}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Search Tips:"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,a.jsx)("li",{children:"• Use the exact passport/birth certificate number from your registration"}),(0,a.jsx)("li",{children:"• Check for any spaces or special characters in your document number"}),(0,a.jsx)("li",{children:"• Ensure your date of birth matches your registration details"}),(0,a.jsx)("li",{children:"• Results are available immediately after test completion"})]})]})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19352:(e,s,t)=>{"use strict";t.d(s,{a:()=>d});var a=t(60687),r=t(43210),i=t(13516),l=t(18908),n=t(11860),c=t(7766);function d({isOpen:e,onClose:s,title:t,children:d,size:o="md"}){return(0,a.jsx)(i.e,{appear:!0,show:e,as:r.Fragment,children:(0,a.jsxs)(l.lG,{as:"div",className:"relative z-50",onClose:s,children:[(0,a.jsx)(i.e.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,a.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,a.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,a.jsx)(i.e.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,a.jsxs)(l.lG.Panel,{className:(0,c.cn)("w-full transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl"}[o]),children:[t&&(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)(l.lG.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:t}),(0,a.jsx)("button",{type:"button",className:"rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500",onClick:s,children:(0,a.jsx)(n.A,{className:"h-5 w-5"})})]}),d]})})})})]})})}},20916:(e,s,t)=>{"use strict";function a(e,s="UZS"){return"UZS"===s?new Intl.NumberFormat("uz-UZ",{style:"currency",currency:"UZS",minimumFractionDigits:0}).format(e):new Intl.NumberFormat("en-US",{style:"currency",currency:s}).format(e)}t.d(s,{ej:()=>a})},21820:e=>{"use strict";e.exports=require("os")},25541:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},26496:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var a=t(37413),r=t(61120),i=t(99129),l=t(71682),n=t(32767),c=t(94634),d=t(16048),o=t(94154),m=t(27467),x=t(26373);let h=(0,x.A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var u=t(78768),g=t(4536),j=t.n(g);async function p({passport:e,dateOfBirth:s}){try{let t=await l.db.select({candidate:n.candidates,organization:n.organizations}).from(n.candidates).leftJoin(n.organizations,(0,c.eq)(n.candidates.organizationId,n.organizations.id)).where((0,c.Uo)((0,c.eq)(n.candidates.passportNumber,e.trim()),(0,c.eq)(n.candidates.dateOfBirth,new Date(s)))).limit(1);if(0===t.length)return(0,a.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8 text-center",children:[(0,a.jsx)("div",{className:"bg-red-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center",children:(0,a.jsx)(m.A,{className:"h-8 w-8 text-red-600"})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"No Results Found"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"We couldn't find any test results for the provided passport/birth certificate number and date of birth."}),(0,a.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6",children:[(0,a.jsx)("h3",{className:"font-medium text-yellow-800 mb-2",children:"Please check:"}),(0,a.jsxs)("ul",{className:"text-sm text-yellow-700 text-left space-y-1",children:[(0,a.jsx)("li",{children:"• Your passport/birth certificate number is entered correctly"}),(0,a.jsx)("li",{children:"• Your date of birth matches your registration details"}),(0,a.jsx)("li",{children:"• You have taken a test at one of our participating centers"}),(0,a.jsx)("li",{children:"• Your results have been processed (usually within 24 hours)"})]})]}),(0,a.jsx)(j(),{href:"/search",children:(0,a.jsxs)("button",{className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(h,{className:"h-4 w-4 mr-2"}),"Try Another Search"]})})]})});let{candidate:r,organization:i}=t[0],x=await l.db.select({registration:n.testRegistrations,result:n.testResults}).from(n.testRegistrations).leftJoin(n.testResults,(0,c.eq)(n.testRegistrations.id,n.testResults.testRegistrationId)).where((0,c.eq)(n.testRegistrations.candidateId,r.id)).orderBy((0,d.i)(n.testRegistrations.testDate));if(0===x.length)return(0,a.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8 text-center",children:[(0,a.jsx)("div",{className:"bg-blue-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center",children:(0,a.jsx)(u.A,{className:"h-8 w-8 text-blue-600"})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Candidate Found"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"We found your profile, but you don't have any test registrations yet."}),(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6",children:[(0,a.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"Next Steps:"}),(0,a.jsxs)("ul",{className:"text-sm text-blue-700 text-left space-y-1",children:[(0,a.jsx)("li",{children:"• Contact your test center to register for an IELTS test"}),(0,a.jsx)("li",{children:"• Results will appear here after test completion"}),(0,a.jsx)("li",{children:"• Check back after your test date for results"})]})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Test Center:"})," ",i?.name||"Unknown"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Candidate:"})," ",r.fullName]})]}),(0,a.jsx)(j(),{href:"/search",className:"mt-4 inline-block",children:(0,a.jsxs)("button",{className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(h,{className:"h-4 w-4 mr-2"}),"New Search"]})})]})});return(0,a.jsx)(o.PublicResultsInterface,{candidate:r,organization:i,testData:x})}catch(e){return console.error("Search results error:",e),(0,a.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8 text-center",children:[(0,a.jsx)("div",{className:"bg-red-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center",children:(0,a.jsx)(m.A,{className:"h-8 w-8 text-red-600"})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Search Error"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"We encountered an error while searching for your results. Please try again."}),(0,a.jsx)(j(),{href:"/search",children:(0,a.jsxs)("button",{className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(h,{className:"h-4 w-4 mr-2"}),"Try Again"]})})]})})}}var b=t(88804),y=t(83799);let f=(0,x.A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]);function N({searchParams:e}){let s=e.passport&&e.dob;return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[(0,a.jsx)("div",{className:"bg-white shadow-sm border-b",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"bg-blue-600 p-2 rounded-lg",children:(0,a.jsx)(b.A,{className:"h-6 w-6 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"IELTS Results Portal"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Search and view your test results"})]})]})})})}),(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:s?(0,a.jsx)(r.Suspense,{fallback:(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}),children:(0,a.jsx)(p,{passport:e.passport,dateOfBirth:e.dob})}):(0,a.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("div",{className:"bg-blue-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center",children:(0,a.jsx)(u.A,{className:"h-8 w-8 text-blue-600"})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Find Your Results"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Enter your passport/birth certificate number and date of birth to view your IELTS test results"})]}),(0,a.jsx)(i.PublicSearchForm,{}),(0,a.jsxs)("div",{className:"mt-8 pt-8 border-t border-gray-200",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"What you can access:"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)(b.A,{className:"h-5 w-5 text-blue-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"Test Results"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Detailed score breakdown"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)(y.A,{className:"h-5 w-5 text-green-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"Progress Tracking"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Historical performance"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)(f,{className:"h-5 w-5 text-purple-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"Premium Features"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"AI feedback & certificates"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"mt-8 grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"How to Search"}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm text-gray-600",children:[(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"bg-blue-100 text-blue-600 rounded-full w-5 h-5 flex items-center justify-center text-xs font-medium mr-2 mt-0.5",children:"1"}),"Enter your passport or birth certificate number exactly as registered"]}),(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"bg-blue-100 text-blue-600 rounded-full w-5 h-5 flex items-center justify-center text-xs font-medium mr-2 mt-0.5",children:"2"}),"Select your date of birth from the calendar"]}),(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"bg-blue-100 text-blue-600 rounded-full w-5 h-5 flex items-center justify-center text-xs font-medium mr-2 mt-0.5",children:"3"}),"Click search to view all your test results"]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Need Help?"}),(0,a.jsxs)("div",{className:"space-y-3 text-sm text-gray-600",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Can't find your results?"}),(0,a.jsx)("br",{}),"Make sure you're using the exact passport/birth certificate number used during registration."]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Multiple test centers?"}),(0,a.jsx)("br",{}),"Our system searches across all participating IELTS centers automatically."]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Contact Support:"}),(0,a.jsx)("br",{}),(0,a.jsx)("a",{href:"mailto:<EMAIL>",className:"text-blue-600 hover:underline",children:"<EMAIL>"})]})]})]})]})]})}),(0,a.jsx)("footer",{className:"bg-white border-t mt-16",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsxs)("div",{className:"text-center text-gray-600",children:[(0,a.jsx)("p",{children:"\xa9 2024 IELTS Results Portal. All rights reserved."}),(0,a.jsxs)("div",{className:"mt-2 space-x-4",children:[(0,a.jsx)("a",{href:"/privacy",className:"text-blue-600 hover:underline",children:"Privacy Policy"}),(0,a.jsx)("a",{href:"/terms",className:"text-blue-600 hover:underline",children:"Terms of Service"}),(0,a.jsx)("a",{href:"/contact",className:"text-blue-600 hover:underline",children:"Contact Us"})]})]})})})]})}},27168:(e,s,t)=>{"use strict";t.d(s,{PublicResultsInterface:()=>eg});var a=t(60687),r=t(43210),i=t(7766),l=t(85008);let n=r.createContext(void 0);function c(){let e=r.useContext(n);if(!e)throw Error("Tabs components must be used within a Tabs provider");return e}function d({value:e,onValueChange:s,children:t,className:r}){return(0,a.jsx)(n.Provider,{value:{value:e,onValueChange:s},children:(0,a.jsx)("div",{className:(0,i.cn)("w-full",r),children:t})})}function o({children:e,className:s}){return(0,a.jsx)("div",{className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-gray-100 p-1 text-gray-500",s),children:e})}function m({value:e,children:s,className:t,disabled:r}){let{value:l,onValueChange:n}=c(),d=l===e;return(0,a.jsx)("button",{type:"button",disabled:r,onClick:()=>!r&&n(e),className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-white transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",d?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900",t),children:s})}function x({value:e,children:s,className:t}){let{value:r}=c();return r!==e?null:(0,a.jsx)("div",{className:(0,i.cn)("mt-2 ring-offset-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2",t),children:s})}var h=t(96834),u=t(40228),g=t(5336),j=t(48730),p=t(10022),b=t(97992),y=t(62688);let f=(0,y.A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);var N=t(25541);function v({candidate:e,testData:s}){let t=s.filter(e=>e.result&&"completed"===e.result.status),r=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),i=e=>{let s={registered:{color:"blue",label:"Registered",icon:u.A},completed:{color:"green",label:"Completed",icon:g.A},cancelled:{color:"red",label:"Cancelled",icon:j.A}}[e]||{color:"gray",label:e,icon:j.A},t=s.icon;return(0,a.jsxs)(h.E,{variant:s.color,className:"flex items-center",children:[(0,a.jsx)(t,{className:"h-3 w-3 mr-1"}),s.label]})},n=(e,s,t,r)=>{let i=s?parseFloat(s):0,n=(0,l.Fd)(i);return(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:e}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:s||"N/A"}),null!==t&&r&&(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[t,"/",r]})]})]}),s&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(h.E,{variant:n.color,className:"text-xs",children:n.level}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:(0,l.EH)(i)})]})]})};return 0===t.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"bg-blue-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center",children:(0,a.jsx)(p.A,{className:"h-8 w-8 text-blue-600"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No Completed Tests"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"You don't have any completed test results yet."}),s.length>0&&(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md mx-auto",children:[(0,a.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"Upcoming Tests"}),(0,a.jsx)("div",{className:"space-y-2",children:s.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{children:r(e.registration.testDate)}),i(e.registration.status)]},e.registration.id))})]})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Test Results"}),(0,a.jsxs)(h.E,{variant:"blue",children:[t.length," Completed Test",t.length>1?"s":""]})]}),t.map((e,s)=>(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["IELTS Test #",e.registration.candidateNumber]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 mt-2 text-sm text-gray-600",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-1"}),r(e.registration.testDate)]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 mr-1"}),e.registration.testCenter]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-1",children:e.result?.overallBandScore||"N/A"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Overall Band Score"}),i(e.registration.status)]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[n("Listening",e.result?.listeningBandScore,e.result?.listeningScore,40),n("Reading",e.result?.readingBandScore,e.result?.readingScore,40),n("Writing",e.result?.writingBandScore),n("Speaking",e.result?.speakingBandScore)]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsxs)("h4",{className:"font-medium text-gray-900 mb-3 flex items-center",children:[(0,a.jsx)(f,{className:"h-4 w-4 mr-2"}),"Detailed Breakdown"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"font-medium text-gray-800 mb-2",children:"Writing Tasks"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Task 1:"}),(0,a.jsx)("span",{className:"font-medium",children:e.result?.writingTask1Score||"N/A"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Task 2:"}),(0,a.jsx)("span",{className:"font-medium",children:e.result?.writingTask2Score||"N/A"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"font-medium text-gray-800 mb-2",children:"Speaking Criteria"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Fluency & Coherence:"}),(0,a.jsx)("span",{className:"font-medium",children:e.result?.speakingFluencyScore||"N/A"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Lexical Resource:"}),(0,a.jsx)("span",{className:"font-medium",children:e.result?.speakingLexicalScore||"N/A"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Grammar:"}),(0,a.jsx)("span",{className:"font-medium",children:e.result?.speakingGrammarScore||"N/A"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Pronunciation:"}),(0,a.jsx)("span",{className:"font-medium",children:e.result?.speakingPronunciationScore||"N/A"})]})]})]})]})]}),e.result?.overallBandScore&&(0,a.jsx)("div",{className:"mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(N.A,{className:"h-5 w-5 text-blue-600 mr-3 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"font-medium text-blue-900 mb-1",children:"Performance Summary"}),(0,a.jsx)("p",{className:"text-sm text-blue-800",children:(0,l.EH)(parseFloat(e.result.overallBandScore))})]})]})})]},e.registration.id)),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"IELTS Band Score Guide"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-800",children:"Band 9-8"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Expert to Very Good User"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-800",children:"Band 7-6"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Good to Competent User"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-800",children:"Band 5-4"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Modest to Limited User"})]})]})]})]})}var w=t(19352),k=t(29523),S=t(27605),A=t(63442),C=t(9275),P=t(89667),T=t(17313),F=t(85778);let D=(0,y.A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);var I=t(93613),E=t(41862),M=t(20916);let B=C.z.object({paymentMethod:C.z.enum(["bank_transfer","cash"]),referenceNumber:C.z.string().min(1,"Reference number is required"),paymentDate:C.z.string().min(1,"Payment date is required"),notes:C.z.string().optional()});function L({isOpen:e,onClose:s,candidateId:t,featureType:i,resultId:l,amount:n,currency:c,onSubmit:d}){let[o,m]=(0,r.useState)(!1),[x,h]=(0,r.useState)(!1),{register:u,handleSubmit:j,reset:b,watch:y,formState:{errors:f}}=(0,S.mN)({resolver:(0,A.u)(B)}),N=y("paymentMethod"),v={bankName:"National Bank of Uzbekistan",accountNumber:"20208000600000000001",accountName:"TLD System LLC",swift:"NBFAUZ2X",purpose:`Payment for ${i} feature - ${t}`},C=async e=>{m(!0);try{let s=await fetch("/api/payments/manual",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({candidateId:t,featureType:i,resultId:l,amount:n,currency:c,...e})});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to submit payment")}b(),d()}catch(e){console.error("Manual payment submission error:",e),alert("Failed to submit payment. Please try again.")}finally{m(!1)}};return(0,a.jsx)(w.a,{isOpen:e,onClose:s,size:"large",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[(0,a.jsx)(T.A,{className:"h-6 w-6 text-blue-600 mr-2"}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Manual Payment"})]}),(0,a.jsxs)("form",{onSubmit:j(C),className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-blue-50 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-blue-900",children:"Payment Amount"}),(0,a.jsx)("div",{className:"text-sm text-blue-700",children:"One-time payment"})]}),(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-900",children:(0,M.ej)(n,c)})]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Payment Method"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("label",{className:"relative",children:[(0,a.jsx)("input",{type:"radio",value:"bank_transfer",...u("paymentMethod"),className:"sr-only"}),(0,a.jsxs)("div",{className:`
                  border-2 rounded-lg p-4 cursor-pointer transition-all
                  ${"bank_transfer"===N?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"}
                `,children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(F.A,{className:"h-5 w-5 text-gray-600 mr-2"}),(0,a.jsx)("span",{className:"font-medium",children:"Bank Transfer"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Transfer to our bank account"})]})]}),(0,a.jsxs)("label",{className:"relative",children:[(0,a.jsx)("input",{type:"radio",value:"cash",...u("paymentMethod"),className:"sr-only"}),(0,a.jsxs)("div",{className:`
                  border-2 rounded-lg p-4 cursor-pointer transition-all
                  ${"cash"===N?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"}
                `,children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(T.A,{className:"h-5 w-5 text-gray-600 mr-2"}),(0,a.jsx)("span",{className:"font-medium",children:"Cash Payment"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Pay at our office"})]})]})]}),f.paymentMethod&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:f.paymentMethod.message})]}),"bank_transfer"===N&&(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900",children:"Bank Transfer Details"}),(0,a.jsx)(k.$,{type:"button",variant:"outline",size:"sm",onClick:()=>{let e=`
Bank: ${v.bankName}
Account: ${v.accountNumber}
Account Name: ${v.accountName}
SWIFT: ${v.swift}
Amount: ${(0,M.ej)(n,c)}
Purpose: ${v.purpose}
    `.trim();navigator.clipboard.writeText(e),h(!0),setTimeout(()=>h(!1),2e3)},children:x?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.A,{className:"h-4 w-4 mr-1"}),"Copied"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(D,{className:"h-4 w-4 mr-1"}),"Copy Details"]})})]}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Bank Name:"}),(0,a.jsx)("span",{className:"font-medium",children:v.bankName})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Account Number:"}),(0,a.jsx)("span",{className:"font-medium font-mono",children:v.accountNumber})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Account Name:"}),(0,a.jsx)("span",{className:"font-medium",children:v.accountName})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"SWIFT Code:"}),(0,a.jsx)("span",{className:"font-medium font-mono",children:v.swift})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Amount:"}),(0,a.jsx)("span",{className:"font-bold text-blue-600",children:(0,M.ej)(n,c)})]}),(0,a.jsxs)("div",{className:"pt-2 border-t",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Purpose:"}),(0,a.jsx)("div",{className:"font-medium text-sm mt-1 p-2 bg-white rounded border",children:v.purpose})]})]})]}),"cash"===N&&(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900 mb-3",children:"Office Address"}),(0,a.jsxs)("div",{className:"text-sm text-gray-700",children:[(0,a.jsx)("div",{className:"font-medium",children:"TLD System Office"}),(0,a.jsx)("div",{children:"123 Amir Temur Street"}),(0,a.jsx)("div",{children:"Tashkent, Uzbekistan 100000"}),(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)("span",{className:"font-medium",children:"Office Hours:"})," Mon-Fri 9:00-18:00"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Phone:"})," +998 71 123 4567"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Reference Number"}),(0,a.jsx)(P.p,{placeholder:"Transaction/Receipt number",...u("referenceNumber"),error:f.referenceNumber?.message})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Payment Date"}),(0,a.jsx)(P.p,{type:"date",...u("paymentDate"),error:f.paymentDate?.message})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Additional Notes (Optional)"}),(0,a.jsx)("textarea",{...u("notes"),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Any additional information about your payment..."})]}),(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(I.A,{className:"h-5 w-5 text-yellow-600 mr-3 mt-0.5"}),(0,a.jsxs)("div",{className:"text-sm text-yellow-800",children:[(0,a.jsx)("div",{className:"font-medium mb-1",children:"Important Notice"}),(0,a.jsx)("div",{children:"Manual payments require admin approval and may take 1-3 business days to process. You will receive an email confirmation once your payment is verified and access is granted."})]})]})}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,a.jsx)(k.$,{type:"button",variant:"outline",onClick:s,disabled:o,children:"Cancel"}),(0,a.jsx)(k.$,{type:"submit",disabled:o,children:o?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(E.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Submitting..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Submit Payment"]})})]})]})]})})}var R=t(17581);let U=(0,y.A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);function q({candidateId:e,featureType:s,resultId:t,amount:i,currency:l,onPaymentComplete:n,onPaymentError:c,onProcessingChange:d}){let[o,m]=(0,r.useState)(null),[x,u]=(0,r.useState)(!1),[p,b]=(0,r.useState)(!1),y=[{id:"click",name:"Click",icon:(0,a.jsx)(F.A,{className:"h-6 w-6"}),description:"Pay with Click - Fast and secure",processingTime:"Instant",badge:"Recommended",badgeColor:"green"},{id:"payme",name:"Payme",icon:(0,a.jsx)(R.A,{className:"h-6 w-6"}),description:"Pay with Payme - Convenient mobile payments",processingTime:"Instant",badge:"Popular",badgeColor:"blue"},{id:"manual",name:"Manual Payment",icon:(0,a.jsx)(T.A,{className:"h-6 w-6"}),description:"Bank transfer or cash payment with admin approval",processingTime:"1-3 business days",badge:"Manual Approval",badgeColor:"orange"}],f=async a=>{if("manual"===a)return void u(!0);m(a),b(!0),d(!0);try{let r=await fetch("/api/payments/initiate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({candidateId:e,featureType:s,gateway:a,resultId:t,returnUrl:`${window.location.origin}/payment/return`})}),i=await r.json();if(!r.ok)throw Error(i.error||"Payment initiation failed");if(i.paymentUrl)window.location.href=i.paymentUrl;else throw Error("No payment URL received")}catch(e){console.error("Payment initiation error:",e),c(e instanceof Error?e.message:"Payment failed"),b(!1),d(!1),m(null)}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Select Payment Method"}),y.map(e=>(0,a.jsxs)("div",{className:`
              relative border rounded-lg p-4 cursor-pointer transition-all
              ${o===e.id?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300 hover:bg-gray-50"}
              ${p&&o!==e.id?"opacity-50 pointer-events-none":""}
            `,onClick:()=>!p&&f(e.id),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:`
                  p-2 rounded-lg
                  ${o===e.id?"bg-blue-100 text-blue-600":"bg-gray-100 text-gray-600"}
                `,children:e.icon}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:e.name}),(0,a.jsx)(h.E,{variant:e.badgeColor,children:e.badge})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center mt-1",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 text-gray-400 mr-1"}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:e.processingTime})]})]})]}),(0,a.jsx)("div",{className:"flex items-center",children:p&&o===e.id?(0,a.jsx)(E.A,{className:"h-5 w-5 text-blue-600 animate-spin"}):o===e.id?(0,a.jsx)(g.A,{className:"h-5 w-5 text-blue-600"}):(0,a.jsx)(U,{className:"h-5 w-5 text-gray-400"})})]}),"click"===e.id&&(0,a.jsx)("div",{className:"mt-3 pt-3 border-t border-gray-200",children:(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"• Supports all major cards and Click wallet • Instant confirmation • Secure 3D authentication"})}),"payme"===e.id&&(0,a.jsx)("div",{className:"mt-3 pt-3 border-t border-gray-200",children:(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"• Mobile-first payment experience • QR code and SMS payments • Instant confirmation"})}),"manual"===e.id&&(0,a.jsx)("div",{className:"mt-3 pt-3 border-t border-gray-200",children:(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"• Bank transfer or cash payment • Requires admin approval • Upload payment proof"})})]},e.id)),(0,a.jsx)("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 text-green-500 mt-0.5"}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900 mb-1",children:"Secure Payment"}),(0,a.jsx)("div",{children:"All payments are processed securely with 256-bit SSL encryption. Your payment information is never stored on our servers."})]})]})}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 text-center",children:["By proceeding with payment, you agree to our"," ",(0,a.jsx)("a",{href:"/terms",className:"text-blue-600 hover:underline",children:"Terms of Service"})," ","and"," ",(0,a.jsx)("a",{href:"/privacy",className:"text-blue-600 hover:underline",children:"Privacy Policy"})]})]}),(0,a.jsx)(L,{isOpen:x,onClose:()=>u(!1),candidateId:e,featureType:s,resultId:t,amount:i,currency:l,onSubmit:()=>{u(!1),n()}})]})}let z=(0,y.A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]),O=(0,y.A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),$=(0,y.A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);var G=t(11860);let W={feedback:{amount:5e4,currency:"UZS",description:"AI-powered personalized feedback and study recommendations"},certificate:{amount:3e4,currency:"UZS",description:"Official IELTS certificate with verification"},progress:{amount:25e3,currency:"UZS",description:"Detailed progress tracking and analytics"}},V={feedback:"Get detailed AI-powered feedback on your IELTS performance with personalized study recommendations",certificate:"Download your official IELTS certificate with verification QR code",progress:"Access detailed progress tracking, performance analytics, and historical comparisons"};function _({isOpen:e,onClose:s,featureType:t,candidateId:i,resultId:l,onPaymentSuccess:n,children:c}){let[d,o]=(0,r.useState)(!1),[m,x]=(0,r.useState)(!1),u=W[t],p=V[t],b=()=>{switch(t){case"feedback":return"AI-Powered Feedback";case"certificate":return"Official Certificate";case"progress":return"Progress Analytics";default:return"Premium Feature"}};return(0,a.jsxs)(a.Fragment,{children:[c&&(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"filter blur-sm pointer-events-none",children:c}),(0,a.jsx)("div",{className:"absolute inset-0 bg-white/80 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center p-6",children:[(0,a.jsx)($,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Premium Feature Locked"}),(0,a.jsxs)("p",{className:"text-gray-600 mb-4",children:["Unlock ",b()," to access this content"]}),(0,a.jsxs)(k.$,{onClick:()=>o(!0),children:[(0,a.jsx)(F.A,{className:"h-4 w-4 mr-2"}),"Unlock Now - ",(0,M.ej)(u.amount,u.currency)]})]})})]}),(0,a.jsx)(w.a,{isOpen:e||d,onClose:s,size:"large",children:(0,a.jsx)("div",{className:"p-6",children:d?(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Choose Payment Method"}),(0,a.jsx)(k.$,{variant:"ghost",size:"sm",onClick:()=>o(!1),children:(0,a.jsx)(G.A,{className:"h-5 w-5"})})]}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:b()}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"One-time payment"})]}),(0,a.jsx)("div",{className:"text-xl font-bold text-gray-900",children:(0,M.ej)(u.amount,u.currency)})]})}),(0,a.jsx)(q,{candidateId:i,featureType:t,resultId:l,amount:u.amount,currency:u.currency,onPaymentComplete:()=>{x(!1),o(!1),s(),n?.()},onPaymentError:e=>{x(!1),console.error("Payment error:",e)},onProcessingChange:x}),m&&(0,a.jsx)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(j.A,{className:"h-5 w-5 text-blue-600 mr-3 animate-spin"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-blue-900",children:"Processing Payment..."}),(0,a.jsx)("div",{className:"text-sm text-blue-700",children:"Please wait while we process your payment"})]})]})})]}):(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-6",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:"flex items-center justify-center mb-4",children:(()=>{switch(t){case"feedback":return(0,a.jsx)(z,{className:"h-8 w-8 text-yellow-500"});case"certificate":return(0,a.jsx)(O,{className:"h-8 w-8 text-blue-500"});case"progress":return(0,a.jsx)(g.A,{className:"h-8 w-8 text-green-500"});default:return(0,a.jsx)($,{className:"h-8 w-8 text-gray-500"})}})()}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:b()}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:p})]}),(0,a.jsx)(k.$,{variant:"ghost",size:"sm",onClick:s,className:"text-gray-400 hover:text-gray-600",children:(0,a.jsx)(G.A,{className:"h-5 w-5"})})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6 mb-6",children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-gray-900 mb-2",children:(0,M.ej)(u.amount,u.currency)}),(0,a.jsx)(h.E,{variant:"blue",className:"mb-4",children:"One-time payment"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:u.description})]}),(0,a.jsxs)("div",{className:"text-left mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"What you'll get:"}),(0,a.jsx)("div",{className:"space-y-3",children:(()=>{switch(t){case"feedback":return["Detailed performance analysis","Personalized study recommendations","Weakness identification","Improvement strategies","AI-powered insights"];case"certificate":return["Official IELTS certificate","Verification QR code","Digital download","Shareable format","Lifetime validity"];case"progress":return["Detailed progress tracking","Performance comparisons","Historical analytics","Trend analysis","Goal setting tools"];default:return[]}})().map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 text-green-500 mr-3 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-gray-700",children:e})]},s))})]}),(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,a.jsx)(O,{className:"h-4 w-4 mr-2"}),(0,a.jsx)("span",{children:"Secure payment processing with 256-bit SSL encryption"})]})}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)(k.$,{variant:"outline",onClick:s,className:"flex-1",children:"Maybe Later"}),(0,a.jsxs)(k.$,{onClick:()=>{o(!0)},className:"flex-1",disabled:m,children:[(0,a.jsx)(F.A,{className:"h-4 w-4 mr-2"}),"Continue to Payment"]})]})]})})})]})}function H({candidateId:e,featureType:s,resultId:t}){let[a,i]=(0,r.useState)({hasAccess:!1,isLoading:!0,error:null}),l=async()=>{i(e=>({...e,isLoading:!0,error:null}));try{let a=new URLSearchParams({candidateId:e,featureType:s});t&&a.append("resultId",t);let r=await fetch(`/api/payments/verify?${a.toString()}`);if(!r.ok)throw Error("Failed to check access");let l=await r.json();i({hasAccess:l.hasAccess,isLoading:!1,error:null,accessType:l.accessType,expiresAt:l.expiresAt?new Date(l.expiresAt):void 0,daysRemaining:l.daysRemaining})}catch(e){i({hasAccess:!1,isLoading:!1,error:e instanceof Error?e.message:"Unknown error"})}};return{...a,refreshAccess:()=>{l()}}}let Z=(0,y.A)("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]),Y=(0,y.A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]]);var Q=t(53411);let J=(0,y.A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);var K=t(86561);let X=(0,y.A)("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]),ee=(0,y.A)("arrow-down",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);function es({candidate:e,testData:s}){let[t,i]=(0,r.useState)(!1),{hasAccess:l,isLoading:n}=H({candidateId:e.id,featureType:"progress"}),c=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),d=(e,s)=>{let t=e-s;return .5>Math.abs(t)?{trend:"stable",icon:Z,color:"text-gray-500"}:t>0?{trend:"up",icon:N.A,color:"text-green-500"}:{trend:"down",icon:Y,color:"text-red-500"}},o=()=>{if(s.length<2)return null;let e=s[0],t=s[1],a=parseFloat(e.result.overallBandScore||"0"),r=parseFloat(t.result.overallBandScore||"0");return{overall:{current:a,previous:r,trend:d(a,r)},listening:{current:parseFloat(e.result.listeningBandScore||"0"),previous:parseFloat(t.result.listeningBandScore||"0")},reading:{current:parseFloat(e.result.readingBandScore||"0"),previous:parseFloat(t.result.readingBandScore||"0")},writing:{current:parseFloat(e.result.writingBandScore||"0"),previous:parseFloat(t.result.writingBandScore||"0")},speaking:{current:parseFloat(e.result.speakingBandScore||"0"),previous:parseFloat(t.result.speakingBandScore||"0")}}},m=()=>{if(0===s.length)return(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(Q.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No Progress Data"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Take more tests to see your progress over time."})]});if(1===s.length){let e=s[0];return(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(J,{className:"h-12 w-12 text-blue-600 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"First Test Completed!"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Take another test to see your progress and improvement trends."}),(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md mx-auto",children:[(0,a.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"Your Current Score"}),(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:e.result.overallBandScore}),(0,a.jsxs)("div",{className:"text-sm text-blue-700",children:["Test Date: ",c(e.registration.testDate)]})]})]})})}let e=o();return e?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Overall Progress"}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-blue-600",children:e.overall.current}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Current Band Score"})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(e.overall.trend.icon,{className:`h-5 w-5 ${e.overall.trend.color}`}),(0,a.jsx)("span",{className:`font-medium ${e.overall.trend.color}`,children:Math.abs(e.overall.current-e.overall.previous).toFixed(1)})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["vs Previous: ",e.overall.previous]})]})]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[{name:"Listening",data:e.listening},{name:"Reading",data:e.reading},{name:"Writing",data:e.writing},{name:"Speaking",data:e.speaking}].map(e=>{let s=d(e.data.current,e.data.previous);return(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:e.name}),(0,a.jsx)(s.icon,{className:`h-4 w-4 ${s.color}`})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"text-xl font-bold text-gray-900",children:e.data.current}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["was ",e.data.previous]})]})]},e.name)})}),(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Test History"}),(0,a.jsx)("div",{className:"space-y-3",children:s.slice(0,3).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"bg-blue-100 p-2 rounded-full",children:(0,a.jsx)(u.A,{className:"h-4 w-4 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:c(e.registration.testDate)}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.registration.testCenter})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-gray-900",children:e.result.overallBandScore}),0===s&&(0,a.jsx)(h.E,{variant:"blue",className:"text-xs",children:"Latest"})]})]},e.result.id))})]})]}):null};return n?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):l?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Progress Analytics"}),(0,a.jsx)(h.E,{variant:"green",children:"Premium Feature"})]}),m(),s.length>=2&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-900 mb-4 flex items-center",children:[(0,a.jsx)(J,{className:"h-5 w-5 mr-2"}),"Improvement Recommendations"]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(X,{className:"h-5 w-5 text-green-600 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-green-900",children:"Strengths"}),(0,a.jsx)("div",{className:"text-sm text-green-800",children:"Your listening skills show consistent improvement. Keep practicing with varied accents."})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(ee,{className:"h-5 w-5 text-orange-600 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-orange-900",children:"Areas for Improvement"}),(0,a.jsx)("div",{className:"text-sm text-orange-800",children:"Focus on writing task 2 structure and coherence to boost your writing score."})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-blue-900 mb-4",children:"Goal Tracking"}),(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-blue-900",children:"Target Band Score: 7.0"}),(0,a.jsxs)("span",{className:"text-sm text-blue-700",children:["Current: ",s[0]?.result.overallBandScore||"N/A"]})]}),(0,a.jsx)("div",{className:"w-full bg-blue-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${Math.min(100,parseFloat(s[0]?.result.overallBandScore||"0")/7*100)}%`}})})]})})]})]})]}):(0,a.jsx)(_,{isOpen:t,onClose:()=>i(!1),featureType:"progress",candidateId:e.id,onPaymentSuccess:()=>window.location.reload(),children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"filter blur-sm pointer-events-none",children:m()}),(0,a.jsx)("div",{className:"absolute inset-0 bg-white/80 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center p-6",children:[(0,a.jsx)($,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Premium Progress Analytics"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Unlock detailed progress tracking, performance analytics, and historical comparisons"}),(0,a.jsxs)(k.$,{onClick:()=>i(!0),children:[(0,a.jsx)(K.A,{className:"h-4 w-4 mr-2"}),"Unlock Progress Analytics - 25,000 UZS"]})]})})]})})}let et=(0,y.A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]),ea=(0,y.A)("lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]),er=(0,y.A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]),ei=(0,y.A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]);function el({candidate:e,testData:s}){let[t,i]=(0,r.useState)(s[0]?.result.id||""),[l,n]=(0,r.useState)(!1),[c,d]=(0,r.useState)(null),[o,m]=(0,r.useState)(!1),[x,u]=(0,r.useState)(null),{hasAccess:j,isLoading:p}=H({candidateId:e.id,featureType:"feedback",resultId:t}),b=async()=>{try{m(!0),u(null);let e=await fetch(`/api/ai/feedback?testResultId=${t}`);if(404===e.status)return void await y();if(!e.ok)throw Error("Failed to fetch feedback");let s=await e.json();d(s),s.listeningFeedback&&""!==s.overallFeedback||setTimeout(b,3e3)}catch(e){u(e instanceof Error?e.message:"Failed to load feedback")}finally{m(!1)}},y=async()=>{try{let e=await fetch("/api/ai/feedback",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({testResultId:t})});if(!e.ok)throw Error("Failed to generate feedback");let s=await e.json();d(s),setTimeout(b,3e3)}catch(e){u(e instanceof Error?e.message:"Failed to generate feedback")}},f=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});if(p)return(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})});if(!j)return(0,a.jsx)(_,{isOpen:l,onClose:()=>n(!1),featureType:"feedback",candidateId:e.id,resultId:t,onPaymentSuccess:()=>window.location.reload(),children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"filter blur-sm pointer-events-none",children:(()=>{if(0===s.length)return(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(et,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No Test Results"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Complete a test to receive AI-powered feedback and recommendations."})]});let e=s.find(e=>e.result.id===t)||s[0];return(0,a.jsxs)("div",{className:"space-y-6",children:[s.length>1&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Test for Feedback"}),(0,a.jsx)("select",{value:t,onChange:e=>i(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:s.map(e=>(0,a.jsxs)("option",{value:e.result.id,children:[f(e.registration.testDate)," - Band ",e.result.overallBandScore]},e.result.id))})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Performance Overview"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:e.result.listeningBandScore||"N/A"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Listening"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:e.result.readingBandScore||"N/A"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Reading"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:e.result.writingBandScore||"N/A"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Writing"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:e.result.speakingBandScore||"N/A"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Speaking"})]})]})]}),(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-blue-900 mb-4 flex items-center",children:[(0,a.jsx)(ea,{className:"h-5 w-5 mr-2"}),"General Study Tips"]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 text-green-600 mt-0.5"}),(0,a.jsx)("div",{className:"text-sm text-blue-800",children:"Practice regularly with authentic IELTS materials"})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 text-green-600 mt-0.5"}),(0,a.jsx)("div",{className:"text-sm text-blue-800",children:"Focus on time management during practice sessions"})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 text-green-600 mt-0.5"}),(0,a.jsx)("div",{className:"text-sm text-blue-800",children:"Expand your vocabulary through reading diverse topics"})]})]})]})]})})()}),(0,a.jsx)("div",{className:"absolute inset-0 bg-white/80 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center p-6",children:[(0,a.jsx)($,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"AI-Powered Feedback"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Get detailed AI analysis, personalized study recommendations, and improvement strategies"}),(0,a.jsxs)(k.$,{onClick:()=>n(!0),children:[(0,a.jsx)(z,{className:"h-4 w-4 mr-2"}),"Unlock AI Feedback - 50,000 UZS"]})]})})]})});let v=s.find(e=>e.result.id===t)||s[0];return o?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(E.A,{className:"h-8 w-8 animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:c?.overallFeedback===""?"AI is analyzing your performance...":"Loading feedback..."})]})}):x?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(I.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-red-600 mb-4",children:x}),(0,a.jsx)(k.$,{onClick:b,variant:"outline",children:"Try Again"})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"AI-Powered Feedback"}),(0,a.jsx)(h.E,{variant:"green",children:"Premium Feature"})]}),s.length>1&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Test for Detailed Feedback"}),(0,a.jsx)("select",{value:t,onChange:e=>i(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:s.map(e=>(0,a.jsxs)("option",{value:e.result.id,children:[f(e.registration.testDate)," - Band ",e.result.overallBandScore]},e.result.id))})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-purple-900 mb-4 flex items-center",children:[(0,a.jsx)(er,{className:"h-5 w-5 mr-2"}),"AI Performance Analysis"]}),(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-purple-900 mb-2",children:"Overall Assessment"}),(0,a.jsx)("p",{className:"text-sm text-purple-800",children:c?.overallFeedback||`Based on your band score of ${v.result.overallBandScore}, you demonstrate ${parseFloat(v.result.overallBandScore||"0")>=7?" good":" developing"} English proficiency.`})]})})]}),c&&(c.strengths.length>0||c.weaknesses.length>0)&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[c.strengths.length>0&&(0,a.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-900 mb-4 flex items-center",children:[(0,a.jsx)(N.A,{className:"h-5 w-5 mr-2"}),"Strengths"]}),(0,a.jsx)("div",{className:"space-y-2",children:c.strengths.map((e,s)=>(0,a.jsx)(h.E,{variant:"secondary",className:"bg-green-100 text-green-800 mr-2 mb-2",children:e},s))})]}),c.weaknesses.length>0&&(0,a.jsxs)("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-orange-900 mb-4 flex items-center",children:[(0,a.jsx)(J,{className:"h-5 w-5 mr-2"}),"Areas for Improvement"]}),(0,a.jsx)("div",{className:"space-y-2",children:c.weaknesses.map((e,s)=>(0,a.jsx)(h.E,{variant:"secondary",className:"bg-orange-100 text-orange-800 mr-2 mb-2",children:e},s))})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,a.jsxs)("h4",{className:"font-medium text-gray-900 mb-3 flex items-center",children:[(0,a.jsx)(J,{className:"h-4 w-4 mr-2 text-blue-600"}),"Listening Analysis"]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Band Score"}),(0,a.jsx)("span",{className:"font-bold text-blue-600",children:v.result.listeningBandScore})]}),(0,a.jsx)("div",{className:"bg-blue-50 p-3 rounded",children:(0,a.jsx)("p",{className:"text-xs text-blue-800",children:c?.listeningFeedback||"Good comprehension of main ideas and supporting details. Practice with different accents and faster speech patterns."})})]})]}),(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,a.jsxs)("h4",{className:"font-medium text-gray-900 mb-3 flex items-center",children:[(0,a.jsx)(ei,{className:"h-4 w-4 mr-2 text-green-600"}),"Reading Analysis"]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Band Score"}),(0,a.jsx)("span",{className:"font-bold text-green-600",children:v.result.readingBandScore})]}),(0,a.jsx)("div",{className:"bg-green-50 p-3 rounded",children:(0,a.jsx)("p",{className:"text-xs text-green-800",children:c?.readingFeedback||"Effective skimming and scanning techniques. Work on inference and understanding implicit meaning."})})]})]}),(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,a.jsxs)("h4",{className:"font-medium text-gray-900 mb-3 flex items-center",children:[(0,a.jsx)(et,{className:"h-4 w-4 mr-2 text-purple-600"}),"Writing Analysis"]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Band Score"}),(0,a.jsx)("span",{className:"font-bold text-purple-600",children:v.result.writingBandScore})]}),(0,a.jsx)("div",{className:"bg-purple-50 p-3 rounded",children:(0,a.jsx)("p",{className:"text-xs text-purple-800",children:c?.writingFeedback||"Clear task response and good organization. Enhance lexical resource and grammatical range."})})]})]}),(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,a.jsxs)("h4",{className:"font-medium text-gray-900 mb-3 flex items-center",children:[(0,a.jsx)(z,{className:"h-4 w-4 mr-2 text-yellow-600"}),"Speaking Analysis"]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Band Score"}),(0,a.jsx)("span",{className:"font-bold text-yellow-600",children:v.result.speakingBandScore})]}),(0,a.jsx)("div",{className:"bg-yellow-50 p-3 rounded",children:(0,a.jsx)("p",{className:"text-xs text-yellow-800",children:c?.speakingFeedback||"Good fluency and natural conversation flow. Focus on pronunciation clarity and intonation."})})]})]})]}),c&&(c.studyRecommendations||c.studyPlan)&&(0,a.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-900 mb-4 flex items-center",children:[(0,a.jsx)(ei,{className:"h-5 w-5 mr-2"}),"Study Recommendations"]}),c.studyRecommendations&&(0,a.jsx)("div",{className:"bg-white rounded-lg p-4 mb-4",children:(0,a.jsx)("p",{className:"text-sm text-green-800",children:c.studyRecommendations})}),c.studyPlan?.plan&&(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-green-900 mb-2",children:"Personalized Study Plan"}),(0,a.jsx)("div",{className:"whitespace-pre-line text-sm text-green-800",children:c.studyPlan.plan})]})]}),!c&&(0,a.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-900 mb-4 flex items-center",children:[(0,a.jsx)(N.A,{className:"h-5 w-5 mr-2"}),"General Study Plan"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-green-900 mb-2",children:"Week 1-2: Focus on Writing"}),(0,a.jsxs)("ul",{className:"text-sm text-green-800 space-y-1",children:[(0,a.jsx)("li",{children:"• Practice Task 2 essay structure daily"}),(0,a.jsx)("li",{children:"• Learn 10 new academic vocabulary words per day"}),(0,a.jsx)("li",{children:"• Complete 3 practice essays with time limits"})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-green-900 mb-2",children:"Week 3-4: Speaking Enhancement"}),(0,a.jsxs)("ul",{className:"text-sm text-green-800 space-y-1",children:[(0,a.jsx)("li",{children:"• Record yourself speaking for 2 minutes daily"}),(0,a.jsx)("li",{children:"• Practice pronunciation with tongue twisters"}),(0,a.jsx)("li",{children:"• Engage in conversation practice with native speakers"})]})]})]})]})]})}let en=(0,y.A)("qr-code",[["rect",{width:"5",height:"5",x:"3",y:"3",rx:"1",key:"1tu5fj"}],["rect",{width:"5",height:"5",x:"16",y:"3",rx:"1",key:"1v8r4q"}],["rect",{width:"5",height:"5",x:"3",y:"16",rx:"1",key:"1x03jg"}],["path",{d:"M21 16h-3a2 2 0 0 0-2 2v3",key:"177gqh"}],["path",{d:"M21 21v.01",key:"ents32"}],["path",{d:"M12 7v3a2 2 0 0 1-2 2H7",key:"8crl2c"}],["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M12 3h.01",key:"n36tog"}],["path",{d:"M12 16v.01",key:"133mhm"}],["path",{d:"M16 12h1",key:"1slzba"}],["path",{d:"M21 12v.01",key:"1lwtk9"}],["path",{d:"M12 21v-1",key:"1880an"}]]),ec=(0,y.A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]),ed=(0,y.A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]);function eo({candidate:e,testData:s}){let[t,i]=(0,r.useState)(s[0]?.result.id||""),[l,n]=(0,r.useState)(!1),[c,d]=(0,r.useState)(null),[o,m]=(0,r.useState)(!1),[x,u]=(0,r.useState)(null),[j,b]=(0,r.useState)(!1),{hasAccess:y,isLoading:f}=H({candidateId:e.id,featureType:"certificate",resultId:t}),N=async()=>{try{m(!0),u(null);let e=await fetch(`/api/certificates?resultId=${t}`);if(404===e.status)return void d(null);if(!e.ok)throw Error("Failed to fetch certificate");let s=await e.json();d(s)}catch(e){u(e instanceof Error?e.message:"Failed to load certificate")}finally{m(!1)}},v=async()=>{try{b(!0),u(null);let e=await fetch("/api/certificates/generate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({resultId:t})});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to generate certificate")}let s=await e.json();d({id:s.certificateId,serialNumber:s.serialNumber,generatedAt:new Date().toISOString(),expiresAt:new Date(Date.now()+15552e6).toISOString(),status:"active",downloadUrl:s.downloadUrl})}catch(e){u(e instanceof Error?e.message:"Failed to generate certificate")}finally{b(!1)}},w=async()=>{if(c)try{let e=await fetch(c.downloadUrl);if(!e.ok)throw Error("Download failed");let s=await e.blob(),t=window.URL.createObjectURL(s),a=document.createElement("a");a.href=t,a.download=`IELTS_Certificate_${c.serialNumber}.pdf`,document.body.appendChild(a),a.click(),window.URL.revokeObjectURL(t),document.body.removeChild(a)}catch(e){u("Failed to download certificate")}},S=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});if(f)return(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})});if(!y)return(0,a.jsx)(_,{isOpen:l,onClose:()=>n(!1),featureType:"certificate",candidateId:e.id,resultId:t,onPaymentSuccess:()=>window.location.reload(),children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"filter blur-sm pointer-events-none",children:(()=>{if(0===s.length)return(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(K.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No Certificates Available"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Complete a test to generate your official IELTS certificate."})]});let r=s.find(e=>e.result.id===t)||s[0];return(0,a.jsxs)("div",{className:"space-y-6",children:[s.length>1&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Test for Certificate"}),(0,a.jsx)("select",{value:t,onChange:e=>i(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:s.map(e=>(0,a.jsxs)("option",{value:e.result.id,children:[S(e.registration.testDate)," - Band ",e.result.overallBandScore]},e.result.id))})]}),(0,a.jsx)("div",{className:"bg-gradient-to-br from-blue-50 to-indigo-100 border-2 border-blue-200 rounded-lg p-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"bg-blue-600 p-3 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center",children:(0,a.jsx)(K.A,{className:"h-8 w-8 text-white"})}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:"IELTS Test Certificate"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Official certification of your English proficiency"}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 mb-6 shadow-sm",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900 mb-4",children:e.fullName}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Test Date:"}),(0,a.jsx)("div",{className:"font-medium",children:S(r.registration.testDate)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Test Center:"}),(0,a.jsx)("div",{className:"font-medium",children:r.registration.testCenter})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Candidate Number:"}),(0,a.jsx)("div",{className:"font-medium",children:r.registration.candidateNumber})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Overall Band Score:"}),(0,a.jsx)("div",{className:"font-bold text-blue-600 text-lg",children:r.result.overallBandScore})]})]})]}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"This is a preview. Unlock the full certificate with verification features."})]})}),(0,a.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-yellow-900 mb-4",children:"Certificate Features"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(O,{className:"h-5 w-5 text-yellow-600 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-yellow-900",children:"Official Verification"}),(0,a.jsx)("div",{className:"text-sm text-yellow-800",children:"Digitally signed and verifiable"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(en,{className:"h-5 w-5 text-yellow-600 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-yellow-900",children:"QR Code Verification"}),(0,a.jsx)("div",{className:"text-sm text-yellow-800",children:"Instant verification via QR code"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(ec,{className:"h-5 w-5 text-yellow-600 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-yellow-900",children:"PDF Download"}),(0,a.jsx)("div",{className:"text-sm text-yellow-800",children:"High-quality PDF format"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(ed,{className:"h-5 w-5 text-yellow-600 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-yellow-900",children:"Shareable Link"}),(0,a.jsx)("div",{className:"text-sm text-yellow-800",children:"Share with institutions"})]})]})]})]})]})})()}),(0,a.jsx)("div",{className:"absolute inset-0 bg-white/80 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center p-6",children:[(0,a.jsx)($,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Official IELTS Certificate"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Download your official certificate with verification QR code and digital signature"}),(0,a.jsxs)(k.$,{onClick:()=>n(!0),children:[(0,a.jsx)(K.A,{className:"h-4 w-4 mr-2"}),"Get Certificate - 30,000 UZS"]})]})})]})});let A=s.find(e=>e.result.id===t)||s[0];return o?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(E.A,{className:"h-8 w-8 animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading certificate information..."})]})}):x?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(I.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-red-600 mb-4",children:x}),(0,a.jsx)(k.$,{onClick:N,variant:"outline",children:"Try Again"})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Official Certificate"}),(0,a.jsx)(h.E,{variant:"green",children:"Premium Feature"})]}),s.length>1&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Test for Certificate"}),(0,a.jsx)("select",{value:t,onChange:e=>i(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:s.map(e=>(0,a.jsxs)("option",{value:e.result.id,children:[S(e.registration.testDate)," - Band ",e.result.overallBandScore]},e.result.id))})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-indigo-100 border-2 border-blue-200 rounded-lg p-8",children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)("div",{className:"bg-blue-600 p-3 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center",children:(0,a.jsx)(K.A,{className:"h-8 w-8 text-white"})}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Official IELTS Certificate"}),(0,a.jsx)(h.E,{variant:"green",className:"mb-4",children:"Verified & Authentic"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-8 shadow-lg border-2 border-gray-200",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-blue-900 mb-2",children:"IELTS"}),(0,a.jsx)("h3",{className:"text-xl text-gray-700",children:"International English Language Testing System"}),(0,a.jsx)("div",{className:"w-24 h-1 bg-blue-600 mx-auto mt-4"})]}),(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("p",{className:"text-lg text-gray-700 mb-4",children:"This is to certify that"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:e.fullName}),(0,a.jsx)("p",{className:"text-lg text-gray-700 mb-2",children:"has achieved an overall band score of"}),(0,a.jsx)("div",{className:"text-4xl font-bold text-blue-600 mb-4",children:A.result.overallBandScore}),(0,a.jsx)("p",{className:"text-lg text-gray-700",children:"in the IELTS test"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 gap-4 mb-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-gray-900",children:A.result.listeningBandScore}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Listening"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-gray-900",children:A.result.readingBandScore}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Reading"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-gray-900",children:A.result.writingBandScore}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Writing"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-gray-900",children:A.result.speakingBandScore}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Speaking"})]})]}),(0,a.jsx)("div",{className:"border-t border-gray-200 pt-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Test Date:"}),(0,a.jsx)("div",{className:"font-medium",children:S(A.registration.testDate)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Test Center:"}),(0,a.jsx)("div",{className:"font-medium",children:A.registration.testCenter})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Candidate Number:"}),(0,a.jsx)("div",{className:"font-medium",children:A.registration.candidateNumber})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Certificate ID:"}),(0,a.jsxs)("div",{className:"font-medium",children:["CERT-",A.result.id.slice(-8).toUpperCase()]})]})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-between mt-6 pt-6 border-t border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(en,{className:"h-8 w-8 text-gray-600"}),(0,a.jsxs)("div",{className:"text-xs text-gray-600",children:[(0,a.jsx)("div",{children:"Scan to verify"}),(0,a.jsx)("div",{children:"authenticity"})]})]}),(0,a.jsxs)("div",{className:"text-right text-xs text-gray-600",children:[(0,a.jsxs)("div",{children:["Issued: ",S(A.result.createdAt)]}),(0,a.jsx)("div",{children:"Valid for 2 years"})]})]})]})]}),c?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 border",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Certificate Information"}),(0,a.jsx)(h.E,{variant:"active"===c.status?"default":"destructive",className:"active"===c.status?"bg-green-100 text-green-800":"",children:c.status.toUpperCase()})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-500",children:"Serial Number"}),(0,a.jsx)("p",{className:"font-mono text-lg",children:c.serialNumber})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-500",children:"Issue Date"}),(0,a.jsx)("p",{className:"text-lg",children:new Date(c.generatedAt).toLocaleDateString()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-500",children:"Expiry Date"}),(0,a.jsx)("p",{className:"text-lg",children:new Date(c.expiresAt).toLocaleDateString()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-gray-500",children:"Validity"}),(0,a.jsx)("p",{className:`text-lg ${new Date>new Date(c.expiresAt)?"text-orange-600":"text-green-600"}`,children:new Date>new Date(c.expiresAt)?"Expired":"Valid"})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,a.jsxs)(k.$,{onClick:w,className:"flex-1 sm:flex-none",disabled:"deleted"===c.status,children:[(0,a.jsx)(ec,{className:"h-4 w-4 mr-2"}),"Download PDF"]}),(0,a.jsxs)(k.$,{variant:"outline",className:"flex-1 sm:flex-none",onClick:()=>window.open(`/verify/${c.serialNumber}`,"_blank"),children:[(0,a.jsx)(U,{className:"h-4 w-4 mr-2"}),"Verify Online"]}),(0,a.jsxs)(k.$,{variant:"outline",className:"flex-1 sm:flex-none",onClick:()=>{let e=`${window.location.origin}/verify/${c.serialNumber}`;navigator.clipboard.writeText(e)},children:[(0,a.jsx)(ed,{className:"h-4 w-4 mr-2"}),"Copy Link"]})]})]}):(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(p.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No Certificate Generated"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Generate an official IELTS certificate for your test results."}),(0,a.jsx)(k.$,{onClick:v,disabled:j,className:"flex items-center gap-2",children:j?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(E.A,{className:"h-4 w-4 animate-spin"}),"Generating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"h-4 w-4"}),"Generate Certificate"]})})]}),c&&(0,a.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-900 mb-4 flex items-center",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 mr-2"}),"Certificate Verification"]}),(0,a.jsxs)("div",{className:"space-y-3 text-sm text-green-800",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Verification URL:"}),(0,a.jsxs)("span",{className:"ml-2 font-mono bg-white px-2 py-1 rounded",children:[window.location.origin,"/verify/",c.serialNumber]})]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Serial Number:"})," ",c.serialNumber]}),(0,a.jsx)("p",{children:"This certificate is digitally signed and can be verified by institutions worldwide. The QR code provides instant verification of authenticity."})]})]}),(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-blue-900 mb-4",children:"Certificate Information"}),(0,a.jsxs)("div",{className:"space-y-3 text-sm text-blue-800",children:[(0,a.jsx)("p",{children:"• This certificate is an official document verifying your IELTS test results."}),(0,a.jsx)("p",{children:"• The certificate is valid for 6 months from the test date."}),(0,a.jsx)("p",{children:"• You can verify the authenticity of this certificate using the QR code or serial number."}),(0,a.jsx)("p",{children:"• The certificate includes a secure verification system to prevent fraud."}),(0,a.jsx)("p",{children:"• After expiration, the certificate will be automatically deleted from our system."})]})]})]})}var em=t(58869);let ex=(0,y.A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var eh=t(85814),eu=t.n(eh);function eg({candidate:e,organization:s,testData:t}){let[i,l]=(0,r.useState)("results"),n=t.filter(e=>e.result&&"completed"===e.result.status),c=n[0],h=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});return(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg mb-6",children:[(0,a.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"bg-blue-100 p-3 rounded-full",children:(0,a.jsx)(em.A,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:e.fullName}),(0,a.jsx)("p",{className:"text-gray-600",children:"IELTS Test Results"})]})]}),(0,a.jsx)(eu(),{href:"/search",children:(0,a.jsxs)("button",{className:"inline-flex items-center px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:[(0,a.jsx)(ex,{className:"h-4 w-4 mr-2"}),"New Search"]})})]}),(0,a.jsxs)("div",{className:"mt-6 grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Document Number"}),(0,a.jsx)("div",{className:"font-medium",children:e.passportNumber})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Date of Birth"}),(0,a.jsx)("div",{className:"font-medium",children:h(e.dateOfBirth)})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Test Center"}),(0,a.jsx)("div",{className:"font-medium",children:s?.name||"Unknown"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(N.A,{className:"h-4 w-4 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Total Tests"}),(0,a.jsx)("div",{className:"font-medium",children:e.totalTests})]})]})]})]}),c&&(0,a.jsxs)("div",{className:"p-6 bg-gradient-to-r from-blue-50 to-indigo-50",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Latest Test Result"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:c.result?.overallBandScore||"N/A"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Overall"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-xl font-semibold text-gray-900",children:c.result?.listeningBandScore||"N/A"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Listening"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-xl font-semibold text-gray-900",children:c.result?.readingBandScore||"N/A"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Reading"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-xl font-semibold text-gray-900",children:c.result?.writingBandScore||"N/A"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Writing"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-xl font-semibold text-gray-900",children:c.result?.speakingBandScore||"N/A"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Speaking"})]})]}),(0,a.jsxs)("div",{className:"mt-3 text-sm text-gray-600",children:["Test Date: ",h(c.registration.testDate)," • Test Center: ",c.registration.testCenter]})]})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-lg",children:(0,a.jsxs)(d,{value:i,onValueChange:l,children:[(0,a.jsxs)(o,{className:"grid w-full grid-cols-4 bg-gray-50 p-1 rounded-t-lg",children:[(0,a.jsxs)(m,{value:"results",className:"flex items-center space-x-2",children:[(0,a.jsx)(p.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Results"})]}),(0,a.jsxs)(m,{value:"progress",className:"flex items-center space-x-2",children:[(0,a.jsx)(N.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Progress"})]}),(0,a.jsxs)(m,{value:"feedback",className:"flex items-center space-x-2",children:[(0,a.jsx)(et,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Feedback"})]}),(0,a.jsxs)(m,{value:"certificate",className:"flex items-center space-x-2",children:[(0,a.jsx)(K.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Certificate"})]})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)(x,{value:"results",className:"mt-0",children:(0,a.jsx)(v,{candidate:e,testData:t})}),(0,a.jsx)(x,{value:"progress",className:"mt-0",children:(0,a.jsx)(es,{candidate:e,testData:n})}),(0,a.jsx)(x,{value:"feedback",className:"mt-0",children:(0,a.jsx)(el,{candidate:e,testData:n})}),(0,a.jsx)(x,{value:"certificate",className:"mt-0",children:(0,a.jsx)(eo,{candidate:e,testData:n})})]})]})}),(0,a.jsx)("div",{className:"mt-8 text-center text-gray-600",children:(0,a.jsx)("p",{className:"text-sm",children:"Results are updated in real-time. For questions about your results, contact your test center directly."})})]})}},27467:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(26373).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,s,t)=>{"use strict";t.d(s,{$:()=>l});var a=t(60687),r=t(43210),i=t(7766);let l=r.forwardRef(({className:e,variant:s="default",size:t="default",...r},l)=>(0,a.jsx)("button",{className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{"bg-blue-600 text-white hover:bg-blue-700":"default"===s,"bg-red-600 text-white hover:bg-red-700":"destructive"===s,"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50":"outline"===s,"bg-gray-100 text-gray-900 hover:bg-gray-200":"secondary"===s,"hover:bg-gray-100 hover:text-gray-900":"ghost"===s,"text-blue-600 underline-offset-4 hover:underline":"link"===s},{"h-10 px-4 py-2":"default"===t,"h-9 rounded-md px-3":"sm"===t,"h-11 rounded-md px-8":"lg"===t,"h-10 w-10":"icon"===t},e),ref:l,...r}));l.displayName="Button"},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},41862:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},48730:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63463:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,85814,23)),Promise.resolve().then(t.bind(t,27168)),Promise.resolve().then(t.bind(t,18062))},74998:e=>{"use strict";e.exports=require("perf_hooks")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},83799:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(26373).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},85008:(e,s,t)=>{"use strict";t.d(s,{AI:()=>l,EH:()=>c,Fd:()=>d,dN:()=>n});let a={39:9,38:9,37:8.5,36:8.5,35:8,34:8,33:7.5,32:7.5,31:7,30:7,29:6.5,28:6.5,27:6,26:6,25:5.5,24:5.5,23:5,22:5,21:4.5,20:4.5,19:4,18:4,17:3.5,16:3.5,15:3,14:3,13:2.5,12:2.5,11:2,10:2,9:1.5,8:1.5,7:1,6:1,5:.5,4:.5,3:0,2:0,1:0,0:0},r={39:9,38:9,37:8.5,36:8.5,35:8,34:8,33:7.5,32:7.5,31:7,30:7,29:6.5,28:6.5,27:6,26:6,25:5.5,24:5.5,23:5,22:5,21:4.5,20:4.5,19:4,18:4,17:3.5,16:3.5,15:3,14:3,13:2.5,12:2.5,11:2,10:2,9:1.5,8:1.5,7:1,6:1,5:.5,4:.5,3:0,2:0,1:0,0:0},i={40:9,39:8.5,38:8.5,37:8,36:8,35:7.5,34:7.5,33:7,32:7,31:6.5,30:6.5,29:6,28:6,27:5.5,26:5.5,25:5,24:5,23:4.5,22:4.5,21:4,20:4,19:3.5,18:3.5,17:3,16:3,15:2.5,14:2.5,13:2,12:2,11:1.5,10:1.5,9:1,8:1,7:.5,6:.5,5:0,4:0,3:0,2:0,1:0,0:0};function l(e,s,t="academic"){let n=Math.max(0,Math.min(40,Math.floor(s)));return"listening"===e?a[n]||0:"reading"===e&&("academic"===t?r:i)[n]||0}function n(e,s,t,a){return Math.round((e+s+t+a)/4*2)/2}function c(e){return({9:"Expert User",8.5:"Very Good User",8:"Very Good User",7.5:"Good User",7:"Good User",6.5:"Competent User",6:"Competent User",5.5:"Modest User",5:"Modest User",4.5:"Limited User",4:"Limited User",3.5:"Extremely Limited User",3:"Extremely Limited User",2.5:"Intermittent User",2:"Intermittent User",1.5:"Non User",1:"Non User",.5:"Did not attempt the test",0:"Did not attempt the test"})[e]||"Invalid Score"}function d(e){return e>=8.5?{level:"Excellent",color:"green",description:"Very high proficiency level"}:e>=7?{level:"Good",color:"blue",description:"Good proficiency level"}:e>=6?{level:"Competent",color:"yellow",description:"Competent proficiency level"}:e>=5?{level:"Modest",color:"orange",description:"Modest proficiency level"}:{level:"Limited",color:"red",description:"Limited proficiency level"}}},88804:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(26373).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},89667:(e,s,t)=>{"use strict";t.d(s,{p:()=>l});var a=t(60687),r=t(43210),i=t(7766);let l=r.forwardRef(({className:e,type:s,error:t,...r},l)=>(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)("input",{type:s,className:(0,i.cn)("flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:cursor-not-allowed disabled:opacity-50",t&&"border-red-500 focus:ring-red-500 focus:border-red-500",e),ref:l,...r}),t&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-500",children:t})]}));l.displayName="Input"},91645:e=>{"use strict";e.exports=require("net")},93613:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94154:(e,s,t)=>{"use strict";t.d(s,{PublicResultsInterface:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call PublicResultsInterface() from the server but PublicResultsInterface is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\public\\public-results-interface.tsx","PublicResultsInterface")},97992:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},99129:(e,s,t)=>{"use strict";t.d(s,{PublicSearchForm:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call PublicSearchForm() from the server but PublicSearchForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\public\\public-search-form.tsx","PublicSearchForm")}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[4447,5552,145,1658,9069,5814,367,7864],()=>t(7959));module.exports=a})();