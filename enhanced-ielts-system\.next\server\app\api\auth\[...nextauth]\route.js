/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/[...nextauth]/route";
exports.ids = ["app/api/auth/[...nextauth]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CTLD%20System%5Cenhanced-ielts-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CTLD%20System%5Cenhanced-ielts-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CTLD%20System%5Cenhanced-ielts-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CTLD%20System%5Cenhanced-ielts-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Windows_11_Documents_augment_projects_TLD_System_enhanced_ielts_system_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/[...nextauth]/route.ts */ \"(rsc)/./src/app/api/auth/[...nextauth]/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_Windows_11_Documents_augment_projects_TLD_System_enhanced_ielts_system_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_Windows_11_Documents_augment_projects_TLD_System_enhanced_ielts_system_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/[...nextauth]/route\",\n        pathname: \"/api/auth/[...nextauth]\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/[...nextauth]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\TLD System\\\\enhanced-ielts-system\\\\src\\\\app\\\\api\\\\auth\\\\[...nextauth]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Windows_11_Documents_augment_projects_TLD_System_enhanced_ielts_system_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CTLD%20System%5Cenhanced-ielts-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CTLD%20System%5Cenhanced-ielts-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/[...nextauth]/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/auth/[...nextauth]/route.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _lib_auth_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/auth/config */ \"(rsc)/./src/lib/auth/config.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_auth_config__WEBPACK_IMPORTED_MODULE_0__]);\n_lib_auth_config__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nconst { GET, POST } = _lib_auth_config__WEBPACK_IMPORTED_MODULE_0__.handlers;\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9hdXRoL1suLi5uZXh0YXV0aF0vcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTZDO0FBRXRDLE1BQU0sRUFBRUMsR0FBRyxFQUFFQyxJQUFJLEVBQUUsR0FBR0Ysc0RBQVFBLENBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV2luZG93cyAxMVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxUTEQgU3lzdGVtXFxlbmhhbmNlZC1pZWx0cy1zeXN0ZW1cXHNyY1xcYXBwXFxhcGlcXGF1dGhcXFsuLi5uZXh0YXV0aF1cXHJvdXRlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGhhbmRsZXJzIH0gZnJvbSAnQC9saWIvYXV0aC9jb25maWcnO1xuXG5leHBvcnQgY29uc3QgeyBHRVQsIFBPU1QgfSA9IGhhbmRsZXJzO1xuIl0sIm5hbWVzIjpbImhhbmRsZXJzIiwiR0VUIiwiUE9TVCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth/config.ts":
/*!********************************!*\
  !*** ./src/lib/auth/config.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   handlers: () => (/* binding */ handlers),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _auth_drizzle_adapter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @auth/drizzle-adapter */ \"(rsc)/./node_modules/@auth/drizzle-adapter/index.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _lib_db_schema__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! bcryptjs */ \"bcryptjs\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_db__WEBPACK_IMPORTED_MODULE_3__, bcryptjs__WEBPACK_IMPORTED_MODULE_5__]);\n([_lib_db__WEBPACK_IMPORTED_MODULE_3__, bcryptjs__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst { handlers, auth, signIn, signOut } = (0,next_auth__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    adapter: (0,_auth_drizzle_adapter__WEBPACK_IMPORTED_MODULE_2__.DrizzleAdapter)(_lib_db__WEBPACK_IMPORTED_MODULE_3__.db),\n    session: {\n        strategy: 'jwt'\n    },\n    pages: {\n        signIn: '/login'\n    },\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: 'credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                try {\n                    const user = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_4__.users).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_4__.users.email, credentials.email)).limit(1);\n                    if (!user.length) {\n                        return null;\n                    }\n                    const foundUser = user[0];\n                    if (foundUser.status !== 'active') {\n                        return null;\n                    }\n                    const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_5__[\"default\"].compare(credentials.password, foundUser.password);\n                    if (!isPasswordValid) {\n                        return null;\n                    }\n                    // Update last login\n                    await _lib_db__WEBPACK_IMPORTED_MODULE_3__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_4__.users).set({\n                        lastLoginAt: new Date()\n                    }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_4__.users.id, foundUser.id));\n                    return {\n                        id: foundUser.id,\n                        email: foundUser.email,\n                        name: foundUser.name,\n                        role: foundUser.role,\n                        organizationId: foundUser.organizationId,\n                        masterAdmin: foundUser.masterAdmin\n                    };\n                } catch (error) {\n                    console.error('Auth error:', error);\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n                token.organizationId = user.organizationId;\n                token.masterAdmin = user.masterAdmin;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (session.user) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n                session.user.organizationId = token.organizationId;\n                session.user.masterAdmin = token.masterAdmin;\n            }\n            return session;\n        }\n    }\n});\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth/config.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/index.ts":
/*!*****************************!*\
  !*** ./src/lib/db/index.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accessPermissions: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.accessPermissions),\n/* harmony export */   accessPermissionsRelations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.accessPermissionsRelations),\n/* harmony export */   aiFeedback: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.aiFeedback),\n/* harmony export */   aiFeedbackRelations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.aiFeedbackRelations),\n/* harmony export */   candidates: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.candidates),\n/* harmony export */   candidatesRelations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.candidatesRelations),\n/* harmony export */   certificateLifecycle: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.certificateLifecycle),\n/* harmony export */   certificateLifecycleRelations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.certificateLifecycleRelations),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   organizations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.organizations),\n/* harmony export */   organizationsRelations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.organizationsRelations),\n/* harmony export */   paymentTransactions: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.paymentTransactions),\n/* harmony export */   paymentTransactionsRelations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.paymentTransactionsRelations),\n/* harmony export */   promotionalRules: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.promotionalRules),\n/* harmony export */   promotionalRulesRelations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.promotionalRulesRelations),\n/* harmony export */   testRegistrations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.testRegistrations),\n/* harmony export */   testRegistrationsRelations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.testRegistrationsRelations),\n/* harmony export */   testResults: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.testResults),\n/* harmony export */   testResultsRelations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.testResultsRelations),\n/* harmony export */   users: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.users),\n/* harmony export */   usersRelations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.usersRelations)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/postgres-js */ \"(rsc)/./node_modules/drizzle-orm/postgres-js/driver.js\");\n/* harmony import */ var postgres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! postgres */ \"postgres\");\n/* harmony import */ var _schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./schema */ \"(rsc)/./src/lib/db/schema.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([postgres__WEBPACK_IMPORTED_MODULE_0__, drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__]);\n([postgres__WEBPACK_IMPORTED_MODULE_0__, drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nfunction createDatabase() {\n    if (!process.env.DATABASE_URL) {\n        throw new Error('DATABASE_URL environment variable is required');\n    }\n    const connectionString = process.env.DATABASE_URL;\n    // Disable prefetch as it is not supported for \"Transaction\" pool mode\n    const client = (0,postgres__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(connectionString, {\n        prepare: false\n    });\n    return (0,drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__.drizzle)(client, {\n        schema: _schema__WEBPACK_IMPORTED_MODULE_1__\n    });\n}\nconst db = createDatabase();\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFrRDtBQUNsQjtBQUNHO0FBRW5DLFNBQVNHO0lBQ1AsSUFBSSxDQUFDQyxRQUFRQyxHQUFHLENBQUNDLFlBQVksRUFBRTtRQUM3QixNQUFNLElBQUlDLE1BQU07SUFDbEI7SUFFQSxNQUFNQyxtQkFBbUJKLFFBQVFDLEdBQUcsQ0FBQ0MsWUFBWTtJQUVqRCxzRUFBc0U7SUFDdEUsTUFBTUcsU0FBU1Isb0RBQVFBLENBQUNPLGtCQUFrQjtRQUFFRSxTQUFTO0lBQU07SUFFM0QsT0FBT1YsZ0VBQU9BLENBQUNTLFFBQVE7UUFBRVAsTUFBTUEsc0NBQUFBO0lBQUM7QUFDbEM7QUFFTyxNQUFNUyxLQUFLUixpQkFBaUI7QUFHViIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXaW5kb3dzIDExXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXFRMRCBTeXN0ZW1cXGVuaGFuY2VkLWllbHRzLXN5c3RlbVxcc3JjXFxsaWJcXGRiXFxpbmRleC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBkcml6emxlIH0gZnJvbSAnZHJpenpsZS1vcm0vcG9zdGdyZXMtanMnO1xuaW1wb3J0IHBvc3RncmVzIGZyb20gJ3Bvc3RncmVzJztcbmltcG9ydCAqIGFzIHNjaGVtYSBmcm9tICcuL3NjaGVtYSc7XG5cbmZ1bmN0aW9uIGNyZWF0ZURhdGFiYXNlKCkge1xuICBpZiAoIXByb2Nlc3MuZW52LkRBVEFCQVNFX1VSTCkge1xuICAgIHRocm93IG5ldyBFcnJvcignREFUQUJBU0VfVVJMIGVudmlyb25tZW50IHZhcmlhYmxlIGlzIHJlcXVpcmVkJyk7XG4gIH1cblxuICBjb25zdCBjb25uZWN0aW9uU3RyaW5nID0gcHJvY2Vzcy5lbnYuREFUQUJBU0VfVVJMO1xuXG4gIC8vIERpc2FibGUgcHJlZmV0Y2ggYXMgaXQgaXMgbm90IHN1cHBvcnRlZCBmb3IgXCJUcmFuc2FjdGlvblwiIHBvb2wgbW9kZVxuICBjb25zdCBjbGllbnQgPSBwb3N0Z3Jlcyhjb25uZWN0aW9uU3RyaW5nLCB7IHByZXBhcmU6IGZhbHNlIH0pO1xuXG4gIHJldHVybiBkcml6emxlKGNsaWVudCwgeyBzY2hlbWEgfSk7XG59XG5cbmV4cG9ydCBjb25zdCBkYiA9IGNyZWF0ZURhdGFiYXNlKCk7XG5cbmV4cG9ydCB0eXBlIERhdGFiYXNlID0gdHlwZW9mIGRiO1xuZXhwb3J0ICogZnJvbSAnLi9zY2hlbWEnO1xuIl0sIm5hbWVzIjpbImRyaXp6bGUiLCJwb3N0Z3JlcyIsInNjaGVtYSIsImNyZWF0ZURhdGFiYXNlIiwicHJvY2VzcyIsImVudiIsIkRBVEFCQVNFX1VSTCIsIkVycm9yIiwiY29ubmVjdGlvblN0cmluZyIsImNsaWVudCIsInByZXBhcmUiLCJkYiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/schema.ts":
/*!******************************!*\
  !*** ./src/lib/db/schema.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accessPermissions: () => (/* binding */ accessPermissions),\n/* harmony export */   accessPermissionsRelations: () => (/* binding */ accessPermissionsRelations),\n/* harmony export */   aiFeedback: () => (/* binding */ aiFeedback),\n/* harmony export */   aiFeedbackRelations: () => (/* binding */ aiFeedbackRelations),\n/* harmony export */   candidates: () => (/* binding */ candidates),\n/* harmony export */   candidatesRelations: () => (/* binding */ candidatesRelations),\n/* harmony export */   certificateLifecycle: () => (/* binding */ certificateLifecycle),\n/* harmony export */   certificateLifecycleRelations: () => (/* binding */ certificateLifecycleRelations),\n/* harmony export */   organizations: () => (/* binding */ organizations),\n/* harmony export */   organizationsRelations: () => (/* binding */ organizationsRelations),\n/* harmony export */   paymentTransactions: () => (/* binding */ paymentTransactions),\n/* harmony export */   paymentTransactionsRelations: () => (/* binding */ paymentTransactionsRelations),\n/* harmony export */   promotionalRules: () => (/* binding */ promotionalRules),\n/* harmony export */   promotionalRulesRelations: () => (/* binding */ promotionalRulesRelations),\n/* harmony export */   testRegistrations: () => (/* binding */ testRegistrations),\n/* harmony export */   testRegistrationsRelations: () => (/* binding */ testRegistrationsRelations),\n/* harmony export */   testResults: () => (/* binding */ testResults),\n/* harmony export */   testResultsRelations: () => (/* binding */ testResultsRelations),\n/* harmony export */   users: () => (/* binding */ users),\n/* harmony export */   usersRelations: () => (/* binding */ usersRelations)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/table.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/text.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/json.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/timestamp.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/indexes.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/boolean.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/integer.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/unique-constraint.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/numeric.js\");\n/* harmony import */ var _paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @paralleldrive/cuid2 */ \"(rsc)/./node_modules/@paralleldrive/cuid2/index.js\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/relations.js\");\n\n\n\n// Organizations table - Master level management\nconst organizations = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('organizations', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    name: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('name').notNull(),\n    slug: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('slug').unique().notNull(),\n    settings: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.json)('settings').$type().default({}),\n    features: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.json)('features').$type().default([]),\n    billingPlan: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('billing_plan', {\n        enum: [\n            'basic',\n            'premium',\n            'enterprise'\n        ]\n    }).default('basic'),\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status', {\n        enum: [\n            'active',\n            'suspended',\n            'disabled'\n        ]\n    }).default('active'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('updated_at').defaultNow().notNull()\n}, (table)=>({\n        slugIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('org_slug_idx').on(table.slug),\n        statusIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('org_status_idx').on(table.status)\n    }));\n// Users table - Multi-level authentication\nconst users = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('users', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    organizationId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('organization_id').references(()=>organizations.id, {\n        onDelete: 'cascade'\n    }),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').unique().notNull(),\n    password: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('password').notNull(),\n    name: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('name').notNull(),\n    role: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('role', {\n        enum: [\n            'admin',\n            'checker'\n        ]\n    }).notNull(),\n    masterAdmin: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.boolean)('master_admin').default(false),\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status', {\n        enum: [\n            'active',\n            'inactive',\n            'suspended'\n        ]\n    }).default('active'),\n    lastLoginAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('last_login_at'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('updated_at').defaultNow().notNull()\n}, (table)=>({\n        emailIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('user_email_idx').on(table.email),\n        orgIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('user_org_idx').on(table.organizationId),\n        roleIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('user_role_idx').on(table.role)\n    }));\n// Candidates table - Single profile per person\nconst candidates = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('candidates', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    organizationId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('organization_id').references(()=>organizations.id, {\n        onDelete: 'cascade'\n    }).notNull(),\n    fullName: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('full_name').notNull(),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email'),\n    phoneNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('phone_number'),\n    dateOfBirth: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('date_of_birth'),\n    nationality: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('nationality'),\n    passportNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('passport_number').notNull(),\n    photoData: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('photo_data'),\n    studentStatus: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.boolean)('student_status').default(false),\n    totalTests: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.integer)('total_tests').default(0),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('updated_at').defaultNow().notNull()\n}, (table)=>({\n        passportIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('candidate_passport_idx').on(table.passportNumber),\n        orgIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('candidate_org_idx').on(table.organizationId),\n        nameIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('candidate_name_idx').on(table.fullName),\n        uniquePassport: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_8__.unique)('unique_passport_per_org').on(table.organizationId, table.passportNumber)\n    }));\n// Test Registrations table - Multiple tests per candidate\nconst testRegistrations = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('test_registrations', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    candidateId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_id').references(()=>candidates.id, {\n        onDelete: 'cascade'\n    }).notNull(),\n    candidateNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_number').notNull(),\n    testDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('test_date').notNull(),\n    testCenter: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_center').notNull(),\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status', {\n        enum: [\n            'registered',\n            'completed',\n            'cancelled'\n        ]\n    }).default('registered'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('updated_at').defaultNow().notNull()\n}, (table)=>({\n        candidateIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('test_reg_candidate_idx').on(table.candidateId),\n        dateIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('test_reg_date_idx').on(table.testDate),\n        statusIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('test_reg_status_idx').on(table.status)\n    }));\n// Test Results table - Comprehensive IELTS scoring\nconst testResults = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('test_results', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    testRegistrationId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_registration_id').references(()=>testRegistrations.id, {\n        onDelete: 'cascade'\n    }).notNull(),\n    // Listening scores\n    listeningScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.integer)('listening_score'),\n    listeningBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_9__.decimal)('listening_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Reading scores\n    readingScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.integer)('reading_score'),\n    readingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_9__.decimal)('reading_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Writing scores\n    writingTask1Score: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_9__.decimal)('writing_task1_score', {\n        precision: 2,\n        scale: 1\n    }),\n    writingTask2Score: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_9__.decimal)('writing_task2_score', {\n        precision: 2,\n        scale: 1\n    }),\n    writingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_9__.decimal)('writing_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Speaking scores\n    speakingFluencyScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_9__.decimal)('speaking_fluency_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingLexicalScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_9__.decimal)('speaking_lexical_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingGrammarScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_9__.decimal)('speaking_grammar_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingPronunciationScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_9__.decimal)('speaking_pronunciation_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_9__.decimal)('speaking_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Overall score\n    overallBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_9__.decimal)('overall_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Metadata\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status', {\n        enum: [\n            'draft',\n            'completed',\n            'verified'\n        ]\n    }).default('draft'),\n    enteredBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('entered_by').references(()=>users.id),\n    verifiedBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('verified_by').references(()=>users.id),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('updated_at').defaultNow().notNull()\n}, (table)=>({\n        testRegIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('test_result_reg_idx').on(table.testRegistrationId),\n        statusIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('test_result_status_idx').on(table.status),\n        overallScoreIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('test_result_overall_idx').on(table.overallBandScore)\n    }));\n// Payment Transactions table - Payment tracking\nconst paymentTransactions = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('payment_transactions', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    candidateId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_id').references(()=>candidates.id, {\n        onDelete: 'cascade'\n    }).notNull(),\n    organizationId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('organization_id').references(()=>organizations.id, {\n        onDelete: 'cascade'\n    }).notNull(),\n    amount: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_9__.decimal)('amount', {\n        precision: 10,\n        scale: 2\n    }).notNull(),\n    currency: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('currency').default('UZS').notNull(),\n    gateway: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('gateway', {\n        enum: [\n            'click',\n            'payme',\n            'manual'\n        ]\n    }).notNull(),\n    gatewayTransactionId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('gateway_transaction_id'),\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status', {\n        enum: [\n            'pending',\n            'completed',\n            'failed',\n            'cancelled',\n            'refunded'\n        ]\n    }).default('pending'),\n    featureType: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('feature_type', {\n        enum: [\n            'feedback',\n            'certificate',\n            'progress'\n        ]\n    }).notNull(),\n    resultId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('result_id').references(()=>testResults.id),\n    metadata: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.json)('metadata').$type().default({}),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('created_at').defaultNow().notNull(),\n    completedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('completed_at')\n}, (table)=>({\n        candidateIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('payment_candidate_idx').on(table.candidateId),\n        statusIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('payment_status_idx').on(table.status),\n        gatewayIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('payment_gateway_idx').on(table.gateway),\n        featureIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('payment_feature_idx').on(table.featureType)\n    }));\n// Access Permissions table - Premium feature access\nconst accessPermissions = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('access_permissions', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    candidateId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_id').references(()=>candidates.id, {\n        onDelete: 'cascade'\n    }).notNull(),\n    resultId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('result_id').references(()=>testResults.id, {\n        onDelete: 'cascade'\n    }),\n    featureType: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('feature_type', {\n        enum: [\n            'feedback',\n            'certificate',\n            'progress'\n        ]\n    }).notNull(),\n    accessType: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('access_type', {\n        enum: [\n            'paid',\n            'promotional',\n            'manual'\n        ]\n    }).notNull(),\n    grantedBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('granted_by').references(()=>users.id),\n    grantedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('granted_at').defaultNow().notNull(),\n    expiresAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('expires_at'),\n    metadata: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.json)('metadata').$type().default({})\n}, (table)=>({\n        candidateIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('access_candidate_idx').on(table.candidateId),\n        resultIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('access_result_idx').on(table.resultId),\n        featureIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('access_feature_idx').on(table.featureType),\n        expiryIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('access_expiry_idx').on(table.expiresAt)\n    }));\n// Promotional Rules table - Flexible promotion system\nconst promotionalRules = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('promotional_rules', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    organizationId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('organization_id').references(()=>organizations.id, {\n        onDelete: 'cascade'\n    }).notNull(),\n    name: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('name').notNull(),\n    type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('type', {\n        enum: [\n            'student_discount',\n            'loyalty_reward',\n            'time_based',\n            'custom'\n        ]\n    }).notNull(),\n    featureType: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('feature_type', {\n        enum: [\n            'feedback',\n            'certificate',\n            'progress',\n            'all'\n        ]\n    }).notNull(),\n    criteria: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.json)('criteria').$type().notNull(),\n    benefits: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.json)('benefits').$type().notNull(),\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status', {\n        enum: [\n            'active',\n            'inactive',\n            'expired'\n        ]\n    }).default('active'),\n    validFrom: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('valid_from').notNull(),\n    validUntil: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('valid_until'),\n    usageLimit: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.integer)('usage_limit'),\n    usageCount: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.integer)('usage_count').default(0),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('updated_at').defaultNow().notNull()\n}, (table)=>({\n        orgIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('promo_org_idx').on(table.organizationId),\n        statusIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('promo_status_idx').on(table.status),\n        typeIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('promo_type_idx').on(table.type),\n        validityIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('promo_validity_idx').on(table.validFrom, table.validUntil)\n    }));\n// AI Feedback table - Generated feedback storage\nconst aiFeedback = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('ai_feedback', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    testResultId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_result_id').references(()=>testResults.id, {\n        onDelete: 'cascade'\n    }).notNull().unique(),\n    listeningFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('listening_feedback'),\n    readingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('reading_feedback'),\n    writingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('writing_feedback'),\n    speakingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('speaking_feedback'),\n    overallFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('overall_feedback'),\n    studyRecommendations: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('study_recommendations'),\n    strengths: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.json)('strengths').$type().default([]),\n    weaknesses: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.json)('weaknesses').$type().default([]),\n    studyPlan: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.json)('study_plan').$type().default({}),\n    generatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('generated_at').defaultNow().notNull()\n}, (table)=>({\n        resultIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('ai_feedback_result_idx').on(table.testResultId)\n    }));\n// Certificate Lifecycle table - Certificate management\nconst certificateLifecycle = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('certificate_lifecycle', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    resultId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('result_id').references(()=>testResults.id, {\n        onDelete: 'cascade'\n    }).notNull().unique(),\n    serialNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('serial_number').unique().notNull(),\n    generatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('generated_at').defaultNow().notNull(),\n    expiresAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('expires_at').notNull(),\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status', {\n        enum: [\n            'active',\n            'expired',\n            'deleted'\n        ]\n    }).default('active'),\n    deletionScheduledAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('deletion_scheduled_at'),\n    metadata: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.json)('metadata').$type().default({}),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.timestamp)('updated_at').defaultNow().notNull()\n}, (table)=>({\n        resultIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('cert_result_idx').on(table.resultId),\n        serialIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('cert_serial_idx').on(table.serialNumber),\n        statusIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('cert_status_idx').on(table.status),\n        expiryIdx: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.index)('cert_expiry_idx').on(table.expiresAt)\n    }));\n// Relations\nconst organizationsRelations = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_10__.relations)(organizations, ({ many })=>({\n        users: many(users),\n        candidates: many(candidates),\n        paymentTransactions: many(paymentTransactions),\n        promotionalRules: many(promotionalRules)\n    }));\nconst usersRelations = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_10__.relations)(users, ({ one, many })=>({\n        organization: one(organizations, {\n            fields: [\n                users.organizationId\n            ],\n            references: [\n                organizations.id\n            ]\n        }),\n        enteredResults: many(testResults, {\n            relationName: 'enteredBy'\n        }),\n        verifiedResults: many(testResults, {\n            relationName: 'verifiedBy'\n        }),\n        grantedPermissions: many(accessPermissions)\n    }));\nconst candidatesRelations = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_10__.relations)(candidates, ({ one, many })=>({\n        organization: one(organizations, {\n            fields: [\n                candidates.organizationId\n            ],\n            references: [\n                organizations.id\n            ]\n        }),\n        testRegistrations: many(testRegistrations),\n        paymentTransactions: many(paymentTransactions),\n        accessPermissions: many(accessPermissions)\n    }));\nconst testRegistrationsRelations = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_10__.relations)(testRegistrations, ({ one, many })=>({\n        candidate: one(candidates, {\n            fields: [\n                testRegistrations.candidateId\n            ],\n            references: [\n                candidates.id\n            ]\n        }),\n        testResults: many(testResults)\n    }));\nconst testResultsRelations = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_10__.relations)(testResults, ({ one })=>({\n        testRegistration: one(testRegistrations, {\n            fields: [\n                testResults.testRegistrationId\n            ],\n            references: [\n                testRegistrations.id\n            ]\n        }),\n        enteredByUser: one(users, {\n            fields: [\n                testResults.enteredBy\n            ],\n            references: [\n                users.id\n            ],\n            relationName: 'enteredBy'\n        }),\n        verifiedByUser: one(users, {\n            fields: [\n                testResults.verifiedBy\n            ],\n            references: [\n                users.id\n            ],\n            relationName: 'verifiedBy'\n        }),\n        aiFeedback: one(aiFeedback, {\n            fields: [\n                testResults.id\n            ],\n            references: [\n                aiFeedback.testResultId\n            ]\n        }),\n        certificate: one(certificateLifecycle, {\n            fields: [\n                testResults.id\n            ],\n            references: [\n                certificateLifecycle.resultId\n            ]\n        })\n    }));\nconst paymentTransactionsRelations = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_10__.relations)(paymentTransactions, ({ one })=>({\n        candidate: one(candidates, {\n            fields: [\n                paymentTransactions.candidateId\n            ],\n            references: [\n                candidates.id\n            ]\n        }),\n        organization: one(organizations, {\n            fields: [\n                paymentTransactions.organizationId\n            ],\n            references: [\n                organizations.id\n            ]\n        }),\n        result: one(testResults, {\n            fields: [\n                paymentTransactions.resultId\n            ],\n            references: [\n                testResults.id\n            ]\n        })\n    }));\nconst accessPermissionsRelations = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_10__.relations)(accessPermissions, ({ one })=>({\n        candidate: one(candidates, {\n            fields: [\n                accessPermissions.candidateId\n            ],\n            references: [\n                candidates.id\n            ]\n        }),\n        result: one(testResults, {\n            fields: [\n                accessPermissions.resultId\n            ],\n            references: [\n                testResults.id\n            ]\n        }),\n        grantedByUser: one(users, {\n            fields: [\n                accessPermissions.grantedBy\n            ],\n            references: [\n                users.id\n            ]\n        })\n    }));\nconst promotionalRulesRelations = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_10__.relations)(promotionalRules, ({ one })=>({\n        organization: one(organizations, {\n            fields: [\n                promotionalRules.organizationId\n            ],\n            references: [\n                organizations.id\n            ]\n        })\n    }));\nconst aiFeedbackRelations = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_10__.relations)(aiFeedback, ({ one })=>({\n        testResult: one(testResults, {\n            fields: [\n                aiFeedback.testResultId\n            ],\n            references: [\n                testResults.id\n            ]\n        })\n    }));\nconst certificateLifecycleRelations = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_10__.relations)(certificateLifecycle, ({ one })=>({\n        result: one(testResults, {\n            fields: [\n                certificateLifecycle.resultId\n            ],\n            references: [\n                testResults.id\n            ]\n        })\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/schema.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "bcryptjs":
/*!***************************!*\
  !*** external "bcryptjs" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("bcryptjs");;

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "postgres":
/*!***************************!*\
  !*** external "postgres" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("postgres");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth","vendor-chunks/next-auth","vendor-chunks/drizzle-orm","vendor-chunks/oauth4webapi","vendor-chunks/jose","vendor-chunks/@noble","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/@paralleldrive","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CTLD%20System%5Cenhanced-ielts-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CTLD%20System%5Cenhanced-ielts-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();