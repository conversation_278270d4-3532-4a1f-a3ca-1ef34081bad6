(()=>{var e={};e.id=578,e.ids=[578],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6017:(e,t,r)=>{Promise.resolve().then(r.bind(r,59091))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35071:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},41550:(e,t,r)=>{Promise.resolve().then(r.bind(r,60573))},43649:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},55511:e=>{"use strict";e.exports=require("crypto")},58869:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},59091:(e,t,r)=>{"use strict";r.d(t,{CertificateVerificationDisplay:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call CertificateVerificationDisplay() from the server but CertificateVerificationDisplay is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\specialized\\certificate-verification.tsx","CertificateVerificationDisplay")},60573:(e,t,r)=>{"use strict";r.d(t,{CertificateVerificationDisplay:()=>m});var s=r(60687),i=r(96834),a=r(5336),n=r(43649),l=r(35071),c=r(58869),d=r(86561),o=r(40228);function m({certificate:e,isValid:t,isExpired:r}){return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"flex justify-center mb-4",children:t?(0,s.jsx)(a.A,{className:"h-8 w-8 text-green-600"}):r?(0,s.jsx)(n.A,{className:"h-8 w-8 text-orange-600"}):(0,s.jsx)(l.A,{className:"h-8 w-8 text-red-600"})}),(0,s.jsx)("h1",{className:`text-3xl font-bold ${t?"text-green-600":r?"text-orange-600":"text-red-600"}`,children:t?"Valid Certificate":r?"Expired Certificate":"Invalid Certificate"}),(0,s.jsxs)("p",{className:"text-gray-600 mt-2",children:["Certificate Serial Number: ",e.serialNumber]})]}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,s.jsx)(c.A,{className:"h-5 w-5 text-blue-600"}),(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Candidate Information"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Full Name"}),(0,s.jsx)("p",{className:"text-lg font-semibold",children:e.metadata.candidateName})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Test Date"}),(0,s.jsx)("p",{className:"text-lg",children:e.metadata.testDate})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Test Center"}),(0,s.jsx)("p",{className:"text-lg",children:e.metadata.organizationName})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,s.jsx)(d.A,{className:"h-5 w-5 text-green-600"}),(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Test Results"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Certificate Type"}),(0,s.jsx)("p",{className:"text-lg",children:e.metadata.certificateType})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Overall Band Score"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-2xl font-bold text-green-600",children:e.metadata.overallBandScore}),(0,s.jsxs)(i.E,{variant:"secondary",className:"bg-green-100 text-green-800",children:["Band ",e.metadata.overallBandScore]})]})]})]})]})}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,s.jsx)(o.A,{className:"h-5 w-5 text-blue-600"}),(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Certificate Status"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Issue Date"}),(0,s.jsx)("p",{className:"text-lg",children:e.generatedAt.toLocaleDateString()})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Expiry Date"}),(0,s.jsx)("p",{className:"text-lg",children:e.expiresAt.toLocaleDateString()})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Status"}),(0,s.jsx)(i.E,{variant:t?"default":"destructive",className:t?"bg-green-100 text-green-800":"",children:e.status.toUpperCase()})]})]}),r&&(0,s.jsx)("div",{className:"mt-4 p-4 bg-orange-50 border border-orange-200 rounded-lg",children:(0,s.jsxs)("p",{className:"text-orange-800",children:[(0,s.jsx)("strong",{children:"Note:"})," This certificate has expired. IELTS certificates are valid for 6 months from the test date."]})}),!t&&!r&&(0,s.jsx)("div",{className:"mt-4 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,s.jsxs)("p",{className:"text-red-800",children:[(0,s.jsx)("strong",{children:"Warning:"})," This certificate is not valid. It may have been revoked or deleted."]})})]}),(0,s.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-blue-800 mb-2",children:"Verification Information"}),(0,s.jsxs)("p",{className:"text-blue-700 text-sm",children:["This certificate has been verified against our secure database. The verification was performed on ",new Date().toLocaleDateString()," at ",new Date().toLocaleTimeString(),"."]})]})]})}},62688:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var s=r(43210);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),n=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:i,className:a="",children:n,iconNode:o,...m},x)=>(0,s.createElement)("svg",{ref:x,...d,width:t,height:t,stroke:e,strokeWidth:i?24*Number(r)/Number(t):r,className:l("lucide",a),...!n&&!c(m)&&{"aria-hidden":"true"},...m},[...o.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(n)?n:[n]])),m=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...a},c)=>(0,s.createElement)(o,{ref:c,iconNode:t,className:l(`lucide-${i(n(e))}`,`lucide-${e}`,r),...a}));return r.displayName=n(e),r}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75142:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(37413),i=r(71682),a=r(32767),n=r(94634),l=r(59091);async function c({params:e}){try{let t=await i.db.select().from(a.certificateLifecycle).where((0,n.eq)(a.certificateLifecycle.serialNumber,e.serial)).limit(1);if(0===t.length)return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"Certificate Not Found"}),(0,s.jsxs)("p",{className:"text-gray-600",children:["The certificate with serial number ",e.serial," could not be found."]})]})});let r=t[0],c=new Date>r.expiresAt,d="active"===r.status&&!c;return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 py-12",children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto px-4",children:(0,s.jsx)(l.CertificateVerificationDisplay,{certificate:r,isValid:d,isExpired:c})})})}catch(e){return console.error("Error verifying certificate:",e),(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"Verification Error"}),(0,s.jsx)("p",{className:"text-gray-600",children:"An error occurred while verifying the certificate."})]})})}}},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},91645:e=>{"use strict";e.exports=require("net")},94159:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>d});var s=r(65239),i=r(48088),a=r(88170),n=r.n(a),l=r(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);r.d(t,c);let d={children:["",{children:["(public)",{children:["verify",{children:["[serial]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,75142)),"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\(public)\\verify\\[serial]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,58448)),"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\(public)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\(public)\\verify\\[serial]\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(public)/verify/[serial]/page",pathname:"/verify/[serial]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,5552,145,1658,9069,7864],()=>r(94159));module.exports=s})();