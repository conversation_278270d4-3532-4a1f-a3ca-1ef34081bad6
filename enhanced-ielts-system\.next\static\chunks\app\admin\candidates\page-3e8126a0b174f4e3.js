(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8997],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var s=r(5155),a=r(2115),i=r(6486);let l=a.forwardRef((e,t)=>{let{className:r,variant:a="default",size:l="default",...n}=e;return(0,s.jsx)("button",{className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{"bg-blue-600 text-white hover:bg-blue-700":"default"===a,"bg-red-600 text-white hover:bg-red-700":"destructive"===a,"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50":"outline"===a,"bg-gray-100 text-gray-900 hover:bg-gray-200":"secondary"===a,"hover:bg-gray-100 hover:text-gray-900":"ghost"===a,"text-blue-600 underline-offset-4 hover:underline":"link"===a},{"h-10 px-4 py-2":"default"===l,"h-9 rounded-md px-3":"sm"===l,"h-11 rounded-md px-8":"lg"===l,"h-10 w-10":"icon"===l},r),ref:t,...n})});l.displayName="Button"},2523:(e,t,r)=>{"use strict";r.d(t,{p:()=>l});var s=r(5155),a=r(2115),i=r(6486);let l=a.forwardRef((e,t)=>{let{className:r,type:a,error:l,...n}=e;return(0,s.jsxs)("div",{className:"w-full",children:[(0,s.jsx)("input",{type:a,className:(0,i.cn)("flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:cursor-not-allowed disabled:opacity-50",l&&"border-red-500 focus:ring-red-500 focus:border-red-500",r),ref:t,...n}),l&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-500",children:l})]})});l.displayName="Input"},3352:(e,t,r)=>{"use strict";r.d(t,{a:()=>d});var s=r(5155),a=r(2115),i=r(5939),l=r(280),n=r(4416),o=r(6486);function d(e){let{isOpen:t,onClose:r,title:d,children:c,size:u="md"}=e;return(0,s.jsx)(i.e,{appear:!0,show:t,as:a.Fragment,children:(0,s.jsxs)(l.lG,{as:"div",className:"relative z-50",onClose:r,children:[(0,s.jsx)(i.e.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,s.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,s.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,s.jsx)(i.e.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,s.jsxs)(l.lG.Panel,{className:(0,o.cn)("w-full transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl"}[u]),children:[d&&(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)(l.lG.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:d}),(0,s.jsx)("button",{type:"button",className:"rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500",onClick:r,children:(0,s.jsx)(n.A,{className:"h-5 w-5"})})]}),c]})})})})]})})}},4180:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6874,23)),Promise.resolve().then(r.bind(r,9438))},5695:(e,t,r)=>{"use strict";var s=r(8999);r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},6486:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var s=r(2596),a=r(9688);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}},9438:(e,t,r)=>{"use strict";r.d(t,{CreateCandidateModal:()=>h});var s=r(5155),a=r(2115),i=r(5695),l=r(2177),n=r(221),o=r(1153),d=r(285),c=r(2523),u=r(3352);let m=o.z.object({fullName:o.z.string().min(2,"Full name must be at least 2 characters"),email:o.z.string().email("Invalid email address").optional().or(o.z.literal("")),phoneNumber:o.z.string().optional(),dateOfBirth:o.z.string().optional(),nationality:o.z.string().min(2,"Nationality is required"),passportNumber:o.z.string().min(5,"Passport number must be at least 5 characters"),studentStatus:o.z.boolean().default(!1)});function h(e){var t,r,o,h,g,f;let{children:x}=e,[p,b]=(0,a.useState)(!1),[y,v]=(0,a.useState)(!1),j=(0,i.useRouter)(),N=(0,l.mN)({resolver:(0,n.u)(m),defaultValues:{studentStatus:!1}}),w=async e=>{v(!0);try{let t=await fetch("/api/candidates",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e,email:e.email||null,dateOfBirth:e.dateOfBirth?new Date(e.dateOfBirth).toISOString():null})});if(!t.ok){let e=await t.json();throw Error(e.message||"Failed to create candidate")}await t.json(),b(!1),N.reset(),j.refresh()}catch(e){console.error("Error creating candidate:",e)}finally{v(!1)}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{onClick:()=>b(!0),children:x}),(0,s.jsx)(u.a,{isOpen:p,onClose:()=>b(!1),title:"Add New Candidate",children:(0,s.jsxs)("form",{onSubmit:N.handleSubmit(w),className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsx)(c.p,{...N.register("fullName"),placeholder:"Full Name *",error:null==(t=N.formState.errors.fullName)?void 0:t.message}),(0,s.jsx)(c.p,{...N.register("nationality"),placeholder:"Nationality *",error:null==(r=N.formState.errors.nationality)?void 0:r.message})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsx)(c.p,{...N.register("email"),type:"email",placeholder:"Email Address",error:null==(o=N.formState.errors.email)?void 0:o.message}),(0,s.jsx)(c.p,{...N.register("phoneNumber"),placeholder:"Phone Number",error:null==(h=N.formState.errors.phoneNumber)?void 0:h.message})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsx)(c.p,{...N.register("passportNumber"),placeholder:"Passport Number *",error:null==(g=N.formState.errors.passportNumber)?void 0:g.message}),(0,s.jsx)(c.p,{...N.register("dateOfBirth"),type:"date",placeholder:"Date of Birth",error:null==(f=N.formState.errors.dateOfBirth)?void 0:f.message})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"checkbox",...N.register("studentStatus"),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,s.jsx)("label",{className:"text-sm text-gray-700",children:"This candidate is a student (eligible for student discounts)"})]}),(0,s.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Important Notes:"}),(0,s.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,s.jsx)("li",{children:"• Passport number must be unique within your organization"}),(0,s.jsx)("li",{children:"• Student status affects promotional pricing eligibility"}),(0,s.jsx)("li",{children:"• Email is optional but recommended for notifications"})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,s.jsx)(d.$,{type:"button",variant:"outline",onClick:()=>b(!1),disabled:y,children:"Cancel"}),(0,s.jsx)(d.$,{type:"submit",disabled:y,children:y?"Creating...":"Create Candidate"})]})]})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,6874,4343,8441,1684,7358],()=>t(4180)),_N_E=e.O()}]);