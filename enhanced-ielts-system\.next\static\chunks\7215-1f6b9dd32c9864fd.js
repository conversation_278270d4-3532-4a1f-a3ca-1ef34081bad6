"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7215],{5493:(e,t,n)=>{n.d(t,{Jv:()=>et,CI:()=>en}),n(5155);var s,r,a,o,c,l=n(2115),i=n.t(l,2);class u extends Error{constructor(e,t){e instanceof Error?super(void 0,{cause:{err:e,...e.cause,...t}}):"string"==typeof e?(t instanceof Error&&(t={err:t,...t.cause}),super(e,t)):super(void 0,e),this.name=this.constructor.name,this.type=this.constructor.type??"AuthError",this.kind=this.constructor.kind??"error",Error.captureStackTrace?.(this,this.constructor);let n=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${n}`}}class d extends u{}d.kind="signIn";class h extends u{}h.type="AdapterError";class p extends u{}p.type="AccessDenied";class y extends u{}y.type="CallbackRouteError";class w extends u{}w.type="ErrorPageLoop";class v extends u{}v.type="EventError";class f extends u{}f.type="InvalidCallbackUrl";class g extends d{constructor(){super(...arguments),this.code="credentials"}}g.type="CredentialsSignin";class x extends u{}x.type="InvalidEndpoints";class E extends u{}E.type="InvalidCheck";class U extends u{}U.type="JWTSessionError";class m extends u{}m.type="MissingAdapter";class R extends u{}R.type="MissingAdapterMethods";class k extends u{}k.type="MissingAuthorize";class b extends u{}b.type="MissingSecret";class A extends d{}A.type="OAuthAccountNotLinked";class L extends d{}L.type="OAuthCallbackError";class S extends u{}S.type="OAuthProfileParseError";class T extends u{}T.type="SessionTokenError";class P extends d{}P.type="OAuthSignInError";class C extends d{}C.type="EmailSignInError";class _ extends u{}_.type="SignOutError";class N extends u{}N.type="UnknownAction";class I extends u{}I.type="UnsupportedStrategy";class M extends u{}M.type="InvalidProvider";class X extends u{}X.type="UntrustedHost";class H extends u{}H.type="Verification";class O extends d{}O.type="MissingCSRF";class j extends u{}j.type="DuplicateConditionalUI";class W extends u{}W.type="MissingWebAuthnAutocomplete";class V extends u{}V.type="WebAuthnVerificationError";class $ extends d{}$.type="AccountNotLinked";class J extends u{}J.type="ExperimentalFeatureNotEnabled";class B extends u{}async function D(e,t,n){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},r="".concat(F(t),"/").concat(e);try{var a;let e={headers:{"Content-Type":"application/json",...(null==s||null==(a=s.headers)?void 0:a.cookie)?{cookie:s.headers.cookie}:{}}};(null==s?void 0:s.body)&&(e.body=JSON.stringify(s.body),e.method="POST");let t=await fetch(r,e),n=await t.json();if(!t.ok)throw n;return n}catch(e){return n.error(new B(e.message,e)),null}}function F(e){return e.basePath}function z(e){let t=new URL("http://localhost:3000/api/auth");e&&!e.startsWith("http")&&(e="https://".concat(e));let n=new URL(e||t),s=("/"===n.pathname?t.pathname:n.pathname).replace(/\/$/,""),r="".concat(n.origin).concat(s);return{origin:n.origin,host:n.host,path:s,base:r,toString:()=>r}}var q=n(9509);let G={baseUrl:z(null!=(r=q.env.NEXTAUTH_URL)?r:q.env.VERCEL_URL).origin,basePath:z(q.env.NEXTAUTH_URL).path,baseUrlServer:z(null!=(o=null!=(a=q.env.NEXTAUTH_URL_INTERNAL)?a:q.env.NEXTAUTH_URL)?o:q.env.VERCEL_URL).origin,basePathServer:z(null!=(c=q.env.NEXTAUTH_URL_INTERNAL)?c:q.env.NEXTAUTH_URL).path,_lastSync:0,_session:void 0,_getSession:()=>{}},K=null;function Q(){return new BroadcastChannel("next-auth")}let Y={debug:console.debug,error:console.error,warn:console.warn};null==(s=l.createContext)||s.call(i,void 0);async function Z(){var e;let t=await D("csrf",G,Y);return null!=(e=null==t?void 0:t.csrfToken)?e:""}async function ee(){return D("providers",G,Y)}async function et(e,t,n){var s,r,a;let{callbackUrl:o,...c}=null!=t?t:{},{redirect:l=!0,redirectTo:i=null!=o?o:window.location.href,...u}=c,d=F(G),h=await ee();if(!h){window.location.href="".concat(d,"/error");return}if(!e||!h[e]){let e="".concat(d,"/signin?").concat(new URLSearchParams({callbackUrl:i}));window.location.href=e;return}let p=h[e].type;if("webauthn"===p)throw TypeError(['Provider id "'.concat(e,'" refers to a WebAuthn provider.'),'Please use `import { signIn } from "next-auth/webauthn"` instead.'].join("\n"));let y="".concat(d,"/").concat("credentials"===p?"callback":"signin","/").concat(e),w=await Z(),v=await fetch("".concat(y,"?").concat(new URLSearchParams(n)),{method:"post",headers:{"Content-Type":"application/x-www-form-urlencoded","X-Auth-Return-Redirect":"1"},body:new URLSearchParams({...u,csrfToken:w,callbackUrl:i})}),f=await v.json();if(l){let e=null!=(s=f.url)?s:i;window.location.href=e,e.includes("#")&&window.location.reload();return}let g=null!=(r=new URL(f.url).searchParams.get("error"))?r:void 0,x=null!=(a=new URL(f.url).searchParams.get("code"))?a:void 0;return v.ok&&await G._getSession({event:"storage"}),{error:g,code:x,status:v.status,ok:v.ok,url:g?null:f.url}}async function en(e){var t,n;let{redirect:s=!0,redirectTo:r=null!=(t=null==e?void 0:e.callbackUrl)?t:window.location.href}=null!=e?e:{},a=F(G),o=await Z(),c=await fetch("".concat(a,"/signout"),{method:"post",headers:{"Content-Type":"application/x-www-form-urlencoded","X-Auth-Return-Redirect":"1"},body:new URLSearchParams({csrfToken:o,callbackUrl:r})}),l=await c.json();if(("undefined"==typeof BroadcastChannel?{postMessage:()=>{},addEventListener:()=>{},removeEventListener:()=>{}}:(null===K&&(K=Q()),K)).postMessage({event:"session",data:{trigger:"signout"}}),s){let e=null!=(n=l.url)?n:r;window.location.href=e,e.includes("#")&&window.location.reload();return}return await G._getSession({event:"storage"}),l}},5695:(e,t,n)=>{var s=n(8999);n.o(s,"usePathname")&&n.d(t,{usePathname:function(){return s.usePathname}}),n.o(s,"useRouter")&&n.d(t,{useRouter:function(){return s.useRouter}}),n.o(s,"useSearchParams")&&n.d(t,{useSearchParams:function(){return s.useSearchParams}})}}]);